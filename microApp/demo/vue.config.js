const { defineConfig } = require('@vue/cli-service')
const { name } = require('./package.json')
const AutoImport = require('unplugin-auto-import/webpack')
const Components = require('unplugin-vue-components/webpack')
const { ElementPlusResolver } = require('unplugin-vue-components/resolvers')
const ElementPlus = require('unplugin-element-plus/webpack')
const Version = new Date().getTime()
const { cdn, externals } = require('common/config/cdn')
console.log(name)
process.env.VUE_APP_URL = process.env?.npm_config_url || ''
process.env.VUE_APP_CATALOG = process.env?.npm_config_catalog || ''
process.env.VUE_APP_NAME = name
module.exports = defineConfig({
  outputDir: `../../dist/microApp/${name}`,
  publicPath: process.env.NODE_ENV === 'development' ? '/' : `${process.env?.npm_config_catalog || '/'}microApp/${name}`,
  productionSourceMap: process.env?.npm_config_map === 'true' ? true : false,
  transpileDependencies: true,
  css: {
    extract: {
      filename: `static/css/[name].${name}.${Version}.css`,
      chunkFilename: `static/css/[name].${name}.${Version}.css`
    },
    loaderOptions: {
      scss: { // 如果配置为"additionalData"无效，请到官网查阅最新配置信息
        additionalData: '@use "@/assets/scss/common.scss" as *;@use "@/assets/scss/element-theme.scss" as *;'
      }
    }
  },
  chainWebpack: config => {
    config.plugin('html').tap(args => {
      args[0].cdn = process.env.NODE_ENV === 'development' ? cdn.dev : cdn.build
      return args
    })
    // config.module.rule('worker-loader').test(/\.worker\.js$/).use({
    //   loader: 'worker-loader', options: { inline: true }
    // }).loader('worker-loader').end()
  },
  configureWebpack: {
    externals: process.env.NODE_ENV === 'development' ? externals.dev : externals.build,
    cache: {
      type: 'filesystem',
      allowCollectingMemory: true
    },
    plugins: [
      AutoImport({
        resolvers: [ElementPlusResolver({ importStyle: 'sass' })]
      }),
      Components({
        resolvers: [ElementPlusResolver({ importStyle: 'sass' })]
      }),
      ElementPlus({ useSource: true })
    ],
    output: {
      filename: `static/js/[name].${name}.${Version}.js`,
      chunkFilename: `static/js/[name].${name}.${Version}.js`,
      library: `${name}-[name]`,
      // 把子应用打包成umd库格式
      // 当我们把 libraryTarget 设置为 umd 后，我们的 library 就暴露为所有的模块定义下都可运行的方式		了，主应用就可以获取到微应用的生命周期钩子函数了
      libraryTarget: 'umd',
      chunkLoading: 'jsonp'
    }
  },
  devServer: {
    port: 200,
    client: {
      overlay: false
    },
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
})
