<template>
  <el-config-provider :locale="locale" namespace="zy-el">
    <router-view v-slot="{ Component }">
      <keep-alive :include="keepAliveRoute">
        <component :is="Component" :key="$route.fullPath" v-if="show"></component>
      </keep-alive>
    </router-view>
  </el-config-provider>
</template>
<script setup>
import { ref, computed, watch } from 'vue'
import { useStore } from 'vuex'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
const locale = zhCn

const store = useStore()
const show = ref(true)
const keepAliveRoute = computed(() => store.getters.getKeepAliveRouteFn.filter(v => v !== store.getters.getRefreshRouteFn))
watch(() => store.state.refreshRoute, (val) => { if (val) { show.value = false } else { show.value = true } })

</script>
<style lang="scss">
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: Microsoft YaHei;
  background-color: #fff;
}
</style>
