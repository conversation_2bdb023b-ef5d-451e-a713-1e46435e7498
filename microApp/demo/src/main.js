import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import 'animate.css'
import elementPuls from './plugins/element-puls'
import components from './components'
import globalComponent from 'common/components'
import globalDirective from 'common/directive'
import { microAppMethod, microAppRender } from 'common/js/GlobalMicroApp'
import './assets/scss/index.scss'

import '@/public-path'
// 独立运行
if (!window.__POWERED_BY_QIANKUN__) {
  microAppMethod()
  const app = createApp(App)
  app.use(router).use(store).use(globalDirective).use(elementPuls).use(globalComponent).use(components).mount('#app')
}

let instance = null
let history = null
let microRouter = null
// 生命周期 - 挂载前,在这里由主应用传过来的参数
export async function bootstrap () { }
// 生命周期 - 挂载后
export async function mount (props) {
  const micro = microAppRender(props)
  history = micro.history
  microRouter = micro.router
  instance = createApp(App)
  instance.use(microRouter).use(store).use(globalDirective).use(elementPuls).use(globalComponent).use(components).mount(props.container ? props.container.querySelector('#app') : '#app')
}
// 生命周期 - 解除挂载
export async function unmount () {
  instance.unmount()
  instance._container.innerHTML = ''
  instance = null
  microRouter = null
  history.destroy()
}
