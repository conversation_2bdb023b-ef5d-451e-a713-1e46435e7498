import { createStore } from 'vuex'

import microApp from 'common/js/microAppStore'

export default createStore({
  state: {
    keepAliveRoute: [],
    refreshRoute: ''
  },
  getters: {
    getKeepAliveRouteFn (state) {
      return state.keepAliveRoute
    },
    getRefreshRouteFn (state) {
      return state.refreshRoute
    }
  },
  mutations: {
    setKeepAliveRoute (state, keepAliveRoute = []) {
      state.keepAliveRoute = keepAliveRoute
    },
    setRefreshRoute (state, refreshRoute = '') {
      state.refreshRoute = refreshRoute
    }
  },
  actions: {
  },
  modules: {
    microApp
  }
})
