{"name": "demo", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "animate.css": "^4.1.1", "common": "workspace:^", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "element-plus": "2.7.6", "element-resize-detector": "^1.2.4", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "qrcode.vue": "^3.3.3", "vue": "^3.3.4", "vue-router": "^4.2.5", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.32.7", "sass-loader": "^12.0.0", "unplugin-auto-import": "^0.16.6", "unplugin-element-plus": "^0.7.2", "unplugin-vue-components": "^0.25.2"}}