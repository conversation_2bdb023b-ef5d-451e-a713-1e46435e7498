const { defineConfig } = require('@vue/cli-service')
const WebpackObfuscator = require('webpack-obfuscator')
const CompressionPlugin = require('compression-webpack-plugin')
const AutoImport = require('unplugin-auto-import/webpack')
const Components = require('unplugin-vue-components/webpack')
const { ElementPlusResolver } = require('unplugin-vue-components/resolvers')
const ElementPlus = require('unplugin-element-plus/webpack')
const Version = new Date().getTime()
const { cdn, externals, obfuscatorConfig } = require('common/config/cdn')
console.log(process.env?.npm_config_url)
process.env.VUE_APP_URL = process.env?.npm_config_url || ''
process.env.VUE_APP_CATALOG = process.env?.npm_config_catalog || ''
process.env.VUE_APP_MICRO_APP = process.env?.npm_config_microapp || process.env?.npm_config_microApp || ''
const WebpackObfuscatorConfig = process.env?.npm_config_confuse ? [new WebpackObfuscator(obfuscatorConfig)] : []
const CompressionPluginConfig = process.env.NODE_ENV === 'development' ? [] : [
  new CompressionPlugin({ algorithm: 'gzip', test: /\.js$|\.html$|\.css$/, threshold: 10240, minRatio: 0.8 })
]
module.exports = defineConfig({
  outputDir: '../dist/main',
  publicPath: process.env.NODE_ENV === 'development' ? '/' : process.env?.npm_config_catalog || '/',
  productionSourceMap: process.env?.npm_config_map === 'true' ? true : false,
  transpileDependencies: true,
  runtimeCompiler: true,
  css: {
    extract: {
      filename: `static/css/[name].${Version}.css`,
      chunkFilename: `static/css/[name].${Version}.css`
    },
    loaderOptions: {
      scss: { // 如果配置为"additionalData"无效，请到官网查阅最新配置信息
        additionalData: '@use "@/assets/scss/common.scss" as *;@use "@/assets/scss/element-theme.scss" as *;'
      }
    }
  },
  chainWebpack: config => {
    config.plugin('html').tap(args => {
      args[0].cdn = process.env.NODE_ENV === 'development' ? cdn.dev : cdn.build
      args[0].title = '正在加载中...'
      return args
    })
    // config.module.rule('worker-loader').test(/\.worker\.js$/).use({
    //   loader: 'worker-loader', options: { inline: true }
    // }).loader('worker-loader').end()
  },
  configureWebpack: {
    externals: process.env.NODE_ENV === 'development' ? externals.dev : externals.build,
    cache: {
      type: 'filesystem',
      allowCollectingMemory: true
    },
    output: {
      filename: `static/js/[name].${Version}.js`,
      chunkFilename: `static/js/[name].${Version}.js`
    },
    plugins: [
      AutoImport({
        resolvers: [ElementPlusResolver({ importStyle: 'sass' })]
      }),
      Components({
        resolvers: [ElementPlusResolver({ importStyle: 'sass' })]
      }),
      ElementPlus({ useSource: true }),
      ...WebpackObfuscatorConfig,
      ...CompressionPluginConfig
    ]
  },
  devServer: {
    port: 2000,
    client: {
      overlay: false
    },
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
})
