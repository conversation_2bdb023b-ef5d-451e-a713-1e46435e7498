{"name": "main", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "animate.css": "^4.1.1", "common": "workspace:^", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "customize": "workspace:^", "docxtemplater": "^3.36.1", "docxtemplater-image-module-free": "^1.1.1", "element-plus": "2.7.6", "element-resize-detector": "^1.2.4", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "flv.js": "^1.6.2", "jszip-utils": "^0.1.0", "pdfjs-dist": "^2.16.105", "pizzip": "^3.1.4", "qiankun": "^2.10.16", "qrcode.vue": "^3.3.3", "socket.io-client": "^2.5.0", "vod-js-sdk-v6": "^1.6.2", "vue": "^3.3.4", "vue-demi": "^0.14.9", "vue-router": "^4.2.5", "vuex": "^4.1.0", "worker-loader": "^3.0.8"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-proposal-private-methods": "^7.18.6", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^11.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.32.7", "sass-loader": "^12.0.0", "unplugin-auto-import": "^0.16.6", "unplugin-element-plus": "^0.7.2", "unplugin-vue-components": "^0.25.2", "webpack-obfuscator": "^3.5.1"}}