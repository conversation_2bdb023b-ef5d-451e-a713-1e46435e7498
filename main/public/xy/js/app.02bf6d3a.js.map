{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/assets/img/svg/icon_mic.svg", "webpack:///./src/assets/img/svg/hand_down.svg", "webpack:///./src/view/components/Loading/index.vue?c12c", "webpack:///./src/view/components/InOutReminder/index.vue?2afd", "webpack:///./src/assets/img/svg/meeting_host.svg", "webpack:///./src/assets/img/svg/setting.svg", "webpack:///./src/assets/img/type/nemo.png", "webpack:///./src/view/components/PromptInfo/index.vue?8822", "webpack:///./src/assets/img/operate/ch_step_operate_permission.png", "webpack:///./src/assets/img/svg/arrow.svg", "webpack:///./src/view/components/Header/index.vue?e455", "webpack:///./src/view/components/Header/index.vue?42cf", "webpack:///./src/assets/img/svg/record.svg", "webpack:///./src/assets/img/svg sync \\.svg$", "webpack:///./src/assets/img/svg/mute_camera_error.svg", "webpack:///./src/assets/img/svg/rotate.svg", "webpack:///./src/assets/img/svg/share.svg", "webpack:///./src/assets/img/operate/ch_step_operate_video.png", "webpack:///./src/assets/img/operate/icon_me.svg", "webpack:///./src/assets/img/svg/icon_play.svg", "webpack:///./src/assets/img/svg/record_stop.svg", "webpack:///./src/assets/img/svg/icon_device_volume.svg", "webpack:///./src/plugins/element.js", "webpack:///./src/view/App.vue", "webpack:///./src/view/components/Login/index.vue", "webpack:///./src/utils/store.js", "webpack:///src/view/components/Login/index.vue", "webpack:///./src/view/components/Login/index.vue?fbd1", "webpack:///./src/view/components/Login/index.vue?d000", "webpack:///./src/view/components/Loading/index.vue", "webpack:///src/view/components/Loading/index.vue", "webpack:///./src/view/components/Loading/index.vue?df08", "webpack:///./src/view/components/Loading/index.vue?f549", "webpack:///./src/view/components/Header/index.vue", "webpack:///./src/components/Timmer/index.vue", "webpack:///src/components/Timmer/index.vue", "webpack:///./src/components/Timmer/index.vue?24cc", "webpack:///./src/components/Timmer/index.vue?c9d7", "webpack:///./src/utils/browser.js", "webpack:///./src/utils/index.js", "webpack:///src/view/components/Header/index.vue", "webpack:///./src/view/components/Header/index.vue?ab1a", "webpack:///./src/view/components/Header/index.vue?05fc", "webpack:///./src/view/components/Setting/index.vue", "webpack:///./src/view/components/Setting/Common.vue", "webpack:///src/view/components/Setting/Common.vue", "webpack:///./src/view/components/Setting/Common.vue?17fb", "webpack:///./src/view/components/Setting/Common.vue?b881", "webpack:///./src/view/components/Setting/Device.vue", "webpack:///./src/view/components/Guide/index.vue", "webpack:///src/view/components/Guide/index.vue", "webpack:///./src/view/components/Guide/index.vue?7a7e", "webpack:///./src/view/components/Guide/index.vue?1988", "webpack:///src/view/components/Setting/Device.vue", "webpack:///./src/view/components/Setting/Device.vue?929a", "webpack:///./src/view/components/Setting/Device.vue?4cf1", "webpack:///./src/view/components/Setting/Feedback.vue", "webpack:///src/view/components/Setting/Feedback.vue", "webpack:///./src/view/components/Setting/Feedback.vue?3d4b", "webpack:///./src/view/components/Setting/Feedback.vue?10ae", "webpack:///./src/view/components/Setting/Version.vue", "webpack:///src/view/components/Setting/Version.vue", "webpack:///./src/view/components/Setting/Version.vue?cf29", "webpack:///./src/view/components/Setting/Version.vue?4b8b", "webpack:///src/view/components/Setting/index.vue", "webpack:///./src/view/components/Setting/index.vue?d0cc", "webpack:///./src/view/components/Setting/index.vue?5b66", "webpack:///./src/view/components/Barrage/index.vue", "webpack:///src/view/components/Barrage/index.vue", "webpack:///./src/view/components/Barrage/index.vue?4d94", "webpack:///./src/view/components/Barrage/index.vue?72d7", "webpack:///./src/view/components/InOutReminder/index.vue", "webpack:///src/view/components/InOutReminder/index.vue", "webpack:///./src/view/components/InOutReminder/index.vue?95e2", "webpack:///./src/view/components/InOutReminder/index.vue?47e2", "webpack:///./src/view/components/Audio/index.vue", "webpack:///src/view/components/Audio/index.vue", "webpack:///./src/view/components/Audio/index.vue?6093", "webpack:///./src/view/components/Audio/index.vue?20dd", "webpack:///./src/view/components/Video/index.vue", "webpack:///./src/view/components/FitAvatar/index.vue", "webpack:///./src/components/Avatar/index.vue", "webpack:///./src/utils/enum.js", "webpack:///src/components/Avatar/index.vue", "webpack:///./src/components/Avatar/index.vue?8eff", "webpack:///./src/components/Avatar/index.vue?3f96", "webpack:///src/view/components/FitAvatar/index.vue", "webpack:///./src/view/components/FitAvatar/index.vue?974e", "webpack:///./src/view/components/FitAvatar/index.vue?8c0c", "webpack:///./src/utils/event.js", "webpack:///src/view/components/Video/index.vue", "webpack:///./src/view/components/Video/index.vue?3fc3", "webpack:///./src/view/components/Video/index.vue?9405", "webpack:///./src/view/components/Internels/index.vue", "webpack:///src/view/components/Internels/index.vue", "webpack:///./src/view/components/Internels/index.vue?5710", "webpack:///./src/view/components/Internels/index.vue?79c2", "webpack:///./src/view/components/Participant/index.vue", "webpack:///src/view/components/Participant/index.vue", "webpack:///./src/view/components/Participant/index.vue?4382", "webpack:///./src/view/components/Participant/index.vue?4778", "webpack:///./src/view/components/EndCall/index.vue", "webpack:///src/view/components/EndCall/index.vue", "webpack:///./src/view/components/EndCall/index.vue?7620", "webpack:///./src/view/components/EndCall/index.vue?1450", "webpack:///./src/view/components/VideoButton/index.vue", "webpack:///src/view/components/VideoButton/index.vue", "webpack:///./src/view/components/VideoButton/index.vue?7a00", "webpack:///./src/view/components/VideoButton/index.vue?92d7", "webpack:///./src/view/components/AudioButton/index.vue", "webpack:///./src/view/components/MicLevel/index.vue", "webpack:///src/view/components/MicLevel/index.vue", "webpack:///./src/view/components/MicLevel/index.vue?b59d", "webpack:///./src/view/components/MicLevel/index.vue?c282", "webpack:///src/view/components/AudioButton/index.vue", "webpack:///./src/view/components/AudioButton/index.vue?9d2b", "webpack:///./src/view/components/AudioButton/index.vue?331e", "webpack:///./src/view/components/PromptInfo/index.vue", "webpack:///./src/view/components/PromptInfo/timer.vue", "webpack:///src/view/components/PromptInfo/timer.vue", "webpack:///./src/view/components/PromptInfo/timer.vue?706d", "webpack:///./src/view/components/PromptInfo/timer.vue?a4c8", "webpack:///src/view/components/PromptInfo/index.vue", "webpack:///./src/view/components/PromptInfo/index.vue?84e6", "webpack:///./src/view/components/PromptInfo/index.vue?b464", "webpack:///./src/utils/config.js", "webpack:///./src/utils/template.js", "webpack:///./src/utils/layout.js", "webpack:///./src/utils/resize.js", "webpack:///src/view/App.vue", "webpack:///./src/view/App.vue?9dee", "webpack:///./src/view/App.vue?5e1c", "webpack:///./src/components/Svg/index.vue", "webpack:///src/components/Svg/index.vue", "webpack:///./src/components/Svg/index.vue?4ecc", "webpack:///./src/components/Svg/index.vue?c4af", "webpack:///./src/main.js", "webpack:///./src/assets/img/svg/copy.svg", "webpack:///./src/view/components/Internels/index.vue?8c58", "webpack:///./src/view/components/EndCall/index.vue?3492", "webpack:///./src/view/components/PromptInfo/timer.vue?5409", "webpack:///./src/assets/img/svg/full.svg", "webpack:///./src/view/App.vue?6670", "webpack:///./src/assets/img/svg/share_stop.svg", "webpack:///./src/view/components/Guide/index.vue?e195", "webpack:///./src/assets/img/svg/mute_mic_error.svg", "webpack:///./src/assets/img/svg/cancel_mic_mute.svg", "webpack:///./src/assets/img/svg/mic_null.svg", "webpack:///./src/assets/img/svg/camera.svg", "webpack:///./src/assets/img/svg/hang_up_full.svg", "webpack:///./src/assets/img/svg/add.svg", "webpack:///./src/assets/img/operate/icon_mute_mic.svg", "webpack:///./src/assets/img/svg/next.svg", "webpack:///./src/view/components/Barrage/index.vue?fcc2", "webpack:///./src/assets/ring.wav", "webpack:///./src/assets/img/operate/icon_mic.svg", "webpack:///./src/assets/img/svg/cancel_full.svg", "webpack:///./src/assets/img/svg/mute_camera.svg", "webpack:///./src/assets/img/operate/icon_speaker.gif", "webpack:///./src/view/components/FitAvatar/index.vue?9427", "webpack:///./src/assets/img/svg/mic_mute.svg", "webpack:///./src/view/components/Setting/index.vue?2e8d", "webpack:///./src/assets/img/type/h323.png", "webpack:///./src/assets/img/svg/hang_up.svg", "webpack:///./src/view/components/Login/index.vue?6b8d", "webpack:///./src/view/components/Video/index.vue?54f4", "webpack:///./src/assets/img/svg/hand_up.svg", "webpack:///./src/assets/img/type/bruce.png", "webpack:///./src/assets/img/confernece.png", "webpack:///./src/assets/img/icon/icon_hide.svg", "webpack:///./src/assets/img/svg/hand_end.svg", "webpack:///./src/assets/img/type/noicon.png", "webpack:///./src/assets/img/svg/layout.svg", "webpack:///./src/assets/img/svg/icon_device_message.svg", "webpack:///./src/assets/img/type/tvbox.png", "webpack:///./src/assets/img/operate/icon_mute_camera.svg", "webpack:///./src/assets/img/svg/more.svg", "webpack:///./src/assets/img/svg/home.svg", "webpack:///./src/assets/img/svg/end_call.svg", "webpack:///./src/assets/img/operate/icon_camera.svg", "webpack:///./src/components/Svg/index.vue?507f", "webpack:///./src/assets/img/svg/previous.svg", "webpack:///./src/assets/img/svg/signal.svg", "webpack:///./src/view/components/MicLevel/index.vue?b0f9"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "symbol", "add", "map", "webpackContext", "req", "id", "webpackContextResolve", "e", "Error", "code", "keys", "resolve", "<PERSON><PERSON>", "use", "Element", "VueClipboard", "render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "loginMeeting", "callMeeting", "callLoading", "_e", "_v", "on", "handleClose", "user", "setting", "isThird", "submitForm", "onToggleSetting", "conferenceInfo", "selected<PERSON><PERSON><PERSON>", "audioOutput", "deviceId", "stop", "toolVisible", "switchDebug", "handleFlipCamera", "forceLayoutId", "chairman", "<PERSON><PERSON><PERSON><PERSON>", "content", "localHide", "isLocalShareContent", "recordStatus", "forceFullScreen", "$event", "stopPropagation", "handleToolVisible", "arguments", "pageStatus", "previous", "switchPage", "pageInfo", "currentPage", "next", "totalPage", "_s", "style", "layoutStyle", "_l", "layout", "item", "index", "roster", "templateMode", "layoutMode", "client", "audioList", "streams", "status", "onhold", "subTitle", "action", "reminders", "class", "numberType", "participantVisible", "participantsCount", "isPc", "switchLayout", "stopShareContent", "contentIsDisabled", "shareContent", "disable<PERSON><PERSON><PERSON>", "toggleRecord", "permission", "audio", "disable<PERSON><PERSON><PERSON>", "handStatus", "stream", "audioOperate", "video", "videoOperate", "rosters", "debug", "senderStatus", "settingVisible", "onSaveSetting", "staticRenderFns", "ref", "loginForm", "rules", "labelPosition", "required", "message", "trigger", "model", "phone", "callback", "$$v", "$set", "expression", "password", "meeting", "meetingPassword", "meeting<PERSON>ame", "muteVideo", "muteAudio", "store", "storage", "localStorage", "set", "serialize", "setItem", "val", "getItem", "newVal", "JSON", "parse", "err", "remove", "removeItem", "clear", "removeList", "array", "type", "toString", "stringify", "props", "computed", "xyRTC", "methods", "formName", "$refs", "validate", "valid", "$emit", "onOpenSetting", "watch", "handler", "newValue", "deep", "component", "_m", "displayName", "require", "bgmAudioEle", "setOutputAudioDevice", "audioOutputValue", "paused", "play", "error", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "pause", "className", "number", "infoVisible", "copyNumber", "slot", "cancel", "timmer", "timerCount", "meetingTimeout", "mounted", "onCreateMeetingTimeCount", "clearTimeout", "secondToDate", "h", "Math", "floor", "setTimeout", "meetingTime", "platform", "ua", "navigator", "userAgent", "lowerUa", "toLocaleLowerCase", "isMac", "toUpperCase", "indexOf", "test", "isLinux", "isWindowsPhone", "isWindows", "isSymbian", "isAndroid", "isFireFox", "isChrome", "isEdge", "<PERSON><PERSON><PERSON><PERSON>", "isMacOrPad", "isIPad", "maxTouchPoints", "isTablet", "isPhone", "isIOS", "isBaidu", "isFirefox", "match", "isQQ", "isWindowsWechat", "isWindowsWxWechat", "isWeixin", "isMiniProgram", "isOpera", "iswxwork", "isDingDing", "isZhazha", "getBrowserName", "getAndroidVersion", "pieces", "toLowerCase", "versions", "split", "v", "parseInt", "major", "minor", "getIOSVersion", "isSupportMobileJoinMeeting", "is<PERSON>in<PERSON><PERSON><PERSON>", "browserName", "info", "Message", "duration", "center", "transformTime", "timestamp", "Date", "time", "y", "getFullYear", "M", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "TYPEOF", "isNumber", "num", "isObject", "obj", "isArray", "arr", "Array", "isString", "str", "isBoolean", "components", "<PERSON><PERSON>", "$copyText", "then", "visible", "onCancel", "current", "handleSelect", "isInMeeting", "onHandleSetting", "feedbackVisible", "onChangeLayoutMode", "layoutModeMap", "onChangeLoginType", "loginType", "loginTypeMap", "onChangeLocalHide", "isLocalHide", "AUTO", "CUSTOM", "XYLINK", "THIRD", "isShowStreamFailTips", "guideVisible", "<PERSON><PERSON><PERSON>", "directives", "rawName", "changeVideoIn", "select", "videoInValue", "videoInList", "label", "videoStatusVisible", "domProps", "changeAudioInput", "audioInputValue", "audioInputList", "transform", "audioLevel", "changeAudioOutput", "audioOutputList", "testAudioStatus", "handleOk", "onClose", "onReload", "location", "reload", "Guide", "camera", "microphone", "_selectedDevice$audio", "_selectedDevice$audio2", "_selectedDevice$video", "defaultSelect", "audioInput", "videoInput", "previewVideoStream", "previewAudioStream", "deviceManager", "audioLevelTimmer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "init", "getStream", "getDevices", "initDeviceManagerEvent", "createStream", "getVideoStream", "getAudioStream", "hideCheckingPage", "_this$selectedDevice", "cameraPermission", "_this$previewVideoStr", "params", "exact", "getTracks", "for<PERSON>ach", "track", "getPreviewStream", "videoTrack", "getVideoTracks", "getSettings", "videoRefEle", "srcObject", "_this$selectedDevice2", "_this$previewAudioStr", "microphonePermission", "audioTrack", "getAudioTracks", "clearAudioLevel", "getAudioLevel", "setInterval", "level", "clearAudioLevelTimmer", "getRangeRandom", "min", "max", "random", "randomTimer", "detail", "nextDevice", "nextDevices", "handleChange", "setDevices", "devices", "clearInterval", "clearStream", "closePreviewStream", "destroy", "close", "audioRef", "findDeviceById", "list", "find", "deviceList", "contact", "downloadLoading", "download", "uploadLoading", "trim", "upload", "logger", "downloadLog", "uploadLog", "version", "update", "splitData", "Common", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Version", "values", "SETTING_KEYS", "wrapperStyle", "titleBgStyle", "titleClass", "fontSizeMap", "small", "middle", "big", "locationMap", "top", "bottom", "intervalTimer", "scroll", "background", "backgroundRGB", "opacity", "Number", "backgroundAlpha", "toFixed", "fontRGB", "fontSize", "locationKey", "color", "textAlign", "setScrollTitle", "obj<PERSON><PERSON><PERSON>", "clientWidth", "initTransformX", "innerWidth", "transformX", "cancelAnimationFrame", "requestAnimationFrame", "visibility", "immediate", "reminderClass", "onToggleReminder", "isHideContent", "newReminders", "actionMap", "isHide", "in", "out", "remindersTemp", "remindersQueue", "timer", "queueTimer", "stopMeeting", "clearTimer", "concat", "pop", "muted", "renderAudio", "audioEle", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streamId", "templateConfig", "isPIP", "positionStyle", "wrapVideoId", "toggleFullScreen", "border", "audioOnlyClass", "videoMuteClass", "textAvatar", "avatar", "containerWidth", "videoRequestClass", "<PERSON><PERSON><PERSON><PERSON>", "audioTxMute", "rotate", "avatarS<PERSON>le", "imageSrc", "onImageError", "DEFAULT_SETTING", "DEFAULT_LOCAL_USER", "DEFAULT_CALL_INFO", "callNumber", "NEXT_DEVICE", "DEFAULT_DEVICE", "DEVICE_TYPE_MAP", "webrtc", "noicon", "soft", "hard", "nemo", "nemono", "virtualnemo", "tvbox", "h323", "bruce", "desk", "default", "PARTICIPANT_PAGE_SIZE", "TEMPLATE_TYPE", "LOCAL", "CONTENT", "src", "String", "defaultSrc", "created", "Avatar", "size", "width", "height", "Event", "lastTapTime", "lastTapTimer", "click", "doubleClick", "singleClick", "currentTime", "timeStamp", "FitAvatar", "state", "isActiveSpeaker", "isFullScreen", "_this$item$positionIn", "positionInfo", "renderVideo", "event", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mimeType", "bytesReceivedSecond", "bytesSentSecond", "sender", "frameWidth", "frameHeight", "expBandwidth", "framesEncodedSecond", "framesSentSecond", "keyFramesEncoded", "receiver", "framesReceivedSecond", "framesDecodedSecond", "keyFramesDecoded", "closeParticipant", "direction", "count", "navbarClass", "onChangeTab", "unmuteCount", "computedRosters", "participantId", "mediagroupid", "memberStatusImg", "isContentOnly", "endpointId", "audioImg", "videoImg", "totalCount", "defaultPageSize", "onChangePage", "tab", "newRosters", "_this$selfRoster", "videoTxMute", "videoRxMute", "audioRxMute", "deviceType", "muteActive", "unmuteActive", "muteCamera", "unmuteCamera", "selfRoster", "local", "speaker", "assign", "pageSize", "originalRosters", "beforeMount", "_this$client", "getSelfRoster", "fetchRosters", "newData", "filter", "fetch", "startIndex", "endIndex", "ceil", "setUnmuteCount", "page", "endCallData", "onHandle", "text", "show", "videoValue", "videoClass", "svgIcon", "svgType", "audioValue", "audioClass", "audioStatus", "micLevel", "setMicLevel", "clearTimmer", "MicLevel", "toggleForceFullScreen", "before", "isEven", "_t", "Boolean", "padStart", "Timer", "SERVER", "wssServer", "httpServer", "logServer", "ACCOUNT", "extId", "clientId", "clientSecret", "RATE", "SPEAKER_TEMPLATE", "position", "resolution", "TEMPLATE", "temp", "0", "1", "2", "3", "4", "5", "6", "7", "8", "rate", "calculateBaseLayoutList", "orderLayoutList", "rateWidth", "rateHeight", "left", "layoutList", "_customStyle", "_customStyle2", "customStyle", "x", "w", "layoutX", "round", "layoutY", "<PERSON><PERSON><PERSON><PERSON>", "layoutHeight", "_customStyle3", "cachePositionInfo", "getLayoutIndexByRotateInfo", "nextLayoutList", "pid", "mid", "listLen", "isSamePid", "isContentOfRotateDevice", "getScreenInfo", "elementId", "nextTemplateRate", "clientHeight", "document", "getElementById", "body", "screenInfoObj", "getOrderLayoutList", "layoutLen", "baseLayout", "chairmain", "activeSpeaker", "audioVideoUnmute", "audioUnmute", "videoUnmute", "audioVideoMute", "orderedLayoutList", "isLocal", "isForceFullScreen", "newLayout", "sort", "a", "b", "ExpectWidth", "setS<PERSON><PERSON><PERSON>er", "orderRosterList", "Resize", "constructor", "eleId", "element", "resizeObserver", "onResize", "ResizeObserver", "isHorizontal", "innerHeight", "observe", "disconnect", "WindowResize", "rotationInfoRef", "nextLayoutListRef", "<PERSON><PERSON>", "Loading", "Barrage", "InOutReminder", "Audio", "Video", "Internels", "Setting", "Participant", "EndCall", "VideoButton", "AudioButton", "PromptInfo", "<PERSON><PERSON><PERSON><PERSON>", "screenInfo", "participantCount", "confChangeInfo", "chairman<PERSON><PERSON>", "control", "recordPermission", "server", "account", "IFacingMode", "setAttribute", "addEventListener", "handleInit", "facingMode", "setUserInfo", "checkSupportWebRTC", "checkSupportMobileWebRTC", "isSupport", "msg", "join", "callStatus", "extUserId", "setLogLevel", "createClient", "container", "offset", "setFeatureConfig", "enableAutoResizeLayout", "enableLayoutAvatar", "enableCheckRecordPermission", "initEventListener", "loginExternalAccount", "loginXYlinkAccount", "token", "access_token", "makeCall", "confNumber", "subscribeBulkRoster", "publish", "isSharePeople", "visibilitychange", "disconnected", "switchCamera", "MOBILE_HORIZONTAL", "MOBILE_VERTICAL", "LayoutOrientationType", "orientation", "setLayoutOrientation", "handleOrientationChange", "updateLayoutSize", "contentUri", "customRequestLayout", "createCustomLayout", "visibilityState", "unmuteVideo", "removeEventListener", "parent", "postMessage", "xy_video_meeting", "showMessage", "participantsNum", "chairManUrl", "cacheCustomPageInfo", "calcPageInfo", "forceLayout", "disableMute", "muteOperation", "recordIsDisabled", "onHand", "calculateRotate", "handleBulkRoster", "authorize", "reason", "reasonText", "recordInfo", "recordSessionId", "isStart", "bulkRosterType", "addRosterInfo", "changeRosterInfo", "deleteRosterInfo", "findIndex", "calcForceFullScreenRequestLayout", "reqList", "calluri", "quality", "extReqList", "requestNewLayout", "uiShowLocalWhenPageMode", "_this$client2", "realContentLen", "realLen", "isRequest", "templateLayout", "forceLayoutList", "rotationInfo", "cacheNextLayoutList", "cloneDeep", "rotateInfo", "rotation", "layoutItem", "getLayoutRotateInfo", "isRotate", "max<PERSON><PERSON><PERSON>", "maxHeight", "unmuteAudio", "funcMap", "handup", "func", "handdown", "mute", "onAudioOperate", "customSwitchPage", "nextPage", "_this$client3", "setPageInfo", "toggleLocal", "device", "deviceMap", "zh_text", "setAudioOutput", "switchDevice", "Promise", "reject", "_this$selectedDevice3", "onSwitchDevice", "xySetting", "createContentStream", "screenAudio", "isShareContent", "startCloudRecord", "stopCloudRecord", "svgClass", "iconName", "icon", "SvgIcon", "requireAll", "requireContext", "config", "productionTip", "App", "$mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,sGCvJT,qDAEIyC,EAAS,IAAI,IAAa,CAC5B,GAAM,gBACN,IAAO,sBACP,QAAW,YACX,QAAW,ilDAEA,IAAOC,IAAID,GACT,gB,oCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,iBACN,IAAO,uBACP,QAAW,YACX,QAAW,oyEAEA,IAAOC,IAAID,GACT,gB,oCCTf,W,oCCAA,W,6DCAA,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,oBACN,IAAO,0BACP,QAAW,YACX,QAAW,4+FAEA,IAAOC,IAAID,GACT,gB,oCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,eACN,IAAO,qBACP,QAAW,YACX,QAAW,ugEAEA,IAAOC,IAAID,GACT,gB,qBCTf9B,EAAOD,QAAU,8hD,kCCAjB,W,gDCAAC,EAAOD,QAAU,IAA0B,+C,oCCA3C,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,aACN,IAAO,mBACP,QAAW,YACX,QAAW,8nBAEA,IAAOC,IAAID,GACT,gB,kCCTf,W,oCCAA,W,oCCAA,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,cACN,IAAO,oBACP,QAAW,YACX,QAAW,m4BAEA,IAAOC,IAAID,GACT,gB,uBCTf,IAAIE,EAAM,CACT,YAAa,OACb,cAAe,OACf,eAAgB,OAChB,oBAAqB,OACrB,wBAAyB,OACzB,aAAc,OACd,iBAAkB,OAClB,aAAc,OACd,kBAAmB,OACnB,iBAAkB,OAClB,gBAAiB,OACjB,gBAAiB,OACjB,qBAAsB,OACtB,aAAc,OACd,4BAA6B,OAC7B,2BAA4B,OAC5B,iBAAkB,OAClB,kBAAmB,OACnB,eAAgB,OAChB,qBAAsB,OACtB,iBAAkB,OAClB,iBAAkB,OAClB,aAAc,OACd,oBAAqB,OACrB,0BAA2B,OAC3B,uBAAwB,OACxB,aAAc,OACd,iBAAkB,OAClB,eAAgB,OAChB,oBAAqB,OACrB,eAAgB,OAChB,gBAAiB,OACjB,cAAe,OACf,mBAAoB,OACpB,eAAgB,QAIjB,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOtC,EAAoBuC,GAE5B,SAASC,EAAsBF,GAC9B,IAAItC,EAAoBW,EAAEyB,EAAKE,GAAM,CACpC,IAAIG,EAAI,IAAIC,MAAM,uBAAyBJ,EAAM,KAEjD,MADAG,EAAEE,KAAO,mBACHF,EAEP,OAAOL,EAAIE,GAEZD,EAAeO,KAAO,WACrB,OAAO9D,OAAO8D,KAAKR,IAEpBC,EAAeQ,QAAUL,EACzBpC,EAAOD,QAAUkC,EACjBA,EAAeE,GAAK,Q,oCCxDpB,qDAEIL,EAAS,IAAI,IAAa,CAC5B,GAAM,yBACN,IAAO,+BACP,QAAW,YACX,QAAW,u2HAEA,IAAOC,IAAID,GACT,gB,oCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,cACN,IAAO,oBACP,QAAW,YACX,QAAW,4sDAEA,IAAOC,IAAID,GACT,gB,oCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,aACN,IAAO,mBACP,QAAW,YACX,QAAW,yoEAEA,IAAOC,IAAID,GACT,gB,uBCTf9B,EAAOD,QAAU,IAA0B,0C,qBCA3CC,EAAOD,QAAU,09I,oCCAjB,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,iBACN,IAAO,uBACP,QAAW,YACX,QAAW,otBAEA,IAAOC,IAAID,GACT,gB,oCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,mBACN,IAAO,yBACP,QAAW,YACX,QAAW,y9BAEA,IAAOC,IAAID,GACT,gB,kCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,0BACN,IAAO,gCACP,QAAW,YACX,QAAW,igDAEA,IAAOC,IAAID,GACT,gB,+GCAfY,aAAIC,IAAIC,KAERF,aAAIC,IAAIE,KCXR,IAAIC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,MAAM,CAACG,YAAY,YAAYD,MAAM,CAAC,GAAK,cAAc,CAAIJ,EAAIM,cAAqC,OAArBN,EAAIM,cAA2BN,EAAIO,aAAgBP,EAAIQ,YAAuMR,EAAIS,KAA9LP,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACL,EAAIU,GAAG,gBAAgBR,EAAG,MAAM,CAACG,YAAY,WAAWM,GAAG,CAAC,MAAQX,EAAIY,cAAc,CAACZ,EAAIU,GAAG,WAAqBV,EAAIM,cAAqC,OAArBN,EAAIM,cAA2BN,EAAIO,aAAgBP,EAAIQ,YAAwJR,EAAIS,KAA/IP,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAOJ,EAAIa,KAAK,QAAUb,EAAIc,QAAQC,SAASJ,GAAG,CAAC,WAAaX,EAAIgB,WAAW,gBAAkBhB,EAAIiB,mBAA6BjB,EAAIO,aAAeP,EAAIQ,YAAaN,EAAG,UAAU,CAACE,MAAM,CAAC,eAAiBJ,EAAIkB,eAAe,iBAAmBlB,EAAImB,eAAeC,YAAYC,UAAUV,GAAG,CAAC,KAAOX,EAAIsB,QAAQtB,EAAIS,KAAMT,EAAIO,cAAgBP,EAAIQ,YAAaN,EAAG,MAAM,CAACG,YAAY,WAAW,CAACH,EAAG,gBAAgB,CAACE,MAAM,CAAC,UAAYJ,EAAIuB,YAAc,WAAa,WAAW,eAAiBvB,EAAIkB,gBAAgBP,GAAG,CAAC,YAAcX,EAAIwB,YAAY,KAAOxB,EAAIsB,KAAK,WAAatB,EAAIyB,oBAAoBvB,EAAG,aAAa,CAACE,MAAM,CAAC,YAAcJ,EAAIuB,YAAY,cAAgBvB,EAAI0B,cAAc,SAAW1B,EAAI2B,SAASC,YAAY,QAAU5B,EAAI6B,QAAQ,UAAY7B,EAAIc,QAAQgB,UAAU,oBAAsB9B,EAAI+B,oBAAoB,aAAe/B,EAAIgC,cAAcrB,GAAG,CAAC,gBAAkBX,EAAIiC,mBAAmB/B,EAAG,MAAM,CAACG,YAAY,kBAAkBD,MAAM,CAAC,GAAK,WAAWO,GAAG,CAAC,MAAQ,SAASuB,GAAiC,OAAzBA,EAAOC,kBAAyBnC,EAAIoC,kBAAkB/F,MAAM,KAAMgG,cAAc,CAAErC,EAAIsC,WAAWC,UAAYvC,EAAIuB,YAAarB,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkBM,GAAG,CAAC,MAAQ,SAASuB,GAAiC,OAAzBA,EAAOC,kBAAyBnC,EAAIwC,WAAW,eAAe,CAACtC,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,eAAe,GAAIJ,EAAIyC,SAASC,YAAc,EAAGxC,EAAG,MAAM,CAACG,YAAY,cAAcM,GAAG,CAAC,MAAQ,SAASuB,GAAiC,OAAzBA,EAAOC,kBAAyBnC,EAAIwC,WAAW,WAAW,CAACxC,EAAIU,GAAG,SAASV,EAAIS,OAAOT,EAAIS,KAAMT,EAAIsC,WAAWK,MAAQ3C,EAAIuB,YAAarB,EAAG,MAAM,CAACG,YAAY,YAAY,CAACH,EAAG,MAAM,CAACG,YAAY,cAAcM,GAAG,CAAC,MAAQ,SAASuB,GAAiC,OAAzBA,EAAOC,kBAAyBnC,EAAIwC,WAAW,WAAW,CAACtC,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAWJ,EAAIyC,SAASG,UAAY,GAAK5C,EAAIyC,SAASC,YAAc,EAAGxC,EAAG,MAAM,CAACG,YAAY,eAAe,CAACL,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIyC,SAASC,aAAa,MAAM1C,EAAI6C,GAAG7C,EAAIyC,SAASG,UAAY,IAAM,MAAQ5C,EAAIyC,SAASG,WAAW,OAAO5C,EAAIS,MAAM,KAAKT,EAAIS,KAAKP,EAAG,MAAM,CAACG,YAAY,iBAAiByC,MAAO9C,EAAI+C,aAAc/C,EAAIgD,GAAIhD,EAAIiD,QAAQ,SAASC,EAAKC,GAAO,OAAOjD,EAAG,QAAQ,CAAC7B,IAAI6E,EAAKE,OAAOhE,GAAGgB,MAAM,CAAC,GAAK8C,EAAKE,OAAOhE,GAAG,MAAQ+D,EAAM,MAAQnD,EAAIqD,aAAa,WAAarD,EAAIc,QAAQwC,WAAW,KAAOJ,EAAK,cAAgBlD,EAAI0B,cAAc,OAAS1B,EAAIuD,QAAQ5C,GAAG,CAAC,gBAAkBX,EAAIiC,sBAAqB,GAAG/B,EAAG,MAAM,CAACG,YAAY,cAAcL,EAAIgD,GAAIhD,EAAIwD,WAAW,SAASN,GAAM,OAAOhD,EAAG,QAAQ,CAAC7B,IAAI6E,EAAKhI,KAAKuI,QAAQ,GAAGrE,GAAGgB,MAAM,CAAC,MAAwB,UAAhB8C,EAAKQ,OAAmB,SAAWR,EAAKhI,KAAKuI,QAAQ,GAAGrE,GAAG,OAASY,EAAIuD,aAAY,IAAKvD,EAAI2D,QAAU3D,EAAI4D,SAAS/B,SAAmC,SAAxB7B,EAAI4D,SAASC,OAAmB3D,EAAG,UAAU,CAACE,MAAM,CAAC,SAAWJ,EAAI4D,YAAY5D,EAAIS,KAAOT,EAAI2D,OAAgE3D,EAAIS,KAA5DP,EAAG,gBAAgB,CAACE,MAAM,CAAC,UAAYJ,EAAI8D,cAAuB,GAAG5D,EAAG,MAAM,CAAC6D,MAAM/D,EAAIuB,YAAc,0BAA4B,2BAA2B,CAACrB,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,MAAM,CAAC6D,MAAM,CAAC,cAAe,CAAE,kBAAqD,QAAlC/D,EAAIkB,eAAe8C,aAAwBrD,GAAG,CAAC,MAAQ,SAASuB,GAAQlC,EAAIiE,oBAAqB,KAAQ,CAAC/D,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,kBAAkBF,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAG,SAASR,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIkE,uBAAuB,GAAIlE,EAAImE,KAAMjE,EAAG,MAAM,CAACG,YAAY,gBAAgBM,GAAG,CAAC,MAAQX,EAAIoE,eAAe,CAAClE,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,YAAYF,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAG,WAAW,GAAGV,EAAIS,KAAMT,EAAImE,MAAQnE,EAAI+B,oBAAqB7B,EAAG,MAAM,CAACG,YAAY,gCAAgCM,GAAG,CAAC,MAAQX,EAAIqE,mBAAmB,CAACnE,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,aAAa,KAAO,YAAYF,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAG,WAAW,GAAGV,EAAIS,KAAMT,EAAImE,OAASnE,EAAI+B,oBAAqB7B,EAAG,MAAM,CAAC6D,MAAM,CAAC,eAAgB,CAAE,kBAAmB/D,EAAIsE,oBAAqB3D,GAAG,CAAC,MAAQX,EAAIuE,eAAe,CAACrE,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWF,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAG,SAAS,GAAGV,EAAIS,KAAKP,EAAG,MAAM,CAAC6D,MAAM,CAAC,eAAgB,CAAE,kBAAmB/D,EAAIwE,gBAAiB7D,GAAG,CAAC,MAAQX,EAAIyE,eAAe,CAACvE,EAAG,WAAW,CAACE,MAAM,CAAC,KAA4B,IAArBJ,EAAIgC,aAAqB,SAAW,iBAAiB9B,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAGV,EAAI6C,GAAwB,IAArB7C,EAAIgC,aAAqB,OAAS,YAAY,GAAIhC,EAAImE,KAAMjE,EAAG,MAAM,CAACG,YAAY,SAASL,EAAIS,KAAKP,EAAG,cAAc,CAACE,MAAM,CAAC,WAAaJ,EAAI0E,WAAW,MAAQ1E,EAAI2E,MAAM,aAAe3E,EAAI4E,aAAa,WAAa5E,EAAI6E,WAAW,OAAS7E,EAAI8E,QAAQnE,GAAG,CAAC,aAAeX,EAAI+E,gBAAgB7E,EAAG,cAAc,CAACE,MAAM,CAAC,WAAaJ,EAAI0E,WAAW,MAAQ1E,EAAIgF,OAAOrE,GAAG,CAAC,aAAeX,EAAIiF,gBAAgB/E,EAAG,UAAU,CAACS,GAAG,CAAC,KAAOX,EAAIsB,SAAS,KAAMtB,EAAIiE,mBAAoB/D,EAAG,cAAc,CAACE,MAAM,CAAC,OAASJ,EAAIuD,OAAO,QAAUvD,EAAI6B,QAAQ,QAAU7B,EAAIkF,QAAQ,MAAQlF,EAAIkE,mBAAmBvD,GAAG,CAAC,gBAAkB,SAASuB,GAAQlC,EAAIiE,oBAAqB,MAAUjE,EAAIS,KAAMT,EAAImF,MAAOjF,EAAG,YAAY,CAACE,MAAM,CAAC,aAAeJ,EAAIoF,cAAczE,GAAG,CAAC,YAAcX,EAAIwB,eAAexB,EAAIS,MAAM,GAAGT,EAAIS,KAAMT,EAAIqF,eAAgBnF,EAAG,UAAU,CAACE,MAAM,CAAC,QAAU,IAAKJ,EAAIc,QAASK,eAAgBnB,EAAImB,gBAAiB,QAAUnB,EAAIqF,gBAAgB1E,GAAG,CAAC,OAASX,EAAIiB,gBAAgB,QAAUjB,EAAIsF,iBAAiBtF,EAAIS,MAAM,MAE7mL8E,EAAkB,G,sDCFlBxF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACH,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,MAAM,CAACG,YAAY,eAAe,CAACL,EAAIU,GAAG,UAAUR,EAAG,UAAU,CAACsF,IAAI,YAAYnF,YAAY,aAAaD,MAAM,CAAC,MAAQJ,EAAIyF,UAAU,cAAc,GAAG,MAAQzF,EAAI0F,MAAM,iBAAiB1F,EAAI2F,gBAAgB,CAAG3F,EAAIe,QAAgUf,EAAIS,KAA3TP,EAAG,eAAe,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,CAAEwF,UAAU,EAAMC,QAAS,UAAWC,QAAS,UAAY,CAAC5F,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,YAAc,UAAU2F,MAAM,CAAChI,MAAOiC,EAAIyF,UAAUO,MAAOC,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIyF,UAAW,QAASS,IAAME,WAAW,sBAAsB,GAAcpG,EAAIe,QAA8Vf,EAAIS,KAAzVP,EAAG,eAAe,CAACE,MAAM,CAAC,KAAO,WAAW,MAAQ,CAAEwF,UAAU,EAAMC,QAAS,UAAWC,QAAS,UAAY,CAAC5F,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAW,aAAe,MAAM,YAAc,UAAU2F,MAAM,CAAChI,MAAOiC,EAAIyF,UAAUY,SAAUJ,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIyF,UAAW,WAAYS,IAAME,WAAW,yBAAyB,GAAYlG,EAAG,eAAe,CAACE,MAAM,CAAC,KAAO,YAAY,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,YAAc,eAAe2F,MAAM,CAAChI,MAAOiC,EAAIyF,UAAUa,QAASL,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIyF,UAAW,UAAWS,IAAME,WAAW,wBAAwB,GAAGlG,EAAG,eAAe,CAACE,MAAM,CAAC,KAAO,oBAAoB,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAW,aAAe,MAAM,YAAc,QAAQ2F,MAAM,CAAChI,MAAOiC,EAAIyF,UAAUc,gBAAiBN,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIyF,UAAW,kBAAmBS,IAAME,WAAW,gCAAgC,GAAGlG,EAAG,eAAe,CAACE,MAAM,CAAC,KAAO,gBAAgB,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,YAAc,cAAc2F,MAAM,CAAChI,MAAOiC,EAAIyF,UAAUe,YAAaP,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIyF,UAAW,cAAeS,IAAME,WAAW,4BAA4B,GAAGlG,EAAG,YAAY,CAACG,YAAY,WAAWD,MAAM,CAAC,KAAO,WAAWO,GAAG,CAAC,MAAQ,SAASuB,GAAQ,OAAOlC,EAAIgB,WAAW,gBAAgB,CAAChB,EAAIU,GAAG,UAAUR,EAAG,eAAe,CAACG,YAAY,uBAAuB,CAACH,EAAG,cAAc,CAAC6F,MAAM,CAAChI,MAAOiC,EAAIyF,UAAUgB,UAAWR,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIyF,UAAW,YAAaS,IAAME,WAAW,wBAAwB,CAACpG,EAAIU,GAAG,eAAe,GAAGR,EAAG,eAAe,CAACG,YAAY,uBAAuB,CAACH,EAAG,cAAc,CAAC6F,MAAM,CAAChI,MAAOiC,EAAIyF,UAAUiB,UAAWT,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIyF,UAAW,YAAaS,IAAME,WAAW,wBAAwB,CAACpG,EAAIU,GAAG,YAAY,IAAI,IAAI,QAE7/E6E,EAAkB,GCItB,MAAMoB,EAAQ,CACZC,QAAShI,OAAOiI,aAChBC,IAAIzI,EAAKN,GACP,IAAKA,EAAO,OAEZ,IAAI7C,EAAO6L,EAAUhJ,IAAU,GAE/BkC,KAAK2G,QAAQI,QAAQ3I,EAAKnD,IAE5ByC,IAAIU,GACF,IAAKA,EAAK,OAEV,MAAM4I,EAAMhH,KAAK2G,QAAQM,QAAQ7I,GACjC,IAAI8I,EAASF,GAAO,GAEpB,IACEE,EAASC,KAAKC,MAAMF,GACpB,MAAOG,GACPH,EAASF,GAAO,GAGlB,OAAOE,GAETI,OAAOlJ,GACAA,GAEL4B,KAAK2G,QAAQY,WAAWnJ,IAE1BoJ,QACExH,KAAK2G,QAAQa,SAGfC,WAAWC,GACT,IAAK,IAAItJ,KAAOsJ,EACd1H,KAAK2G,QAAQY,WAAWnJ,KAKxB0I,EAAY,SAAShJ,GACzB,IAAKA,EAAO,OAEZ,IAAIkJ,EAAM,GACV,MAAMW,EAAOjM,OAAOC,UAAUiM,SAAS/L,KAAKiC,GAO5C,OALEkJ,EADW,oBAATW,GAAuC,mBAATA,EAC1BR,KAAKU,UAAU/J,GAEfA,EAGDkJ,GAGMN,QCXA,GACfoB,MAAA,mBACAC,SAAA,GACA9M,OACA,OACA+M,UACAtC,cAAA,QACAF,UAAA,KAAA5E,KACA6E,MAAA,CACAY,QAAA,EAAAV,UAAA,EAAAC,QAAA,SAAAC,QAAA,SACAS,gBAAA,EAAAT,QAAA,SACAU,YAAA,EAAAZ,UAAA,EAAAC,QAAA,UAAAC,QAAA,YAIAoC,QAAA,CACAlH,WAAAmH,GACA,KAAAC,MAAAD,GAAAE,SAAAC,IACA,IAAAA,EACA,SAGA,KAAAC,MAAA,kBAAA9C,cAIA+C,gBACA,KAAAD,MAAA,qBAGAE,MAAA,CACAhD,UAAA,CACAiD,QAAAC,GACAhC,EAAAG,IAAA,UAAA6B,IAEAC,MAAA,KCnFid,I,wBCQ7cC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,EAAAA,E,QCnBX9I,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,WAAW,CAACH,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACL,EAAI8I,GAAG,GAAG5I,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,WAAW,CAACL,EAAIU,GAAG,UAAUR,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACL,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIkB,gBAAkBlB,EAAIkB,eAAe6H,kBAAkB7I,EAAG,MAAM,CAACG,YAAY,WAAWM,GAAG,CAAC,MAAQ,SAASuB,GAAQ,OAAOlC,EAAIsB,KAAK,SAAS,CAACpB,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,cAAc,GAAGF,EAAG,QAAQ,CAACsF,IAAI,cAAcpF,MAAM,CAAC,SAAW,GAAG,KAAO,GAAG,IAAM4I,EAAQ,gBAEhiBzD,EAAkB,CAAC,WAAY,IAAIvF,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM4I,EAAQ,QAA+B,IAAM,qBCkB7J,GACfjB,MAAA,sCACA,gBACA,MAAAkB,EAAA,KAAAb,MAAA,eAIA,GAFAH,IAAAiB,qBAAAD,EAAA,KAAAE,kBAAA,WAEAF,EAAAG,OACA,UACAH,EAAAI,OACA,MAAAC,GACAC,QAAAC,IAAA,uBAAAF,KAIAG,gBACA,MAAAR,EAAA,KAAAb,MAAA,eAEAa,EAAAS,SAEAxB,QAAA,CACA5G,OACA,KAAAiH,MAAA,WC1Cid,ICQ7c,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXxI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC6D,MAAM,CAAC,iBAAkB/D,EAAI2J,YAAY,CAACzJ,EAAG,OAAO,CAACG,YAAY,cAAcM,GAAG,CAAC,MAAQX,EAAIwB,cAAc,CAACtB,EAAG,WAAW,CAACG,YAAY,uBAAuBD,MAAM,CAAC,KAAO,YAAYF,EAAG,WAAW,GAAGA,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,OAAO,CAACG,YAAY,eAAe,CAACL,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIkB,eAAe6H,aAAa,KAAwC,eAAlC/I,EAAIkB,eAAe8C,WAA6B9D,EAAG,OAAO,CAACF,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIkB,eAAe0I,QAAQ,QAAQ5J,EAAIS,OAAST,EAAImE,KAAyJnE,EAAIS,KAAvJP,EAAG,MAAM,CAACG,YAAY,mBAAmBM,GAAG,CAAC,MAAQ,SAASuB,GAAQlC,EAAI6J,aAAc,KAAQ,CAAC3J,EAAG,IAAI,CAACG,YAAY,8BAAwCL,EAAImE,KAAMjE,EAAG,aAAa,CAACE,MAAM,CAAC,eAAe,kBAAkB,UAAY,MAAM,MAAQ,GAAG,QAAU,UAAU,CAACF,EAAG,MAAM,CAACA,EAAG,MAAM,CAACG,YAAY,oBAAoBD,MAAM,CAAC,MAAQJ,EAAIkB,eAAe6H,cAAc,CAAC/I,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIkB,eAAe6H,aAAa,OAAO7I,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACL,EAAIU,GAAG,QAAQR,EAAG,OAAO,CAACG,YAAY,UAAU,CAACL,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIkB,eAAe0I,WAAW1J,EAAG,OAAO,CAACG,YAAY,WAAWM,GAAG,CAAC,MAAQX,EAAI8J,kBAAkB5J,EAAG,MAAM,CAACG,YAAY,mBAAmBD,MAAM,CAAC,KAAO,aAAa2J,KAAK,aAAa,CAAC7J,EAAG,IAAI,CAACG,YAAY,gCAAgCL,EAAIS,MAAM,GAAGP,EAAG,YAAY,CAACE,MAAM,CAAC,QAAUJ,EAAI6J,YAAY,UAAY,MAAM,eAAe7J,EAAIgK,OAAO,kBAAiB,EAAK,YAAa,EAAM,eAAe,yBAAyBrJ,GAAG,CAAC,iBAAiB,SAASuB,GAAQlC,EAAI6J,YAAY3H,KAAU,CAAChC,EAAG,MAAM,CAACG,YAAY,oBAAoBD,MAAM,CAAC,MAAQJ,EAAIkB,eAAe6H,cAAc,CAAC/I,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIkB,eAAe6H,aAAa,OAAO7I,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACL,EAAIU,GAAG,QAAQR,EAAG,OAAO,CAACG,YAAY,UAAU,CAACL,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIkB,eAAe0I,WAAW1J,EAAG,OAAO,CAACG,YAAY,WAAWM,GAAG,CAAC,MAAQX,EAAI8J,kBAAoB9J,EAAImE,KAAsyDnE,EAAIS,KAApyDP,EAAG,MAAM,CAACG,YAAY,aAAaM,GAAG,CAAC,MAAQX,EAAIyB,mBAAmB,CAACvB,EAAG,MAAM,CAACG,YAAY,OAAOD,MAAM,CAAC,EAAI,gBAAgB,QAAU,gBAAgB,QAAU,MAAM,MAAQ,6BAA6B,OAAO,OAAO,MAAQ,KAAK,OAAS,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,EAAI,0+CAA0+C,KAAO,UAAU,OAAO,eAAwB,IAEhsHmF,EAAkB,GCFlBxF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,OAAO,CAACF,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIiK,QAAQ,QAExG1E,EAAkB,GCIP,GACfrK,OACA,OACA+O,OAAA,EACAC,WAAA,EACAC,eAAA,OAGAC,UACA,KAAAC,4BAEAZ,gBACAa,aAAA,KAAAH,gBACA,KAAAA,eAAA,MAEAjC,QAAA,CACAqC,aAAAhO,GACA,IAAAiO,EACAC,KAAAC,MAAAnO,EAAA,SACA,IAAAkO,KAAAC,MAAAnO,EAAA,MACAkO,KAAAC,MAAAnO,EAAA,MACAY,EACAsN,KAAAC,MAAAnO,EAAA,UACA,IAAAkO,KAAAC,MAAAnO,EAAA,OACAkO,KAAAC,MAAAnO,EAAA,OACAO,EACA2N,KAAAC,MAAAnO,EAAA,OACA,IAAAkO,KAAAC,MAAAnO,EAAA,IACAkO,KAAAC,MAAAnO,EAAA,IACA,OAAAiO,EAAA,IAAArN,EAAA,IAAAL,GAEAuN,2BACA,KAAAH,aAEA,KAAAC,eAAAQ,WAAA,KACAL,aAAA,KAAAH,gBACA,KAAAA,eAAA,KAEA,MAAAS,EAAA,KAAAL,aAAA,KAAAL,YAEA,KAAAD,OAAAW,EAEA,KAAAP,4BACA,QCjDkc,ICO9b,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,I,QCXR,MAAMQ,EAAWA,KACtB,MAAMC,EAAKC,UAAUC,UACfC,EAAUH,EAAGI,oBACbC,EAAQJ,UAAUF,SAASO,cAAcC,QAAQ,QAAU,GAAK,sBAAsBC,KAAKR,GAC3FS,EAAUR,UAAUF,SAASO,cAAcC,QAAQ,UAAY,EAC/DG,EAAiB,oBAAoBF,KAAKR,GAC1CW,EAAY,uBAAuBH,KAAKR,GACxCY,EAAY,gBAAgBJ,KAAKR,IAAOU,EACxCG,EAAY,cAAcL,KAAKR,IAAOG,EAAQI,QAAQ,YAAc,EACpEO,EAAY,cAAcN,KAAKR,GAC/Be,EAAW,mBAAmBP,KAAKR,MAASA,EAAGO,QAAQ,QAAU,GACjES,EAAShB,EAAGO,QAAQ,QAAU,EAC9BU,EAAWjB,EAAGO,QAAQ,WAAa,KAAOP,EAAGO,QAAQ,WAAa,GAClEW,EAAajB,UAAUC,UAAUI,cAAcC,QAAQ,QAAU,GAAK,sBAAsBC,KAAKR,GACjGmB,EAAS,QAAQX,KAAKP,UAAUC,YAAegB,GAAcjB,UAAUmB,eAAiB,EACxFC,EACJ,oBAAoBb,KAAKR,IAAQa,IAAc,aAAaL,KAAKR,IAASc,GAAa,aAAaN,KAAKR,GACrGsB,EAAU,aAAad,KAAKR,KAAQqB,EACpCE,EAAQD,GAAWH,EACnB9H,GAAQiI,IAAYT,IAAcD,IAAcO,EAChDK,EAAUxB,EAAGO,QAAQ,UAAY,GAAKP,EAAGO,QAAQ,gBAAkB,GAAKP,EAAGO,QAAQ,gBAAkB,EACrGkB,IAActB,EAAQuB,MAAM,YAAc1B,EAAGO,QAAQ,YAAc,EACnEoB,IACFxB,EAAQuB,MAAM,sBACdvB,EAAQuB,MAAM,cAChB1B,EAAGO,QAAQ,QAAU,GACrBP,EAAGO,QAAQ,mBAAqB,EAE5BqB,EACJ5B,EAAGO,QAAQ,mBAAqB,GAChCJ,EAAQI,QAAQ,YAAc,GAC9BJ,EAAQI,QAAQ,kBAAoB,GAEpCJ,EAAQI,QAAQ,eAAiB,EAE7BsB,EACJ7B,EAAGO,QAAQ,mBAAqB,GAChCJ,EAAQI,QAAQ,WAAa,GAC7BJ,EAAQI,QAAQ,kBAAoB,GAEpCJ,EAAQI,QAAQ,eAAiB,EAE7BuB,EACJ9B,EAAGO,QAAQ,mBAAqB,GAAKJ,EAAQI,QAAQ,YAAc,GAAKJ,EAAQI,QAAQ,mBAAqB,EACzGwB,EAAgB/B,EAAGO,QAAQ,gBAAkB,EAC7CyB,EAAUhC,EAAGO,QAAQ,UAAY,GAAKP,EAAGO,QAAQ,QAAU,EAC3D0B,EAAW9B,EAAQI,QAAQ,WAAa,GAAKJ,EAAQI,QAAQ,mBAAqB,EAClF2B,EAAa/B,EAAQI,QAAQ,aAAe,EAC5C4B,EACJnC,EAAGO,QAAQ,iBAAmB,GAC9BP,EAAGO,QAAQ,cAAgB,GAC3BP,EAAGO,QAAQ,UAAY,GACvBP,EAAGO,QAAQ,UAAY,GACvBP,EAAGO,QAAQ,cAAgB,GAC3BP,EAAGO,QAAQ,OAAS,GACpBP,EAAGO,QAAQ,aAAe,GAC1BP,EAAGO,QAAQ,UAAY,GACvBP,EAAGO,QAAQ,UAAY,GACvBP,EAAGO,QAAQ,gBAAkB,GAC7BP,EAAGO,QAAQ,cAAgB,GAC3BP,EAAGO,QAAQ,UAAY,GACvBP,EAAGO,QAAQ,aAAe,GAC1BP,EAAGO,QAAQ,kBAAoB,GAC/BP,EAAGO,QAAQ,WAAa,GACxBP,EAAGO,QAAQ,WAAa,GACxBP,EAAGO,QAAQ,UAAY,GACvBP,EAAGO,QAAQ,OAAS,GACpBP,EAAGO,QAAQ,YAAc,GACzBP,EAAGO,QAAQ,cAAgB,GAC3BP,EAAGO,QAAQ,WAAa,GACxBP,EAAGO,QAAQ,iBAAmB,GAC9BP,EAAGO,QAAQ,oBAAsB,GACjCP,EAAGO,QAAQ,kBAAoB,EAEjC,MAAO,CACLc,WACAC,UACAT,YACAxH,OACAgH,QACAU,WACAN,UACAgB,YACAD,UACAG,OACAK,UACAG,WACAnB,SACAC,WACAE,SACAW,WACAG,WACAC,aACAN,kBACAC,oBACAlB,YACAY,QACAQ,kBA+FSK,EAAiBA,KAC5B,MAAM,UAAEX,EAAS,SAAEV,EAAQ,SAAEE,EAAQ,gBAAEW,EAAe,kBAAEC,EAAiB,SAAEM,EAAQ,OAAEnB,EAAM,SAAEc,EAAQ,SAAEG,GACrGlC,IAEF,OAAI0B,EACK,UACER,EACF,SACEW,EACF,gBACEC,EACF,kBACEC,EACF,SACEG,EACF,WACEH,EACF,SACEd,EACF,OACED,EACF,SACEoB,EACF,QAGF,IAGIE,EAAoBA,KAC/B,IAAIC,EAASrC,UAAUC,UAAUqC,cAAcb,MAAM,kBAErD,GAAIY,EAAQ,CACV,IAAIE,EAAWF,EAAO,GAAGG,MAAM,KAG/B,OADAD,EAAWA,EAASrO,IAAKuO,GAAMC,SAASD,EAAG,KACpC,CACLE,MAAOJ,EAAS,GAChBK,MAAOL,EAAS,MAMTM,EAAgBA,KAC3B,IAAIR,EAASrC,UAAUC,UAAUqC,cAAcb,MAAM,sCAErD,GAAIY,EAAQ,CACV,IAAIE,EAAWF,EAAO,GAAGG,MAAM,KAI/B,OAFAD,EAAWA,EAASrO,IAAKuO,GAAMC,SAASD,EAAG,KAEpC,CACLE,MAAOJ,EAAS,GAChBK,MAAOL,EAAS,OAMT,KAAEnJ,GAAS0G,IAOXgD,EAA6BA,KACxC,MAAM,UAAElC,EAAS,QAAES,EAAO,OAAEH,EAAM,SAAEF,EAAQ,SAAEgB,GAAalC,IAC3D,IAAIiD,GAAe,EACnB,MAAMC,EAAcb,IACdN,EAA2B,WAAhBmB,EAEjB,GAAIpC,EAAW,CACb,MAAM,MAAE+B,GAAUP,KAAuB,CAAEO,MAAO,EAAGC,MAAO,GAE5DG,EAAeJ,GAAS,OACnB,GAAItB,GAAWH,EAAQ,CAC5B,MAAM,MAAEyB,EAAK,MAAEC,GAAUC,KAAmB,CAAEF,MAAO,EAAGC,MAAO,GAE3DD,EAAQ,IAAiB,KAAVA,GAAgBC,EAAQ,EACzCG,EAAelB,GAAYb,GAClB2B,EAAQ,IAAiB,KAAVA,GAAgBC,EAAQ,KAChDG,EAAe/B,GAMnB,OAFA+B,EAAeA,IAAiBf,EAEzBe,GCxRIjI,EAAU,CACrBmI,KAAOnI,IACLoI,aAAQD,KAAK,CAAEnI,UAASqI,SAAU,IAAMC,QAAQ,MAIvCC,EAAgBA,CAACC,GAAa,IAAIC,QAC7C,GAAID,EAAW,CACb,IAAIE,EAAO,IAAID,KAAKD,GAChBG,EAAID,EAAKE,cACTC,EAAIH,EAAKI,WAAa,EACtBtR,EAAIkR,EAAKK,UACTpE,EAAI+D,EAAKM,WACT1R,EAAIoR,EAAKO,aACThS,EAAIyR,EAAKQ,aACb,OAAOP,EAAI,IAAME,EAAI,IAAMrR,EAAI,IAAMmN,EAAI,IAAMrN,EAAI,IAAML,EAEzD,MAAO,IAQEkS,EAAS,CACpBC,SAASC,GACP,MAAsB,kBAARA,GAEhBC,SAASC,GACP,MAAmE,oBAA5DzT,OAAOC,UAAUiM,SAAS/L,KAAKsT,GAAKlE,qBAE7CmE,QAAQC,GACN,OAAOC,MAAMF,QAAQC,IAEvBE,SAASC,GACP,MAAsB,kBAARA,GAEhBC,UAAUD,GACR,MAAsB,mBAARA,ICcH,OACf1H,MAAA,+BACA4H,WAAA,CACAC,UAGA1U,OACA,OACA2O,aAAA,EACA1F,SAGA+D,QAAA,CACA8B,SACA,KAAAH,aAAA,GAEArI,cACA,KAAA+G,MAAA,gBAEAjH,OACA,KAAAiH,MAAA,SAEA9G,mBACA,KAAA8G,MAAA,eAGAuB,aACA,KAAA+F,UAAA,KAAA3O,eAAA0I,QAAAkG,KAAA,KACAjK,EAAAmI,KAAA,gBCzFid,ICS7c,G,oBAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCpBXjO,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAImE,KAAMjE,EAAG,YAAY,CAACE,MAAM,CAAC,eAAe,oBAAoB,QAAUJ,EAAI+P,UAAU,CAAC7P,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACL,EAAIU,GAAG,QAAQR,EAAG,MAAM,CAACG,YAAY,QAAQM,GAAG,CAAC,MAAQX,EAAIgQ,cAAc9P,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACH,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,UAAU,CAACG,YAAY,mBAAmBD,MAAM,CAAC,iBAAiBJ,EAAIiQ,SAAStP,GAAG,CAAC,OAASX,EAAIkQ,eAAe,CAAChQ,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACF,EAAG,IAAI,CAACG,YAAY,oBAAoBH,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,SAAS2J,KAAK,SAAS,CAAC/J,EAAIU,GAAG,YAAYR,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACF,EAAG,IAAI,CAACG,YAAY,yBAAyBH,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,SAAS2J,KAAK,SAAS,CAAC/J,EAAIU,GAAG,WAAWR,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,aAAa,CAACF,EAAG,IAAI,CAACG,YAAY,yBAAyBH,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,SAAS2J,KAAK,SAAS,CAAC/J,EAAIU,GAAG,UAAUR,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,IAAI,CAACG,YAAY,0BAA0BH,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,SAAS2J,KAAK,SAAS,CAAC/J,EAAIU,GAAG,WAAW,IAAI,GAAGR,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAAkB,WAAhBL,EAAIiQ,QAAsB/P,EAAG,SAAS,CAACE,MAAM,CAAC,YAAcJ,EAAImQ,YAAY,QAAUnQ,EAAIe,QAAQ,UAAYf,EAAI8B,UAAU,WAAa9B,EAAIsD,YAAY3C,GAAG,CAAC,QAAUX,EAAIoQ,mBAAmBpQ,EAAIS,KAAsB,WAAhBT,EAAIiQ,QAAsB/P,EAAG,SAAS,CAACE,MAAM,CAAC,QAAUJ,EAAIc,QAAQ,QAAUd,EAAIiQ,SAAStP,GAAG,CAAC,QAAUX,EAAIoQ,mBAAmBpQ,EAAIS,KAAsB,aAAhBT,EAAIiQ,QAAwB/P,EAAG,YAAYF,EAAIS,KAAsB,UAAhBT,EAAIiQ,QAAqB/P,EAAG,WAAWF,EAAIS,MAAM,OAAOP,EAAG,MAAM,CAACA,EAAG,YAAY,CAACE,MAAM,CAAC,QAAUJ,EAAI+P,QAAQ,UAAY,MAAM,kBAAiB,EAAK,YAAa,EAAM,eAAe,qBAAqB,eAAe/P,EAAIgQ,UAAUrP,GAAG,CAAC,iBAAiB,SAASuB,GAAQlC,EAAI+P,QAAQ7N,KAAU,CAAChC,EAAG,WAAWA,EAAG,SAAS,CAACE,MAAM,CAAC,YAAcJ,EAAImQ,YAAY,QAAUnQ,EAAIe,QAAQ,UAAYf,EAAI8B,UAAU,WAAa9B,EAAIsD,YAAY3C,GAAG,CAAC,QAAUX,EAAIoQ,mBAAmBlQ,EAAG,MAAM,CAACG,YAAY,YAAYM,GAAG,CAAC,MAAQ,SAASuB,GAAQlC,EAAIqQ,iBAAkB,KAAQ,CAACnQ,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,UAAUR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,IAAI,CAACG,YAAY,6BAA6B,GAAGH,EAAG,YAAY,CAACE,MAAM,CAAC,QAAUJ,EAAIqQ,gBAAgB,UAAY,MAAM,kBAAiB,EAAK,YAAa,EAAM,eAAe,sBAAsB1P,GAAG,CAAC,iBAAiB,SAASuB,GAAQlC,EAAIqQ,gBAAgBnO,KAAU,CAAChC,EAAG,MAAM,CAACG,YAAY,qBAAqBM,GAAG,CAAC,MAAQ,SAASuB,GAAQlC,EAAIqQ,iBAAkB,KAAS,CAACrQ,EAAIU,GAAG,UAAUR,EAAG,MAAM,CAACG,YAAY,yBAAyB,CAACH,EAAG,aAAa,MAAM,IAE7lFqF,EAAkB,GCFlBxF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,2BAA2B,CAAGL,EAAImQ,YAA0gCnQ,EAAIS,KAAjgCP,EAAG,MAAM,CAAEF,EAAImE,KAAMjE,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,UAAUR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,YAAY,CAACE,MAAM,CAAC,eAAe,aAAaO,GAAG,CAAC,OAASX,EAAIsQ,oBAAoBvK,MAAM,CAAChI,MAAOiC,EAAI/B,KAAMgI,SAAS,SAAUC,GAAMlG,EAAI/B,KAAKiI,GAAKE,WAAW,SAASpG,EAAIgD,GAAIrH,OAAO8D,KAAKO,EAAIuQ,gBAAgB,SAASlS,GAAK,OAAO6B,EAAG,YAAY,CAAC7B,IAAIA,EAAI+B,MAAM,CAAC,MAAQJ,EAAIuQ,cAAclS,GAAK,MAAQA,IAAM,CAAC2B,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIuQ,cAAclS,IAAM,UAAS,IAAI,KAAK2B,EAAIS,KAAMT,EAAImE,KAAMjE,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,UAAUR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,YAAY,CAACE,MAAM,CAAC,eAAe,aAAaO,GAAG,CAAC,OAASX,EAAIwQ,mBAAmBzK,MAAM,CAAChI,MAAOiC,EAAIyQ,UAAWxK,SAAS,SAAUC,GAAMlG,EAAIyQ,UAAUvK,GAAKE,WAAW,cAAcpG,EAAIgD,GAAIrH,OAAO8D,KAAKO,EAAI0Q,eAAe,SAASrS,GAAK,OAAO6B,EAAG,YAAY,CAAC7B,IAAIA,EAAI+B,MAAM,CAAC,MAAQJ,EAAI0Q,aAAarS,GAAK,MAAQA,IAAM,CAAC2B,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAI0Q,aAAarS,IAAM,UAAS,IAAI,KAAK2B,EAAIS,OAAiBT,EAAImE,KAAMjE,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,YAAYR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,YAAY,CAACS,GAAG,CAAC,OAASX,EAAI2Q,mBAAmB5K,MAAM,CAAChI,MAAOiC,EAAI4Q,YAAa3K,SAAS,SAAUC,GAAMlG,EAAI4Q,YAAY1K,GAAKE,WAAW,kBAAkB,KAAKpG,EAAIS,QAE56C8E,GAAkB,GCmCP,IACfwC,MAAA,mDACA7M,OACA,OACAqV,cAAA,CACAM,KAAA,OACAC,OAAA,SAEAJ,aAAA,CACAK,OAAA,SACAC,MAAA,WAEAP,UAAA,KAAA1P,QAAA,iBACA6P,YAAA,KAAA9O,UACA7D,KAAA,KAAAqF,WACAa,SAGA+D,QAAA,CACAoI,mBAAAvS,GACA,KAAAwK,MAAA,WAAAjF,WAAAvF,KAEAyS,kBAAAzS,GACA,KAAAwK,MAAA,WAAAxH,QAAA,UAAAhD,KAEA4S,kBAAA5S,GACA,KAAAwK,MAAA,WAAAzG,UAAA/D,MAGA0K,MAAA,IClEkd,MCO9c,GAAY,eACd,GACA,EACA,IACA,EACA,KACA,KACA,MAIa,M,QClBX1I,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,2BAA2B,CAAEL,EAAIiR,qBAAsB/Q,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,GAAG,KAAO,UAAU,CAACF,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,OAAO,CAACG,YAAY,mBAAmB,CAACL,EAAIU,GAAG,iBAAiBR,EAAG,OAAO,CAACG,YAAY,WAAWM,GAAG,CAAC,MAAQ,SAASuB,GAAQlC,EAAIkR,cAAe,KAAQ,CAAClR,EAAIU,GAAG,cAAcV,EAAIS,KAAoB,WAAdT,EAAImR,MAAoBjR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAI8I,GAAG,KAAK9I,EAAIS,KAAoB,YAAdT,EAAImR,MAAqBjR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,MAAM,CAACkR,WAAW,CAAC,CAAC9T,KAAK,UAAU+T,QAAQ,YAAYtT,OAAO,EAAMqI,WAAW,SAAS/F,YAAY,mBAAmBD,MAAM,CAAC,uBAAuB,gBAAgBJ,EAAIS,KAAoB,UAAdT,EAAImR,MAAmBjR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAI8I,GAAG,KAAK9I,EAAIS,KAAKP,EAAG,MAAM,CAAC6D,MAAM,CAAC,+BAA8C,YAAd/D,EAAImR,MAAsB,UAAY,WAAW,CAACjR,EAAG,MAAM,CAACA,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,WAAWR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,YAAY,CAACE,MAAM,CAAC,eAAe,aAAaO,GAAG,CAAC,OAASX,EAAIsR,eAAevL,MAAM,CAAChI,MAAOiC,EAAIuR,OAAOC,aAAcvL,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIuR,OAAQ,eAAgBrL,IAAME,WAAW,wBAAwBpG,EAAIgD,GAAIhD,EAAIyR,aAAa,SAASvO,GAAM,OAAOhD,EAAG,YAAY,CAAC7B,IAAI6E,EAAK7B,SAASjB,MAAM,CAAC,MAAQ8C,EAAKwO,MAAM,MAAQxO,EAAK7B,eAAc,IAAI,KAAKnB,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,QAAQH,EAAG,MAAM,CAACG,YAAY,qBAAqB,CAACH,EAAG,MAAM,CAAC6D,MAAM,CAAC/D,EAAI2R,mBAAqB,UAAY,SAAU,qBAAqB,CAAC3R,EAAIU,GAAG,aAAaR,EAAG,QAAQ,CAACsF,IAAI,WAAWnF,YAAY,gBAAgBD,MAAM,CAAC,SAAW,GAAG,UAAW,EAAM,YAAc,IAAIwR,SAAS,CAAC,OAAQ,SAAY1R,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,SAASR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,YAAY,CAACE,MAAM,CAAC,eAAe,aAAaO,GAAG,CAAC,OAASX,EAAI6R,kBAAkB9L,MAAM,CAAChI,MAAOiC,EAAIuR,OAAOO,gBAAiB7L,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIuR,OAAQ,kBAAmBrL,IAAME,WAAW,2BAA2BpG,EAAIgD,GAAIhD,EAAI+R,gBAAgB,SAAS7O,GAAM,OAAOhD,EAAG,YAAY,CAAC7B,IAAI6E,EAAK7B,SAASjB,MAAM,CAAC,MAAQ8C,EAAKwO,MAAM,MAAQxO,EAAK7B,eAAc,IAAI,KAAKnB,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,QAAQR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,WAAW,CAACG,YAAY,SAASD,MAAM,CAAC,KAAO,wBAAwBF,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,MAAM,CAACG,YAAY,cAAcyC,MAAO,CAAEkP,UAAY,cAAahS,EAAIiS,qBAAuB,KAAK/R,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,SAASR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,YAAY,CAACE,MAAM,CAAC,eAAe,aAAaO,GAAG,CAAC,OAASX,EAAIkS,mBAAmBnM,MAAM,CAAChI,MAAOiC,EAAIuR,OAAOpI,iBAAkBlD,SAAS,SAAUC,GAAMlG,EAAImG,KAAKnG,EAAIuR,OAAQ,mBAAoBrL,IAAME,WAAW,4BAA4BpG,EAAIgD,GAAIhD,EAAImS,iBAAiB,SAASjP,GAAM,OAAOhD,EAAG,YAAY,CAAC7B,IAAI6E,EAAK7B,SAASjB,MAAM,CAAC,MAAQ8C,EAAKwO,MAAM,MAAQxO,EAAK7B,eAAc,IAAI,KAAKnB,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,QAAQH,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,OAAO,CAACG,YAAY,aAAaM,GAAG,CAAC,MAAQX,EAAIqJ,OAAO,CAACrJ,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIoS,gBAAkB,QAAU,YAAapS,EAAIoS,gBAAiBlS,EAAG,OAAO,CAACG,YAAY,qBAAqB,CAACL,EAAIU,GAAG,eAAeV,EAAIS,KAAKP,EAAG,QAAQ,CAACsF,IAAI,WAAWnF,YAAY,gBAAgBD,MAAM,CAAC,MAAO,EAAK,IAAM,oDAAoDJ,EAAI8I,GAAG,GAAG5I,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACH,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWO,GAAG,CAAC,MAAQX,EAAIqS,WAAW,CAACrS,EAAIU,GAAG,SAAS,KAAKR,EAAG,QAAQ,CAACE,MAAM,CAAC,QAAUJ,EAAIkR,cAAcvQ,GAAG,CAAC,MAAQ,SAASuB,GAAQlC,EAAIkR,cAAe,OAAW,IAErqH3L,GAAkB,CAAC,WAAY,IAAIvF,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACL,EAAIU,GAAG,wCACtJ,WAAY,IAAIV,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACL,EAAIU,GAAG,oCACjI,WAAY,IAAIV,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,UAAUR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,IAAI,CAACE,MAAM,CAAC,KAAO,uDAAuD,IAAM,sBAAsB,OAAS,WAAW,CAACJ,EAAIU,GAAG,kBCJjSX,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACE,MAAM,CAAC,eAAe,kBAAkB,QAAUJ,EAAI+P,QAAQ,MAAQ,QAAQ,iBAAiB,GAAG,MAAQ,GAAG,IAAM,QAAQpP,GAAG,CAAC,iBAAiB,SAASuB,GAAQlC,EAAI+P,QAAQ7N,KAAU,CAAChC,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,MAAM,CAACG,YAAY,2BAA2B,CAACH,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,OAAO,CAACG,YAAY,aAAaM,GAAG,CAAC,MAAQX,EAAIsS,aAAapS,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAG,iBAAiBR,EAAG,MAAM,CAACG,YAAY,mCAAmC,CAACH,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACL,EAAIU,GAAG,4BAA4BR,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,OAAO,IAAM4I,EAAQ,QAAkD,IAAM,UAAU9I,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACL,EAAIU,GAAG,mCAAmCR,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,MAAM,CAACE,MAAM,CAAC,MAAQ,OAAO,IAAM4I,EAAQ,QAAuD,IAAM,UAAU9I,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACL,EAAIU,GAAG,+BAA+BR,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACL,EAAIU,GAAG,4BAA4BR,EAAG,MAAM,CAACG,YAAY,qBAAqB,CAACH,EAAG,MAAM,CAACF,EAAIU,GAAG,2DAA2DR,EAAG,MAAM,CAACF,EAAIU,GAAG,yDAAyDR,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACL,EAAIU,GAAG,oBAAoBR,EAAG,OAAO,CAACG,YAAY,OAAOM,GAAG,CAAC,MAAQX,EAAIuS,WAAW,CAACvS,EAAIU,GAAG,UAAUV,EAAIU,GAAG,yBAEprD6E,GAAkB,GCwEP,IACfwC,MAAA,YACA7M,OACA,UAEAgN,QAAA,CACAoK,UACA,KAAA/J,MAAA,UAEAgK,WACA3T,OAAA4T,SAAAC,YCpFid,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OAIa,M,QC2GA,IACf1K,MAAA,sBACA4H,WAAA,CACA+C,UAEA1K,SAAA,CACAiJ,uBACA,aAAA0B,EAAA,WAAAC,GAAA,KAAAlO,YAAA,GAEA,iBAAAiO,GAAA,WAAAA,GAAA,WAAAC,GAAA,WAAAA,IAGA1X,OAAA,IAAA2X,EAAAC,EAAAC,EACA,qBAAA5R,GAAA,KAAAL,SAAA,GACAkS,EAAA,CACAlB,iBAAA,OAAA3Q,QAAA,IAAAA,GAAA,QAAA0R,EAAA1R,EAAA8R,kBAAA,IAAAJ,OAAA,EAAAA,EAAAxR,WAAA,UACA8H,kBAAA,OAAAhI,QAAA,IAAAA,GAAA,QAAA2R,EAAA3R,EAAAC,mBAAA,IAAA0R,OAAA,EAAAA,EAAAzR,WAAA,UACAmQ,cAAA,OAAArQ,QAAA,IAAAA,GAAA,QAAA4R,EAAA5R,EAAA+R,kBAAA,IAAAH,OAAA,EAAAA,EAAA1R,WAAA,IAEA,OACAgE,eAAA,KAAA0K,QACAoB,MAAA,UACArM,OAAA,KACAqO,mBAAA,KACAC,mBAAA,KACAC,cAAA,KACAtB,eAAA,GACAI,gBAAA,GACAV,YAAA,GACAQ,WAAA,EACAV,OAAAyB,EACAZ,iBAAA,EACAT,oBAAA,EACA2B,iBAAA,KACA5O,WAAA,CAAAiO,OAAA,GAAAC,WAAA,IACA1B,cAAA,IAIA,gBACA,KAAAmC,cAAA,IAAAE,yBACA,KAAAF,cAAAG,aAGA,KAAAC,kBAEA,KAAAC,mBAEA,KAAAC,0BAGAlK,gBACA,KAAAnI,QAGA4G,QAAA,CACA,kBACA,KAAApD,SACA,KAAAA,OAAAmD,IAAA2L,sBAGA,KAAAC,uBAEA,KAAAC,iBAEA,KAAAC,oBAGA,qBAAA1S,EAAA,CAAA2S,GAAA,QAAAA,EAAA,KAAA7S,sBAAA,IAAA6S,GAAA,QAAAA,IAAAd,kBAAA,IAAAc,OAAA,EAAAA,EAAA3S,SAAA,IACA,IAAA4S,EAAA,SAEA,QAAAC,EACA,IAAAC,EAAA,GAcA,GAZA9S,IACA8S,EAAA,UACA9S,SAAA,CAAA+S,MAAA/S,KAIA,QAAA6S,EAAA,KAAAf,0BAAA,IAAAe,KAAAG,YAAAC,QAAAC,IACAA,EAAAjT,SAGA,KAAA6R,yBAAA,KAAArO,OAAA0P,kBAAA,KAAAL,GAEA,KAAAhB,mBAAA,CACA,MAAAsB,EAAA,KAAAtB,mBAAAuB,iBAAA,GAEAxB,GAAA,OAAAuB,QAAA,IAAAA,OAAA,EAAAA,EAAAE,cAAA,iBAEA,KAAApD,OAAA,IACA,KAAAA,OACAC,aAAA0B,GAGA,MAAA0B,EAAA,KAAAxM,MAAA,YAEAwM,GAAAH,IACAG,EAAAC,UAAA,KAAA1B,oBAIAc,EAAA,UACA,MAAA3M,GACA,KAAAqK,oBAAA,EAEApI,QAAAC,IAAA,aAAAlC,GAEA,yBAAAA,QAAA,IAAAA,OAAA,EAAAA,EAAA9H,QACAyU,EAAA,UAGA1K,QAAAC,IAAA,0BAAAlC,GAGA,KAAA5C,WAAA,IACA,KAAAA,WACAiO,OAAAsB,IAIA,qBAAA5S,EAAA,CAAAyT,GAAA,QAAAA,EAAA,KAAA3T,sBAAA,IAAA2T,GAAA,QAAAA,IAAA7B,kBAAA,IAAA6B,OAAA,EAAAA,EAAAzT,SAAA,QAAA0T,EACA,IAAAC,EAAA,SACAb,EAAA,GAEA9S,IACA8S,EAAA,UACA9S,SAAA,CAAA+S,MAAA/S,KAIA,QAAA0T,EAAA,KAAA3B,0BAAA,IAAA2B,KAAAV,YAAAC,QAAAC,IACAA,EAAAjT,SAGA,IAGA,GAFA,KAAA8R,yBAAA,KAAAtO,OAAA0P,kBAAA,KAAAL,GAEA,KAAAf,mBAAA,CACA,MAAA6B,EAAA,KAAA7B,mBAAA8B,iBAAA,GACAjC,GAAA,OAAAgC,QAAA,IAAAA,OAAA,EAAAA,EAAAN,cAAA,wBAEA,KAAApD,OAAA,IACA,KAAAA,OACAO,gBAAAmB,GAGA,KAAAnO,OAAAqQ,wBAEA,KAAArQ,OAAAsQ,cAAA,KAAAhC,oBAEA,KAAAE,iBAAA+B,YAAA,UACA,IACA,MAAAC,QAAA,KAAAxQ,OAAAsQ,gBAGA,KAAAnD,WAAAqD,EACA,MAAAhO,GACA,KAAAiO,0BAEA,KAEAP,EAAA,UACA,MAAA1N,GACAiC,QAAAC,IAAA,2BAAAlC,GAEA,yBAAAA,QAAA,IAAAA,OAAA,EAAAA,EAAA9H,QACAwV,EAAA,UAIA,KAAAtQ,WAAA,IACA,KAAAA,WACAkO,WAAAoC,IAIAQ,eAAAC,EAAA,GAAAC,EAAA,KACA,MAAAxG,EAAAzE,KAAAC,MAAAD,KAAAkL,UAAAD,EAAAD,EAAA,GAAAA,GACA,OAAAvG,GAGA6E,mBACA,MAAA6B,EAAA,KAAAJ,iBAEA7K,WAAA,KACA,KAAAwG,MAAA,WACAyE,IAGA,+BACA,KAAAvC,cAAA1S,GAAA,uBACA,aAAAgS,EAAA,WAAAC,GAAAtT,EAEA,YAAAqT,SACA,KAAAkB,iBAGA,YAAAjB,SACA,KAAAkB,iBAGA,KAAAJ,aAEA,KAAAhP,WAAApF,IAGA,KAAA+T,cAAA1S,GAAA,SAAArB,IACA,aAAAuW,EAAA,WAAAC,GAAAxW,GACA,WAAA4T,EAAA,WAAAD,EAAA,YAAA7R,GAAA0U,EACAC,EAAAF,EAEA3C,GACA,KAAA8C,aAAA,eAAA9C,EAAA7R,UAGA4R,GACA,KAAA+C,aAAA,kBAAA/C,EAAA5R,UAGAD,GACA,KAAA4U,aAAA,mBAAA5U,EAAAC,UAGA,KAAA4U,WAAAF,MAIA,mBAAA1X,EAAAiB,GACA,oBAAAjB,GAAA,iBAAAA,SACA,KAAAwV,eAAAvU,GAGA,iBAAAjB,SACA,KAAAyV,eAAAxU,GAGA,KAAAiS,OAAA,IACA,KAAAA,OACA,CAAAlT,GAAAiB,IAIAgS,cAAAhS,GACA,KAAA0W,aAAA,eAAA1W,IAGAuS,iBAAAvS,GACA,KAAA0W,aAAA,kBAAA1W,IAGA4S,kBAAA5S,GACA,KAAA0W,aAAA,mBAAA1W,IAGA,mBACA,MAAA4W,QAAA,KAAA7C,cAAAK,aAEA,KAAAuC,WAAAC,IAGAD,WAAAC,GACA,qBAAAnE,EAAA,gBAAAI,EAAA,YAAAV,GAAAyE,EACA,KAAAnE,iBACA,KAAAI,kBACA,KAAAV,eAGA8D,wBACA,KAAAjC,kBAAA6C,cAAA,KAAA7C,mBAGA8C,cACA,KAAAjD,oBAAAlL,IAAAoO,mBAAA,KAAAlD,oBAEA,KAAAC,oBAAAnL,IAAAoO,mBAAA,KAAAjD,qBAGA9R,OACA,KAAAiU,wBAEA,MAAAlC,EAAA,KAAAjL,MAAA,iBAEAiL,GACAA,EAAAiD,UAGA,KAAAF,cAEA,KAAAtR,OAAAyR,QAEA,KAAAhO,MAAA,WAEA,aACA,MAAAiO,EAAA,KAAApO,MAAA,YACAoO,IACAA,EAAApN,SAAA,KAAAgJ,iBACAnK,IAAAiB,qBAAAsN,EAAA,KAAAjF,OAAApI,wBAEAqN,EAAAnN,OACA,KAAA+I,iBAAA,UAEAoE,EAAA9M,QACA,KAAA0I,iBAAA,KAIAqE,eAAApV,EAAAqV,GACA,OACAA,EAAAC,KAAAzT,GACAA,EAAA7B,eACA,CAAAA,SAAA,GAAAqQ,MAAA,KAIAW,WACA,KAAA/Q,OAEA,sBAAAwQ,EAAA,iBAAA3I,EAAA,aAAAqI,GAAA,KAAAD,OAEA,KAAAhJ,MAAA,WACApH,eAAA,CACA8R,WAAA,KAAAwD,eAAA3E,EAAA,KAAAC,gBACA3Q,YAAA,KAAAqV,eAAAtN,EAAA,KAAAgJ,iBACAe,WAAA,KAAAuD,eAAAjF,EAAA,KAAAC,cAEAmF,WAAA,CACA7E,eAAA,KAAAA,eACAI,gBAAA,KAAAA,gBACAV,YAAA,KAAAA,gBAKAvI,qBAAAjC,EAAA,KAAAsK,OAAApI,kBACA,MAAAqN,EAAA,KAAApO,MAAA,YAEAoO,GAAAvP,GAAAgB,IAAAiB,qBAAAsN,EAAAvP,KAIAwB,MAAA,CACA,2BACAC,QAAAvB,GACA,KAAA+B,qBAAA/B,OCvdkd,MCO9c,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QClBXpH,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,YAAY,CAACH,EAAG,MAAM,CAACG,YAAY,qBAAqB,CAACH,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,UAAUR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,gBAAgB2F,MAAM,CAAChI,MAAOiC,EAAI6B,QAASoE,SAAS,SAAUC,GAAMlG,EAAI6B,QAAQqE,GAAKE,WAAW,cAAc,KAAKlG,EAAG,MAAM,CAACG,YAAY,kCAAkC,CAACH,EAAG,MAAM,CAACG,YAAY,OAAO,CAACL,EAAIU,GAAG,UAAUR,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,WAAW,CAACG,YAAY,0BAA0BD,MAAM,CAAC,aAAe,MAAM,YAAc,aAAa2F,MAAM,CAAChI,MAAOiC,EAAI6W,QAAS5Q,SAAS,SAAUC,GAAMlG,EAAI6W,QAAQ3Q,GAAKE,WAAW,cAAc,OAAOlG,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,YAAY,CAACG,YAAY,WAAWD,MAAM,CAAC,KAAO,OAAO,QAAUJ,EAAI8W,iBAAiBnW,GAAG,CAAC,MAAQX,EAAI+W,WAAW,CAAC/W,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACG,YAAY,aAAaD,MAAM,CAAC,QAAUJ,EAAIgX,cAAc,KAAO,UAAU,UAAYhX,EAAI6B,QAAQoV,QAAQtW,GAAG,CAAC,MAAQX,EAAIkX,SAAS,CAAClX,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIgX,cAAgB,MAAQ,MAAM,QAAQ,MAE7oCzR,GAAkB,GC+CP,IACfrK,OACA,OACA2G,QAAA,GACAgV,QAAA,GACAG,eAAA,EACAF,iBAAA,IAGA5O,QAAA,CACA,iBACA,KAAA4O,iBAAA,QACA7O,IAAAkP,OAAAC,cACA,KAAAN,iBAAA,GAEA,eACA,KAAAE,eAAA,EACA,IACA,kBAAAxQ,EAAA,IAAAG,EAAAhJ,IAAA,eAEApB,QAAA0L,IAAAkP,OAAAE,UACA7Q,EACA,KAAAqQ,QACA,KAAAhV,SAGAtF,GACAsJ,EAAAmI,KAAA,QAEA,KAAAnM,QAAA,IAEAgE,EAAAmI,KAAA,QAGA,KAAA6I,QAAA,GACA,MAAAvP,GACAzB,EAAAmI,KAAA,QAGA,KAAAgJ,eAAA,KCxFod,MCOhd,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QClBXjX,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,0BAA0B,CAACH,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIU,GAAG,OAAOV,EAAI6C,GAAG7C,EAAIsX,eAEjL/R,GAAkB,GCOP,IACfrK,OACA,OACAoc,QAAA,GACAC,OAAA,KAGAnN,UACA,MAAAoN,EAAAvP,IAAAqP,QAAA/J,MAAA,cAEA,KAAA+J,QAAAE,EAAA,GACA,KAAAD,OAAAC,EAAA,IAEAtP,QAAA,ICtBmd,MCO/c,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QC2EA,IACfH,MAAA,oCACA4H,WAAA,CACA8H,UACAC,UACAC,YACAC,YAEA1c,OACA,gBAAA4G,GAAA,aAAAwB,EAAA,QAAAvC,GAAA,KAAAD,SAAA,GAEA,OACAmP,QAAA,SACAlP,UACAuC,aACAxB,YACAqC,OACAkM,iBAAA,IAGAnI,QAAA,CACAgI,aAAA5Q,GACA,KAAA2Q,QAAA3Q,GAEA8Q,gBAAAlV,GACA,MAAA2c,EAAA,GACAC,EAAA,+BAEAA,EAAAxD,QAAAjW,IACAnD,EAAAmD,KACA,mBAAAA,GACAsI,EAAAG,IAAA,iBAAA5L,EAAAiG,gBAEA,KAAAoH,MAAA,WAEAsP,EAAAxZ,GAAAnD,EAAAmD,MAKA,KAAAkK,MAAA,UAAArN,IAEA8U,WACA,KAAAzH,MAAA,aCxIid,MCQ7c,I,UAAY,eACd,GACA,EACA,GACA,EACA,KACA,KACA,OAIa,M,QCnBXxI,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,wBAAwByC,MAAO9C,EAAI+X,cAAe,CAAC7X,EAAG,MAAM,CAACG,YAAY,mBAAmByC,MAAO9C,EAAIgY,eAAgB9X,EAAG,OAAO,CAACsF,IAAI,cAAczB,MAAM/D,EAAIiY,YAAY,CAACjY,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAI4D,SAAS/B,SAAS,UAExS0D,GAAkB,GCOtB,MAAA2S,GAAA,CACAC,MAAA,OACAC,OAAA,OACAC,IAAA,QAEAC,GAAA,CACAC,IAAA,MACAH,OAAA,MACAI,OAAA,OAGA,IAAAC,GAEe,QACf1Q,MAAA,aACAC,SAAA,CACAiQ,aACA,iBAAArU,SAAA8U,OAAA,yBAEAV,eACA,OACAW,WAAA,KAAA/U,SAAAgV,eAAA,cACAC,SAAAC,OAAA,KAAAlV,SAAAmV,iBAAA,KAAAC,QAAA,KAGAjB,eACA,oBACAa,EAAA,WACAK,EAAA,cACAP,EAAA,aACAlG,EAAA,eACA0G,EAAA,UACA,KAAAtV,SACAuV,EAAA,WAAA3G,EAAA,MAAAA,EACA1P,EAAA,CACAoW,SAAAhB,GAAAgB,GACAE,MAAAH,EACAN,WAAAC,GAAA,cACA,CAAAO,GAAAb,GAAA9F,IAEA6G,EACA,MAAAX,EAAA,CAAAW,UAAA,SAAAA,UAAA,UAEA,UAAAA,KAAAvW,EAAA6V,WAAA,cAAAE,QAAA,KAGA3Q,QAAA,CACAoR,eAAA3Q,GACA,MAAAsH,EAAA,KAAA7H,MAAA,eAEA,GAAA6H,EAAA,CACA,MAAAsJ,EAAAtJ,EAAAuJ,YACAC,EAAA7a,OAAA8a,WACA,IAAAC,EAAAF,EAEA,MAAA1Z,OACAkQ,EAAAnN,MAAAkP,UAAA,eAAA2H,aAEAA,GAAA,GACAA,EAAAJ,EAAA,IACAK,qBAAAnB,GAAAxI,SACA0J,EAAAF,GAEAhB,GAAAoB,sBAAA9Z,IAGA,MAAA4I,EAAA+P,SACAzI,EAAAnN,MAAAgX,WAAA,UAEA/Z,KAIA4I,GAAA,SAAAA,EAAA9E,QAAA,MAAA8E,EAAA+P,QACAkB,qBAAAnB,MAIArO,UACA,KAAAkP,eAAA,KAAA1V,WAEA6F,gBACAmQ,qBAAAnB,KAEAhQ,MAAA,CACA7E,SAAA,CACA8E,QAAAC,GACA,KAAA2Q,eAAA3Q,IAEAC,MAAA,EACAmR,WAAA,KCnGid,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBXha,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC6D,MAAM/D,EAAIga,eAAe,CAAC9Z,EAAG,MAAM,CAACG,YAAY,mBAAmBM,GAAG,CAAC,MAAQX,EAAIia,mBAAmB,CAAC/Z,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM4I,EAAQ,QAAmC,IAAM,MAAMhJ,EAAIU,GAAG,IAAIV,EAAI6C,IAAI7C,EAAIka,eAAiB,MAAM,OAAOha,EAAG,KAAK,CAACG,YAAY,oBAAoBL,EAAIgD,GAAIhD,EAAIma,cAAc,SAASjX,GAAM,OAAOhD,EAAG,KAAK,CAAC7B,IAAI6E,EAAKnF,OAAO,CAACmC,EAAG,MAAM,CAACA,EAAG,OAAO,CAACG,YAAY,QAAQ,CAACL,EAAIU,GAAGV,EAAI6C,GAAGK,EAAK6F,gBAAgB/I,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIoa,UAAUlX,EAAKW,SAAS,eAAc,MAE/iB0B,GAAkB,GCqBP,IACfwC,MAAA,cACAC,SAAA,CACAgS,gBACA,kBACA,KAAAE,cAAA,sCACA,KAAAG,OAAA,oCAGAnf,OACA,OACAkf,UAAA,CACAE,GAAA,KACAC,IAAA,MAEAJ,aAAA,GACAE,QAAA,EACAH,eAAA,EACAM,cAAA,GACAC,eAAA,GACAC,MAAA,KACAC,WAAA,OAGAzS,QAAA,CACA0S,cACA,KAAArS,MAAA,mBAEAsS,aACA,KAAAH,QACApQ,aAAA,KAAAoQ,OACA,KAAAA,MAAA,OAGAT,mBACA,KAAAC,eAAA,KAAAA,gBAGAzQ,gBACA,KAAA+Q,cAAA,GACArE,cAAA,KAAAwE,YACA,KAAAA,WAAA,MAEAlS,MAAA,CACA3E,UAAA,CACA4E,QAAAC,GACA,KAAA8R,eAAA,KAAAA,eAAAK,OAAAnS,IAGA,KAAAgS,YAAA,KAAAF,eAAA/e,OAAA,IACA,KAAAif,WAAAtF,YAAA,KACA,KAAAoF,eAAA/e,OAAA,IACA,KAAA8e,cAAA,KAAAA,cACAM,OAAA,KAAAL,eAAAM,OAAA,IACAjc,OAAA,GAEA,KAAA2b,eAAA,KAEA,MAIA,SAAAA,eAAA/e,SACA,KAAA8e,cAAA,KAAAA,cAAAM,OAAAnS,KAGAC,MAAA,EACAmR,WAAA,GAEAS,cAAA,CACA9R,QAAAC,GACA,IAAAA,EAAAjN,SACA,KAAA2e,QAAA,EAEA,KAAAF,aAAAxR,EACA,KAAA+R,MAAA/P,WAAA,KACA,KAAA0P,QAAA,EACA,KAAAG,cAAA,GACArE,cAAA,KAAAwE,YACA,KAAAA,WAAA,KACA,KAAAF,eAAA,IACA,OAGA7R,MAAA,EACAmR,WAAA,KC5Gid,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBXha,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,QAAQ,CAACsF,IAAI,WAAWpF,MAAM,CAAC,SAAW,GAAG,MAAQJ,EAAIgb,YAEhKzV,GAAkB,GCKP,IACfwC,MAAA,8BACAqC,UACA,KAAA6Q,eAEAxR,gBACA,KAAArB,MAAA,kBAAAA,MAAA,YAAAsB,SAEAxB,QAAA,CACA+S,cACA,MAAAC,EAAA,KAAA9S,MAAA,YACA8S,GAAA,KAAA3X,QACA,KAAAA,OAAA4X,iBAAA,KAAAC,SAAAF,MCnBid,MCO7c,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIa,M,QClBXnb,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACsF,IAAI,eAAezB,MAAM,CAAC,aAAc,CAAE,cAAe/D,EAAIkD,KAAKmY,gBAAkBrb,EAAIkD,KAAKmY,eAAeC,QAASxY,MAAO9C,EAAIkD,KAAKqY,cAAenb,MAAM,CAAC,GAAKJ,EAAIwb,aAAa7a,GAAG,CAAC,MAAQ,SAASuB,GAAQ,OAAOlC,EAAIyb,iBAAiBvZ,MAAW,CAAChC,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgByC,MAAO,CAAE4Y,OAAQ1b,EAAI0b,SAAW,CAACxb,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,MAAM,CAAC6D,MAAM/D,EAAI2b,gBAAgB,CAACzb,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACL,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIkD,KAAKE,OAAO2F,aAAe,OAAO7I,EAAG,MAAM,CAACF,EAAIU,GAAG,eAAeR,EAAG,MAAM,CAAC6D,MAAM/D,EAAI4b,gBAAgB,CAAC1b,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,YAAY,CAACE,MAAM,CAAC,WAAaJ,EAAIkD,KAAK2Y,WAAW,OAAS7b,EAAIkD,KAAK4Y,OAAO,eAAiB9b,EAAI+b,mBAAmB,KAAK7b,EAAG,MAAM,CAAC6D,MAAM/D,EAAIgc,mBAAmB,CAAC9b,EAAG,MAAM,CAACG,YAAY,sBAAsBH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAAGL,EAAIkD,KAAKE,OAAO6Y,UAAyGjc,EAAIS,KAAlGP,EAAG,MAAM,CAAC6D,MAAM/D,EAAIkD,KAAKE,OAAO8Y,YAAc,qBAAuB,yBAAkChc,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACL,EAAIU,GAAG,IAAIV,EAAI6C,GAAI,IAAE7C,EAAIkD,KAAKE,OAAO2F,aAAe,UAAW,aAAa7I,EAAG,QAAQ,CAAC4C,MAAO9C,EAAIkD,KAAKiZ,OAAQ/b,MAAM,CAAC,SAAW,WAEzuCmF,GAAkB,GCFlBxF,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAI6b,WAAY3b,EAAG,MAAM,CAACG,YAAY,eAAeyC,MAAO9C,EAAIoc,aAAc,CAACpc,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAI6b,YAAY,OAAO3b,EAAG,SAAS,CAACG,YAAY,eAAeyC,MAAO9C,EAAIoc,YAAahc,MAAM,CAAC,IAAM,aAErQmF,GAAkB,GCFlBxF,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMJ,EAAIqc,SAAS,IAAM,UAAU1b,GAAG,CAAC,MAAQX,EAAIsc,iBAEnI/W,GAAkB,G,wHCQf,MAAMgX,GAAkB,CAC7Bza,WAAW,EACXwB,WAAY,OACZvC,SAAS,GAGEyb,GAAqB,CAChCxW,MAAO,GACPK,SAAU,GACVC,QAAS,GACTC,gBAAiB,GACjBC,YAAa,GACbC,WAAW,EACXC,WAAW,GAGA+V,GAAoB,CAC/BX,OAAQ,GACR/S,YAAa,GACb/E,WAAY,aACZ4F,OAAQ,GACR8S,WAAY,IAGDC,GAAc,CACzBtb,SAAU,GACVqQ,MAAO,IAMIkL,GAAiB,CAC5B/G,OAAQ,CACN9D,eAAgB,GAChBI,gBAAiB,GACjBV,YAAa,IAEfqE,WAAY,CACV7C,WAAY0J,GACZzJ,WAAYyJ,GACZvb,YAAaub,KAOJE,GAAkB,CAC7BC,OAAQC,KACRC,KAAMD,KACNE,KAAMC,KACNC,OAAQD,KACRE,YAAaF,KACbA,UACAG,WACAC,UACAC,WACAC,KAAMT,KACNU,QAASV,MAWEW,GAAwB,GASxBC,GAAgB,CAC3BC,MAAO,QACPC,QAAS,WCrFI,QACf9V,MAAA,CACA+V,IAAA,CACAlW,KAAAmW,OACAnY,UAAA,GAEAgC,KAAA,CACAA,KAAAmW,OACAN,QAAA,YAGAviB,OACA,OACAmhB,SAAA,KAAAyB,IACAE,WAAAnB,GAAA,KAAAjV,OAAAiV,GAAAY,UAGAQ,UACA,KAAA5B,SAAA,KAAAyB,KAEA5V,QAAA,CACAoU,eACA,KAAAD,SAAA,KAAA2B,cC5Bkc,MCO9b,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QCRA,IACfjW,MAAA,yCACA4H,WAAA,CAAAuO,WACAlW,SAAA,CACAoU,cACA,IAAA+B,EAAA,GACAjF,EAAA,GAMA,OALA,KAAA6C,eAAA,MACAoC,EAAA,GACAjF,EAAA,IAGA,CACAkF,MAAAD,EAAA,KACAE,OAAAF,EAAA,KACAjF,WAAA,QAIAhe,OACA,UAEA+iB,YACA/V,QAAA,ICjCid,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QClBR,MAAMoW,GAAQ,CACnBC,YAAa,EACbC,aAAc,KACdC,MAAO,SAAUtK,EAAQuK,EAAaC,EAAcA,UAClD,MAAMC,EAAczK,EAAO0K,UACrBN,EAActe,KAAKse,YACzBte,KAAKse,YAAcK,EAEfA,EAAcL,EAAc,KAC9Bte,KAAKse,YAAc,EACfG,GAAaA,IAEjBpU,aAAarK,KAAKue,eAGlBve,KAAKue,aAAe7T,WAAW,KAC7B1K,KAAKse,YAAc,EACfI,GAAaA,KAChB,OCsBM,QACf5W,MAAA,oEACA4H,WAAA,CAAAmP,cACA9W,SAAA,CACA+W,QACA,YAAA7b,KAAA6b,OAEArD,SACA,IAAAA,EAAA,GAMA,OAJAA,EADA,iBAAA3V,OAAA,KAAA7C,KAAAE,OAAA4b,gBACA,oBAEA,OAEAtD,GAEAC,iBACA,sCAAAoD,OAAA,uBAAAA,OAAA,oBAAAA,MACA,aACA,iBAGAnD,iBACA,iCAAAmD,OAAA,iBAAAA,MAAA,8BAEA/C,oBACA,oCAAA+C,MAAA,8BAGAE,eACA,YAAAvd,gBAAA,KAAAwB,KAAAE,OAAAhE,IAEA2c,iBAAA,IAAAmD,EACA,eAAAA,EAAA,KAAAhc,KAAAic,oBAAA,IAAAD,OAAA,EAAAA,EAAAd,QAAA,IAGAljB,OACA,OACAsgB,YAAA,aAAApc,KAGAgL,UACA,KAAAgV,YAAA,KAAAhgB,KAEA8I,QAAA,CACA,uBAAAmX,GACAf,GAAAG,MAAAY,EAAA,KACAA,EAAAld,kBAEA,KAAAoG,MAAA,uBAAA0W,aAAA,QAAA7f,OAIAggB,YAAAzW,GACAA,GAAA,KAAApF,QACA,KAAAA,OAAA+b,iBAAA3W,EAAA,QAAAA,KAIAF,MAAA,CACArJ,GAAA,CACAsJ,QAAAC,GACA,KAAAyW,YAAAzW,IAEAC,MAAA,KCzGid,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX7I,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,MAAM,CAACG,YAAY,aAAaM,GAAG,CAAC,MAAQX,EAAIwB,eAAetB,EAAG,KAAK,CAACF,EAAIU,GAAG,SAASR,EAAG,QAAQ,CAACG,YAAY,SAAS,CAACL,EAAI8I,GAAG,GAAG5I,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIuf,aAAarf,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIuO,MAAQ,MAAMrO,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIwf,wBAAwBtf,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAIyf,0BAA0Bvf,EAAG,MAAMA,EAAG,KAAK,CAACF,EAAIU,GAAG,SAASR,EAAG,QAAQ,CAACG,YAAY,SAAS,CAACL,EAAI8I,GAAG,GAAG5I,EAAG,QAAQF,EAAIgD,GAAIhD,EAAI0f,QAAQ,SAASxc,EAAK7E,GAAK,OAAO6B,EAAG,KAAK,CAAC7B,IAAIA,GAAK,CAAC6B,EAAG,KAAK,CAACF,EAAIU,GAAG,UAAUR,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGK,EAAKyc,YAAY,IAAI3f,EAAI6C,GAAGK,EAAK0c,gBAAgB1f,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGK,EAAK2c,iBAAiB3f,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGK,EAAKuc,oBAAoBvf,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGK,EAAK4c,wBAAwB5f,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGK,EAAK6c,qBAAqB7f,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGK,EAAK8c,0BAAyB,KAAK9f,EAAG,MAAMA,EAAG,KAAK,CAACF,EAAIU,GAAG,UAAUR,EAAG,QAAQ,CAACG,YAAY,SAAS,CAACL,EAAI8I,GAAG,GAAG5I,EAAG,QAAQF,EAAIgD,GAAIhD,EAAIigB,UAAU,UAAS,WACxkCN,EAAU,YACVC,EAAW,oBACXJ,EAAmB,qBACnBU,EAAoB,oBACpBC,EAAmB,KACnBvY,EAAI,KACJtK,EAAI,UACJ2e,EAAS,iBACTmE,GACA/hB,GAAK,OAAO6B,EAAG,KAAK,CAAC7B,IAAIA,GAAK,CAAC6B,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGvF,MAAS4C,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAG+E,GAAM,MAAM5H,EAAI6C,GAAGoZ,EAAY,MAAQ,UAAU/b,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAG8c,GAAY,IAAI3f,EAAI6C,GAAG+c,MAAgB1f,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGsd,MAAwBjgB,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGqd,MAAyBhgB,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAG2c,MAAwBtf,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAI6C,GAAGud,WAAyB,UAE3Y7a,GAAkB,CAAC,WAAY,IAAIvF,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACG,YAAY,eAAe,CAACH,EAAG,KAAK,CAACF,EAAIU,GAAG,UAAUR,EAAG,KAAK,CAACF,EAAIU,GAAG,QAAQR,EAAG,KAAK,CAACF,EAAIU,GAAG,cAAcR,EAAG,KAAK,CAACF,EAAIU,GAAG,mBACtN,WAAY,IAAIV,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACG,YAAY,eAAe,CAACH,EAAG,KAAK,CAACF,EAAIU,GAAG,QAAQR,EAAG,KAAK,CAACF,EAAIU,GAAG,SAASR,EAAG,KAAK,CAACF,EAAIU,GAAG,gBAAgBR,EAAG,KAAK,CAACF,EAAIU,GAAG,cAAcR,EAAG,KAAK,CAACF,EAAIU,GAAG,aAAaR,EAAG,KAAK,CAACF,EAAIU,GAAG,aAAaR,EAAG,KAAK,CAACF,EAAIU,GAAG,cAC1R,WAAY,IAAIV,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACG,YAAY,eAAe,CAACH,EAAG,KAAK,CAACF,EAAIU,GAAG,QAAQR,EAAG,KAAK,CAACF,EAAIU,GAAG,QAAQR,EAAG,KAAK,CAACF,EAAIU,GAAG,WAAWR,EAAG,KAAK,CAACF,EAAIU,GAAG,aAAaR,EAAG,KAAK,CAACF,EAAIU,GAAG,aAAaR,EAAG,KAAK,CAACF,EAAIU,GAAG,cAAcR,EAAG,KAAK,CAACF,EAAIU,GAAG,eCmFvQ,IACfqH,MAAA,iBACAC,SAAA,CACAuG,OACA,OAAAH,EAAA,KAAAC,aAGAnT,OACA,eAAAqkB,EAAA,OAAAG,EAAA,aAAArR,EAAA,SAAA4R,EAAA,uBAAAT,EAAA,gBAAAC,GAAA,KAAAra,aACA,OACAma,WACAG,SACArR,YACA4R,WACAT,sBACAC,oBAGAvX,QAAA,CACA1G,cACA,KAAA+G,MAAA,iBAGAE,MAAA,CACArD,aAAA,CACAsD,QAAAC,GACA,eAAA4W,EAAA,OAAAG,EAAA,aAAArR,EAAA,SAAA4R,EAAA,uBAAAT,EAAA,gBAAAC,GAAA9W,EACA,KAAA4W,WACA,KAAAG,SACA,KAAArR,YACA,KAAA4R,WACA,KAAAT,sBACA,KAAAC,sBCjIid,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX1f,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,GAAG,YAAa,EAAM,QAAUJ,EAAI+P,QAAQ,eAAe/P,EAAIqgB,iBAAiB,KAAOrgB,EAAIme,KAAK,UAAYne,EAAIsgB,UAAU,eAAe,qBAAqB3f,GAAG,CAAC,iBAAiB,SAASuB,GAAQlC,EAAI+P,QAAQ7N,KAAU,CAAChC,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,OAAO,CAACG,YAAY,kBAAkBM,GAAG,CAAC,MAAQX,EAAIqgB,oBAAoBrgB,EAAIU,GAAG,SAAWV,EAAImE,KAAqDnE,EAAIS,KAAnDP,EAAG,OAAO,CAACF,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAIugB,OAAO,SAAkBrgB,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,OAAO,CAAC6D,MAAM/D,EAAIwgB,YAAY,OAAO7f,GAAG,CAAC,MAAQ,SAASuB,GAAQ,OAAOlC,EAAIygB,YAAY,UAAU,CAACzgB,EAAIU,GAAG,QAAQV,EAAI6C,GAAG7C,EAAIugB,OAAO,QAASvgB,EAAI0gB,YAAaxgB,EAAG,OAAO,CAAC6D,MAAM/D,EAAIwgB,YAAY,UAAU7f,GAAG,CAAC,MAAQ,SAASuB,GAAQ,OAAOlC,EAAIygB,YAAY,aAAa,CAACzgB,EAAIU,GAAG,QAAQV,EAAI6C,GAAG7C,EAAI0gB,aAAa,QAAQ1gB,EAAIS,OAAOP,EAAG,MAAM,CAACG,YAAY,kBAAkBL,EAAIgD,GAAIhD,EAAI2gB,iBAAiB,SAASzd,GAAM,OAAOhD,EAAG,MAAM,CAAC7B,IAAI6E,EAAK0d,cAAgB1d,EAAK2d,aAAaxgB,YAAY,eAAe,CAACH,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM8C,EAAK4Y,OAAO,IAAM,YAAa5Y,EAAK4d,gBAAiB5gB,EAAG,MAAM,CAACG,YAAY,iBAAiBD,MAAM,CAAC,IAAM8C,EAAK4d,gBAAgB,IAAM,MAAM9gB,EAAIS,OAAOP,EAAG,MAAM,CAACG,YAAY,OAAOD,MAAM,CAAC,MAAQ8C,EAAK6F,cAAc,CAAC7I,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIU,GAAG,IAAIV,EAAI6C,GAAGK,EAAK6F,aAAa,KAAM7F,EAAK6d,cAAe7gB,EAAG,OAAO,CAACF,EAAIU,GAAG,aAAaV,EAAIS,QAAST,EAAI6B,SAAW7B,EAAI6B,QAAQmf,cAAgB9d,EAAK8d,YAAe9d,EAAK6d,cAA2E/gB,EAAIS,KAAhEP,EAAG,OAAO,CAACG,YAAY,gBAAgB,CAACL,EAAIU,GAAG,iBAA4BwC,EAAK+Y,UAAiQjc,EAAIS,KAA1PP,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM8C,EAAK+d,SAAS,IAAM,cAAc/gB,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM8C,EAAKge,SAAS,IAAM,mBAA2B,GAAIlhB,EAAIyC,SAASG,UAAY,EAAG1C,EAAG,MAAM,CAACG,YAAY,qBAAqB,CAACH,EAAG,gBAAgB,CAACE,MAAM,CAAC,WAAa,GAAG,OAAQ,EAAK,OAAS,oBAAoB,MAAQJ,EAAIyC,SAAS0e,WAAW,eAAenhB,EAAIyC,SAASC,YAAY,YAAY1C,EAAIohB,gBAAgB,cAAc,GAAGzgB,GAAG,CAAC,iBAAiBX,EAAIqhB,iBAAiB,GAAGrhB,EAAIS,UAE1vE8E,GAAkB,G,gJC+EP,I,UAAA,CACfwC,MAAA,uCACAC,SAAA,CACAwY,cACA,gBAAAc,GACA,kCAAAA,QAAA,kCAGAX,kBACA,YAAAY,WAAAtiB,IAAAiE,IAAA,IAAAse,EACA,oBACAZ,EAAA,WACAI,EAAA,aACAH,EAAA,YACAY,EAAA,YACAC,EAAA,YACAxF,EAAA,YACAyF,EAAA,WACAC,EAAA,UACA3F,EAAA,gBACA+C,GACA9b,EAEA7E,EAAAuiB,EAAAC,EACA/E,EAAAe,GAAA+E,IAAA/E,GAAAY,QACAwD,EAAA/E,EAAA2F,KAAAC,KACAZ,EAAAO,EAAAM,KAAAC,KACAjB,EAAAU,GAAAC,GAAAC,GAAAzF,EAEA,IAAA4E,EAAA,GAEA,OAAA7E,EACA,MAGA+E,KAAA,QAAAQ,EAAA,KAAAS,kBAAA,IAAAT,OAAA,EAAAA,EAAAR,YACAF,EAAAoB,KAEAlD,IAAA9C,IACA4E,EAAAqB,MAIAxmB,OAAAymB,OAAAlf,EAAA,CACA7E,MACAyd,SACAmF,WACAC,WACAH,gBACAD,yBAKA5lB,OACA,OACA6U,SAAA,EACAkS,WAAA,KACAX,IAAA,MACAnD,KAAA,IACAuC,YAAA,EACAU,gBAAA1D,GACAjb,SAAA,CACA4f,SAAA3E,GACAhb,YAAA,EACAE,UAAA,EACAue,WAAA,GAEAmB,gBAAA,KAAApd,QACAqc,WAAA,GACAjB,UAAAnc,EAAA,YACAA,SAGAoe,cAAA,IAAAC,EACA,KAAAP,WAAA,QAAAO,EAAA,KAAAjf,cAAA,IAAAif,OAAA,EAAAA,EAAAC,iBAEArY,UACA,KAAA2F,SAAA,GAEA7H,QAAA,CACAwa,eACA,IAAAC,EAAA,KAAAL,gBAAAxH,SAEA,gBAAAwG,MACAqB,IAAAC,OAAAxf,MAAA8Y,cAGA,KAAA2G,MAAAF,IAEAE,MAAAF,GACA,IAAAxB,EAAA,GAEA,YAAAze,EAAA,SAAA2f,GAAA,KAAA5f,SAEA,GAAAkgB,aAAApT,MAAA,CACA4R,EAAAwB,EAAAjnB,OACA,MAAAonB,EAAArY,KAAAiL,KAAAhT,EAAA,GAAA2f,EAAA,GACAU,EAAAD,EAAAT,EAAAlB,IAAA2B,EAAAT,EACAM,IAAA7jB,MAAAgkB,EAAAC,GAGArgB,EAAA,OAAAigB,EAAAjnB,SACAgH,GAAA,EACA,KAAAD,SAAAC,cAEA,KAAAmgB,MAAAF,IAIA,IAAA/f,EAAA6H,KAAAuY,KAAA7B,EAAAkB,GAEA,KAAA5f,SAAA,IACA,KAAAA,SACAG,YACAF,cACAye,cAGA,KAAAI,WAAAoB,GAEAM,iBACA,MAAAvnB,EAAA,KAAA4mB,gBAAAM,OAAA1f,MAAAgZ,aAAAxgB,OAEA,IAAAA,IACA,KAAA4lB,IAAA,OAGA,KAAAZ,YAAAhlB,GAEA2lB,aAAA6B,GACA,KAAAzgB,SAAAC,YAAAwgB,EACA,KAAAR,gBAEAjC,YAAAa,GACA,KAAAA,MACA,KAAA7e,SAAAC,YAAA,EACA,KAAAggB,gBAEArC,mBACA,KAAA9X,MAAA,wBAGAE,MAAA,CACAvD,QAAA,CACAwD,QAAAC,GACA,KAAA2Z,gBAAA3Z,EACA,KAAAsa,iBACA,KAAAP,gBAEA9Z,MAAA,EACAmR,WAAA,MCxOid,MCO7c,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIa,M,QClBXha,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAQF,EAAImE,KAAMjE,EAAG,aAAa,CAACE,MAAM,CAAC,eAAe,mBAAmB,UAAY,MAAM,MAAQ,GAAG,QAAU,SAAS2F,MAAM,CAAChI,MAAOiC,EAAI+P,QAAS9J,SAAS,SAAUC,GAAMlG,EAAI+P,QAAQ7J,GAAKE,WAAW,YAAY,CAAClG,EAAG,MAAM,CAACG,YAAY,cAAcL,EAAIgD,GAAIhD,EAAImjB,aAAa,SAASjgB,EAAKC,GAAO,OAAOjD,EAAG,MAAM,CAAC7B,IAAI8E,EAAMY,MAAMb,EAAKyG,UAAUhJ,GAAG,CAAC,MAAQuC,EAAKkgB,WAAW,CAACpjB,EAAIU,GAAG,IAAIV,EAAI6C,GAAGK,EAAKmgB,MAAM,UAAS,GAAGnjB,EAAG,MAAM,CAACG,YAAY,8BAA8BD,MAAM,CAAC,KAAO,aAAa2J,KAAK,aAAa,CAAC7J,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAW,KAAO,YAAYF,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAG,SAAS,KAAKR,EAAG,MAAM,CAACA,EAAG,MAAM,CAACG,YAAY,8BAA8BD,MAAM,CAAC,KAAO,aAAaO,GAAG,CAAC,MAAQX,EAAIsjB,MAAMvZ,KAAK,aAAa,CAAC7J,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAW,KAAO,YAAYF,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAG,SAAS,GAAGR,EAAG,YAAY,CAACE,MAAM,CAAC,QAAUJ,EAAI+P,QAAQ,UAAY,MAAM,eAAe/P,EAAIgK,OAAO,kBAAiB,EAAK,YAAa,EAAM,eAAe,kBAAkBrJ,GAAG,CAAC,iBAAiB,SAASuB,GAAQlC,EAAI+P,QAAQ7N,KAAU,CAAChC,EAAG,MAAM,CAACG,YAAY,cAAcL,EAAIgD,GAAIhD,EAAImjB,aAAa,SAASjgB,EAAKC,GAAO,OAAOjD,EAAG,MAAM,CAAC7B,IAAI8E,EAAMY,MAAMb,EAAKyG,UAAUhJ,GAAG,CAAC,MAAQuC,EAAKkgB,WAAW,CAACpjB,EAAIU,GAAG,IAAIV,EAAI6C,GAAGK,EAAKmgB,MAAM,UAAS,MAAM,IAEt0C9d,GAAkB,GC+BP,IACfrK,OACA,OACAiJ,OACA4L,SAAA,EACAoT,YAAA,CACA,CACAE,KAAA,OACA1Z,UAAA,oBACAyZ,SAAA,KAAA9hB,MAEA,CACA+hB,KAAA,KACA1Z,UAAA,uBACAyZ,SAAA,KAAApZ,WAKA9B,QAAA,CACAob,OACA,KAAAvT,SAAA,GAEA/F,SACA,KAAA+F,SAAA,GAEAzO,OACA,KAAAyO,SAAA,EACA,KAAAxH,MAAA,WC7Did,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OAIa,M,QCnBXxI,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC6D,MAAM/D,EAAIujB,WAAWC,WAAW7iB,GAAG,CAAC,MAAQX,EAAIiF,eAAe,CAAC/E,EAAG,WAAW,CAACE,MAAM,CAAC,KAAOJ,EAAIujB,WAAWE,QAAQ,KAAOzjB,EAAIujB,WAAWG,WAAWxjB,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAG,IAAIV,EAAI6C,GAAiB,gBAAd7C,EAAIgF,MAA0B,QAAU,SAAS,QAAQ,IAEzUO,GAAkB,GCQP,IACfwC,MAAA,uBACAC,SAAA,CACAub,aACA,IAAAC,EAAA,0BACAC,EAAA,SACAC,EAAA,UAcA,MAZA,gBAAAhf,WAAAiO,QACA6Q,EAAA,gCACAC,EAAA,oBACAC,EAAA,UACA,qBAAA1e,OACAwe,EAAA,SACAC,EAAA,WAEAD,EAAA,0BACAC,EAAA,cACAC,EAAA,UAEA,CACAF,WAAA,UAAAA,EACAC,UACAC,aAIAxoB,OACA,UAEAgN,QAAA,CACAjD,eACA,KAAAsD,MAAA,mBC1Cid,MCO7c,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QClBXxI,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC6D,MAAM/D,EAAI2jB,WAAWC,WAAWjjB,GAAG,CAAC,MAAQX,EAAI+E,eAAe,CAAC7E,EAAG,MAAM,CAACG,YAAY,YAAY,CAAgC,WAA9BL,EAAI0E,WAAWkO,YAA4B5S,EAAI4E,aAA6E5E,EAAIS,KAAnEP,EAAG,WAAW,CAACE,MAAM,CAAC,OAASJ,EAAI8E,OAAO,MAAQ9E,EAAI2E,SAAkBzE,EAAG,WAAW,CAACE,MAAM,CAAC,KAAOJ,EAAI2jB,WAAWF,QAAQ,KAAOzjB,EAAI2jB,WAAWD,YAAY,GAAGxjB,EAAG,MAAM,CAACG,YAAY,SAAS,CAACL,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAI2jB,WAAWE,mBAExcte,GAAkB,GCFlBxF,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,MAAsB,gBAAdF,EAAI2E,MAAyBzE,EAAG,MAAM,CAACG,YAAY,OAAO,CAACH,EAAG,MAAM,CAACG,YAAY,cAAcyC,MAAO,CAAEkP,UAAY,eAAchS,EAAI8jB,kBAAoB9jB,EAAIS,MAE9N8E,GAAkB,GCQP,IACfwC,MAAA,mBACA7M,OACA,OACAoY,iBAAA,KACAwQ,SAAA,IAGA1Z,UACA,KAAA2Z,eAEAta,gBACA,KAAAqa,SAAA,EAEA,KAAAE,eAEA9b,QAAA,CACA8b,cACA,KAAA1Q,kBAAA6C,cAAA,KAAA7C,kBACA,KAAAA,iBAAA,KACA,KAAAwQ,SAAA,GAGAC,YAAApf,EAAA,KAAAA,OAEA,gBAAAA,GAAA,KAAA2O,iBACA,KAAA0Q,cAIA,KAAA1Q,iBAAA+B,YAAA,UACA,QAAAvQ,OACA,IACA,MAAAwQ,QAAA,KAAAxQ,OAAAsQ,gBAGA,KAAA0O,SAAAxO,EACA,MAAAhO,GACA,KAAA0c,gBAGA,OAGAvb,MAAA,CACA9D,MAAA,CACA+D,QAAAC,GACA,KAAAob,YAAApb,OCzDid,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCFA,IACfZ,MAAA,4DACA4H,WAAA,CACAsU,aAEAjc,SAAA,CACA2b,aACA,IAAAC,EAAA,uBACAC,EAAA,OACAJ,EAAA,WACAC,EAAA,UA8CA,MA5CA,gBAAAhf,WAAAkO,YACAgR,EAAA,6BACAH,EAAA,mBAEA,mBAAA9e,OAAA,KAAAC,eACAgf,EAAA,uBACAH,EAAA,kBACAC,EAAA,UAGA,qBAAA/e,OAAA,KAAAC,eACAif,EAAA,KACAD,EAAA,WAIA,mBAAAjf,OACA,KAAAC,eACA,KAAAC,aAEAgf,EAAA,OACAD,EAAA,UACAH,EAAA,WAIA,mBAAA9e,OACA,KAAAC,cACA,KAAAC,aAEAgf,EAAA,OACAD,EAAA,YACAH,EAAA,aAGA,qBAAA9e,OAAA,KAAAC,eACAif,EAAA,OACAD,EAAA,WACAH,EAAA,aAIAG,EAAA,UAAAA,EAEA,CACAC,cACAD,aACAH,UACAC,aAIAxoB,OACA,UAEAgN,QAAA,CACAnD,eACA,KAAAwD,MAAA,mBCtFid,MCO7c,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QClBXxI,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC6D,MAAM,CAAC,iBAAkB/D,EAAIuB,YAAc,GAAK,uBAAuB,CAAEvB,EAAI0B,cAAexB,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACL,EAAIU,GAAG,YAAYR,EAAG,OAAO,CAACG,YAAY,WAAWM,GAAG,CAAC,MAAQX,EAAIkkB,wBAAwB,CAAClkB,EAAIU,GAAG,YAAYV,EAAIS,KAAMT,EAAI8B,UAAW5B,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACL,EAAIU,GAAG,mBAAmBV,EAAIS,KAA2B,IAArBT,EAAIgC,aAAoB9B,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACH,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAIU,GAAG,WAAW,GAAGV,EAAIS,KAA2B,IAArBT,EAAIgC,cAA2C,IAArBhC,EAAIgC,aAAoB9B,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACH,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,GAAG,MAAO,IAAQ,CAACF,EAAG,MAAM,CAACG,YAAY,yBAAyB,CAACH,EAAG,OAAO,CAACF,EAAIU,GAAG,UAAUV,EAAIU,GAAG,IAAIV,EAAI6C,GAAwB,IAArB7C,EAAIgC,aAAqB,MAAQ,OAAO,UAAU,GAAGhC,EAAIS,KAAMT,EAAI2B,SAAUzB,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACL,EAAIU,GAAG,WAAWV,EAAIS,KAAMT,EAAI+B,oBAAqB7B,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACL,EAAIU,GAAG,WAAWV,EAAIS,KAAMT,EAAI6B,QAAS3B,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACH,EAAG,OAAO,CAACG,YAAY,+BAA+B,CAACL,EAAIU,GAAGV,EAAI6C,GAAG7C,EAAI6B,QAAQkH,gBAAgB/I,EAAIU,GAAG,YAAYV,EAAIS,QAEzsC8E,GAAkB,GCFlBxF,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAAEL,EAAImkB,OAAQjkB,EAAG,IAAI,CAAC6D,MAAM,CAAE,gBAAgB,EAAM,cAAe/D,EAAIokB,OAAQ,eAAgBpkB,EAAIokB,UAAWpkB,EAAIS,KAAKP,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACL,EAAIqkB,GAAG,YAAY,GAAIrkB,EAAIuO,KAAMrO,EAAG,MAAM,CAACF,EAAIU,GAAG,IAAIV,EAAI6C,GAAG7C,EAAI0a,OAAO,OAAO1a,EAAIS,QAEzV8E,GAAkB,GCcP,IACfwC,MAAA,CACAoc,OAAA,CACAvc,KAAA0c,QACA7G,SAAA,GAEAlP,KAAA,CACA3G,KAAA0c,QACA7G,SAAA,IAGAviB,OACA,OACAwf,MAAA,WACAxQ,WAAA,EACAC,eAAA,OAGAnC,SAAA,CACAoc,SACA,OAAA3W,SAAA,KAAAiN,MAAA5b,OAAA,YAGAoJ,QAAA,CACAqC,aAAAhO,GACA,MAAAiO,EAAAuT,OAAAtT,KAAAC,MAAAnO,EAAA,OAAAgoB,SAAA,OACApnB,EAAA4gB,OAAAtT,KAAAC,MAAAnO,EAAA,QAAAgoB,SAAA,OACAznB,EAAAihB,OAAAtT,KAAAC,MAAAnO,EAAA,KAAAgoB,SAAA,OACA,OAAA/Z,EAAA,IAAArN,EAAA,IAAAL,GAEAuN,2BACA,KAAAH,aACA,KAAAC,eAAAQ,WAAA,KACAL,aAAA,KAAAH,gBACA,KAAAA,eAAA,KACA,MAAAS,EAAA,KAAAL,aAAA,KAAAL,YACA,KAAAwQ,MAAA9P,EACA,KAAAP,4BACA,OAGA4T,UACA,KAAA5T,4BAEAZ,gBACAa,aAAA,KAAAH,gBACA,KAAAA,eAAA,OC9Did,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QC+CA,IACfpC,MAAA,sGACA4H,WAAA,CACA6U,UAEAtpB,OACA,UAEAgN,QAAA,CACAgc,wBACA,KAAA3b,MAAA,sBC5Eid,MCQ7c,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCbR,MAAMkc,GAAS,CACpBC,UAAW,4BACXC,WAAY,8BACZC,UAAW,0BAGAC,GAAU,CACrBC,MAAO,2CACPC,SAAU,2BACVC,aAAc,oCCDHC,GAAO,MASPC,GAAmB,CAC9B,MAAO,CACL,CACEC,SAAU,CAAC,EAAG,EAAG,EAAG,GACpBC,WAAY,IAGhB,MAAO,CACL,CACED,SAAU,CAAC,EAAG,EAAG,IAAM,GACvBC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,EAAG,IAAM,MAC1BC,WAAY,IAGhB,MAAO,CACL,CACED,SAAU,CAAC,EAAG,EAAG,IAAM,GACvBC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,EAAG,IAAM,MAC1BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,KAAO,IAAM,MAC9BC,WAAY,IAGhB,MAAO,CACL,CACED,SAAU,CAAC,EAAG,EAAG,IAAM,GACvBC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,EAAG,IAAM,MAC1BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,KAAO,IAAM,MAC9BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,KAAO,IAAM,MAC9BC,WAAY,IAGhB,MAAO,CACL,CACED,SAAU,CAAC,KAAO,EAAG,IAAM,KAC3BC,WAAY,GAEd,CACED,SAAU,CAAC,EAAG,IAAM,IAAM,KAC1BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,GAEd,CACED,SAAU,CAAC,GAAK,IAAM,IAAM,KAC5BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,IAGhB,MAAO,CACL,CACED,SAAU,CAAC,EAAG,EAAG,IAAM,KACvBC,WAAY,GAEd,CACED,SAAU,CAAC,EAAG,IAAM,IAAM,KAC1BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,GAEd,CACED,SAAU,CAAC,GAAK,IAAM,IAAM,KAC5BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,GAAK,IAAM,KAC5BC,WAAY,IAGhB,MAAO,CACL,CACED,SAAU,CAAC,EAAG,EAAG,IAAM,KACvBC,WAAY,GAEd,CACED,SAAU,CAAC,EAAG,IAAM,IAAM,KAC1BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,GAEd,CACED,SAAU,CAAC,GAAK,IAAM,IAAM,KAC5BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,GAAK,IAAM,KAC5BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,IAGhB,MAAO,CACL,CACED,SAAU,CAAC,EAAG,EAAG,IAAM,KACvBC,WAAY,GAEd,CACED,SAAU,CAAC,EAAG,IAAM,IAAM,KAC1BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,GAEd,CACED,SAAU,CAAC,GAAK,IAAM,IAAM,KAC5BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,GAAK,IAAM,KAC5BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,IAAM,IAAM,KAC7BC,WAAY,GAEd,CACED,SAAU,CAAC,IAAM,EAAG,IAAM,KAC1BC,WAAY,KAMLC,GAAWA,KAIf,CACL3pB,OAAQ,EACR4pB,KAAM,CACJC,EAAGL,GAAiB,OACpBM,EAAGN,GAAiB,OACpBO,EAAGP,GAAiB,OACpBQ,EAAGR,GAAiB,OACpBS,EAAGT,GAAiB,OACpBU,EAAGV,GAAiB,OACpBW,EAAGX,GAAiB,OACpBY,EAAGZ,GAAiB,OACpBa,EAAGb,GAAiB,QAEtBc,KAAM,CACJT,EAAG,MACHC,EAAG,MACHC,EAAG,MACHC,EAAG,MACHC,EAAG,MACHC,EAAG,MACHC,EAAG,MACHC,EAAG,MACHC,EAAG,SClNIE,GAA0BA,CAACC,EAAiBC,EAAWC,EAAYjH,KAC9E,IAAI5D,EAAgB,CAAE8K,KAAM,MAAO9N,IAAK,MAAO6F,MAAO,MAAOC,OAAQ,OAErE,MAAMiI,EAAaJ,EAAgBjnB,IAAI,CAACiE,EAAMC,KAAU,IAAAojB,EAAAC,EACtD,IAAI,SAAErB,EAAQ,YAAEsB,EAAc,IAAOtH,EAAahc,IAAU,GAC5D,MAAOujB,EAAGlY,EAAGmY,EAAGnc,GAAK2a,EAErB,IAAIyB,EAAUnc,KAAKoc,MAAMV,EAAYO,GACjCI,EAAUrc,KAAKoc,MAAMT,EAAa5X,GAClCuY,EAActc,KAAKoc,MAAMV,EAAYQ,GACrCK,EAAevc,KAAKgL,IAAIhL,KAAKoc,MAAME,EAAc9B,IAAOmB,GAwB7B,IAAAa,GAtBrB,IAANN,GAAiB,IAANnc,IACbwc,EAAevc,KAAKoc,MAAMT,EAAa5b,IAGpCwE,EAAOG,SAASsX,KACnBA,EAAc,IAGhBlL,EAAgB5f,OAAOymB,OACrB,CACEiE,KAASO,EAAF,KACPrO,IAAQuO,EAAF,KACN1I,MAAU2I,EAAF,KACR1I,OAAW2I,EAAF,MAEXP,GAGa,QAAfF,EAAIE,SAAW,IAAAF,GAAXA,EAAaQ,cACfA,EAActc,KAAKgL,IAAIgR,EAAYM,YAAaZ,IAGnC,QAAfK,EAAIC,SAAW,IAAAD,GAAXA,EAAaQ,gBACfA,EAAevc,KAAKgL,IAAe,QAAZwR,EAACR,SAAW,IAAAQ,OAAA,EAAXA,EAAaD,aAAcZ,IAGrD,MAAMc,EAAoB,CACxB9I,MAAO2I,EACP1I,OAAQ2I,GAGV,MAAO,IAAK9jB,EAAMqY,gBAAe4D,aAAc+H,KAGjD,OAAOZ,GASIa,GAA6BA,CAACC,EAAgBC,EAAKC,KAC9D,IAAInkB,GAAS,EACTokB,EAAUH,EAAe1rB,OAE7B,IAAK,IAAIF,EAAI,EAAGA,EAAI+rB,EAAS/rB,IAAK,CAChC,MAAM0H,EAAOkkB,EAAe5rB,IACtB,UAAEygB,EAAS,cAAE2E,GAAkB1d,EAAKE,OAEpCokB,EAAY5G,IAAkByG,EAE9BI,EAAkC,IAARH,EAC1B9a,EAAQgb,GAAaC,IAA4BxL,EAEnDzP,IACFrJ,EAAQ3H,GAIZ,OAAO2H,GAMIukB,GAAgBA,CAACC,EAAY,GAAIC,KAC5C,MAAM,aAAEC,EAAY,YAAErO,GAAgBsO,SAASC,eAAeJ,IAAcG,SAASE,KAE/E5B,EAAa3b,KAAKC,MAAM8O,EAAcoO,GACtCzB,EAAY1b,KAAKC,MAAMmd,EAAeD,GACtCK,EAAgB,CAAE7B,WAAY,EAAGD,UAAW,GAYlD,OATI0B,EAAezB,GACjB6B,EAAc7B,WAAkC,IAArBwB,EAAyBC,EAAezB,EACnE6B,EAAc9B,UAAY3M,IAG1ByO,EAAc7B,WAAayB,EAC3BI,EAAc9B,UAAiC,IAArByB,EAAyBpO,EAAc2M,GAG5D8B,GAIIC,GAAsB5B,IACjC,MAAM6B,EAAY7B,EAAW5qB,OACvB0sB,EAAa,CACjBvmB,QAAS,GACTwmB,UAAW,GACXC,cAAe,GACfC,iBAAkB,GAClBC,YAAa,GACbC,YAAa,GACbC,eAAgB,IAGlB,IACIzG,EADA0G,EAAoB,GAGxB,IAAK,IAAIntB,EAAI,EAAGA,EAAI2sB,EAAW3sB,IAAK,CAClC,MAAM0H,EAAOojB,EAAW9qB,IAClB,QAAEotB,EAAO,UAAE3M,EAAS,kBAAE4M,EAAiB,gBAAE7J,EAAe,YAAE9C,EAAW,YAAEuF,GAAgBve,EAAKE,OAE9FwlB,EACF3G,EAAa/e,EAMX+Y,EACFmM,EAAW,WAAWpsB,KAAKkH,GAKzB2lB,EACFT,EAAW,aAAapsB,KAAKkH,GAK3B8b,EACFoJ,EAAW,iBAAiBpsB,KAAKkH,GAI9BgZ,GAAgBuF,EAKhBvF,IAAeuF,GAKhBvF,GAAgBuF,EAKhBvF,GAAeuF,GACjB2G,EAAW,kBAAkBpsB,KAAKkH,GALlCklB,EAAW,eAAepsB,KAAKkH,GAL/BklB,EAAW,eAAepsB,KAAKkH,GAL/BklB,EAAW,oBAAoBpsB,KAAKkH,GAoBxC,IAAK,IAAI7E,KAAO+pB,EAAY,CAC1B,IAAIU,EAAYV,EAAW/pB,IAEtByqB,EAAUptB,OAAS,GAAa,qBAAR2C,GAAuC,gBAARA,IAC1DyqB,EAAUC,KAAK,CAACC,EAAGC,IACVA,EAAE7lB,OAAO8lB,YAAcF,EAAE5lB,OAAO8lB,aAI3CP,EAAoBA,EAAkB7N,OAAOgO,GAG/C,OAAOK,GAAcR,EAAmB1G,IAG7BkH,GAAgBA,CAACC,EAAiBnH,IAEd,IAA3BmH,EAAgB1tB,QAClBumB,GAAcmH,EAAgBptB,KAAKimB,GAE5BmH,IAITnH,GAAcmH,EAAgBxsB,OAAO,EAAG,EAAGqlB,GAGpCmH,GAAmB,IClMb,MAAMC,GACnBC,cACErpB,KAAKspB,MAAQ,GACbtpB,KAAKupB,QAAU,KACfvpB,KAAKwpB,eAAiB,KACtBxpB,KAAKypB,SAAW,OAGlBlW,KAAK+V,EAAOG,GACVzpB,KAAKspB,MAAQA,EACbtpB,KAAKypB,SAAWA,EAEhBzpB,KAAKwpB,eAAiB,IAAIE,eAAe,KACvC,MAAMC,EAAehrB,OAAO8a,WAAa9a,OAAOirB,YAEhD5pB,KAAKypB,SAAS,CAAEE,mBAGlB3pB,KAAKupB,QAAU1B,SAASC,eAAe9nB,KAAKspB,OAExCtpB,KAAKupB,SACPvpB,KAAKwpB,eAAeK,QAAQ7pB,KAAKupB,SAOrClT,UACMrW,KAAKwpB,iBACPxpB,KAAKwpB,eAAeM,aACpB9pB,KAAKwpB,eAAiB,MAGxBxpB,KAAKspB,MAAQ,GACbtpB,KAAKupB,QAAU,MAInB,MAAMQ,GAAe,IAAIX,GC8FzBxoB,GAAA8F,EAAAhJ,IAAA,YAAA6e,GAEAmL,GAAA,YAEA,IAAAsC,GAAA,GACAC,GAAA,GAEe,QACf5sB,KAAA,MACAqS,WAAA,CACAwa,QACAC,UACAC,WACAC,iBACAC,SACAC,SACAC,aACAC,WACAC,eACAC,WACAC,eACAC,eACAC,cACAC,iBAEAhjB,SAAA,CACAjF,cACA,gBAAAojB,EAAA,WAAAC,GAAA,KAAA6E,WACA,IAAAnoB,EAAA,CAAAsb,MAAA+H,EAAA,KAAA9H,OAAA+H,EAAA,MACA,OAAAtjB,GAEAR,aACA,kBAAAI,EAAA,YAAAE,EAAA,QAAAH,UACA,iBAAAyoB,EAAA,QAAAC,gBAAA,GAEA,IAAAxoB,EAAAD,EAAAE,EAEAL,EAAA,IAAAG,EAUA,OAJA,IAAAwoB,GAAA,IAAAA,GAAA,KAAApqB,QAAAgB,WAAA,KAAAC,qBAAA,KAAAJ,SAAAypB,eACAzoB,GAAA,EACAJ,GAAA,GAEA,CACAA,WACAI,SAGA6B,gBACA,cAAA8B,EAAA,QAAA+kB,GAAA,KAAAC,iBACA,OAAAhlB,IAAA+kB,GAAA,SAAArpB,cAAA,SAAAA,eAGA9G,OACA,OACAqwB,OAAA9G,GACA+G,QAAA3G,GACAthB,OAAA,KACAuB,OAAA,KACAjE,KAAA8F,EAAAhJ,IAAA,YAAA6e,GACAlc,aAAA,KACAC,aAAA,EACAC,aAAA,EACAyC,OAAA,GACAgoB,WAAA,CAAA9E,UAAA,EAAAC,WAAA,GACA5iB,UAAA,GACAwB,MAAAnE,GAAA4F,UAAA,0BACA9B,MAAA9D,GAAA6F,UAAA,0BACA9B,cAAA,EACAC,YAAA,EACAjB,SAAA,CAAAC,OAAA,SAAAhC,QAAA,IACAwB,aAAA,UACAa,kBAAA,EACAnC,qBAAA,EACAqD,aAAA,CAAAsa,OAAA,GAAAO,SAAA,IACA9a,OAAA,EACAE,gBAAA,EACAvE,QAAA6F,EAAAhJ,IAAA,eAAA4e,GACApb,eAAAyb,GAAA9G,WACAwB,QAAArP,IAAAqP,QACA5S,WAAA,CACAiO,OAAA,GACAC,WAAA,IAEAjP,QAAA,EACAG,UAAA,GACArB,SAAA,CACA4f,SAAA,EACA3f,YAAA,EACAE,UAAA,GAEAuoB,eAAA,GACAzpB,cAAA,GACAwD,QAAA,GACAjB,oBAAA,EACApC,QAAA,KACAX,eAAAub,GACA9a,SAAA,CACAypB,YAAA,GACAxpB,aAAA,GAEAuC,OACA5C,aAAA,EACAS,aAAA,EACAspB,iBAAA,CACAhlB,SAAA,EACA+kB,SAAA,GAEAI,aAAA,IAGAlJ,cACApe,GACA2jB,SAAAE,KAAA0D,aAAA,uBAGAthB,UACAxL,OAAA+sB,iBAAA,eAAAC,YAAA,IAEAniB,gBACA,KAAAnI,QAEA4G,QAAA,CACA0jB,WAAAvM,GACA,IAAAA,EAAAnkB,KAAA,YAAAoF,cAAA,EACA,gBAAAokB,EAAA,WAAAC,EAAA,UAAAC,EAAA,MAAAE,EAAA,SAAAC,EAAA,aAAAC,EAAA,QAAA1e,EAAA,gBAAAC,EAAA,YAAAC,EAAA,aAAAlG,EAAA,WAAAurB,GAAAxM,EAAAnkB,KACA,KAAAuwB,YAAA,SAAAI,EACA,KAAAvrB,iBAAA,EACAokB,IAAA,KAAA6G,OAAA,SAAAA,OAAA7G,cACAC,IAAA,KAAA4G,OAAA,SAAAA,OAAA5G,eACAC,IAAA,KAAA2G,OAAA,SAAAA,OAAA3G,cACAE,GAAAC,GAAAC,IAAA,KAAAwG,QAAA,CAAA1G,QAAAC,WAAAC,iBACA1e,GAAAE,GACA,KAAAxF,WAAA,CAAAsF,UAAAC,kBAAAC,cAAAE,WAAA,EAAAD,WAAA,KAGArE,oBACA,KAAAb,aAAA,KAAAA,aAEAuqB,YAAAjU,GACA,KAAAhX,KAAAgX,EACA,KAAAlT,MAAA,KAAA9D,KAAA6F,UAAA,0BACA,KAAA1B,MAAA,KAAAnE,KAAA4F,UAAA,2BAGA,iBAAAoR,GACA,KAAAiU,YAAAjU,GAEA,MAAAtb,QAAA4H,EAAA8D,IAAA8jB,qBAAA9jB,IAAA+jB,6BACAzvB,OAAA0vB,GAAA1vB,EAEA,IAAA0vB,EAAA,CACA,IAAAC,EAAA,4BAYA,OAJA/nB,GAAA0J,MACAqe,EAAA,0BAEArmB,EAAAmI,KAAAke,GAGA,KAAAC,QAGA,aACA,KAAA5rB,aAAA,EACA,KAAAC,aAAA,EACA,IAAA4rB,GAAA,EACA,IACA,MAAAC,EAAA5hB,KAAAuY,KAAA,IAAAvY,KAAAkL,WACA,QAAArP,EAAA,gBAAAC,EAAA,YAAAC,EAAA,UAAAE,EAAA,UAAAD,GAAA,KAAA5F,MACA,WAAAyC,EAAA,iBAAAxB,GAAA,QAAAhB,SACA,UAAA4jB,EAAA,WAAAC,EAAA,UAAAC,GAAA,KAAA2G,QACA,SAAAxG,EAAA,aAAAC,GAAA,KAAAwG,QA6CA,IAAAjvB,EA3CA0L,IAAAkP,OAAAmV,YAAA,QACA,KAAA/oB,OAAA0E,IAAAskB,aAAA,CAEA7H,YACAC,aACAC,YAEAle,YAEAD,YAIAxD,OAAAK,EACAkpB,UAAA,CACA7E,aAGA8E,OAAA,WAEA1H,WACAC,eAEAljB,cAGA,KAAAyB,OAAAmpB,iBAAA,CACAC,wBAAA,EACAC,oBAAA,EACAC,6BAAA,IAGA,KAAAC,kBAAA,KAAAvpB,QAYA,YAAAuhB,GAAA,KAAA0G,QAGAjvB,EADA,KAAAuE,QAAAC,cACA,KAAAwC,OAAAwpB,qBAAA,CAEAhkB,YAAA,YACA+b,QACAuH,oBAIA,KAAA9oB,OAAAypB,mBAAAnsB,GAAAmF,OAAA,GAAAnF,GAAAwF,UAAA,IAEA,WAAA7G,EAAA,IAAA0sB,EAAA,OAAArW,GAAAtZ,GAAA,GAGA,oBAAAiD,EAIA,OAHAqG,EAAAmI,KAAA,UACA,KAAAzN,aAAA,OACA,KAAAC,aAAA,GAGA,oBAAAhB,EAIA,OAHAqG,EAAAmI,KAAAke,GAAA,QACA,KAAA3rB,aAAA,OACA,KAAAC,aAAA,GAGA,MAAAysB,EAAApX,EAAAqX,aAOA,GANAd,QAAA,KAAA7oB,OAAA4pB,SAAA,CACAF,QACAG,WAAA9mB,EACAD,SAAAE,EACAwC,YAAAvC,IAEA4lB,EAAA,CAEA,KAAA7oB,OAAA8pB,sBACA,KAAAvoB,OAAAmD,IAAA2L,eACA,iBAAAX,EAAA,YAAA7R,EAAA,WAAA8R,GAAA,KAAA/R,qBACA,KAAA2D,OAAA0O,KAAA,CACA0C,QAAA,CACApE,gBAAAmB,EAAA5R,UAAA,UACA8H,iBAAA/H,EAAAC,UAAA,UACAmQ,aAAA0B,EAAA7R,UAAA,MAGA,KAAAkC,OAAA+pB,QAAA,KAAAxoB,OAAA,CAAAyoB,eAAA,IACAvD,GAAAxW,KAAAmU,GAAA,KAAA+B,UAEAvlB,GAAA,KAAAR,QACAmkB,SAAA6D,iBAAA,wBAAA6B,mBAGA,MAAAlmB,GACAiC,QAAAC,IAAA,SAAAlC,GACA,KAAAmmB,aAAAnmB,EAAA4kB,KAAA,gBAIA,8BAAAtC,GACA,GAAAzlB,EAAA,OACA,KAAAsnB,mBACA,KAAAloB,OAAAmqB,aAAA,QAEA,wBAAAC,EAAA,gBAAAC,GAAAC,2BACAC,EAAAlE,EAAA+D,EAAAC,EACA,KAAArqB,OAAAwqB,qBAAAD,GACA,KAAAA,eAEA,yBACA,KAAArC,aAAA,KAAAA,YACA,KAAAA,kBACA,KAAAloB,OAAAmqB,aAAA,eAEA,KAAAnqB,OAAAmqB,aAAA,SAGAhE,UAAA,aAAAE,IACA,KAAArmB,SAEA,gBAAAzC,QAAAwC,YAaA,KAAA0qB,wBAAApE,GACA,KAAArmB,OAAA0qB,oBAXA,KAAA9C,eAAA+C,WACA,KAAAC,sBAEA,KAAAC,uBAUA,yBACA,WAAAtG,SAAAuG,gBACA,qBAAArpB,aACA,KAAAzB,OAAAkD,YAGA,qBAAAzB,aACA,KAAAzB,OAAA+qB,eAKAb,aAAAvB,EAAA,IACArmB,EAAAmI,KAAAke,GACA,KAAA5qB,QAGAA,OACA0oB,GAAA1T,UACAwR,SAAAyG,oBAAA,wBAAAf,kBAEA,KAAA7oB,MAAA,KAAA9D,KAAA6F,UAAA,0BACA,KAAA1B,MAAA,KAAAnE,KAAA4F,UAAA,0BAEA,KAAAlD,QAAA,KAAAA,OAAA+S,UAEA2T,GAAA,GACAC,GAAA,GACA,KAAA3pB,aAAA,EACA,KAAAC,aAAA,EACA,KAAAuB,qBAAA,EACA,KAAAoD,OAAA,EACA,KAAAlC,OAAA,GACA,KAAAoC,gBAAA,EACA,KAAA9B,OAAA,KACA,KAAAuB,OAAA,KACA,KAAAjD,QAAA,KACA,KAAAqC,kBAAA,EACA,KAAAhD,eAAAub,GACA,KAAA7Y,SAAA,CAAAC,OAAA,SAAAhC,QAAA,IACA,KAAAiC,UAAA,GACA,KAAAlD,eAEAA,cACA2I,QAAAC,IAAA5K,QACAA,OAAA4vB,OAAAC,YAAA,aACA7vB,OAAA4vB,OAAAE,kBACA9vB,OAAA4vB,OAAAE,oBAIA5B,kBAAAvpB,GAEAA,EAAA5C,GAAA,kBAAArB,IACA,KAAA4B,eAAA5B,IAGAiE,EAAA5C,GAAA,eAAArB,IACA,MAAAqvB,EAAArvB,EAAAuW,QAAAvW,EAAAuW,OAAAhQ,SAAA,aACA,KAAA4nB,aAAAkB,KAGAprB,EAAA5C,GAAA,qBAAArB,IACA,KAAA4E,kBAAA5E,EAAAsvB,kBAKArrB,EAAA5C,GAAA,mBAAArB,IACA,kBAAAuvB,GAAAvvB,EAOA,GANA,KAAA6rB,eAAA7rB,EACA,KAAAqC,SAAA,IACA,KAAAA,SACAypB,YAAAyD,GAGA,gBAAA/tB,QAAAwC,WAAA,CACA,KAAAzB,QAAA,KAAAqD,QAAAyR,KAAAzT,KAAA8d,aAAA,KAAAmK,eAAA+C,YACA,MAAAY,EAAA,KAAAC,eACA,KAAAZ,oBAAAW,MAKAvrB,EAAA5C,GAAA,SAAArB,IAEA,GADA,KAAA2D,OAAA3D,EACA,KAAAoC,cAAA,CACA,MAAAstB,EAAA,KAAA/rB,OAAA0T,KAAAzT,KAAAE,OAAAhE,KAAA,KAAAsC,eACAstB,IACA,KAAAttB,cAAA,OAOA6B,EAAA5C,GAAA,gBAAArB,IACA,KAAA8uB,mBAAA9uB,KAGAiE,EAAA5C,GAAA,cAAArB,IACA,KAAA2rB,WAAA3rB,IAGAiE,EAAA5C,GAAA,cAAArB,IACA,KAAAkE,UAAAlE,IAGAiE,EAAA5C,GAAA,cAAArB,IAIA,WAAAE,EAAA,IAAA0sB,EAAA,OAAArW,GAAAvW,EACA,iBAAAE,GACAqG,EAAAmI,KAAAke,GAEA,KAAApqB,WACA+D,EAAAmI,KAAA,iBAEA,KAAAxN,aAAA,GACA,iBAAAhB,EACAqG,EAAAmI,KAAAke,GAEA,KAAAuB,aAAAvB,EAAArW,KAIAtS,EAAA5C,GAAA,kBAAArB,IACA,kBAAA2vB,EAAA,cAAAC,EAAA,kBAAA5qB,EAAA,YAAA8mB,EAAA,iBAAA+D,GAAA7vB,EACA,IAAA0O,EAAA,GACA,KAAApJ,aAAAqqB,EACA,KAAA3qB,oBAEA,KAAAgnB,iBAAA,IACA,KAAAA,iBACAD,SAAA8D,GAEA,KAAAxtB,SAAA,IACA,KAAAA,SACAC,cAAAwpB,GAEA,cAAA8D,GAAAD,GACAjhB,EAAA,0BACA,KAAAnJ,YAAA,GACA,cAAAqqB,GAAAD,EAEA,gBAAAC,GAAAD,GACAjhB,EAAA,YACA,KAAAnJ,YAAA,GACA,gBAAAqqB,GAAAD,IACAjhB,EAAA,cALAA,EAAA,YAQA,KAAArK,QAAAqK,GACAnI,EAAAmI,UAIAzK,EAAA5C,GAAA,eAAArB,IACA,KAAAqF,MAAArF,EAAAoE,SAGAH,EAAA5C,GAAA,eAAArB,IACA,KAAA0F,MAAA1F,EAAAoE,SAGAH,EAAA5C,GAAA,gBAAArB,IACA,KAAA8F,aAAA9F,IAGAiE,EAAA5C,GAAA,UAAArB,IACA,KAAAuC,QAAAvC,EAAApE,OAGAqI,EAAA5C,GAAA,YAAArB,IACA,KAAAsE,SAAAtE,IAGAiE,EAAA5C,GAAA,gBAAArB,IACAA,GACA,KAAA8vB,OAAA,cAKA7rB,EAAA5C,GAAA,kBAAArB,IAGA,gBAAAwB,QAAAwC,aACA2mB,GAAA3qB,EAEA4qB,GAAA,KAAAmF,kBAEA,KAAApsB,OAAAinB,MAIA3mB,EAAA5C,GAAA,oBACA,iBAAAsS,EAAA,WAAAC,EAAA,YAAA9R,GAAA9B,EAAAwW,YACA,OAAA5C,QAAA,IAAAA,OAAA,EAAAA,EAAAxB,QAAA7L,EAAAmI,KAAA,cAAAkF,EAAAxB,QACA,OAAAuB,QAAA,IAAAA,OAAA,EAAAA,EAAAvB,QAAA7L,EAAAmI,KAAA,gBAAAiF,EAAAvB,OACA/G,WAAA,MACA,OAAAvJ,QAAA,IAAAA,OAAA,EAAAA,EAAAsQ,QAAA7L,EAAAmI,KAAA,gBAAA5M,EAAAsQ,QACA,OAGAnO,EAAA5C,GAAA,uBACA,KAAA+D,WAAApF,IAGAiE,EAAA5C,GAAA,SAAArB,IACA,KAAAqE,OAAArE,EACAA,GACAqL,WAAA,KACA9E,EAAAmI,KAAA,wBACA,OAIAzK,EAAA5C,GAAA,gBAAA1C,IACA,KAAAoF,aAAApF,IAGAsF,EAAA5C,GAAA,kBAAArB,IACA,KAAAwE,UAAAxE,IAGAiE,EAAA5C,GAAA,YAAA8B,IACA,KAAAA,aAEAc,EAAA5C,GAAA,kBAAA2uB,kBAEA/rB,EAAA5C,GAAA,gBAAAiH,OAAAvJ,MAAAiL,YACA,UAAA1B,GACA2B,QAAAC,IAAA,+BAAAnL,EAAAiL,GAEA,UAAA1B,GACA2B,QAAAC,IAAA,+BAAAnL,EAAAiL,KAIA/F,EAAA5C,GAAA,oBAAAzF,IACA,gBAAAq0B,GAAAr0B,EAEA,KAAAowB,iBAAA,IACA,KAAAA,iBACAhlB,QAAAipB,KAIAhsB,EAAA5C,GAAA,0BAAAzF,IACA,aAAAs0B,EAAA,MAAAzQ,EAAA,WAAA0Q,EAAA,WAAAC,GAAAx0B,GACA,gBAAAy0B,GAAAD,EAEA,yBAAA3Q,IACA,KAAA/c,aAAA,EACAuH,QAAAC,IAAA,qBAAAmmB,IAGA,sBAAA5Q,IACA,KAAA/c,aAAA,EACA,cAAAwtB,EACA3pB,EAAAmI,KAAAyhB,GAEA5pB,EAAAmI,KAAA,gCAKAzK,EAAA5C,GAAA,6BAAAzF,IACA,cAAA00B,EAAA,QAAAhH,EAAA,OAAAllB,EAAA,WAAAgsB,GAAAx0B,GACA,gBAAAy0B,GAAAD,EACAnmB,QAAAC,IAAA,qBAAAmmB,GACAC,IAAAhH,IAEA,KAAA5mB,aAAA,2BAAA0B,EAAA,KAEAksB,IACA,KAAA5tB,aAAA,MAKAstB,iBAAAhwB,GAMA,qBAAAuwB,EAAA,gBAAAC,EAAA,oBAAAC,EAAA,oBAAAC,EAAA,IAAA1wB,EACA,OAAAuwB,EACA,KAAA3qB,QAAA4qB,MACA,CAEA,IAAAvO,EAAA,KAAArc,QAAA4V,OAAAgV,GAEAE,EAAAt0B,OAAA,GACAs0B,EAAA1b,QAAAtG,IACA,MAAA7K,EAAAoe,EAAA0O,UAAA7sB,GACAA,EAAAwd,gBAAA5S,EAAA4S,eAEAzd,GAAA,GACAoe,EAAA3kB,OAAAuG,EAAA,KAKA4sB,EAAAr0B,OAAA,GACAq0B,EAAAzb,QAAAtG,IACA,MAAA7K,EAAAoe,EAAA0O,UAAA7sB,GACAA,EAAAwd,gBAAA5S,EAAA4S,eAEAzd,GAAA,EACAoe,EAAApe,GAAA6K,EAEAuT,EAAAvlB,KAAAgS,KAIA,KAAA9I,QAAAqc,IAIAwN,eACA,uBAAA7D,GAAA,KAAAC,eACA,aAAA9I,EAAA,YAAA3f,GAAA,KAAAD,SACAqsB,EAAA1nB,KAAAC,MAAAD,KAAAU,UAAA,KAAArF,WAGAG,EAAA6H,KAAAuY,MAAAkI,EAAA,GAAA7I,GAYA,OAXAzf,IAAA,EAAAA,EAAA,EAEAF,EAAAE,IACAF,EAAAE,GAEAksB,EAAA,IACAA,EACAlsB,YACAF,eAEA,KAAAD,SAAAqsB,EACAA,GAGAoB,iCAAApB,EAAA,KAAArsB,UACA,SAAAf,cACA,OAEA,eAAA2gB,GAAAyM,EACAqB,EAAA,GACAjtB,EAAA,KAAAD,OAAA0T,KAAAzT,KAAAE,OAAAhE,KAAA,KAAAsC,eACA,GAAAwB,EAAA,KAAAsf,EACA,iBAAAxB,EAAA,gBAAAH,EAAA,WAAA3d,QAAA,IAAAA,OAAA,EAAAA,EAAAE,SAAA,GAEA,IAAAgiB,EAAAvE,EAAA,IACAsP,EAAAn0B,KAAA,CACAo0B,QAAApP,EACAH,eACAuE,aACAiL,QAAA,IAAAjL,EAAA,MAEA,IAAAkL,EAAA,GACA,QAAA9N,EAAA,KAAAjf,cAAA,IAAAif,KAAA+N,iBAAAJ,EAAA9N,EAAA,EAAAiO,EAAA,CACAE,yBAAA,SAGA,KAAA9uB,cAAA,GAEA,YAAAA,eAIAysB,oBAAAW,EAAA,KAAArsB,UAAA,IAAAguB,EAGA,GADA,KAAAP,mCACA,KAAAxuB,cACA,OAEA,kBAAAmtB,EAAA,WAAAX,EAAA,iBAAAhD,GAAA,KAAAC,gBACA,SAAA9I,EAAA,YAAA3f,GAAAosB,EACA,IAAAqB,EAAA,GACAG,EAAA,GACA,MAAAI,EAAA,IAAAhuB,GAAAwrB,EAAA,IACA,IAAAyC,EAAAzF,EAAAwF,EACAC,EAAAtO,IACAsO,EAAAjuB,EAAA2f,EACAsO,IAAAjuB,EAAA,GAAA2f,EAEAsO,EAAAtO,GAGA,IAAAuO,EAAAluB,EAAA,EAEA,IAAAA,GAAAwrB,IACAyC,EAAA,EACAC,GAAA,GAEA,WAAAtL,EAAA,OAAA5pB,GAAA2pB,KACA,IAAAwL,EAAAvL,EAAAqL,IAAArL,EAAA7a,KAAAiL,IAAA,EAAAha,IACAm1B,EAAAvc,QAAA,CAAApR,EAAAC,KACA,eAAAiiB,EAAA,UAAAiL,EAAA,OAAAzoB,GAAA1E,EACAktB,EAAA,GACAvP,EAAA,GACAjZ,IAAA+V,GAAAC,OAAAgT,KAIA,IAAAluB,GAAA,IAAAS,IACAitB,EAAAlC,GAAAW,EACAhO,EAAAqN,EAAA,IACAA,IACA9I,EAAA,EACAiL,EAAA,IAGAF,EAAAn0B,KAAA,CACA6kB,eACAuP,UACAhL,aACAiL,eAGA,QAAAI,EAAA,KAAAltB,cAAA,IAAAktB,KAAAF,iBAAAJ,EAAA9N,EAAA3f,EAAA4tB,EAAA,CACAE,yBAAA,KAIApC,mBAAA9uB,EAAA,KAAA2D,QAEA,QAAAvB,cAAA,CACA,MAAAovB,EAAAxxB,EAAAsjB,OAAA1f,KAAAE,OAAAhE,KAAA,KAAAsC,eACAwB,EAAA4tB,EAAA,GAGA,IAAA5tB,GAAA,IAAAA,EAAAE,OAAAyd,eAAA,KAAAsK,eAAA+C,WAGA,OAFA,KAAAxsB,cAAA,QACA,KAAAysB,sBAGA7uB,EAAAwxB,EAKAxxB,EAAA4oB,GAAA5oB,GAEA,iBAAA4uB,EAAA,iBAAAhD,GAAA,KAAAC,eACA,SAAA1oB,SAAAC,aAAAwrB,GAAAhD,EAAA,IACA5rB,IAAAsjB,OAAA1f,MAAAE,OAAAwlB,UAEA,WAAA5C,EAAA,KAAAV,GAAAD,KACAuC,EAAA5B,EAAA1mB,EAAA5D,SAAA,MACAyjB,EAAAmG,EAAAhmB,EAAA5D,SACA,UAAAyqB,EAAA,WAAAC,GAAAsB,GAAAC,GAAAC,GAEA,KAAAqD,WAAA,CAAA9E,YAAAC,cAIA8D,GAAAjE,GAAA3mB,EAAA6mB,EAAAC,EAAAjH,GAEA+K,GAAA,KAAAmF,kBACA,KAAApsB,OAAAinB,IAGAmF,kBACA,MAAA0B,EAAA9G,GACA+G,EAAAC,IAAA/G,IA8BA,OA5BA6G,EAAAzc,QAAApR,IACA,IAAAguB,EAAA,GACA,oBAAAtQ,EAAA,aAAAC,EAAA,SAAAsQ,GAAAjuB,EACAC,EAAAgkB,GAAA+C,GAAAtJ,EAAAC,GAEA,GAAA1d,GAAA,GACA,MAAAiuB,EAAAJ,EAAA7tB,GAEA,UAAAib,EAAA,OAAAC,GAAA,OAAA+S,QAAA,IAAAA,OAAA,EAAAA,EAAAjS,aAKA+R,EAAAG,iCAAAnuB,EAAAkb,EAAAC,GAGA,MAAAiT,EAAA,IAAAH,GAAA,IAAAA,EAGAD,EADAI,EACA,IAAAJ,EAAAK,SAAAlT,EAAA,MAEA,IAAA6S,EAAAM,UAAA,QAGAR,EAAA7tB,GAAA,UAAA+tB,KAIAF,GAGA,qBACA,IACA,IAAAz0B,EAAA,YAGAA,EADA,qBAAAyI,YACA,KAAAzB,OAAAkD,kBAEA,KAAAlD,OAAA+qB,cAGA,KAAAtpB,MAAAzI,EACA,MAAA+K,GACA,UAAA4kB,EAAA,eAAA5kB,GAAA,GAEAzB,EAAAyD,MAAA4iB,KAKA,uBACA,IACA,IAAA3vB,EAAA,YAEA,qBAAAoI,OACApI,QAAA,KAAAgH,OAAAmD,YAEAb,EAAAmI,KAAA,WAEAzR,QAAA,KAAAgH,OAAAkuB,cAEA,KAAA9sB,MAAApI,EACA,MAAA+K,GACA,UAAA4kB,EAAA,eAAA5kB,GAAA,GAEAzB,EAAAyD,MAAA4iB,KAKA,aAAAtkB,GACA,MAAA8pB,EAAA,CACAC,OAAA,CACAC,KAAA,WACA1F,IAAA,WAEA2F,SAAA,CACAD,KAAA,aACA1F,IAAA,IAEA4F,KAAA,CACAF,KAAA,SACA1F,IAAA,KAIA,IACA,WAAA0F,EAAA,IAAA1F,GAAAwF,EAAA9pB,GACA/C,QAAA,KAAAtB,OAAAquB,KAEA,KAAA/sB,aACAqnB,GACArmB,EAAAmI,KAAAke,GAEA,MAAA5kB,GACAzB,EAAAmI,KAAA,UAKA,qBACA,mBAAArJ,QAAA,KAAAC,cAAA,KAAAC,WAKA,mBAAAF,OAAA,KAAAC,cAAA,KAAAC,iBACA,KAAAuqB,OAAA,YAIA,qBAAAzqB,OAAA,KAAAC,mBACA,KAAAwqB,OAAA,QAIA,KAAA2C,uBAdA,KAAA3C,OAAA,WAkBA,qBACA,UACA,KAAA7rB,OAAAa,eACA,MAAAkD,GACAiC,QAAAC,IAAA,wBAAAlC,KAKA0qB,iBAAApqB,GACA,kBAAAlF,EAAA,UAAAE,GAAA,KAAAH,SACA,IAAAwvB,EAAAvvB,EAEA,SAAAkF,EACAqqB,GAAA,EACA,aAAArqB,EACAqqB,GAAA,EACA,SAAArqB,IACAqqB,EAAA,GAGAA,EAAAxnB,KAAAiL,IAAAuc,EAAA,GACAA,EAAAxnB,KAAAgL,IAAAwc,EAAArvB,GAEA,KAAAH,SAAA,IACA,KAAAA,SACAC,YAAAuvB,GAGA,KAAA9D,oBAAA,KAAA1rB,WAIA,sBAAArD,EAAA,IACA,mBAAA0B,QAAAwC,WAAA,CACA,GAAAlE,EAAA,CACA,MAAA8D,EAAA,KAAAD,OAAA0T,KAAAzT,KAAAE,OAAAhE,QAEA,IAAA8D,GAAA9D,IAAA,KAAAsC,cACA,OAQA,OAJA,KAAAA,cAAAtC,OAEA,KAAA+uB,4BAKA,KAAA5qB,OAAAtB,gBAAA7C,GAEA,KAAAsC,cAAAtC,GAIA,iBAAAwI,GAAA,IAAAsqB,EAGA,GAFA,KAAAxwB,cAAA,GAEA,gBAAAZ,QAAAwC,WAEA,YADA,KAAA0uB,iBAAApqB,GAIA,kBAAAlF,EAAA,UAAAE,GAAA,KAAAH,SACA,IAAAwvB,EAAAvvB,EAEA,SAAAkF,EACAqqB,GAAA,EACA,aAAArqB,EACAqqB,GAAA,EACA,SAAArqB,IACAqqB,EAAA,GAGAA,EAAAxnB,KAAAiL,IAAAuc,EAAA,GACAA,EAAAxnB,KAAAgL,IAAAwc,EAAArvB,GAEA,QAAAsvB,EAAA,KAAA3uB,cAAA,IAAA2uB,KAAAC,YAAAF,IAIAhxB,kBACA,KAAAoE,gBAAA,KAAAA,gBAIA,oBACA,aAAA3B,SAAA,KAAAH,OAAA6uB,cACAlG,EAAAxoB,EAAA,4BACAmC,EAAAmI,KAAAke,IAIA,mBAAA7tB,EAAAg0B,GACA,MAAAC,EAAA,CACArf,WAAA,CACA5U,IAAA,QACAk0B,QAAA,UAEAnxB,YAAA,CACA/C,IAAA,QACAk0B,QAAA,UAEArf,WAAA,CACA7U,IAAA,QACAk0B,QAAA,UAIA,SAAAlxB,EAAA,MAAAqQ,GAAA2gB,EACA,IACA,gBAAAh0B,QACA,KAAAyG,OAAA0tB,eAAAnxB,GACA,eAAAhD,GAAA,eAAAA,SACA,KAAAyG,OAAA2tB,aAAAH,EAAAj0B,GAAA,OAAAgD,GAEAwE,EAAAmI,KAAA,GAAAskB,EAAAj0B,GAAA,oBAAAqT,KACA,MAAApK,GAEA,OADAzB,EAAAyD,MAAA,UACAopB,QAAAC,OAAArrB,KAIA,qBAAAwO,GACA,iBAAA7C,EAAA,WAAAC,EAAA,YAAA9R,GAAA0U,GAAA8G,GAAA9G,WAEA,QAAA9B,EAAAc,EAAA8d,GACA,OAAA3f,QAAA,IAAAA,OAAA,EAAAA,EAAA5R,aAAA,QAAA2S,EAAA,KAAA7S,sBAAA,IAAA6S,GAAA,QAAAA,IAAAf,kBAAA,IAAAe,OAAA,EAAAA,EAAA3S,iBACA,KAAAoxB,aAAA,aAAAxf,IAGA,OAAA7R,QAAA,IAAAA,OAAA,EAAAA,EAAAC,aAAA,QAAAyT,EAAA,KAAA3T,sBAAA,IAAA2T,GAAA,QAAAA,IAAA1T,mBAAA,IAAA0T,OAAA,EAAAA,EAAAzT,iBACA,KAAAoxB,aAAA,cAAArxB,IAGA,OAAA8R,QAAA,IAAAA,OAAA,EAAAA,EAAA7R,aAAA,QAAAuxB,EAAA,KAAAzxB,sBAAA,IAAAyxB,GAAA,QAAAA,IAAA1f,kBAAA,IAAA0f,OAAA,EAAAA,EAAAvxB,iBACA,KAAAoxB,aAAA,aAAAvf,GAEA,MAAA5L,GACA,OAAAorB,QAAAC,OAAArrB,KAIA,oBAAApM,GACA,GAAAA,EAAA,mBACA,KAAAmK,gBAAA,EAEA,IACA,KAAAP,cACA,KAAA+tB,eAAA33B,EAAA,mBAEA,KAAAiG,eAAAjG,EAAAiG,eACA,MAAAmG,GACAiC,QAAAC,IAAA,qBAAAlC,GAGA,OAGA3L,OAAAC,UAAAC,eAAAC,KAAAZ,EAAA,cACA,KAAAqI,QACA,KAAA6uB,cAIA,MAAA/zB,EAAA1C,OAAA8D,KAAAvE,GAAA,GACA6C,EAAA7C,EAAAmD,GAEAy0B,EAAA,SAAAhyB,QAAA,CAAAzC,GAAAN,GAEA4I,EAAAG,IAAA,aAAAgsB,GAEA,KAAAhyB,QAAAgyB,GAIAzuB,mBACA,KAAAd,OAAAc,mBACA,KAAAtC,qBAAA,GAIA,qBACA,IAEA,MAAAxF,QAAA,KAAAuI,OAAAiuB,oBAAA,CACAC,aAAA,IAIAz2B,GACA,KAAAwF,qBAAA,EAEA,KAAA+C,OAAAnE,GAAA,2BACA,KAAA4C,OAAA+pB,QAAA,KAAAxoB,OAAA,CAAAmuB,gBAAA,MAGA,KAAAnuB,OAAAnE,GAAA,0BACA,KAAA0D,sBAGAwB,EAAAmI,KAAA,UAEA,MAAA1G,GACAiC,QAAAC,IAAA,wBAAAlC,GACAA,GAAA,iBAAAA,EAAA9H,MACAqG,EAAAmI,KAAA1G,EAAA4kB,KAAA,YAMA1qB,cACA,KAAA2D,OAAA,KAAAA,MAEA,KAAA5B,OAAA/B,YAAA,KAAA2D,QAIA,qBACA,SAAAnD,mBAEA,KAAAuB,OAAA2vB,mBACA,SAAAlxB,oBAEA,KAAAuB,OAAA4vB,qBCzvCib,MCQ7a,I,UAAY,eACd,GACApzB,EACAwF,GACA,EACA,KACA,KACA,OAIa,M,QCnBXxF,I,oBAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAC6D,MAAM/D,EAAIozB,SAAShzB,MAAM,CAAC,cAAc,SAAS,CAACF,EAAG,MAAM,CAACE,MAAM,CAAC,aAAaJ,EAAIqzB,gBAE7J9tB,GAAkB,GCIP,IACfjI,KAAA,UACAyK,MAAA,CACAurB,KAAA,CACA1rB,KAAAmW,OACAN,QAAA,IAEA9T,UAAA,CACA/B,KAAAmW,OACAN,QAAA,IAEA7V,KAAA,CACAA,KAAAmW,OACAN,QAAA,YAGAzV,SAAA,CACAqrB,WACA,oBAAAC,MAEAF,WACA,uBAAAxrB,KAAA,iBAAAA,KAAA,WAAA+B,eC3Bkc,MCQ9b,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCXfhK,aAAIkJ,UAAU,WAAY0qB,IAE1B,MAAMC,GAAcC,GAClBA,EAAeh0B,OAAOR,IAAIw0B,GAEtBt0B,GAAM6J,UACZwqB,GAAWr0B,IAEXQ,aAAI+zB,OAAOC,eAAgB,EAE3B,IAAIh0B,aAAI,CACNI,OAASyK,GAAMA,EAAEopB,MAChBC,OAAO,S,oCCpBV,qDAEI90B,EAAS,IAAI,IAAa,CAC5B,GAAM,YACN,IAAO,kBACP,QAAW,YACX,QAAW,m3EAEA,IAAOC,IAAID,GACT,gB,yDCTf,W,oCCAA,W,6DCAA,W,oCCAA,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,YACN,IAAO,kBACP,QAAW,YACX,QAAW,wgIAEA,IAAOC,IAAID,GACT,gB,kCCTf,W,oCCAA,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,kBACN,IAAO,wBACP,QAAW,YACX,QAAW,6rFAEA,IAAOC,IAAID,GACT,gB,sFCTf,W,oCCAA,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,sBACN,IAAO,4BACP,QAAW,YACX,QAAW,+mJAEA,IAAOC,IAAID,GACT,gB,6DCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,uBACN,IAAO,6BACP,QAAW,YACX,QAAW,2qFAEA,IAAOC,IAAID,GACT,gB,oCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,gBACN,IAAO,sBACP,QAAW,YACX,QAAW,gtBAEA,IAAOC,IAAID,GACT,gB,sFCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,cACN,IAAO,oBACP,QAAW,YACX,QAAW,mxDAEA,IAAOC,IAAID,GACT,gB,oCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,oBACN,IAAO,0BACP,QAAW,cACX,QAAW,gopBAEA,IAAOC,IAAID,GACT,gB,oCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,WACN,IAAO,iBACP,QAAW,gBACX,QAAW,+nDAEA,IAAOC,IAAID,GACT,gB,qBCTf9B,EAAOD,QAAU,80G,oCCAjB,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,YACN,IAAO,kBACP,QAAW,YACX,QAAW,w8BAEA,IAAOC,IAAID,GACT,gB,oCCTf,W,gGCAA9B,EAAOD,QAAU,IAA0B,2B,mBCA3CC,EAAOD,QAAU,ksF,kCCAjB,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,mBACN,IAAO,yBACP,QAAW,YACX,QAAW,m8IAEA,IAAOC,IAAID,GACT,gB,kCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,mBACN,IAAO,yBACP,QAAW,YACX,QAAW,+rEAEA,IAAOC,IAAID,GACT,gB,mBCTf9B,EAAOD,QAAU,s4D,kCCAjB,W,kCCAA,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,gBACN,IAAO,sBACP,QAAW,YACX,QAAW,ytBAEA,IAAOC,IAAID,GACT,gB,yDCTf,W,mBCAA9B,EAAOD,QAAU,siE,kCCAjB,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,eACN,IAAO,qBACP,QAAW,YACX,QAAW,kwCAEA,IAAOC,IAAID,GACT,gB,kCCTf,W,kCCAA,W,kCCAA,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,eACN,IAAO,qBACP,QAAW,YACX,QAAW,i4EAEA,IAAOC,IAAID,GACT,gB,mBCTf9B,EAAOD,QAAU,klD,mBCAjBC,EAAOD,QAAU,kvD,mBCAjBC,EAAOD,QAAU,02E,kCCAjB,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,gBACN,IAAO,sBACP,QAAW,YACX,QAAW,6wDAEA,IAAOC,IAAID,GACT,gB,qBCTf9B,EAAOD,QAAU,IAA0B,2B,kCCA3C,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,cACN,IAAO,oBACP,QAAW,YACX,QAAW,y4FAEA,IAAOC,IAAID,GACT,gB,kCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,2BACN,IAAO,iCACP,QAAW,YACX,QAAW,2jCAEA,IAAOC,IAAID,GACT,gB,mBCTf9B,EAAOD,QAAU,krC,mBCAjBC,EAAOD,QAAU,04D,yDCAjB,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,YACN,IAAO,kBACP,QAAW,YACX,QAAW,02DAEA,IAAOC,IAAID,GACT,gB,yDCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,YACN,IAAO,kBACP,QAAW,YACX,QAAW,shBAEA,IAAOC,IAAID,GACT,gB,kCCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,gBACN,IAAO,sBACP,QAAW,YACX,QAAW,2qEAEA,IAAOC,IAAID,GACT,gB,mBCTf9B,EAAOD,QAAU,s8C,yDCAjB,W,yDCAA,qDAEI+B,EAAS,IAAI,IAAa,CAC5B,GAAM,gBACN,IAAO,sBACP,QAAW,YACX,QAAW,+jCAEA,IAAOC,IAAID,GACT,gB,uGCTf,qDAEIA,EAAS,IAAI,IAAa,CAC5B,GAAM,cACN,IAAO,oBACP,QAAW,YACX,QAAW,0mCAEA,IAAOC,IAAID,GACT,gB,kCCTf", "file": "js/app.02bf6d3a.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-icon_mic\",\n  \"use\": \"icon-icon_mic-usage\",\n  \"viewBox\": \"0 0 17 17\",\n  \"content\": \"<symbol viewBox=\\\"0 0 17 17\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-icon_mic\\\">\\n    <title></title>\\n    <g id=\\\"icon-icon_mic_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-icon_mic_切图\\\" transform=\\\"translate(-444.000000, -252.000000)\\\">\\n            <g id=\\\"icon-icon_mic_icon-麦克-底座\\\" transform=\\\"translate(444.500000, 252.500000)\\\">\\n                <g id=\\\"icon-icon_mic_静音icon\\\">\\n                    <rect id=\\\"icon-icon_mic_矩形\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"16\\\" height=\\\"14.7692308\\\" />\\n                    <path d=\\\"M12.564425,6.62857143 C12.8800163,6.62857143 13.1358536,6.88440871 13.1358536,7.2 C13.1358536,9.84691312 11.1362187,12.0267865 8.56518383,12.3113853 L8.56442502,14.0571429 C8.56442502,14.3727341 8.30858774,14.6285714 7.99299645,14.6285714 C7.67740517,14.6285714 7.42156788,14.3727341 7.42156788,14.0571429 L7.42146236,12.3114576 C4.8501129,12.0271558 2.85013931,9.84713731 2.85013931,7.2 L2.85013931,7.2 L2.85761835,7.10731132 C2.90190431,6.83579963 3.13753572,6.62857143 3.42156788,6.62857143 C3.73715917,6.62857143 3.99299645,6.88440871 3.99299645,7.2 C3.99299645,9.409139 5.78385745,11.2 7.99299645,11.2 C10.2021355,11.2 11.9929965,9.409139 11.9929965,7.2 L11.9929965,7.2 L12.0004755,7.10731132 C12.0447614,6.83579963 12.2803929,6.62857143 12.564425,6.62857143 Z\\\" id=\\\"icon-icon_mic_形状结合\\\" fill=\\\"#FFFFFF\\\" fill-rule=\\\"nonzero\\\" />\\n                </g>\\n                <rect id=\\\"icon-icon_mic_矩形\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"16\\\" height=\\\"16\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-hand_down\",\n  \"use\": \"icon-hand_down-usage\",\n  \"viewBox\": \"0 0 30 23\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 23\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-hand_down\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-hand_down_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-hand_down_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-hand_down_切图\\\" transform=\\\"translate(-254.000000, -232.000000)\\\">\\n            <g id=\\\"icon-hand_down_icon-取消举手-normal\\\" transform=\\\"translate(254.000000, 232.000000)\\\">\\n                <path d=\\\"M23.8767427,13.537 L19.9532427,18.4475 C19.1377427,20.530875 17.1908677,22 14.9132427,22 L14.8003677,22 L18.0746177,17.681 L10.1996177,17.681 L13.4738677,22 L13.0949927,22 C10.0841177,22 7.64199272,19.435375 7.64199272,16.273125 L7.64199272,13.982375 L5.93749272,6.35325 C5.82461772,5.850125 6.07399272,5.332125 6.49399272,5.197375 L7.25436772,4.95325 C7.67436772,4.8185 8.10574272,5.116875 8.21861772,5.620875 L9.18024272,9.924125 C9.58974272,9.581125 10.0587427,9.2985 10.5846177,9.094625 L9.60024272,3.230375 C9.51361772,2.7115 9.84349272,2.21625 10.3378677,2.124375 L11.2329927,1.959 C11.7273677,1.867125 12.1981177,2.213625 12.2856177,2.7325 L13.2769927,8.636125 L14.0041177,8.636125 L14.0041177,1.406 C14.0041177,1.182 14.1861177,1 14.4109927,1 L16.3246177,1 C16.5486177,1 16.7306177,1.182 16.7306177,1.406 L16.7306177,8.86975 C17.0106177,8.9415 17.2774927,9.031625 17.5347427,9.136625 L18.4491177,3.687125 C18.5366177,3.16825 19.0082427,2.82175 19.5017427,2.91275 L20.3968677,3.079 C20.8912427,3.170875 21.2219927,3.66525 21.1344927,4.185 L19.9296177,11.362625 C20.2008677,11.952375 20.3662427,12.626125 20.3662427,13.40925 L20.3662427,13.476625 L21.7881177,11.696 C22.1109927,11.292625 22.6841177,11.240125 23.0682427,11.57875 L23.7647427,12.192125 C24.1497427,12.53075 24.1996177,13.13275 23.8767427,13.537 Z M15.4391177,12.4484182 L12.8351177,12.4484182 L12.8351177,17.6817932 L15.4391177,17.6817932 L15.4391177,12.4484182 Z\\\" id=\\\"icon-hand_down_形状\\\" />\\n                <mask id=\\\"icon-hand_down_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-hand_down_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-hand_down_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-hand_down_path-1\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=6cb1a7ca&prod&lang=scss&scoped=true\"", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=0c05b1f7&prod&lang=scss&scoped=true\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-meeting_host\",\n  \"use\": \"icon-meeting_host-usage\",\n  \"viewBox\": \"0 0 30 24\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-meeting_host\\\">\\n    <title></title>\\n    <g id=\\\"icon-meeting_host_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-meeting_host_切图\\\" transform=\\\"translate(-276.000000, -107.000000)\\\">\\n            <g id=\\\"icon-meeting_host_icon-参会者--Normal\\\" transform=\\\"translate(276.000000, 108.000000)\\\">\\n                <g id=\\\"icon-meeting_host_编组\\\" transform=\\\"translate(1.500000, 0.500000)\\\">\\n                    <path d=\\\"M26.2173913,18.1095652 C26.2173913,18.513913 25.8852174,18.8417391 25.4756522,18.8417391 C25.066087,18.8417391 24.733913,18.513913 24.733913,18.1095652 C24.7026087,15.8626087 22.9078261,13.7782609 20.7556522,13.1982609 C20.1217391,13.0278261 19.98,12.3252174 20.0243478,11.8565217 C20.0756522,11.3165217 20.3895652,11.0069565 20.7130435,10.8434783 C21.5113043,10.4391304 22.0556522,9.63565217 22.0556522,8.70782609 C22.0556522,7.13217391 21.28,5.93652174 20.2226087,5.65217391 C20.2226087,5.65217391 19.8530435,5.35130435 19.9965217,4.8026087 C20.1156522,4.34521739 20.6104348,3.98956522 21.2895652,4.28782609 C22.7408696,4.92434783 23.7504348,6.38434783 23.7504348,8.69565217 C23.7504348,9.99565217 23.1095652,11.1486957 22.1191304,11.8886957 C24.5130435,12.8556522 26.2173913,15.3052174 26.2173913,18.1095652 Z M4.62775718,8.70782609 C4.62775718,9.63565217 5.17297457,10.4391304 5.97036588,10.8434783 C6.29384414,11.0069565 6.60775718,11.3165217 6.65906153,11.8565217 C6.70340935,12.3252174 6.56167022,13.0278261 5.92775718,13.1982609 C3.77645283,13.7782609 1.98167022,15.8626087 1.95036588,18.1095652 C1.95036588,18.513913 1.61819196,18.8417391 1.20862675,18.8417391 C0.799061528,18.8417391 0.466887615,18.513913 0.466887615,18.1095652 C0.466887615,15.3052174 2.17036588,12.8556522 4.56514848,11.8886957 C3.57384414,11.1486957 2.93384414,9.99565217 2.93384414,8.69565217 C2.93384414,6.44869565 3.94253979,4.92434783 5.3947137,4.28782609 C6.07297457,3.98956522 6.56775718,4.34521739 6.68688762,4.8026087 C6.83036588,5.35130435 6.46080066,5.65217391 6.46080066,5.65217391 C5.40427892,5.93652174 4.62775718,7.14086957 4.62775718,8.70782609 Z\\\" id=\\\"icon-meeting_host_形状\\\" />\\n                    <path d=\\\"M22.2929582,20.1468726 C22.2929582,20.1850197 22.2877113,20.2213503 22.2877113,20.2604057 L4.3974421,20.2604057 C4.39656762,20.2213503 4.39132069,20.1850197 4.39132069,20.1468726 C4.39132069,16.2177172 6.80315774,12.8753013 10.1874251,11.5655828 C8.50053835,10.4847472 7.37507269,8.56921588 7.37507269,6.37938845 C7.37507269,2.99973359 10.0466326,0.260405703 13.3425767,0.260405703 C16.6376463,0.260405703 19.3092062,2.99973359 19.3092062,6.37938845 C19.3092062,8.56921588 18.1837406,10.4847472 16.4968538,11.5655828 C19.8811212,12.8753013 22.2929582,16.2177172 22.2929582,20.1468726 Z\\\" id=\\\"icon-meeting_host_路径\\\" />\\n                </g>\\n                <rect id=\\\"icon-meeting_host_矩形\\\" fill-opacity=\\\"0\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-setting\",\n  \"use\": \"icon-setting-usage\",\n  \"viewBox\": \"0 0 28 28\",\n  \"content\": \"<symbol viewBox=\\\"0 0 28 28\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-setting\\\">\\n<g clip-path=\\\"url(#icon-setting_clip0_2383_22205)\\\">\\n<path d=\\\"M11.8468 3.22324C13.2664 2.9263 14.7323 2.92558 16.1522 3.22114C16.2468 3.84396 16.4809 4.4375 16.8371 4.95765C17.1933 5.47781 17.6624 5.91119 18.2096 6.22559C18.7564 6.54108 19.3675 6.72955 19.9974 6.77699C20.6272 6.82442 21.2597 6.72961 21.8478 6.49958C22.8142 7.57881 23.5463 8.84587 23.9979 10.221C23.5047 10.6143 23.1066 11.1133 22.8333 11.681C22.56 12.2488 22.4185 12.8707 22.4193 13.5005C22.4193 14.8274 23.036 16.0105 24 16.78C23.5457 18.154 22.8128 19.4201 21.8468 20.4994C21.2588 20.2695 20.6265 20.1748 19.9968 20.2222C19.3672 20.2696 18.7563 20.458 18.2096 20.7734C17.6629 21.0877 17.1941 21.5208 16.8381 22.0405C16.4821 22.5603 16.248 23.1534 16.1532 23.7757C14.7337 24.0733 13.2679 24.0748 11.8478 23.7799C11.7537 23.1565 11.5198 22.5624 11.1636 22.0416C10.8073 21.5209 10.338 21.087 9.79036 20.7723C9.24349 20.457 8.63239 20.2687 8.00251 20.2214C7.37263 20.1742 6.74018 20.2692 6.15218 20.4994C5.18564 19.4198 4.45359 18.1524 4.0021 16.7769C4.49505 16.3839 4.89293 15.8852 5.16621 15.3178C5.4395 14.7504 5.58117 14.1289 5.58072 13.4995C5.58124 12.8695 5.43939 12.2475 5.16572 11.6797C4.89205 11.1119 4.49361 10.613 4 10.22C4.45429 8.84596 5.18724 7.57991 6.15323 6.50063C6.74116 6.73049 7.37347 6.82522 8.00315 6.77779C8.63283 6.73035 9.2437 6.54197 9.79036 6.22664C10.3371 5.91234 10.8059 5.47923 11.1619 4.95947C11.5179 4.43971 11.752 3.84664 11.8468 3.22429V3.22324ZM14 16.6498C14.8373 16.6498 15.6404 16.318 16.2325 15.7274C16.8246 15.1368 17.1572 14.3358 17.1572 13.5005C17.1572 12.6653 16.8246 11.8642 16.2325 11.2736C15.6404 10.683 14.8373 10.3512 14 10.3512C13.1626 10.3512 12.3596 10.683 11.7675 11.2736C11.1754 11.8642 10.8428 12.6653 10.8428 13.5005C10.8428 14.3358 11.1754 15.1368 11.7675 15.7274C12.3596 16.318 13.1626 16.6498 14 16.6498V16.6498Z\\\" />\\n</g>\\n<defs>\\n<clipPath id=\\\"icon-setting_clip0_2383_22205\\\">\\n<rect width=\\\"28\\\" height=\\\"28\\\" />\\n</clipPath>\\n</defs>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAABNVBMVEUAAADHy9morr7R1uPN0uDN0uC/xNPV2ODN0uDN0uCorr6orr7W2+bN0uDN0uDq7PDN0uCts8PN0uCorr6orr7M0d/N0uDN0uD9/f7w8vbN0uD09fe4vc3N0uDq7fPN0uDN0uDN0uDN0uDN0uDy9Pj9/f3u8PX3+Pr19vnu8PXAxdTGzNv09ffu8PXN0uDu8PXN0uD19vnN0uDN0uDu8PXN0uDN0uDu8PXN0uD19vnN0uDN0uDz9Pi1u8q1u8rz9Pjz9Pian6/////N0uD7+/2+xNKorr75+vv3+fv19vnz9Pjw8vbJzt2+w9LM0eDl5eWkqLfKz97L0N/HzNzo6u/i5OjNz9XCx9XT19/Q09y4vc22usn8/P3f4urb3+nY2+O7wM3s7/LT2OTIzdjFydTIytKyt8SSOsw+AAAAQXRSTlMACWYEt/v1lZRePCId8dOzbmxmYUxB7uj4+PXYsbBxRjYm3MO6+/Xv6efh2tnTwomJgH1zYmJWNjMRD9DEn55gW3dEkkwAAAK6SURBVFjD3ZjXduIwEEAFxvReAoRAeu+9bEVSNiQ2LGCytEBI/f9PWDkWCS2meJ5y36Rj36MZWWWMdBDOjveSG9YSpSXrRnLv+ExAE+D2J6cIIUqZYgYtK6wxlfS7x7OYHDtEI1vCt/n8LS5ltbZocZhG1lwcREmbGsX5dDqP72qkTfTgYiSNcMhC0hOxEA9HyFZkgXAGhsZZiAxLjp100J3sbuy6qZr3kl7KZXX6y2XSi3f+c8+sh/SjZGu1rEL68cyiflzhkNn8M3s5FtkfZnMo7OrMjNOWiOGJiCVszo9sOX3YAD7ne1w2NsXpCWGfhq0dXTiBmWdiE06EuSgUw2kD4FiIi8zYmAibv74oYFQU4KIZo6IZvkCsRkVWbZE4Lo2KLh1IZce4aBEx3MS4iLiZyA8h8jORBUJkQUiYhhBNC2iOQIjIHArCiILIDiOyIwuMyIK8MCIvisKIosgDI/IgAiMiSIQSTUGFtgCV7G9A0w/2QYItEbBFC7aNgG1sUFst2OYPeRyhRYADEuzIBrtEQF1roC9aoWVjouUQF4W3jF3Yt8JgJQRoUcPLrPjdZJa7OC+zOK6AtV3NvfF0X/+j8Y/Dm/X7J+0J/rg14OqtrkWikssViwX58Yq2+Jt/ObzZolePcqFYzOWIihhBfZyImoh5CjeUZviYXjTPCx9PhtIbWS5wkXiCBhDknoLcoG9Ijepzq/6qel7rredqQ9L6G++mIBqIQ9QCkzNUl2smUk2i4/NyXQ1MadIhNBV1SJ5ZvR8IbDyKNEwkKWxM3nn9XxqyQuhQcopsNyF9IpvNq24eKpWHnq7mZgQNRUitZrqoVKuV7p7VlIBG4Xx/7boDVdTZXts/R6Ni+vX95p1StVL6aC39NqGxcKeW4lIP8aWUG02AcHq069teX5GklfVt3+7RqV5m/gNcTSt93D3oSwAAAABJRU5ErkJggg==\"", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2547c305&prod&lang=scss&scoped=true\"", "module.exports = __webpack_public_path__ + \"img/ch_step_operate_permission.b629fb48.png\";", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-arrow\",\n  \"use\": \"icon-arrow-usage\",\n  \"viewBox\": \"0 0 12 12\",\n  \"content\": \"<symbol viewBox=\\\"0 0 12 12\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-arrow\\\">\\n    <title></title>\\n    <g id=\\\"icon-arrow_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-arrow_切图\\\" transform=\\\"translate(-43.000000, -94.000000)\\\">\\n            <g id=\\\"icon-arrow_icon-展开-常态\\\" transform=\\\"translate(43.000000, 94.000000)\\\">\\n                <polygon id=\\\"icon-arrow_三角形\\\" fill-opacity=\\\"0.8\\\" points=\\\"6 3 10 9 2 9\\\" />\\n                <rect id=\\\"icon-arrow_矩形\\\" fill-opacity=\\\"0\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"12\\\" height=\\\"12\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=1&id=5f1fe634&prod&lang=scss&scoped=true\"", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5f1fe634&prod&lang=scss\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-record\",\n  \"use\": \"icon-record-usage\",\n  \"viewBox\": \"0 0 31 23\",\n  \"content\": \"<symbol viewBox=\\\"0 0 31 23\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-record\\\">\\n    <title></title>\\n    <g id=\\\"icon-record_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-record_icon-录制-normal\\\" transform=\\\"translate(0.169880, 0.000000)\\\">\\n            <path d=\\\"M15,1 C20.7989899,1 25.5,5.70101013 25.5,11.5 C25.5,17.2989899 20.7989899,22 15,22 C9.20101013,22 4.5,17.2989899 4.5,11.5 C4.5,5.70101013 9.20101013,1 15,1 Z M15,3 C10.3055796,3 6.5,6.80557963 6.5,11.5 C6.5,16.1944204 10.3055796,20 15,20 C19.6944204,20 23.5,16.1944204 23.5,11.5 C23.5,6.80557963 19.6944204,3 15,3 Z\\\" id=\\\"icon-record_椭圆形备份-6\\\" />\\n            <circle id=\\\"icon-record_椭圆形备份-7\\\" cx=\\\"15\\\" cy=\\\"11.5\\\" r=\\\"5.01166825\\\" />\\n            <rect id=\\\"icon-record_矩形\\\" fill-opacity=\\\"0\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "var map = {\n\t\"./add.svg\": \"8b65\",\n\t\"./arrow.svg\": \"315f\",\n\t\"./camera.svg\": \"8a64\",\n\t\"./cancel_full.svg\": \"a896\",\n\t\"./cancel_mic_mute.svg\": \"831c\",\n\t\"./copy.svg\": \"5e82\",\n\t\"./end_call.svg\": \"e36d\",\n\t\"./full.svg\": \"73b2\",\n\t\"./hand_down.svg\": \"0fdf\",\n\t\"./hand_end.svg\": \"c910\",\n\t\"./hand_up.svg\": \"c4cf\",\n\t\"./hang_up.svg\": \"bedc\",\n\t\"./hang_up_full.svg\": \"8b06\",\n\t\"./home.svg\": \"dfed\",\n\t\"./icon_device_message.svg\": \"cbe9\",\n\t\"./icon_device_volume.svg\": \"5332\",\n\t\"./icon_mic.svg\": \"0b6f\",\n\t\"./icon_play.svg\": \"47d5\",\n\t\"./layout.svg\": \"ca4f\",\n\t\"./meeting_host.svg\": \"1aef\",\n\t\"./mic_mute.svg\": \"bbda\",\n\t\"./mic_null.svg\": \"87f1\",\n\t\"./more.svg\": \"d9cb\",\n\t\"./mute_camera.svg\": \"ad1c\",\n\t\"./mute_camera_error.svg\": \"3ce8\",\n\t\"./mute_mic_error.svg\": \"81cc\",\n\t\"./next.svg\": \"95b9\",\n\t\"./previous.svg\": \"f3d9\",\n\t\"./record.svg\": \"3aa2\",\n\t\"./record_stop.svg\": \"49c7\",\n\t\"./rotate.svg\": \"3f1b\",\n\t\"./setting.svg\": \"1d1a\",\n\t\"./share.svg\": \"40eb\",\n\t\"./share_stop.svg\": \"7b60\",\n\t\"./signal.svg\": \"fdf9\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"3bff\";", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-mute_camera_error\",\n  \"use\": \"icon-mute_camera_error-usage\",\n  \"viewBox\": \"0 0 30 26\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 26\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-mute_camera_error\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-mute_camera_error_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n        <rect id=\\\"icon-mute_camera_error_path-3\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n    </defs>\\n    <g id=\\\"icon-mute_camera_error_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-mute_camera_error_切图\\\" transform=\\\"translate(-392.000000, -111.000000)\\\">\\n            <g id=\\\"icon-mute_camera_error_icon-关闭摄像头没权限\\\" transform=\\\"translate(392.000000, 111.000000)\\\">\\n                <mask id=\\\"icon-mute_camera_error_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-mute_camera_error_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-mute_camera_error_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-mute_camera_error_path-1\\\" />\\n                <path d=\\\"M26.6713892,19.9017012 L10.1709213,3.40121131 C14.4924486,3.40121131 19.5676727,3.40121131 19.5676727,3.40121131 C19.5676727,3.40121131 21.854718,3.36928852 21.854718,5.74118711 C21.854718,6.13850009 21.854718,7.30049082 21.854718,7.30049082 L27.9551693,4.18088514 L27.9551693,20.5585654 L26.6713892,19.9017012 Z M25.8897408,23.6482229 L25.1839612,24.3540025 C24.9882995,24.5486658 24.672845,24.5486658 24.4781816,24.3540025 L2.18173589,2.05755672 C1.9870725,1.86289333 1.9870725,1.54644053 2.18173589,1.35177714 L2.88751548,0.645997546 C3.08217888,0.451334151 3.39863168,0.451334151 3.59329507,0.645997546 L25.8897408,22.9424433 C26.0844042,23.1371067 26.0844042,23.4525612 25.8897408,23.6482229 Z M4.31704359,21.3382172 C4.31704359,21.3382172 2.029,21.2952915 2.029,18.9982634 C2.029,17.6655679 2.029,8.16399767 2.029,6.14049664 L17.2267206,21.3382172 C12.8772416,21.3382172 4.31704359,21.3382172 4.31704359,21.3382172 Z\\\" id=\\\"icon-mute_camera_error_形状\\\" fill-opacity=\\\"0.8\\\" fill=\\\"#FF6666\\\" mask=\\\"url(#icon-mute_camera_error_mask-2)\\\" />\\n                <mask id=\\\"icon-mute_camera_error_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-mute_camera_error_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-mute_camera_error_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-mute_camera_error_path-3\\\" />\\n                <g id=\\\"icon-mute_camera_error_编组-11\\\" mask=\\\"url(#icon-mute_camera_error_mask-4)\\\">\\n                    <g transform=\\\"translate(20.000000, 0.000000)\\\">\\n                        <circle id=\\\"icon-mute_camera_error_椭圆形\\\" stroke-opacity=\\\"0.9\\\" stroke=\\\"#FFFFFF\\\" stroke-width=\\\"0.8\\\" fill-opacity=\\\"0.8\\\" fill=\\\"#FF6666\\\" fill-rule=\\\"evenodd\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"4.6\\\" />\\n                        <path d=\\\"M5.52767091,6.02524873 L5.73736524,2.75152348 C5.76345067,2.34428068 5.45446156,1.99299874 5.04721875,1.96691331 C5.03149494,1.96590615 5.01574294,1.96540218 4.99998691,1.96540218 C4.59190952,1.96540218 4.26109744,2.29621426 4.26109744,2.70429165 C4.26109744,2.72004768 4.26160141,2.73579968 4.26260858,2.75152348 L4.47230291,6.02524873 C4.49013136,6.30358442 4.72108082,6.520214 4.99998691,6.520214 C5.27889301,6.520214 5.50984247,6.30358442 5.52767091,6.02524873 Z M4.99643852,8.03459782 C5.15256756,8.03459782 5.28030949,7.98361404 5.39385788,7.88164647 C5.49321272,7.77967889 5.54998691,7.64129433 5.54998691,7.48105957 C5.54998691,7.32082482 5.49321272,7.18972365 5.39385788,7.08775608 C5.28740627,6.98578851 5.15256756,6.93480472 4.99643852,6.93480472 C4.84030949,6.93480472 4.71256756,6.98578851 4.61321272,7.08775608 C4.49966433,7.18972365 4.44998691,7.32082482 4.44998691,7.48105957 C4.44998691,7.64129433 4.49966433,7.7723955 4.61321272,7.87436307 C4.71256756,7.97633064 4.84030949,8.03459782 4.99643852,8.03459782 Z\\\" id=\\\"icon-mute_camera_error_！\\\" stroke=\\\"none\\\" fill=\\\"#FFFFFF\\\" fill-rule=\\\"nonzero\\\" />\\n                    </g>\\n                </g>\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-rotate\",\n  \"use\": \"icon-rotate-usage\",\n  \"viewBox\": \"0 0 80 68\",\n  \"content\": \"<symbol viewBox=\\\"0 0 80 68\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-rotate\\\">\\n<rect x=\\\"12\\\" y=\\\"56\\\" width=\\\"21\\\" height=\\\"30\\\" rx=\\\"2\\\" transform=\\\"rotate(-90 12 56)\\\" fill=\\\"white\\\" fill-opacity=\\\"0.4\\\" />\\n<rect x=\\\"13\\\" y=\\\"55\\\" width=\\\"19\\\" height=\\\"28\\\" rx=\\\"1\\\" transform=\\\"rotate(-90 13 55)\\\" stroke=\\\"white\\\" stroke-opacity=\\\"0.6\\\" stroke-width=\\\"2\\\" />\\n<rect x=\\\"67\\\" y=\\\"55\\\" width=\\\"26\\\" height=\\\"38\\\" rx=\\\"1\\\" transform=\\\"rotate(180 67 55)\\\" fill=\\\"white\\\" fill-opacity=\\\"0.5\\\" stroke=\\\"white\\\" stroke-width=\\\"2\\\" />\\n<path d=\\\"M31.9907 24.3569V26.059C31.9907 26.1007 32.0014 26.1415 32.0216 26.177C32.0419 26.2124 32.0709 26.2411 32.1053 26.2597C32.1397 26.2784 32.1783 26.2862 32.2167 26.2825C32.255 26.2788 32.2916 26.2635 32.3224 26.2385L35.9171 23.3212C35.9428 23.3003 35.9637 23.2732 35.9781 23.2421C35.9925 23.2109 36 23.1765 36 23.1417C36 23.1069 35.9925 23.0725 35.9781 23.0413C35.9637 23.0102 35.9428 22.9831 35.9171 22.9622L32.3224 20.0449C32.2916 20.0199 32.255 20.0046 32.2167 20.0009C32.1783 19.9972 32.1397 20.0051 32.1053 20.0237C32.0709 20.0423 32.0419 20.071 32.0216 20.1064C32.0014 20.1419 31.9907 20.1827 31.9907 20.2244V22.1128C29.5171 22.1149 27.1455 23.1797 25.3972 25.0733C23.6488 26.9669 22.6667 29.5342 22.6667 32.2111C22.6666 32.3585 22.6934 32.5044 22.7455 32.6406C22.7976 32.7767 22.874 32.9004 22.9703 33.0046C23.0666 33.1088 23.1809 33.1915 23.3067 33.2479C23.4325 33.3043 23.5673 33.3333 23.7035 33.3333C23.8396 33.3333 23.9745 33.3043 24.1003 33.2479C24.2261 33.1915 24.3404 33.1088 24.4367 33.0046C24.5329 32.9004 24.6093 32.7767 24.6614 32.6406C24.7135 32.5044 24.7403 32.3585 24.7403 32.2111C24.7403 30.1294 25.5039 28.1328 26.8634 26.6601C28.2229 25.1874 30.0671 24.359 31.9907 24.3569V24.3569Z\\\" fill=\\\"white\\\" />\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-share\",\n  \"use\": \"icon-share-usage\",\n  \"viewBox\": \"0 0 30 23\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 23\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-share\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-share_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-share_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-share_切图\\\" transform=\\\"translate(-155.000000, -108.000000)\\\">\\n            <g id=\\\"icon-share_icon-共享--Normal\\\" transform=\\\"translate(155.000000, 108.000000)\\\">\\n                <path d=\\\"M21.8232695,19.3997075 C22.3619087,19.3997075 22.798562,19.8363608 22.798562,20.375 C22.798562,20.9136392 22.3619087,21.3502925 21.8232695,21.3502925 L8.17673051,21.3502925 C7.63809134,21.3502925 7.20143801,20.9136392 7.20143801,20.375 C7.20143801,19.8363608 7.63809134,19.3997075 8.17673051,19.3997075 L21.8232695,19.3997075 Z M25.1802143,1.6497075 C26.2847838,1.6497075 27.1802143,2.545138 27.1802143,3.6497075 L27.1802143,15.2403818 C27.1802143,16.3449513 26.2847838,17.2403818 25.1802143,17.2403818 L4.81978572,17.2403818 C3.71521622,17.2403818 2.81978572,16.3449513 2.81978572,15.2403818 L2.81978572,3.6497075 C2.81978572,2.545138 3.71521622,1.6497075 4.81978572,1.6497075 L25.1802143,1.6497075 Z M14.310983,5.50512043 L11.1657311,8.65037238 C10.8232509,8.99285258 10.8232509,9.54812271 11.1657311,9.89060292 C11.5082113,10.2330831 12.0634814,10.2330831 12.4059616,9.89060292 L14.1228695,8.1736951 L14.1230246,12.7933936 C14.1230246,13.2777338 14.5156598,13.670369 15,13.670369 C15.4843402,13.670369 15.8769754,13.2777338 15.8769754,12.7933936 L15.8764176,8.17300609 L17.5940384,9.89060292 C17.9365186,10.2330831 18.4917887,10.2330831 18.8342689,9.89060292 C19.1767491,9.54812271 19.1767491,8.99285258 18.8342689,8.65037238 L15.689017,5.50512043 C15.3084834,5.12458687 14.6915166,5.12458687 14.310983,5.50512043 Z\\\" id=\\\"icon-share_形状结合\\\" />\\n                <mask id=\\\"icon-share_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-share_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-share_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-share_path-1\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "module.exports = __webpack_public_path__ + \"img/ch_step_operate_video.04b3452b.png\";", "module.exports = \"data:image/svg+xml;base64,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\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-icon_play\",\n  \"use\": \"icon-icon_play-usage\",\n  \"viewBox\": \"0 0 33 32\",\n  \"content\": \"<symbol viewBox=\\\"0 0 33 32\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-icon_play\\\">\\n<path d=\\\"M16.5 32C25.3366 32 32.5 24.8366 32.5 16C32.5 7.16344 25.3366 0 16.5 0C7.66344 0 0.5 7.16344 0.5 16C0.5 24.8366 7.66344 32 16.5 32Z\\\" fill=\\\"black\\\" fill-opacity=\\\"0.6\\\" />\\n<path fill-rule=\\\"evenodd\\\" clip-rule=\\\"evenodd\\\" d=\\\"M22.6044 16.7379L14.0569 22.1363C13.6494 22.3937 13.1104 22.272 12.853 21.8645C12.7649 21.725 12.7182 21.5634 12.7182 21.3984L12.7182 10.6016C12.7182 10.1196 13.1089 9.72888 13.5909 9.72888C13.7559 9.72888 13.9175 9.77564 14.0569 9.86373L22.6044 15.2621C23.0119 15.5195 23.1337 16.0585 22.8763 16.466C22.807 16.5757 22.7141 16.6686 22.6044 16.7379Z\\\" fill=\\\"white\\\" fill-opacity=\\\"0.9\\\" />\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-record_stop\",\n  \"use\": \"icon-record_stop-usage\",\n  \"viewBox\": \"0 0 31 23\",\n  \"content\": \"<symbol viewBox=\\\"0 0 31 23\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-record_stop\\\">\\n    <title></title>\\n    <g id=\\\"icon-record_stop_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-record_stop_icon-停止录制--normal\\\" transform=\\\"translate(0.169880, 0.000000)\\\">\\n            <path d=\\\"M15,1 C20.7989899,1 25.5,5.70101013 25.5,11.5 C25.5,17.2989899 20.7989899,22 15,22 C9.20101013,22 4.5,17.2989899 4.5,11.5 C4.5,5.70101013 9.20101013,1 15,1 Z M15,3 C10.3055796,3 6.5,6.80557963 6.5,11.5 C6.5,16.1944204 10.3055796,20 15,20 C19.6944204,20 23.5,16.1944204 23.5,11.5 C23.5,6.80557963 19.6944204,3 15,3 Z\\\" id=\\\"icon-record_stop_椭圆形备份-6\\\" fill-rule=\\\"nonzero\\\" />\\n            <rect id=\\\"icon-record_stop_矩形\\\" fill=\\\"#FF6666\\\" x=\\\"10.5\\\" y=\\\"7\\\" width=\\\"9\\\" height=\\\"9\\\" rx=\\\"2\\\" />\\n            <rect id=\\\"icon-record_stop_矩形\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-icon_device_volume\",\n  \"use\": \"icon-icon_device_volume-usage\",\n  \"viewBox\": \"0 0 20 16\",\n  \"content\": \"<symbol viewBox=\\\"0 0 20 16\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-icon_device_volume\\\">\\n<path fill-rule=\\\"evenodd\\\" clip-rule=\\\"evenodd\\\" d=\\\"M8.74541 1C9.26388 1 9.69073 1.35986 9.74534 1.82216L9.75124 1.9227V14.0773C9.75124 14.3076 9.65739 14.5295 9.48811 14.6995C9.14231 15.0469 8.57362 15.0961 8.16555 14.8314L8.0671 14.7586L3.89864 11.2667H0.92869C0.415789 11.2667 0 10.8488 0 10.3333V5.66667C0 5.1512 0.415789 4.73333 0.92869 4.73333H3.89864L8.0671 1.24138C8.22156 1.11198 8.41532 1.03062 8.62088 1.0071L8.74541 1ZM16.0554 1.66432C17.7162 3.33344 18.6667 5.5918 18.6667 8C18.6667 10.4082 17.7162 12.6666 16.0554 14.3357C15.8378 14.5544 15.485 14.5544 15.2674 14.3357C15.0498 14.117 15.0498 13.7624 15.2674 13.5437C16.7213 12.0826 17.5522 10.1081 17.5522 8C17.5522 5.8919 16.7213 3.91744 15.2674 2.45628C15.0498 2.23759 15.0498 1.88302 15.2674 1.66432C15.485 1.44563 15.8378 1.44563 16.0554 1.66432ZM12.9372 4.56459C13.1059 4.78315 13.2584 5.01396 13.3934 5.25515C13.8585 6.08651 14.1064 7.0257 14.1064 8C14.1064 8.9743 13.8585 9.91349 13.3934 10.7449C13.2584 10.986 13.1059 11.2169 12.9372 11.4354C12.7799 11.639 12.4882 11.676 12.2856 11.518C12.083 11.36 12.0462 11.0668 12.2035 10.8632C12.3442 10.6809 12.4714 10.4885 12.5839 10.2874C12.9713 9.59485 13.1777 8.81303 13.1777 8C13.1777 7.18697 12.9713 6.40515 12.5839 5.71256C12.4714 5.51152 12.3442 5.31906 12.2035 5.13678C12.0462 4.93316 12.083 4.64 12.2856 4.482C12.4882 4.32399 12.7799 4.36097 12.9372 4.56459Z\\\" fill=\\\"#393946\\\" fill-opacity=\\\"0.4\\\" />\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "/**\n * import element ui lib\n */\n\nimport Vue from 'vue';\nimport Element from 'element-ui';\nimport VueClipboard from 'vue-clipboard2';\nimport '../assets/style/element-variables.scss';\n\nVue.use(Element);\n\nVue.use(VueClipboard);\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('div',{staticClass:\"container\",attrs:{\"id\":\"container\"}},[((!_vm.loginMeeting && _vm.loginMeeting !== null) && !_vm.callMeeting && !_vm.callLoading)?_c('div',{staticClass:\"loginMeetingBody\"},[_c('div',{staticClass:\"loginMeeting\"},[_vm._v(\"未检测到视频会议信息\")]),_c('div',{staticClass:\"stop-btn\",on:{\"click\":_vm.handleClose}},[_vm._v(\"关闭\")])]):_vm._e(),((_vm.loginMeeting && _vm.loginMeeting !== null) && !_vm.callMeeting && !_vm.callLoading)?_c('Login',{attrs:{\"user\":_vm.user,\"isThird\":_vm.setting.isThird},on:{\"submitForm\":_vm.submitForm,\"onToggleSetting\":_vm.onToggleSetting}}):_vm._e(),(_vm.callMeeting && _vm.callLoading)?_c('Loading',{attrs:{\"conferenceInfo\":_vm.conferenceInfo,\"audioOutputValue\":_vm.selectedDevice.audioOutput.deviceId},on:{\"stop\":_vm.stop}}):_vm._e(),(_vm.callMeeting && !_vm.callLoading)?_c('div',{staticClass:\"meeting\"},[_c('MeetingHeader',{attrs:{\"className\":_vm.toolVisible ? 'xy__show' : 'xy__hide',\"conferenceInfo\":_vm.conferenceInfo},on:{\"switchDebug\":_vm.switchDebug,\"stop\":_vm.stop,\"flipCamera\":_vm.handleFlipCamera}}),_c('PromptInfo',{attrs:{\"toolVisible\":_vm.toolVisible,\"forceLayoutId\":_vm.forceLayoutId,\"chairman\":_vm.chairman.hasChairman,\"content\":_vm.content,\"localHide\":_vm.setting.localHide,\"isLocalShareContent\":_vm.isLocalShareContent,\"recordStatus\":_vm.recordStatus},on:{\"forceFullScreen\":_vm.forceFullScreen}}),_c('div',{staticClass:\"meeting-content\",attrs:{\"id\":\"meeting\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.handleToolVisible.apply(null, arguments)}}},[(_vm.pageStatus.previous && _vm.toolVisible)?_c('div',{staticClass:\"previous-box\"},[_c('div',{staticClass:\"previous-button\",on:{\"click\":function($event){$event.stopPropagation();return _vm.switchPage('previous')}}},[_c('svg-icon',{attrs:{\"icon\":\"previous\"}})],1),(_vm.pageInfo.currentPage > 1)?_c('div',{staticClass:\"home-button\",on:{\"click\":function($event){$event.stopPropagation();return _vm.switchPage('home')}}},[_vm._v(\"回首页\")]):_vm._e()]):_vm._e(),(_vm.pageStatus.next && _vm.toolVisible)?_c('div',{staticClass:\"next-box\"},[_c('div',{staticClass:\"next-button\",on:{\"click\":function($event){$event.stopPropagation();return _vm.switchPage('next')}}},[_c('svg-icon',{attrs:{\"icon\":\"next\"}}),(_vm.pageInfo.totalPage > 1 && _vm.pageInfo.currentPage > 0)?_c('div',{staticClass:\"page-number\"},[_vm._v(\" \"+_vm._s(_vm.pageInfo.currentPage)+\" / \"+_vm._s(_vm.pageInfo.totalPage > 100 ? '...' : _vm.pageInfo.totalPage)+\" \")]):_vm._e()],1)]):_vm._e(),_c('div',{staticClass:\"meeting-layout\",style:(_vm.layoutStyle)},_vm._l((_vm.layout),function(item,index){return _c('Video',{key:item.roster.id,attrs:{\"id\":item.roster.id,\"index\":index,\"model\":_vm.templateMode,\"layoutMode\":_vm.setting.layoutMode,\"item\":item,\"forceLayoutId\":_vm.forceLayoutId,\"client\":_vm.client},on:{\"forceFullScreen\":_vm.forceFullScreen}})}),1),_c('div',{staticClass:\"audio-list\"},_vm._l((_vm.audioList),function(item){return _c('Audio',{key:item.data.streams[0].id,attrs:{\"muted\":item.status === 'local',\"streamId\":item.data.streams[0].id,\"client\":_vm.client}})}),1),(!_vm.onhold && _vm.subTitle.content && _vm.subTitle.action === 'push')?_c('Barrage',{attrs:{\"subTitle\":_vm.subTitle}}):_vm._e(),(!_vm.onhold)?_c('InOutReminder',{attrs:{\"reminders\":_vm.reminders}}):_vm._e()],1),_c('div',{class:_vm.toolVisible ? 'meeting-footer xy__show' : 'meeting-footer xy__hide'},[_c('div',{staticClass:\"middle\"},[_c('div',{class:['button host', { 'disabled-button': _vm.conferenceInfo.numberType === 'APP' }],on:{\"click\":function($event){_vm.participantVisible = true}}},[_c('svg-icon',{attrs:{\"icon\":\"meeting_host\"}}),_c('div',{staticClass:\"title\"},[_vm._v(\"参会者\")]),_c('div',{staticClass:\"tag\"},[_vm._v(_vm._s(_vm.participantsCount))])],1),(_vm.isPc)?_c('div',{staticClass:\"button layout\",on:{\"click\":_vm.switchLayout}},[_c('svg-icon',{attrs:{\"icon\":\"layout\"}}),_c('div',{staticClass:\"title\"},[_vm._v(\"窗口布局\")])],1):_vm._e(),(_vm.isPc && _vm.isLocalShareContent)?_c('div',{staticClass:\"button button-warn share-stop\",on:{\"click\":_vm.stopShareContent}},[_c('svg-icon',{attrs:{\"icon\":\"share_stop\",\"type\":\"danger\"}}),_c('div',{staticClass:\"title\"},[_vm._v(\"结束共享\")])],1):_vm._e(),(_vm.isPc && !_vm.isLocalShareContent)?_c('div',{class:['button share', { 'disabled-button': _vm.contentIsDisabled }],on:{\"click\":_vm.shareContent}},[_c('svg-icon',{attrs:{\"icon\":\"share\"}}),_c('div',{staticClass:\"title\"},[_vm._v(\"共享\")])],1):_vm._e(),_c('div',{class:['button share', { 'disabled-button': _vm.disableRecord }],on:{\"click\":_vm.toggleRecord}},[_c('svg-icon',{attrs:{\"icon\":_vm.recordStatus === 0 ? 'record' : 'record_stop'}}),_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.recordStatus === 1 ? '停止录制' : '开始录制'))])],1),(_vm.isPc)?_c('div',{staticClass:\"line\"}):_vm._e(),_c('AudioButton',{attrs:{\"permission\":_vm.permission,\"audio\":_vm.audio,\"disableAudio\":_vm.disableAudio,\"handStatus\":_vm.handStatus,\"stream\":_vm.stream},on:{\"audioOperate\":_vm.audioOperate}}),_c('VideoButton',{attrs:{\"permission\":_vm.permission,\"video\":_vm.video},on:{\"videoOperate\":_vm.videoOperate}}),_c('EndCall',{on:{\"stop\":_vm.stop}})],1)]),(_vm.participantVisible)?_c('Participant',{attrs:{\"client\":_vm.client,\"content\":_vm.content,\"rosters\":_vm.rosters,\"count\":_vm.participantsCount},on:{\"showParticipant\":function($event){_vm.participantVisible = false}}}):_vm._e(),(_vm.debug)?_c('Internels',{attrs:{\"senderStatus\":_vm.senderStatus},on:{\"switchDebug\":_vm.switchDebug}}):_vm._e()],1):_vm._e(),(_vm.settingVisible)?_c('Setting',{attrs:{\"setting\":{ ..._vm.setting, selectedDevice: _vm.selectedDevice },\"visible\":_vm.settingVisible},on:{\"cancel\":_vm.onToggleSetting,\"setting\":_vm.onSaveSetting}}):_vm._e()],1)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"login\"},[_c('div',{staticClass:\"login-container\"},[_c('div',{staticClass:\"login-content\"},[_c('div',{staticClass:\"login-title\"},[_vm._v(\"加入会议\")]),_c('el-form',{ref:\"loginForm\",staticClass:\"login-form\",attrs:{\"model\":_vm.loginForm,\"status-icon\":\"\",\"rules\":_vm.rules,\"label-position\":_vm.labelPosition}},[(!_vm.isThird)?_c('el-form-item',{attrs:{\"prop\":\"phone\",\"rules\":{ required: true, message: '请输入小鱼账号', trigger: 'blur', }}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"placeholder\":\"输入小鱼账号\"},model:{value:(_vm.loginForm.phone),callback:function ($$v) {_vm.$set(_vm.loginForm, \"phone\", $$v)},expression:\"loginForm.phone\"}})],1):_vm._e(),(!_vm.isThird)?_c('el-form-item',{attrs:{\"prop\":\"password\",\"rules\":{ required: true, message: '请输入账号密码', trigger: 'blur', }}},[_c('el-input',{attrs:{\"type\":\"password\",\"autocomplete\":\"off\",\"placeholder\":\"输入账号密码\"},model:{value:(_vm.loginForm.password),callback:function ($$v) {_vm.$set(_vm.loginForm, \"password\", $$v)},expression:\"loginForm.password\"}})],1):_vm._e(),_c('el-form-item',{attrs:{\"prop\":\"meeting\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"placeholder\":\"输入云会议室号或终端号\"},model:{value:(_vm.loginForm.meeting),callback:function ($$v) {_vm.$set(_vm.loginForm, \"meeting\", $$v)},expression:\"loginForm.meeting\"}})],1),_c('el-form-item',{attrs:{\"prop\":\"meetingPassword\"}},[_c('el-input',{attrs:{\"type\":\"password\",\"autocomplete\":\"off\",\"placeholder\":\"入会密码\"},model:{value:(_vm.loginForm.meetingPassword),callback:function ($$v) {_vm.$set(_vm.loginForm, \"meetingPassword\", $$v)},expression:\"loginForm.meetingPassword\"}})],1),_c('el-form-item',{attrs:{\"prop\":\"meetingName\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"placeholder\":\"输入会议中显示的名称\"},model:{value:(_vm.loginForm.meetingName),callback:function ($$v) {_vm.$set(_vm.loginForm, \"meetingName\", $$v)},expression:\"loginForm.meetingName\"}})],1),_c('el-button',{staticClass:\"join-btn\",attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.submitForm('loginForm')}}},[_vm._v(\"加入会议\")]),_c('el-form-item',{staticClass:\"login-form-checkbox\"},[_c('el-checkbox',{model:{value:(_vm.loginForm.muteVideo),callback:function ($$v) {_vm.$set(_vm.loginForm, \"muteVideo\", $$v)},expression:\"loginForm.muteVideo\"}},[_vm._v(\"入会时关闭摄像头\")])],1),_c('el-form-item',{staticClass:\"login-form-checkbox\"},[_c('el-checkbox',{model:{value:(_vm.loginForm.muteAudio),callback:function ($$v) {_vm.$set(_vm.loginForm, \"muteAudio\", $$v)},expression:\"loginForm.muteAudio\"}},[_vm._v(\"入会时静音\")])],1)],1)],1)])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "/**\n * Store LocalStorage\n * @authors <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)\n * @date  2019-11-01 14:36:12\n */\n\nconst store = {\n  storage: window.localStorage,\n  set(key, value) {\n    if (!value) return;\n\n    let data = serialize(value) || \"\";\n\n    this.storage.setItem(key, data);\n  },\n  get(key) {\n    if (!key) return;\n\n    const val = this.storage.getItem(key);\n    let newVal = val || \"\";\n\n    try {\n      newVal = JSON.parse(newVal);\n    } catch (err) {\n      newVal = val || \"\";\n    }\n\n    return newVal;\n  },\n  remove(key) {\n    if (!key) return;\n\n    this.storage.removeItem(key);\n  },\n  clear() {\n    this.storage.clear();\n  },\n  // 批量删除 array => key 数组\n  removeList(array) {\n    for (let key of array) {\n      this.storage.removeItem(key);\n    }\n  },\n};\n\nconst serialize = function(value) {\n  if (!value) return;\n\n  let val = \"\";\n  const type = Object.prototype.toString.call(value);\n  if (type === \"[object Object]\" || type === \"[object Array]\") {\n    val = JSON.stringify(value);\n  } else {\n    val = value;\n  }\n\n  return val;\n};\n\nexport default store;\n", "<template>\n  <div class=\"login\">\n    <div class=\"login-container\">\n      <div class=\"login-content\">\n        <div class=\"login-title\">加入会议</div>\n        <el-form :model=\"loginForm\" status-icon :rules=\"rules\" :label-position=\"labelPosition\" ref=\"loginForm\"\n          class=\"login-form\">\n          <el-form-item v-if=\"!isThird\" prop=\"phone\" :rules=\"{ required: true, message: '请输入小鱼账号', trigger: 'blur', }\">\n            <el-input v-model=\"loginForm.phone\" autocomplete=\"off\" placeholder=\"输入小鱼账号\"></el-input>\n          </el-form-item>\n\n          <el-form-item v-if=\"!isThird\" prop=\"password\"\n            :rules=\"{ required: true, message: '请输入账号密码', trigger: 'blur', }\">\n            <el-input type=\"password\" v-model=\"loginForm.password\" autocomplete=\"off\" placeholder=\"输入账号密码\"></el-input>\n          </el-form-item>\n\n          <el-form-item prop=\"meeting\">\n            <el-input v-model=\"loginForm.meeting\" autocomplete=\"off\" placeholder=\"输入云会议室号或终端号\"></el-input>\n          </el-form-item>\n\n          <el-form-item prop=\"meetingPassword\">\n            <el-input type=\"password\" v-model=\"loginForm.meetingPassword\" autocomplete=\"off\"\n              placeholder=\"入会密码\"></el-input>\n          </el-form-item>\n\n          <el-form-item prop=\"meetingName\">\n            <el-input v-model=\"loginForm.meetingName\" autocomplete=\"off\" placeholder=\"输入会议中显示的名称\"></el-input>\n          </el-form-item>\n\n          <el-button class=\"join-btn\" type=\"primary\" @click=\"submitForm('loginForm')\">加入会议</el-button>\n          <el-form-item class=\"login-form-checkbox\">\n            <el-checkbox v-model=\"loginForm.muteVideo\">入会时关闭摄像头</el-checkbox>\n          </el-form-item>\n          <el-form-item class=\"login-form-checkbox\">\n            <el-checkbox v-model=\"loginForm.muteAudio\">入会时静音</el-checkbox>\n          </el-form-item>\n        </el-form>\n        <!-- <div class=\"setting-btn\">\n          <span @click=\"onOpenSetting\"> 设置 <i class=\"el-icon-setting\"></i> </span>\n        </div> -->\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport store from '@/utils/store';\nimport xyRTC from '@xylink/xy-rtc-sdk';\n\nexport default {\n  props: ['user', 'isThird'],\n  computed: {},\n  data () {\n    return {\n      xyRTC,\n      labelPosition: 'right',\n      loginForm: this.user,\n      rules: {\n        meeting: [{ required: true, message: '请输入会议号', trigger: 'blur' }],\n        meetingPassword: [{ trigger: 'blur' }],\n        meetingName: [{ required: true, message: '请输入入会昵称', trigger: 'blur' }],\n      },\n    };\n  },\n  methods: {\n    submitForm (formName) {\n      this.$refs[formName].validate((valid) => {\n        if (!valid) {\n          return false;\n        }\n\n        this.$emit('submitForm', this.loginForm);\n      });\n    },\n\n    onOpenSetting () {\n      this.$emit('onToggleSetting');\n    },\n  },\n  watch: {\n    loginForm: {\n      handler (newValue) {\n        store.set('xy-user', newValue);\n      },\n      deep: true,\n    },\n  },\n};\n</script>\n<style lang=\"scss\">\n@import './index.scss';\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=3b44c641\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3b44c641&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"loading\"},[_c('div',{staticClass:\"loading-content\"},[_vm._m(0),_c('div',{staticClass:\"name\"},[_c('div',{staticClass:\"calling\"},[_vm._v(\"正在呼叫\")]),_c('div',{staticClass:\"text\"},[_vm._v(_vm._s(_vm.conferenceInfo && _vm.conferenceInfo.displayName))])]),_c('div',{staticClass:\"stop-btn\",on:{\"click\":function($event){return _vm.stop('OK')}}},[_c('svg-icon',{attrs:{\"icon\":\"hang_up\"}})],1),_c('audio',{ref:\"bgmAudioRef\",attrs:{\"autoPlay\":\"\",\"loop\":\"\",\"src\":require(\"@/assets/ring.wav\")}})])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"avatar\"},[_c('img',{attrs:{\"src\":require(\"@/assets/img/confernece.png\"),\"alt\":\"nemo-avatar\"}})])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"loading\">\n    <div class=\"loading-content\">\n      <div class=\"avatar\">\n        <img src=\"@/assets/img/confernece.png\" alt=\"nemo-avatar\" />\n      </div>\n      <div class=\"name\">\n        <div class=\"calling\">正在呼叫</div>\n        <div class=\"text\">{{ conferenceInfo && conferenceInfo.displayName }}</div>\n      </div>\n      <div class=\"stop-btn\" @click=\"stop('OK')\">\n        <svg-icon icon=\"hang_up\" />\n      </div>\n\n      <audio ref=\"bgmAudioRef\" autoPlay loop src=\"@/assets/ring.wav\"></audio>\n    </div>\n  </div>\n</template>\n<script>\nimport xyRTC from \"@xylink/xy-rtc-sdk\";\nexport default {\n  props: [\"conferenceInfo\", \"audioOutputValue\"],\n  async mounted() {\n    const bgmAudioEle = this.$refs[\"bgmAudioRef\"];\n\n    xyRTC.setOutputAudioDevice(bgmAudioEle, this.audioOutputValue || \"default\");\n\n    if (bgmAudioEle.paused) {\n      try {\n        await bgmAudioEle.play();\n      } catch (error) {\n        console.log(\"bgmAudio play error:\", error);\n      }\n    }\n  },\n  beforeDestroy() {\n    const bgmAudioEle = this.$refs[\"bgmAudioRef\"];\n\n    bgmAudioEle.pause();\n  },\n  methods: {\n    stop() {\n      this.$emit(\"stop\");\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n@import \"./index.scss\";\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=6cb1a7ca&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6cb1a7ca&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6cb1a7ca\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['meeting-header', _vm.className]},[_c('span',{staticClass:\"header-time\",on:{\"click\":_vm.switchDebug}},[_c('svg-icon',{staticClass:\"meeting-stats-switch\",attrs:{\"icon\":\"signal\"}}),_c('Timmer')],1),_c('div',{staticClass:\"header-count\"},[_c('span',{staticClass:\"header-name\"},[_vm._v(\" \"+_vm._s(_vm.conferenceInfo.displayName)+\" \"),(_vm.conferenceInfo.numberType !== 'CONFERENCE')?_c('span',[_vm._v(\"(\"+_vm._s(_vm.conferenceInfo.number)+\") \")]):_vm._e()]),(!_vm.isPc)?_c('div',{staticClass:\"meeting-info-btn\",on:{\"click\":function($event){_vm.infoVisible = true}}},[_c('i',{staticClass:\"el-icon-warning-outline\"})]):_vm._e(),(_vm.isPc)?_c('el-popover',{attrs:{\"popper-class\":\"meeting-popover\",\"placement\":\"top\",\"title\":\"\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticClass:\"meeting-info-name\",attrs:{\"title\":_vm.conferenceInfo.displayName}},[_vm._v(\" \"+_vm._s(_vm.conferenceInfo.displayName)+\" \")]),_c('div',{staticClass:\"meeting-info-number\"},[_vm._v(\" 会议号\"),_c('span',{staticClass:\"number\"},[_vm._v(_vm._s(_vm.conferenceInfo.number))]),_c('span',{staticClass:\"copy-btn\",on:{\"click\":_vm.copyNumber}})])]),_c('div',{staticClass:\"meeting-info-btn\",attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_c('i',{staticClass:\"el-icon-warning-outline\"})])]):_vm._e()],1),_c('el-drawer',{attrs:{\"visible\":_vm.infoVisible,\"direction\":\"btt\",\"before-close\":_vm.cancel,\"append-to-body\":true,\"withHeader\":false,\"custom-class\":\"xy__drawer-conference\"},on:{\"update:visible\":function($event){_vm.infoVisible=$event}}},[_c('div',{staticClass:\"meeting-info-name\",attrs:{\"title\":_vm.conferenceInfo.displayName}},[_vm._v(\" \"+_vm._s(_vm.conferenceInfo.displayName)+\" \")]),_c('div',{staticClass:\"meeting-info-number\"},[_vm._v(\" 会议号\"),_c('span',{staticClass:\"number\"},[_vm._v(_vm._s(_vm.conferenceInfo.number))]),_c('span',{staticClass:\"copy-btn\",on:{\"click\":_vm.copyNumber}})])]),(!_vm.isPc)?_c('div',{staticClass:\"FlipCamera\",on:{\"click\":_vm.handleFlipCamera}},[_c('svg',{staticClass:\"icon\",attrs:{\"t\":\"1727337807530\",\"viewBox\":\"0 0 1024 1024\",\"version\":\"1.1\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"p-id\":\"2331\",\"width\":\"26\",\"height\":\"26\"}},[_c('path',{attrs:{\"d\":\"M914.510769 258.835692H709.671385L665.875692 172.898462c-10.24-20.125538-36.982154-36.371692-59.746461-36.371693H400.797538c-22.784 0-49.427692 16.443077-59.608615 36.706462l-42.850461 85.602461H92.416c-22.724923 0-41.216 18.176-41.216 40.605539v530.353231c0 22.291692 18.451692 40.605538 41.216 40.605538H914.510769c22.744615 0 41.216-18.176 41.216-40.605538V299.441231c0-22.291692-18.451692-40.605538-41.216-40.605539z m-411.037538 509.636923c-113.526154 0-205.587692-91.254154-205.587693-203.854769 0-32.649846 7.739077-63.488 21.504-90.840615a30.936615 30.936615 0 0 1 52.125539-15.970462l43.638154 43.264a30.326154 30.326154 0 0 1-0.019693 43.244308 30.916923 30.916923 0 0 1-43.579076 0l-8.979693-8.861539c-1.988923 9.393231-3.012923 19.180308-3.012923 29.164308 0 78.808615 64.413538 142.710154 143.911385 142.710154 22.055385 0 42.948923-4.923077 61.636923-13.725538l45.430154 45.056a205.922462 205.922462 0 0 1-107.067077 29.814153z m167.305846-85.405538l-0.078769-0.078769a30.483692 30.483692 0 0 1-6.833231 10.121846 30.916923 30.916923 0 0 1-43.559385 0.039384L576.649846 649.846154a30.326154 30.326154 0 0 1 0-43.244308 30.956308 30.956308 0 0 1 43.618462 0l15.143384 15.025231a140.8 140.8 0 0 0 11.953231-57.028923c0-78.808615-64.413538-142.690462-143.891692-142.690462a144.088615 144.088615 0 0 0-74.988308 20.873846l-44.504615-44.110769a205.902769 205.902769 0 0 1 119.492923-37.927384c113.545846 0 205.587692 91.273846 205.587692 203.854769 0 44.169846-14.178462 85.070769-38.281846 118.449231z\",\"fill\":\"#ffffff\",\"p-id\":\"2332\"}})])]):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('span',[_vm._v(\" \"+_vm._s(_vm.timmer)+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <span>\n    {{ timmer }}\n  </span>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      timmer: 0,\n      timerCount: 0,\n      meetingTimeout: null,\n    };\n  },\n  mounted() {\n    this.onCreateMeetingTimeCount();\n  },\n  beforeDestroy() {\n    clearTimeout(this.meetingTimeout);\n    this.meetingTimeout = null;\n  },\n  methods: {\n    secondToDate(result) {\n      var h =\n        Math.floor(result / 3600) < 10\n          ? \"0\" + Math.floor(result / 3600)\n          : Math.floor(result / 3600);\n      var m =\n        Math.floor((result / 60) % 60) < 10\n          ? \"0\" + Math.floor((result / 60) % 60)\n          : Math.floor((result / 60) % 60);\n      var s =\n        Math.floor(result % 60) < 10\n          ? \"0\" + Math.floor(result % 60)\n          : Math.floor(result % 60);\n      return h + \":\" + m + \":\" + s;\n    },\n    onCreateMeetingTimeCount() {\n      this.timerCount++;\n\n      this.meetingTimeout = setTimeout(() => {\n        clearTimeout(this.meetingTimeout);\n        this.meetingTimeout = null;\n\n        const meetingTime = this.secondToDate(this.timerCount);\n\n        this.timmer = meetingTime;\n\n        this.onCreateMeetingTimeCount();\n      }, 1000);\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=5e7d47b4\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "/**\n * Browser version tools\n *\n * @authors <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)\n * @date  2020-02-21 16:29:12\n */\n\nexport const platform = () => {\n  const ua = navigator.userAgent;\n  const lowerUa = ua.toLocaleLowerCase();\n  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0 || /macintosh|mac os x/i.test(ua);\n  const isLinux = navigator.platform.toUpperCase().indexOf('LINUX') >= 0;\n  const isWindowsPhone = /(?:Windows Phone)/.test(ua);\n  const isWindows = /windows|win32|win64/i.test(ua);\n  const isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone;\n  const isAndroid = /(?:Android)/.test(ua) || lowerUa.indexOf('harmony') > -1;\n  const isFireFox = /(?:Firefox)/.test(ua);\n  const isChrome = /(?:Chrome|CriOS)/.test(ua) && !(ua.indexOf('Edg') > -1);\n  const isEdge = ua.indexOf('Edg') > -1;\n  const isSafari = ua.indexOf('Safari') > -1 && !(ua.indexOf('Chrome') > -1);\n  const isMacOrPad = navigator.userAgent.toUpperCase().indexOf('MAC') >= 0 || /macintosh|mac os x/i.test(ua);\n  const isIPad = /iPad/i.test(navigator.userAgent) || (isMacOrPad && navigator.maxTouchPoints > 1);\n  const isTablet =\n    /(?:iPad|PlayBook)/.test(ua) || (isAndroid && !/(?:Mobile)/.test(ua)) || (isFireFox && /(?:Tablet)/.test(ua));\n  const isPhone = /(?:iPhone)/.test(ua) && !isTablet;\n  const isIOS = isPhone || isIPad;\n  const isPc = !isPhone && !isAndroid && !isSymbian && !isIPad;\n  const isBaidu = ua.indexOf('Baidu') > -1 || ua.indexOf('BIDUBrowser') > -1 || ua.indexOf('baiduboxapp') > -1;\n  const isFirefox = !!lowerUa.match(/firefox/) || ua.indexOf('Firefox') > -1;\n  const isQQ =\n    !!lowerUa.match(/tencenttraveler/) ||\n    !!lowerUa.match(/qqbrowser/) ||\n    ua.indexOf('QQ/') > -1 ||\n    ua.indexOf('MicroMessenger') > -1;\n  // PC微信浏览器\n  const isWindowsWechat =\n    ua.indexOf('MicroMessenger') > -1 &&\n    lowerUa.indexOf('wxwork') <= -1 &&\n    lowerUa.indexOf('windowswechat') > -1 &&\n    // 排除mac客户端\n    lowerUa.indexOf('macintosh') <= -1;\n  // PC企业微信浏览器\n  const isWindowsWxWechat =\n    ua.indexOf('MicroMessenger') > -1 &&\n    lowerUa.indexOf('wxwork') > -1 &&\n    lowerUa.indexOf('windowswechat') > -1 &&\n    // 排除mac客户端\n    lowerUa.indexOf('macintosh') <= -1;\n  // 移动端微信浏览器\n  const isWeixin =\n    ua.indexOf('MicroMessenger') > -1 && lowerUa.indexOf('wxwork') <= -1 && lowerUa.indexOf('windowswechat') <= -1;\n  const isMiniProgram = ua.indexOf('miniProgram') > -1;\n  const isOpera = ua.indexOf('Opera') > -1 || ua.indexOf('OPR') > -1;\n  const iswxwork = lowerUa.indexOf('wxwork') > -1 && lowerUa.indexOf('micromessenger') > -1;\n  const isDingDing = lowerUa.indexOf('dingtalk') > -1;\n  const isZhazha =\n    ua.indexOf('QihooBrowser') > -1 ||\n    ua.indexOf('QHBrowser') > -1 ||\n    ua.indexOf('360SE') > -1 ||\n    ua.indexOf('360EE') > -1 ||\n    ua.indexOf('LBBROWSER') > -1 ||\n    ua.indexOf('UC') > -1 ||\n    ua.indexOf('UBrowser') > -1 ||\n    ua.indexOf('UCWEB') > -1 ||\n    ua.indexOf('Quark') > -1 ||\n    ua.indexOf('MiuiBrowser') > -1 ||\n    ua.indexOf('AliApp(TB') > -1 ||\n    ua.indexOf('Weibo') > -1 ||\n    ua.indexOf('IqiyiApp') > -1 ||\n    ua.indexOf('HuaweiBrowser') > -1 ||\n    ua.indexOf('HUAWEI') > -1 ||\n    ua.indexOf('MetaSr') > -1 ||\n    ua.indexOf('Sogou') > -1 ||\n    ua.indexOf('SE') > -1 ||\n    ua.indexOf('Maxthon') > -1 ||\n    ua.indexOf('The world') > -1 ||\n    ua.indexOf('MetaSr') > -1 ||\n    ua.indexOf('2345Explorer') > -1 ||\n    ua.indexOf('TencentTraveler') > -1 ||\n    ua.indexOf('Mb2345Browser') > -1;\n\n  return {\n    isTablet,\n    isPhone,\n    isAndroid,\n    isPc,\n    isMac,\n    isChrome,\n    isLinux,\n    isFirefox,\n    isBaidu,\n    isQQ,\n    isOpera,\n    isZhazha,\n    isEdge,\n    isSafari,\n    isIPad,\n    isWeixin,\n    iswxwork,\n    isDingDing,\n    isWindowsWechat,\n    isWindowsWxWechat,\n    isWindows,\n    isIOS,\n    isMiniProgram,\n  };\n};\n\nexport const getChromeVersion = () => {\n  let pieces = navigator.userAgent.match(/Chrom(?:e|ium)\\/([0-9]+)\\.([0-9]+)\\.([0-9]+)\\.([0-9]+)/);\n  if (pieces == null || pieces.length !== 5) {\n    return undefined;\n  }\n\n  pieces = pieces.map((piece) => parseInt(piece, 10));\n\n  return {\n    major: pieces[1],\n    minor: pieces[2],\n    build: pieces[3],\n    patch: pieces[4],\n  };\n};\n\nexport const getEdgeVersion = () => {\n  let pieces = navigator.userAgent.match(/Ed(?:g)\\/([0-9]+)\\.([0-9]+)\\.([0-9]+)/);\n  if (pieces == null || pieces.length < 4) {\n    return undefined;\n  }\n\n  pieces = pieces.map((piece) => parseInt(piece, 10));\n  return {\n    major: pieces[1],\n    minor: pieces[2],\n    build: pieces[3],\n  };\n};\n\nexport const getSafariVersion = () => {\n  let pieces = navigator.userAgent.match(/Version\\/([0-9]+)\\.([0-9]+)/);\n  if (pieces === null || pieces.length < 3) {\n    return undefined;\n  }\n\n  pieces = pieces.map((piece) => parseInt(piece, 10));\n  return {\n    major: pieces[1],\n    minor: pieces[2],\n  };\n};\n\nexport const getWeixinVersion = () => {\n  const { isWeixin } = platform();\n\n  if (isWeixin) {\n    const weixinInfo = navigator.userAgent.match(/MicroMessenger\\/([\\d.]+)/i) || ['', ''];\n    const weixinVersion = weixinInfo[1].split('.');\n\n    if (weixinVersion.length) {\n      return {\n        major: Number(weixinVersion[0]),\n        minor: Number(weixinVersion[1]),\n        build: Number(weixinVersion[2]),\n      };\n    }\n\n    return {};\n  } else {\n    return {};\n  }\n};\n\nexport const getOperationSystem = () => {\n  const { isIPad, isMac, isWindowsWechat, isWindowsWxWechat, isWindows, isPhone, isAndroid, isLinux, isMiniProgram } =\n    platform();\n\n  if (isWindowsWxWechat) {\n    return 'Windows WXWechat';\n  } else if (isWindowsWechat) {\n    return 'Windows Wechat';\n  } else if (isWindows) {\n    return 'Windows';\n  } else if (isPhone) {\n    return 'iPhone';\n  } else if (isAndroid) {\n    return 'Android';\n  } else if (isIPad) {\n    return 'iPad';\n  } else if (isMac) {\n    return 'Mac';\n  } else if (isLinux) {\n    return 'Linux';\n  } else if (isMiniProgram) {\n    return 'MiniProgram';\n  }\n\n  return '';\n};\n\nexport const getBrowserName = () => {\n  const { isFirefox, isChrome, isSafari, isWindowsWechat, isWindowsWxWechat, isZhazha, isEdge, isWeixin, iswxwork } =\n    platform();\n\n  if (isFirefox) {\n    return 'Firefox';\n  } else if (isSafari) {\n    return 'Safari';\n  } else if (isWindowsWechat) {\n    return 'WindowsWechat';\n  } else if (isWindowsWxWechat) {\n    return 'WindowsWxWechat';\n  } else if (isWeixin) {\n    return 'Weixin';\n  } else if (iswxwork) {\n    return 'WXWeixin';\n  } else if (isWeixin) {\n    return 'Weixin';\n  } else if (isEdge) {\n    return 'Edge';\n  } else if (isChrome) {\n    return 'Chrome';\n  } else if (isZhazha) {\n    return 'Other';\n  }\n\n  return '';\n};\n\nexport const getAndroidVersion = () => {\n  let pieces = navigator.userAgent.toLowerCase().match(/android (.*?);/);\n\n  if (pieces) {\n    let versions = pieces[1].split('.');\n\n    versions = versions.map((v) => parseInt(v, 10));\n    return {\n      major: versions[0],\n      minor: versions[1],\n    };\n  }\n  return undefined;\n};\n\nexport const getIOSVersion = () => {\n  let pieces = navigator.userAgent.toLowerCase().match(/cpu( iphone)? os (.*?) like mac os/);\n\n  if (pieces) {\n    let versions = pieces[2].split('_');\n\n    versions = versions.map((v) => parseInt(v, 10));\n\n    return {\n      major: versions[0],\n      minor: versions[1],\n    };\n  }\n  return undefined;\n};\n\nexport const { isPc } = platform();\n\n/**\n * ios 12.2+ safari\n * ios 14.3+ 微信浏览器\n *  android 8+ 微信浏览器 QQ浏览器等\n */\nexport const isSupportMobileJoinMeeting = () => {\n  const { isAndroid, isPhone, isIPad, isSafari, iswxwork } = platform();\n  let isMinBrowser = false;\n  const browserName = getBrowserName();\n  const isWeixin = browserName === 'Weixin';\n\n  if (isAndroid) {\n    const { major } = getAndroidVersion() || { major: 0, minor: 0 };\n\n    isMinBrowser = major >= 8;\n  } else if (isPhone || isIPad) {\n    const { major, minor } = getIOSVersion() || { major: 0, minor: 0 };\n\n    if (major > 14 || (major === 14 && minor > 3)) {\n      isMinBrowser = isWeixin || isSafari;\n    } else if (major > 12 || (major === 12 && minor > 2)) {\n      isMinBrowser = isSafari;\n    }\n  }\n\n  isMinBrowser = isMinBrowser && !iswxwork;\n\n  return isMinBrowser;\n};", "/**\n * Tools lib\n * @authors <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)\n * @date  2020-04-28 17:26:40\n */\n\nimport { Message } from 'element-ui';\n\nexport const message = {\n  info: (message) => {\n    Message.info({ message, duration: 3000, center: true });\n  },\n};\n\nexport const transformTime = (timestamp = +new Date()) => {\n  if (timestamp) {\n    var time = new Date(timestamp);\n    var y = time.getFullYear(); //getFullYear方法以四位数字返回年份\n    var M = time.getMonth() + 1; // getMonth方法从 Date 对象返回月份 (0 ~ 11)，返回结果需要手动加一\n    var d = time.getDate(); // getDate方法从 Date 对象返回一个月中的某一天 (1 ~ 31)\n    var h = time.getHours(); // getHours方法返回 Date 对象的小时 (0 ~ 23)\n    var m = time.getMinutes(); // getMinutes方法返回 Date 对象的分钟 (0 ~ 59)\n    var s = time.getSeconds(); // getSeconds方法返回 Date 对象的秒数 (0 ~ 59)\n    return y + '-' + M + '-' + d + ' ' + h + ':' + m + ':' + s;\n  } else {\n    return '';\n  }\n};\n\nexport const getErrorMsg = (err) => {\n  return JSON.stringify(err, Object.getOwnPropertyNames(err), 2);\n};\n\nexport const TYPEOF = {\n  isNumber(num) {\n    return typeof num === 'number';\n  },\n  isObject(obj) {\n    return Object.prototype.toString.call(obj).toLocaleLowerCase() === '[object object]';\n  },\n  isArray(arr) {\n    return Array.isArray(arr);\n  },\n  isString(str) {\n    return typeof str === 'string';\n  },\n  isBoolean(str) {\n    return typeof str === 'boolean';\n  },\n};\n", "<template>\n  <div :class=\"['meeting-header', className]\">\n    <span class=\"header-time\" @click=\"switchDebug\">\n      <svg-icon icon=\"signal\" class=\"meeting-stats-switch\" />\n      <Timmer />\n    </span>\n    <div class=\"header-count\">\n      <span class=\"header-name\">\n        {{ conferenceInfo.displayName }}\n        <span v-if=\"conferenceInfo.numberType !== 'CONFERENCE'\">({{ conferenceInfo.number }}) </span>\n      </span>\n\n      <div v-if=\"!isPc\" class=\"meeting-info-btn\" @click=\"infoVisible = true\">\n        <i class=\"el-icon-warning-outline\"></i>\n      </div>\n\n      <el-popover v-if=\"isPc\" popper-class=\"meeting-popover\" placement=\"top\" title=\"\" trigger=\"hover\">\n        <div>\n          <div class=\"meeting-info-name\" :title=\"conferenceInfo.displayName\">\n            {{ conferenceInfo.displayName }}\n          </div>\n          <div class=\"meeting-info-number\">\n            会议号<span class=\"number\">{{ conferenceInfo.number }}</span>\n            <span class=\"copy-btn\" @click=\"copyNumber\" />\n          </div>\n        </div>\n        <div class=\"meeting-info-btn\" slot=\"reference\">\n          <i class=\"el-icon-warning-outline\"></i>\n        </div>\n      </el-popover>\n    </div>\n\n    <el-drawer :visible.sync=\"infoVisible\" direction=\"btt\" :before-close=\"cancel\" :append-to-body=\"true\"\n      :withHeader=\"false\" custom-class=\"xy__drawer-conference\">\n      <div class=\"meeting-info-name\" :title=\"conferenceInfo.displayName\">\n        {{ conferenceInfo.displayName }}\n      </div>\n      <div class=\"meeting-info-number\">\n        会议号<span class=\"number\">{{ conferenceInfo.number }}</span>\n        <span class=\"copy-btn\" @click=\"copyNumber\" />\n      </div>\n    </el-drawer>\n\n    <div class=\"FlipCamera\" @click=\"handleFlipCamera\" v-if=\"!isPc\">\n      <svg t=\"1727337807530\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\"\n        p-id=\"2331\" width=\"26\" height=\"26\">\n        <path\n          d=\"M914.510769 258.835692H709.671385L665.875692 172.898462c-10.24-20.125538-36.982154-36.371692-59.746461-36.371693H400.797538c-22.784 0-49.427692 16.443077-59.608615 36.706462l-42.850461 85.602461H92.416c-22.724923 0-41.216 18.176-41.216 40.605539v530.353231c0 22.291692 18.451692 40.605538 41.216 40.605538H914.510769c22.744615 0 41.216-18.176 41.216-40.605538V299.441231c0-22.291692-18.451692-40.605538-41.216-40.605539z m-411.037538 509.636923c-113.526154 0-205.587692-91.254154-205.587693-203.854769 0-32.649846 7.739077-63.488 21.504-90.840615a30.936615 30.936615 0 0 1 52.125539-15.970462l43.638154 43.264a30.326154 30.326154 0 0 1-0.019693 43.244308 30.916923 30.916923 0 0 1-43.579076 0l-8.979693-8.861539c-1.988923 9.393231-3.012923 19.180308-3.012923 29.164308 0 78.808615 64.413538 142.710154 143.911385 142.710154 22.055385 0 42.948923-4.923077 61.636923-13.725538l45.430154 45.056a205.922462 205.922462 0 0 1-107.067077 29.814153z m167.305846-85.405538l-0.078769-0.078769a30.483692 30.483692 0 0 1-6.833231 10.121846 30.916923 30.916923 0 0 1-43.559385 0.039384L576.649846 649.846154a30.326154 30.326154 0 0 1 0-43.244308 30.956308 30.956308 0 0 1 43.618462 0l15.143384 15.025231a140.8 140.8 0 0 0 11.953231-57.028923c0-78.808615-64.413538-142.690462-143.891692-142.690462a144.088615 144.088615 0 0 0-74.988308 20.873846l-44.504615-44.110769a205.902769 205.902769 0 0 1 119.492923-37.927384c113.545846 0 205.587692 91.273846 205.587692 203.854769 0 44.169846-14.178462 85.070769-38.281846 118.449231z\"\n          fill=\"#ffffff\" p-id=\"2332\"></path>\n      </svg>\n    </div>\n    <!-- <EndCall v-if=\"!isPc\" @stop=\"stop\" /> -->\n  </div>\n</template>\n\n<script>\nimport Timmer from '@/components/Timmer';\n// import EndCall from '@/view/components/EndCall';\nimport { isPc } from '@/utils/browser';\nimport { message } from '@/utils/index';\n\nexport default {\n  props: ['className', 'conferenceInfo'],\n  components: {\n    Timmer,\n    // EndCall,\n  },\n  data () {\n    return {\n      infoVisible: false,\n      isPc,\n    };\n  },\n  methods: {\n    cancel () {\n      this.infoVisible = false;\n    },\n    switchDebug () {\n      this.$emit('switchDebug');\n    },\n    stop () {\n      this.$emit('stop');\n    },\n    handleFlipCamera () {\n      this.$emit('flipCamera');\n    },\n    // 复制\n    copyNumber () {\n      this.$copyText(this.conferenceInfo.number).then(() => {\n        message.info('已复制到剪贴板');\n      });\n    },\n  },\n};\n</script>\n<style lang=\"scss\">\n.el-popover.meeting-popover {\n  width: 360px;\n  height: 80px;\n  background: #ffffff;\n  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.26);\n  font-size: 12px;\n  z-index: 1009;\n  padding: 14px 16px;\n\n  .popper__arrow {\n    display: none;\n  }\n\n  .upload-icon {\n    position: absolute;\n    right: 12px;\n    top: 16px;\n    cursor: pointer;\n\n    .svg-icon {\n      width: 16px;\n      height: 16px;\n      fill-opacity: 0.8;\n      fill: #393946;\n    }\n\n    &:hover {\n      .svg-icon {\n        fill-opacity: 0.6;\n      }\n    }\n  }\n}\n\n.meeting-info-name {\n  font-size: 14px;\n  color: #393946;\n  margin-bottom: 12px;\n  margin-right: 30px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.meeting-info-number {\n  display: flex;\n  align-items: center;\n  line-height: 18px;\n  color: rgba(57, 57, 70, 0.6);\n\n  .number {\n    color: #393946;\n    margin-left: 12px;\n  }\n\n  .copy-btn {\n    margin-left: 16px;\n    width: 12px;\n    height: 13px;\n    background: url('~@/assets/img/icon/icon_copy.png') no-repeat center center;\n    background-size: cover;\n\n    &:hover {\n      cursor: pointer;\n    }\n  }\n}\n\n.meeting-info-btn {\n  cursor: pointer;\n}\n</style>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=5f1fe634&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=5f1fe634&prod&lang=scss\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=5f1fe634&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5f1fe634\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.isPc)?_c('el-dialog',{attrs:{\"custom-class\":\"xy__setting-modal\",\"visible\":_vm.visible}},[_c('div',{staticClass:\"setting__header\"},[_vm._v(\" 设置 \"),_c('div',{staticClass:\"close\",on:{\"click\":_vm.onCancel}})]),_c('div',{staticClass:\"setting__container\"},[_c('div',{staticClass:\"setting__menu\"},[_c('el-menu',{staticClass:\"xy__setting-menu\",attrs:{\"default-active\":_vm.current},on:{\"select\":_vm.handleSelect}},[_c('el-menu-item',{attrs:{\"index\":\"common\"}},[_c('i',{staticClass:\"el-icon-setting\"}),_c('span',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(\"常规设置\")])]),_c('el-menu-item',{attrs:{\"index\":\"device\"}},[_c('i',{staticClass:\"el-icon-video-camera\"}),_c('span',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(\"音视频\")])]),_c('el-menu-item',{attrs:{\"index\":\"feedback\"}},[_c('i',{staticClass:\"el-icon-edit-outline\"}),_c('span',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(\"反馈\")])]),_c('el-menu-item',{attrs:{\"index\":\"about\"}},[_c('i',{staticClass:\"el-icon-s-opportunity\"}),_c('span',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(\"关于\")])])],1)],1),_c('div',{staticClass:\"setting__content\"},[(_vm.current === 'common')?_c('Common',{attrs:{\"isInMeeting\":_vm.isInMeeting,\"isThird\":_vm.isThird,\"localHide\":_vm.localHide,\"layoutMode\":_vm.layoutMode},on:{\"setting\":_vm.onHandleSetting}}):_vm._e(),(_vm.current === 'device')?_c('Device',{attrs:{\"setting\":_vm.setting,\"current\":_vm.current},on:{\"setting\":_vm.onHandleSetting}}):_vm._e(),(_vm.current === 'feedback')?_c('Feedback'):_vm._e(),(_vm.current === 'about')?_c('Version'):_vm._e()],1)])]):_c('div',[_c('el-drawer',{attrs:{\"visible\":_vm.visible,\"direction\":\"btt\",\"append-to-body\":true,\"withHeader\":false,\"custom-class\":\"xy__drawer-setting\",\"before-close\":_vm.onCancel},on:{\"update:visible\":function($event){_vm.visible=$event}}},[_c('Version'),_c('Common',{attrs:{\"isInMeeting\":_vm.isInMeeting,\"isThird\":_vm.isThird,\"localHide\":_vm.localHide,\"layoutMode\":_vm.layoutMode},on:{\"setting\":_vm.onHandleSetting}}),_c('div',{staticClass:\"list-item\",on:{\"click\":function($event){_vm.feedbackVisible = true}}},[_c('div',{staticClass:\"key\"},[_vm._v(\"快速反馈\")]),_c('div',{staticClass:\"value\"},[_c('i',{staticClass:\"el-icon-arrow-right\"})])])],1),_c('el-drawer',{attrs:{\"visible\":_vm.feedbackVisible,\"direction\":\"btt\",\"append-to-body\":true,\"withHeader\":false,\"custom-class\":\"xy__drawer-setting\"},on:{\"update:visible\":function($event){_vm.feedbackVisible=$event}}},[_c('div',{staticClass:\"mobile-drawer-back\",on:{\"click\":function($event){_vm.feedbackVisible = false}}},[_vm._v(\" 返回 \")]),_c('div',{staticClass:\"mobile-drawer-content\"},[_c('Feedback')],1)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"setting__content-common\"},[(!_vm.isInMeeting)?_c('div',[(_vm.isPc)?_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"布局模式\")]),_c('div',{staticClass:\"value\"},[_c('el-select',{attrs:{\"popper-class\":\"xy-select\"},on:{\"change\":_vm.onChangeLayoutMode},model:{value:(_vm.mode),callback:function ($$v) {_vm.mode=$$v},expression:\"mode\"}},_vm._l((Object.keys(_vm.layoutModeMap)),function(key){return _c('el-option',{key:key,attrs:{\"label\":_vm.layoutModeMap[key],\"value\":key}},[_vm._v(\" \"+_vm._s(_vm.layoutModeMap[key])+\" \")])}),1)],1)]):_vm._e(),(_vm.isPc)?_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"登录方式\")]),_c('div',{staticClass:\"value\"},[_c('el-select',{attrs:{\"popper-class\":\"xy-select\"},on:{\"change\":_vm.onChangeLoginType},model:{value:(_vm.loginType),callback:function ($$v) {_vm.loginType=$$v},expression:\"loginType\"}},_vm._l((Object.keys(_vm.loginTypeMap)),function(key){return _c('el-option',{key:key,attrs:{\"label\":_vm.loginTypeMap[key],\"value\":key}},[_vm._v(\" \"+_vm._s(_vm.loginTypeMap[key])+\" \")])}),1)],1)]):_vm._e()]):_vm._e(),(_vm.isPc)?_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"隐藏本地画面\")]),_c('div',{staticClass:\"value\"},[_c('el-switch',{on:{\"change\":_vm.onChangeLocalHide},model:{value:(_vm.isLocalHide),callback:function ($$v) {_vm.isLocalHide=$$v},expression:\"isLocalHide\"}})],1)]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"setting__content-common\">\n    <div v-if=\"!isInMeeting\">\n      <div v-if=\"isPc\" class=\"item\">\n        <div class=\"key\">布局模式</div>\n        <div class=\"value\">\n          <el-select popper-class=\"xy-select\" v-model=\"mode\" @change=\"onChangeLayoutMode\">\n            <el-option v-for=\"key in Object.keys(layoutModeMap)\" :key=\"key\" :label=\"layoutModeMap[key]\" :value=\"key\">\n              {{ layoutModeMap[key] }}\n            </el-option>\n          </el-select>\n        </div>\n      </div>\n\n      <div v-if=\"isPc\" class=\"item\">\n        <div class=\"key\">登录方式</div>\n        <div class=\"value\">\n          <el-select popper-class=\"xy-select\" v-model=\"loginType\" @change=\"onChangeLoginType\">\n            <el-option v-for=\"key in Object.keys(loginTypeMap)\" :key=\"key\" :label=\"loginTypeMap[key]\" :value=\"key\">\n              {{ loginTypeMap[key] }}\n            </el-option>\n          </el-select>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"item\" v-if=\"isPc\">\n      <div class=\"key\">隐藏本地画面</div>\n      <div class=\"value\">\n        <el-switch v-model=\"isLocalHide\" @change=\"onChangeLocalHide\" />\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport { isPc } from '@/utils/browser';\n\nexport default {\n  props: ['isThird', 'layoutMode', 'localHide', 'isInMeeting'],\n  data() {\n    return {\n      layoutModeMap: {\n        AUTO: '自动布局',\n        CUSTOM: '自定义布局',\n      },\n      loginTypeMap: {\n        XYLINK: '小鱼账号登录',\n        THIRD: '第三方账号登录',\n      },\n      loginType: this.isThird ? 'THIRD' : 'XYLINK',\n      isLocalHide: this.localHide,\n      mode: this.layoutMode,\n      isPc,\n    };\n  },\n  methods: {\n    onChangeLayoutMode(value) {\n      this.$emit('setting', { layoutMode: value });\n    },\n    onChangeLoginType(value) {\n      this.$emit('setting', { isThird: value === 'THIRD' });\n    },\n    onChangeLocalHide(value) {\n      this.$emit('setting', { localHide: value });\n    },\n  },\n  watch: {},\n};\n</script>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./Common.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./Common.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Common.vue?vue&type=template&id=52a3999a\"\nimport script from \"./Common.vue?vue&type=script&lang=js\"\nexport * from \"./Common.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"setting__content-device\"},[(_vm.isShowStreamFailTips)?_c('el-alert',{attrs:{\"title\":\"\",\"type\":\"error\"}},[_c('div',{staticClass:\"stream-fail\"},[_c('span',{staticClass:\"stream-fail-tip\"},[_vm._v(\"摄像头或麦克风打开失败\")]),_c('span',{staticClass:\"click-me\",on:{\"click\":function($event){_vm.guideVisible = true}}},[_vm._v(\" 点我 \")])])]):_vm._e(),(_vm.iauth === 'prompt')?_c('div',{staticClass:\"fixed\"},[_vm._m(0)]):_vm._e(),(_vm.iauth === 'pending')?_c('div',{staticClass:\"fixed\"},[_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(true),expression:\"true\"}],staticClass:\"request__loading\",attrs:{\"element-loading-text\":\"设备检测中...\"}})]):_vm._e(),(_vm.iauth === 'error')?_c('div',{staticClass:\"fixed\"},[_vm._m(1)]):_vm._e(),_c('div',{class:['setting__content-device-main', _vm.iauth === 'granted' ? 'visible' : 'hidden']},[_c('div',[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\" 摄像头 \")]),_c('div',{staticClass:\"value\"},[_c('el-select',{attrs:{\"popper-class\":\"xy-select\"},on:{\"change\":_vm.changeVideoIn},model:{value:(_vm.select.videoInValue),callback:function ($$v) {_vm.$set(_vm.select, \"videoInValue\", $$v)},expression:\"select.videoInValue\"}},_vm._l((_vm.videoInList),function(item){return _c('el-option',{key:item.deviceId,attrs:{\"label\":item.label,\"value\":item.deviceId}})}),1)],1)]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"}),_c('div',{staticClass:\"value video-value\"},[_c('div',{class:[_vm.videoStatusVisible ? 'visible' : 'hidden', 'preview-video-bg']},[_vm._v(\" 预览不可用 \")]),_c('video',{ref:\"videoRef\",staticClass:\"preview-video\",attrs:{\"autoPlay\":\"\",\"controls\":false,\"playsInline\":\"\"},domProps:{\"muted\":true}})])]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"麦克风\")]),_c('div',{staticClass:\"value\"},[_c('el-select',{attrs:{\"popper-class\":\"xy-select\"},on:{\"change\":_vm.changeAudioInput},model:{value:(_vm.select.audioInputValue),callback:function ($$v) {_vm.$set(_vm.select, \"audioInputValue\", $$v)},expression:\"select.audioInputValue\"}},_vm._l((_vm.audioInputList),function(item){return _c('el-option',{key:item.deviceId,attrs:{\"label\":item.label,\"value\":item.deviceId}})}),1)],1)]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"音量\")]),_c('div',{staticClass:\"value\"},[_c('svg-icon',{staticClass:\"volume\",attrs:{\"icon\":\"icon_device_volume\"}}),_c('div',{staticClass:\"level-process\"},[_c('div',{staticClass:\"level-value\",style:({ transform: `translateX(${_vm.audioLevel}%)` })})])],1)]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"扬声器\")]),_c('div',{staticClass:\"value\"},[_c('el-select',{attrs:{\"popper-class\":\"xy-select\"},on:{\"change\":_vm.changeAudioOutput},model:{value:(_vm.select.audioOutputValue),callback:function ($$v) {_vm.$set(_vm.select, \"audioOutputValue\", $$v)},expression:\"select.audioOutputValue\"}},_vm._l((_vm.audioOutputList),function(item){return _c('el-option',{key:item.deviceId,attrs:{\"label\":item.label,\"value\":item.deviceId}})}),1)],1)]),_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"}),_c('div',{staticClass:\"value\"},[_c('span',{staticClass:\"play-audio\",on:{\"click\":_vm.play}},[_vm._v(_vm._s(_vm.testAudioStatus ? '停止扬声器' : '测试扬声器'))]),(_vm.testAudioStatus)?_c('span',{staticClass:\"play-audio-status\"},[_vm._v(\"正在播放声音...\")]):_vm._e(),_c('audio',{ref:\"audioRef\",staticClass:\"preview-audio\",attrs:{\"loop\":true,\"src\":\"https://cdn.xylink.com/wechatMP/ring.ogg\"}})])])]),_vm._m(2),_c('div',{staticClass:\"setting__footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleOk}},[_vm._v(\"保存\")])],1)]),_c('Guide',{attrs:{\"visible\":_vm.guideVisible},on:{\"close\":function($event){_vm.guideVisible = false}}})],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"request__loading\"},[_c('div',{staticClass:\"init\"},[_vm._v(\" 请求获取摄像头&麦克风权限，请点击【允许】按钮进行授权操作 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"request__loading\"},[_c('div',{staticClass:\"init\"},[_vm._v(\" 浏览器版本太低，请升级最新的Chrome浏览器访问 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"详细检测\")]),_c('div',{staticClass:\"value\"},[_c('a',{attrs:{\"href\":\"https://cdn.xylink.com/webrtc/web/index.html#/detect\",\"rel\":\"noopener noreferrer\",\"target\":\"_blank\"}},[_vm._v(\" 开始检测 \")])])])\n}]\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('el-dialog',{attrs:{\"custom-class\":\"xy__guide-modal\",\"visible\":_vm.visible,\"width\":\"542px\",\"append-to-body\":\"\",\"title\":\"\",\"top\":\"10vh\"},on:{\"update:visible\":function($event){_vm.visible=$event}}},[_c('div',{staticClass:\"guide-content\"},[_c('div',{staticClass:\"model-title guide-title\"},[_c('div',{staticClass:\"close\"},[_c('span',{staticClass:\"close-icon\",on:{\"click\":_vm.onClose}})]),_c('div',{staticClass:\"title\"},[_vm._v(\"摄像头或麦克风设置\")])]),_c('div',{staticClass:\"model-container guide-container\"},[_c('div',{staticClass:\"guide-tip\"},[_c('div',{staticClass:\"guide-tip-title\"},[_vm._v(\" 1、检查Web权限是否允许摄像头或麦克风 \")]),_c('div',{staticClass:\"guide-tip-img\"},[_c('img',{attrs:{\"width\":\"100%\",\"src\":require(\"@/assets/img/operate/ch_step_operate_video.png\"),\"alt\":\"\"}})])]),_c('div',{staticClass:\"guide-tip\"},[_c('div',{staticClass:\"guide-tip-title\"},[_vm._v(\" 2、确保通话中选择了正确的设备，可通过以下操作切换设备 \")]),_c('div',{staticClass:\"guide-tip-img\"},[_c('img',{attrs:{\"width\":\"100%\",\"src\":require(\"@/assets/img/operate/ch_step_operate_permission.png\"),\"alt\":\"\"}})])]),_c('div',{staticClass:\"guide-tip\"},[_c('div',{staticClass:\"guide-tip-title\"},[_vm._v(\" 3、检查摄像头或麦克风硬件设备本身是否正常 \")])]),_c('div',{staticClass:\"guide-tip\"},[_c('div',{staticClass:\"guide-tip-title\"},[_vm._v(\" 4、检查系统权限是否允许摄像头或麦克风; \")]),_c('div',{staticClass:\"guide-tip-content\"},[_c('div',[_vm._v(\" -{`Windows 系统：设置 > 隐私 > 相机/麦克风 > 允许桌面应用访问你的相机/麦克风`} \")]),_c('div',[_vm._v(\" -{` Mac OS 系统：系统偏好设置 > 安全与隐私 > 隐私 > 摄像头/麦克风`} \")])])]),_c('div',{staticClass:\"guide-tip\"},[_c('div',{staticClass:\"guide-tip-title\"},[_vm._v(\" 5、如果以上步骤未设置成功，可\"),_c('span',{staticClass:\"link\",on:{\"click\":_vm.onReload}},[_vm._v(\" 刷新 \")]),_vm._v(\"页面尝试重新授权； \")])])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <el-dialog\n    custom-class=\"xy__guide-modal\"\n    :visible.sync=\"visible\"\n    width=\"542px\"\n    append-to-body\n    title=\"\"\n    top = \"10vh\"\n  >\n    <div class=\"guide-content\">\n      <div class=\"model-title guide-title\">\n        <div class=\"close\">\n          <span class=\"close-icon\" @click=\"onClose\" />\n        </div>\n        <div class=\"title\">摄像头或麦克风设置</div>\n      </div>\n\n      <div class=\"model-container guide-container\">\n        <div class=\"guide-tip\">\n          <div class=\"guide-tip-title\">\n            1、检查Web权限是否允许摄像头或麦克风\n          </div>\n          <div class=\"guide-tip-img\">\n            <img\n              width=\"100%\"\n              src=\"@/assets/img/operate/ch_step_operate_video.png\"\n              alt=\"\"\n            />\n          </div>\n        </div>\n        <div class=\"guide-tip\">\n          <div class=\"guide-tip-title\">\n            2、确保通话中选择了正确的设备，可通过以下操作切换设备\n          </div>\n          <div class=\"guide-tip-img\">\n            <img\n              width=\"100%\"\n              src=\"@/assets/img/operate/ch_step_operate_permission.png\"\n              alt=\"\"\n            />\n          </div>\n        </div>\n        <div class=\"guide-tip\">\n          <div class=\"guide-tip-title\">\n            3、检查摄像头或麦克风硬件设备本身是否正常\n          </div>\n        </div>\n        <div class=\"guide-tip\">\n          <div class=\"guide-tip-title\">\n            4、检查系统权限是否允许摄像头或麦克风;\n          </div>\n          <div class=\"guide-tip-content\">\n            <div>\n              -{`Windows 系统：设置 > 隐私 > 相机/麦克风 >\n              允许桌面应用访问你的相机/麦克风`}\n            </div>\n            <div>\n              -{` Mac OS 系统：系统偏好设置 > 安全与隐私 > 隐私 >\n              摄像头/麦克风`}\n            </div>\n          </div>\n        </div>\n        <div class=\"guide-tip\">\n          <div class=\"guide-tip-title\">\n            5、如果以上步骤未设置成功，可<span class=\"link\" @click=\"onReload\">\n              刷新 </span\n            >页面尝试重新授权；\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n<script>\nexport default {\n  props: [\"visible\"],\n  data() {\n    return {};\n  },\n  methods: {\n    onClose() {\n      this.$emit(\"close\");\n    },\n    onReload() {\n      window.location.reload();\n    },\n  },\n};\n</script>\n<style lang=\"scss\">\n@import \"./index.scss\";\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=4eaabb0c\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4eaabb0c&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"setting__content-device\">\n    <el-alert v-if=\"isShowStreamFailTips\" title=\"\" type=\"error\">\n      <div class=\"stream-fail\">\n        <span class=\"stream-fail-tip\">摄像头或麦克风打开失败</span>\n        <span class=\"click-me\" @click=\"guideVisible = true\">\n          点我\n        </span>\n      </div>\n    </el-alert>\n\n    <div v-if=\"iauth === 'prompt'\" class=\"fixed\">\n      <div class=\"request__loading\">\n        <div class=\"init\">\n          请求获取摄像头&麦克风权限，请点击【允许】按钮进行授权操作\n        </div>\n      </div>\n    </div>\n\n    <div v-if=\"iauth === 'pending'\" class=\"fixed\">\n      <div class=\"request__loading\" v-loading=\"true\" element-loading-text=\"设备检测中...\" />\n    </div>\n\n    <div v-if=\"iauth === 'error'\" class=\"fixed\">\n      <div class=\"request__loading\">\n        <div class=\"init\">\n          浏览器版本太低，请升级最新的Chrome浏览器访问\n        </div>\n      </div>\n    </div>\n\n    <div :class=\"['setting__content-device-main', iauth === 'granted' ? 'visible' : 'hidden']\">\n      <div>\n        <div class=\"item\">\n          <div class=\"key\">\n            摄像头\n          </div>\n          <div class=\"value\">\n            <el-select popper-class=\"xy-select\" v-model=\"select.videoInValue\" @change=\"changeVideoIn\">\n              <el-option v-for=\"item in videoInList\" :key=\"item.deviceId\" :label=\"item.label\" :value=\"item.deviceId\">\n              </el-option>\n            </el-select>\n          </div>\n        </div>\n\n        <div class=\"item\">\n          <div class=\"key\"></div>\n          <div class=\"value video-value\">\n            <div :class=\"[videoStatusVisible ? 'visible' : 'hidden', 'preview-video-bg']\">\n              预览不可用\n            </div>\n            <video class=\"preview-video\" autoPlay :muted=\"true\" ref=\"videoRef\" :controls=\"false\" playsInline></video>\n          </div>\n        </div>\n\n        <div class=\"item\">\n          <div class=\"key\">麦克风</div>\n          <div class=\"value\">\n            <el-select popper-class=\"xy-select\" v-model=\"select.audioInputValue\" @change=\"changeAudioInput\">\n              <el-option v-for=\"item in audioInputList\" :key=\"item.deviceId\" :label=\"item.label\" :value=\"item.deviceId\">\n              </el-option>\n            </el-select>\n          </div>\n        </div>\n\n        <div class=\"item\">\n          <div class=\"key\">音量</div>\n          <div class=\"value\">\n            <svg-icon icon=\"icon_device_volume\" class=\"volume\"/>\n            <div class=\"level-process\">\n              <div class=\"level-value\" :style=\"{ transform: `translateX(${audioLevel}%)` }\"></div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"item\">\n          <div class=\"key\">扬声器</div>\n          <div class=\"value\">\n            <el-select popper-class=\"xy-select\" v-model=\"select.audioOutputValue\" @change=\"changeAudioOutput\">\n              <el-option\n                v-for=\"item in audioOutputList\"\n                :key=\"item.deviceId\"\n                :label=\"item.label\"\n                :value=\"item.deviceId\"\n              >\n              </el-option>\n            </el-select>\n          </div>\n        </div>\n\n        <div class=\"item\">\n          <div class=\"key\"></div>\n          <div class=\"value\">\n            <span class=\"play-audio\" @click=\"play\">{{ testAudioStatus ? '停止扬声器' : '测试扬声器' }}</span>\n            <span v-if=\"testAudioStatus\" class=\"play-audio-status\">正在播放声音...</span>\n            <audio\n              class=\"preview-audio\"\n              ref=\"audioRef\"\n              :loop=\"true\"\n              src=\"https://cdn.xylink.com/wechatMP/ring.ogg\"\n            ></audio>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"item\">\n        <div class=\"key\">详细检测</div>\n        <div class=\"value\">\n          <a href=\"https://cdn.xylink.com/webrtc/web/index.html#/detect\" rel=\"noopener noreferrer\" target=\"_blank\">\n            开始检测\n          </a>\n        </div>\n      </div>\n\n      <div class=\"setting__footer\">\n        <el-button type=\"primary\" @click=\"handleOk\">保存</el-button>\n      </div>\n    </div>\n\n    <Guide :visible=\"guideVisible\" @close=\"guideVisible = false\" />\n  </div>\n</template>\n<script>\nimport xyRTC, { DeviceManager } from '@xylink/xy-rtc-sdk';\nimport Guide from '../Guide';\n\nexport default {\n  props: ['setting', 'current'],\n  components: {\n    Guide,\n  },\n  computed: {\n    isShowStreamFailTips() {\n      const { camera, microphone } = this.permission || {};\n\n      return camera === 'denied' || camera === 'failed' || microphone === 'denied' || microphone === 'failed';\n    },\n  },\n  data() {\n    const { selectedDevice } = this.setting || {};\n    const defaultSelect = {\n      audioInputValue: selectedDevice?.audioInput?.deviceId || 'default',\n      audioOutputValue: selectedDevice?.audioOutput?.deviceId || 'default',\n      videoInValue: selectedDevice?.videoInput?.deviceId || '',\n    };\n    return {\n      settingVisible: this.visible,\n      iauth: 'pending',\n      stream: null,\n      previewVideoStream: null,\n      previewAudioStream: null,\n      deviceManager: null,\n      audioInputList: [],\n      audioOutputList: [],\n      videoInList: [],\n      audioLevel: 0,\n      select: defaultSelect,\n      testAudioStatus: false,\n      videoStatusVisible: false,\n      audioLevelTimmer: null,\n      permission: { camera: '', microphone: '' },\n      guideVisible: false,\n    };\n  },\n\n  async mounted() {\n    this.deviceManager = new DeviceManager();\n    await this.deviceManager.init();\n\n    // 在某些浏览器需要先采流授权，才能得到对应的设备信息\n    await this.getStream();\n\n    await this.getDevices();\n\n    await this.initDeviceManagerEvent();\n  },\n\n  beforeDestroy() {\n    this.stop();\n  },\n\n  methods: {\n    async getStream() {\n      if (!this.stream) {\n        this.stream = xyRTC.createStream();\n      }\n\n      await this.getVideoStream();\n\n      await this.getAudioStream();\n\n      this.hideCheckingPage();\n    },\n\n    async getVideoStream(deviceId = this.selectedDevice?.videoInput?.deviceId) {\n      let cameraPermission = 'failed';\n\n      try {\n        let params = {};\n\n        if (deviceId) {\n          params['video'] = {\n            deviceId: { exact: deviceId },\n          };\n        }\n\n        this.previewVideoStream?.getTracks().forEach((track) => {\n          track.stop();\n        });\n\n        this.previewVideoStream = await this.stream.getPreviewStream(true, false, params);\n\n        if (this.previewVideoStream) {\n          const videoTrack = this.previewVideoStream.getVideoTracks()[0];\n\n          const videoInput = videoTrack?.getSettings()['deviceId'] || '';\n\n          this.select = {\n            ...this.select,\n            videoInValue: videoInput,\n          };\n\n          const videoRefEle = this.$refs['videoRef'];\n\n          if (videoRefEle && videoTrack) {\n            videoRefEle.srcObject = this.previewVideoStream;\n          }\n        }\n\n        cameraPermission = 'granted';\n      } catch (err) {\n        this.videoStatusVisible = true;\n\n        console.log('video err:', err);\n\n        if (err?.code === 'XYSDK:950404') {\n          cameraPermission = 'denied';\n        }\n\n        console.log('get video stream error:', err);\n      }\n\n      this.permission = {\n        ...this.permission,\n        camera: cameraPermission,\n      };\n    },\n\n    async getAudioStream(deviceId = this.selectedDevice?.audioInput?.deviceId) {\n      let microphonePermission = 'failed';\n      let params = {};\n\n      if (deviceId) {\n        params['audio'] = {\n          deviceId: { exact: deviceId },\n        };\n      }\n\n      this.previewAudioStream?.getTracks().forEach((track) => {\n        track.stop();\n      });\n\n      try {\n        this.previewAudioStream = await this.stream.getPreviewStream(false, true, params);\n\n        if (this.previewAudioStream) {\n          const audioTrack = this.previewAudioStream.getAudioTracks()[0];\n          const audioInput = audioTrack?.getSettings()['deviceId'] || 'default';\n\n          this.select = {\n            ...this.select,\n            audioInputValue: audioInput,\n          };\n\n          this.stream.clearAudioLevel();\n\n          await this.stream.getAudioLevel(this.previewAudioStream);\n          // 实时获取音量大小\n          this.audioLevelTimmer = setInterval(async () => {\n            try {\n              const level = await this.stream.getAudioLevel();\n\n              // 更新Audio的实时音量显示\n              this.audioLevel = level;\n            } catch (err) {\n              this.clearAudioLevelTimmer();\n            }\n          }, 100);\n        }\n        microphonePermission = 'granted';\n      } catch (err) {\n        console.log('get audio stream failed:', err);\n\n        if (err?.code === 'XYSDK:950404') {\n          microphonePermission = 'denied';\n        }\n      }\n\n      this.permission = {\n        ...this.permission,\n        microphone: microphonePermission,\n      };\n    },\n\n    getRangeRandom(min = 50, max = 500) {\n      const num = Math.floor(Math.random() * (max - min + 1) + min);\n      return num;\n    },\n\n    hideCheckingPage() {\n      const randomTimer = this.getRangeRandom();\n\n      setTimeout(() => {\n        this.iauth = 'granted';\n      }, randomTimer);\n    },\n\n    async initDeviceManagerEvent() {\n      this.deviceManager.on('permission', async (e) => {\n        const { camera, microphone } = e;\n\n        if (camera === 'granted') {\n          await this.getVideoStream();\n        }\n\n        if (microphone === 'granted') {\n          await this.getAudioStream();\n        }\n\n        this.getDevices();\n\n        this.permission = e;\n      });\n\n      this.deviceManager.on('device', (e) => {\n        const { detail, nextDevice } = e;\n        const { videoInput, audioInput, audioOutput } = nextDevice;\n        const nextDevices = detail;\n\n        if (videoInput) {\n          this.handleChange('videoInValue', videoInput.deviceId);\n        }\n\n        if (audioInput) {\n          this.handleChange('audioInputValue', audioInput.deviceId);\n        }\n\n        if (audioOutput) {\n          this.handleChange('audioOutputValue', audioOutput.deviceId);\n        }\n\n        this.setDevices(nextDevices);\n      });\n    },\n\n    async handleChange(key, e) {\n      if (key === 'audioInputValue' || key === 'videoInValue') {\n        await this.getVideoStream(e);\n      }\n\n      if (key === 'videoInValue') {\n        await this.getAudioStream(e);\n      }\n\n      this.select = {\n        ...this.select,\n        [key]: e,\n      };\n    },\n\n    changeVideoIn(e) {\n      this.handleChange('videoInValue', e);\n    },\n\n    changeAudioInput(e) {\n      this.handleChange('audioInputValue', e);\n    },\n\n    changeAudioOutput(e) {\n      this.handleChange('audioOutputValue', e);\n    },\n\n    async getDevices() {\n      const devices = await this.deviceManager.getDevices();\n\n      this.setDevices(devices);\n    },\n\n    setDevices(devices) {\n      const { audioInputList, audioOutputList, videoInList } = devices;\n      this.audioInputList = audioInputList;\n      this.audioOutputList = audioOutputList;\n      this.videoInList = videoInList;\n    },\n\n    clearAudioLevelTimmer() {\n      this.audioLevelTimmer && clearInterval(this.audioLevelTimmer);\n    },\n\n    clearStream() {\n      this.previewVideoStream && xyRTC.closePreviewStream(this.previewVideoStream);\n\n      this.previewAudioStream && xyRTC.closePreviewStream(this.previewAudioStream);\n    },\n\n    stop() {\n      this.clearAudioLevelTimmer();\n\n      const deviceManager = this.$refs['deviceManager'];\n\n      if (deviceManager) {\n        deviceManager.destroy();\n      }\n\n      this.clearStream();\n\n      this.stream.close();\n\n      this.$emit('cancel');\n    },\n    async play() {\n      const audioRef = this.$refs['audioRef'];\n      if (audioRef) {\n        if (audioRef.paused && !this.testAudioStatus) {\n          xyRTC.setOutputAudioDevice(audioRef, this.select.audioOutputValue);\n\n          await audioRef.play();\n          this.testAudioStatus = true;\n        } else {\n          await audioRef.pause();\n          this.testAudioStatus = false;\n        }\n      }\n    },\n    findDeviceById(deviceId, list) {\n      return (\n        list.find((item) => {\n          return item.deviceId === deviceId;\n        }) || { deviceId: '', label: '' }\n      );\n    },\n\n    handleOk() {\n      this.stop();\n\n      const { audioInputValue, audioOutputValue, videoInValue } = this.select;\n\n      this.$emit('setting', {\n        selectedDevice: {\n          audioInput: this.findDeviceById(audioInputValue, this.audioInputList),\n          audioOutput: this.findDeviceById(audioOutputValue, this.audioOutputList),\n          videoInput: this.findDeviceById(videoInValue, this.videoInList),\n        },\n        deviceList: {\n          audioInputList: this.audioInputList,\n          audioOutputList: this.audioOutputList,\n          videoInList: this.videoInList,\n        },\n      });\n    },\n\n    setOutputAudioDevice(val = this.select.audioOutputValue) {\n      const audioRef = this.$refs['audioRef'];\n\n      audioRef && val && xyRTC.setOutputAudioDevice(audioRef, val);\n    },\n  },\n\n  watch: {\n    'select.audioOutputValue': {\n      handler(newVal) {\n        this.setOutputAudioDevice(newVal);\n      },\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./Device.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./Device.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Device.vue?vue&type=template&id=236c5046\"\nimport script from \"./Device.vue?vue&type=script&lang=js\"\nexport * from \"./Device.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"feedback\"},[_c('div',{staticClass:\"feedback__content\"},[_c('div',{staticClass:\"item\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"内容描述\")]),_c('div',{staticClass:\"value\"},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":2,\"placeholder\":\"请输入您的宝贵意见和建议\"},model:{value:(_vm.content),callback:function ($$v) {_vm.content=$$v},expression:\"content\"}})],1)]),_c('div',{staticClass:\"item feedback__content-contact\"},[_c('div',{staticClass:\"key\"},[_vm._v(\"联系方式\")]),_c('div',{staticClass:\"value\"},[_c('el-input',{staticClass:\"feedback__content-input\",attrs:{\"autocomplete\":\"off\",\"placeholder\":\"请输入您的联系方式\"},model:{value:(_vm.contact),callback:function ($$v) {_vm.contact=$$v},expression:\"contact\"}})],1)])]),_c('div',{staticClass:\"feedback__footer\"},[_c('el-button',{staticClass:\"download\",attrs:{\"type\":\"text\",\"loading\":_vm.downloadLoading},on:{\"click\":_vm.download}},[_vm._v(\"下载日志\")]),_c('el-button',{staticClass:\"upload-btn\",attrs:{\"loading\":_vm.uploadLoading,\"type\":\"primary\",\"disabled\":!_vm.content.trim()},on:{\"click\":_vm.upload}},[_vm._v(\" \"+_vm._s(_vm.uploadLoading ? \"提交中\" : \"提交\")+\" \")])],1)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"feedback\">\n    <div class=\"feedback__content\">\n      <div class=\"item\">\n        <div class=\"key\">内容描述</div>\n        <div class=\"value\">\n          <el-input\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入您的宝贵意见和建议\"\n            v-model=\"content\"\n          >\n          </el-input>\n        </div>\n      </div>\n\n      <div class=\"item feedback__content-contact\">\n        <div class=\"key\">联系方式</div>\n        <div class=\"value\">\n          <el-input\n            v-model=\"contact\"\n            class=\"feedback__content-input\"\n            autocomplete=\"off\"\n            placeholder=\"请输入您的联系方式\"\n          ></el-input>\n        </div>\n      </div>\n    </div>\n    <div class=\"feedback__footer\">\n      <el-button class=\"download\" type=\"text\" @click=\"download\" :loading=\"downloadLoading\"\n        >下载日志</el-button\n      >\n      <el-button\n        class=\"upload-btn\"\n        :loading=\"uploadLoading\"\n        @click=\"upload\"\n        type=\"primary\"\n        :disabled=\"!content.trim()\"\n      >\n        {{ uploadLoading ? \"提交中\" : \"提交\" }}\n      </el-button>\n    </div>\n  </div>\n</template>\n<script>\nimport xyRTC from \"@xylink/xy-rtc-sdk\";\nimport store from \"@/utils/store\";\nimport { message } from \"@/utils/index\";\n\nexport default {\n  data() {\n    return {\n      content: \"\",\n      contact: \"\",\n      uploadLoading: false,\n      downloadLoading: false\n    };\n  },\n  methods: {\n    async download() {\n      this.downloadLoading = true;\n      await xyRTC.logger.downloadLog();\n      this.downloadLoading = false;\n    },\n    async upload() {\n      this.uploadLoading = false;\n      try {\n        const { meetingName = \"\" } = store.get(\"xy-user\") || {};\n\n        const result = await xyRTC.logger.uploadLog(\n          meetingName,\n          this.contact,\n          this.content\n        );\n\n        if (result) {\n          message.info(\"提交成功\");\n\n          this.content = \"\";\n        } else {\n          message.info(\"提交失败\");\n        }\n\n        this.contact = \"\";\n      } catch (err) {\n        message.info(\"提交失败\");\n      }\n\n      this.uploadLoading = false;\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./Feedback.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./Feedback.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Feedback.vue?vue&type=template&id=51bf329b\"\nimport script from \"./Feedback.vue?vue&type=script&lang=js\"\nexport * from \"./Feedback.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"setting__content-about\"},[_c('div',{staticClass:\"about-version\"},[_vm._v(\"版本号：\"+_vm._s(_vm.version))])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"setting__content-about\">\n    <div class=\"about-version\">版本号：{{ version }}</div>\n    <!-- <div class=\"about-version about-time\">变更时间：{{ update }}</div> -->\n  </div>\n</template>\n<script>\nimport xyRTC from \"@xylink/xy-rtc-sdk\";\n\nexport default {\n  data () {\n    return {\n      version: \"\",\n      update: \"\",\n    };\n  },\n  mounted () {\n    const splitData = xyRTC.version.split(\"- build on\");\n\n    this.version = splitData[0];\n    this.update = splitData[1];\n  },\n  methods: {},\n};\n</script>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./Version.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./Version.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Version.vue?vue&type=template&id=21ad176c\"\nimport script from \"./Version.vue?vue&type=script&lang=js\"\nexport * from \"./Version.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <el-dialog v-if=\"isPc\" custom-class=\"xy__setting-modal\" :visible=\"visible\">\n    <div class=\"setting__header\">\n      设置\n      <div class=\"close\" @click=\"onCancel\" />\n    </div>\n    <div class=\"setting__container\">\n      <div class=\"setting__menu\">\n        <el-menu :default-active=\"current\" class=\"xy__setting-menu\" @select=\"handleSelect\">\n          <el-menu-item index=\"common\">\n            <i class=\"el-icon-setting\"></i>\n            <span slot=\"title\">常规设置</span>\n          </el-menu-item>\n          <el-menu-item index=\"device\">\n            <i class=\"el-icon-video-camera\"></i>\n            <span slot=\"title\">音视频</span>\n          </el-menu-item>\n\n          <el-menu-item index=\"feedback\">\n            <i class=\"el-icon-edit-outline\"></i>\n            <span slot=\"title\">反馈</span>\n          </el-menu-item>\n\n          <el-menu-item index=\"about\">\n            <i class=\"el-icon-s-opportunity\"></i>\n            <span slot=\"title\">关于</span>\n          </el-menu-item>\n        </el-menu>\n      </div>\n      <div class=\"setting__content\">\n        <Common\n          v-if=\"current === 'common'\"\n          :isInMeeting=\"isInMeeting\"\n          :isThird=\"isThird\"\n          :localHide=\"localHide\"\n          :layoutMode=\"layoutMode\"\n          @setting=\"onHandleSetting\"\n        />\n        <Device v-if=\"current === 'device'\" :setting=\"setting\" :current=\"current\" @setting=\"onHandleSetting\" />\n        <Feedback v-if=\"current === 'feedback'\" />\n        <Version v-if=\"current === 'about'\" />\n      </div>\n    </div>\n  </el-dialog>\n\n  <div v-else>\n    <el-drawer\n      :visible.sync=\"visible\"\n      direction=\"btt\"\n      :append-to-body=\"true\"\n      :withHeader=\"false\"\n      custom-class=\"xy__drawer-setting\"\n      :before-close=\"onCancel\"\n    >\n      <Version />\n      <Common\n        :isInMeeting=\"isInMeeting\"\n        :isThird=\"isThird\"\n        :localHide=\"localHide\"\n        :layoutMode=\"layoutMode\"\n        @setting=\"onHandleSetting\"\n      />\n      <div class=\"list-item\" @click=\"feedbackVisible = true\">\n        <div class=\"key\">快速反馈</div>\n        <div class=\"value\">\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n      </div>\n    </el-drawer>\n    <el-drawer\n      :visible.sync=\"feedbackVisible\"\n      direction=\"btt\"\n      :append-to-body=\"true\"\n      :withHeader=\"false\"\n      custom-class=\"xy__drawer-setting\"\n    >\n      <div class=\"mobile-drawer-back\" @click=\"feedbackVisible = false\">\n        返回\n      </div>\n      <div class=\"mobile-drawer-content\">\n        <Feedback />\n      </div>\n    </el-drawer>\n  </div>\n</template>\n<script>\nimport Common from './Common.vue';\nimport Device from './Device.vue';\nimport Feedback from './Feedback.vue';\nimport Version from './Version.vue';\nimport store from '@/utils/store';\nimport { isPc } from '@/utils/browser';\n\nexport default {\n  props: ['visible', 'setting', 'isInMeeting'],\n  components: {\n    Common,\n    Device,\n    Feedback,\n    Version,\n  },\n  data() {\n    const { localHide = false, layoutMode, isThird } = this.setting || {};\n\n    return {\n      current: 'common',\n      isThird,\n      layoutMode,\n      localHide,\n      isPc,\n      feedbackVisible: false,\n    };\n  },\n  methods: {\n    handleSelect(e) {\n      this.current = e;\n    },\n    onHandleSetting(data) {\n      const values = {};\n      const SETTING_KEYS = ['selectedDevice', 'localHide'];\n\n      SETTING_KEYS.forEach((key) => {\n        if (data[key]) {\n          if (key === 'selectedDevice') {\n            store.set('selectedDevice', data.selectedDevice);\n\n            this.$emit('cancel');\n          } else {\n            values[key] = data[key];\n          }\n        }\n      });\n\n      this.$emit('setting', data);\n    },\n    onCancel() {\n      this.$emit('cancel');\n    },\n  },\n};\n</script>\n<style lang=\"scss\">\n@import './style/index.scss';\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=daca2554\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=daca2554&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"barrage-title-wrapper\",style:(_vm.wrapperStyle)},[_c('div',{staticClass:\"barrage-title-bg\",style:(_vm.titleBgStyle)}),_c('span',{ref:\"subTitleRef\",class:_vm.titleClass},[_vm._v(\" \"+_vm._s(_vm.subTitle.content)+\" \")])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"barrage-title-wrapper\" :style=\"wrapperStyle\">\n    <div class=\"barrage-title-bg\" :style=\"titleBgStyle\"></div>\n    <span :class=\"titleClass\" ref=\"subTitleRef\">\n      {{ subTitle.content }}\n    </span>\n  </div>\n</template>\n<script>\nconst fontSizeMap = {\n  small: \"18px\",\n  middle: \"22px\",\n  big: \"24px\",\n};\nconst locationMap = {\n  top: \"0px\",\n  middle: \"42%\",\n  bottom: \"0px\",\n};\n\nlet intervalTimer;\n\nexport default {\n  props: [\"subTitle\"],\n  computed: {\n    titleClass() {\n      return this.subTitle.scroll === \"1\" ? \"barrage-title-text\" : \"\";\n    },\n    titleBgStyle() {\n      return {\n        background: this.subTitle.backgroundRGB || \"transparent\",\n        opacity: (Number(this.subTitle.backgroundAlpha) / 100).toFixed(2),\n      };\n    },\n    wrapperStyle() {\n      const {\n        backgroundRGB = \"\",\n        fontRGB = \"#fff\",\n        scroll = \"0\",\n        location = \"top\",\n        fontSize = \"middle\",\n      } = this.subTitle;\n      const locationKey = location === \"middle\" ? \"top\" : location;\n      const style = {\n        fontSize: fontSizeMap[fontSize],\n        color: fontRGB,\n        background: backgroundRGB || \"transparent\",\n        [locationKey]: locationMap[location],\n      };\n      const textAlign =\n        scroll === \"1\" ? { textAlign: \"left\" } : { textAlign: \"center\" };\n\n      return { ...textAlign, ...style, background: \"transparent\", opacity: 1 };\n    },\n  },\n  methods: {\n    setScrollTitle(newValue) {\n      const current = this.$refs[\"subTitleRef\"];\n      // 实现弹幕\n      if (current) {\n        const objWidth = current.clientWidth;\n        const initTransformX = window.innerWidth;\n        let transformX = initTransformX;\n\n        const render = () => {\n          current.style.transform = `translate3d(${transformX}px, 0, 0)`;\n\n          transformX -= 0.5;\n          if (transformX + objWidth < 0) {\n            cancelAnimationFrame(intervalTimer.current);\n            transformX = initTransformX;\n          }\n          intervalTimer = requestAnimationFrame(render);\n        };\n\n        if (newValue.scroll === \"1\") {\n          current.style.visibility = \"initial\";\n\n          render();\n        }\n      }\n\n      if (!newValue || newValue.action !== \"push\" || newValue.scroll !== \"1\") {\n        cancelAnimationFrame(intervalTimer);\n      }\n    },\n  },\n  mounted() {\n    this.setScrollTitle(this.subTitle);\n  },\n  beforeDestroy() {\n    cancelAnimationFrame(intervalTimer);\n  },\n  watch: {\n    subTitle: {\n      handler(newValue) {\n        this.setScrollTitle(newValue);\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n};\n</script>\n<style scoped>\n.barrage-title-wrapper {\n  position: absolute;\n  left: 0;\n  width: 100%;\n  padding: 15px 10px;\n  color: #fff;\n  font-size: 18px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  z-index: 999;\n  line-height: 1;\n}\n.barrage-title-bg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0;\n  top: 0;\n  z-index: -1;\n}\n.barrage-title-text {\n  display: inline-block;\n  position: relative;\n  z-index: 1;\n}\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=3692ac76&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3692ac76&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3692ac76\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:_vm.reminderClass},[_c('div',{staticClass:\"reminder-toolbar\",on:{\"click\":_vm.onToggleReminder}},[_c('img',{attrs:{\"src\":require(\"@/assets/img/icon/icon_hide.svg\"),\"alt\":\"\"}}),_vm._v(\" \"+_vm._s(!_vm.isHideContent && \"隐藏\")+\" \")]),_c('ul',{staticClass:\"reminder-content\"},_vm._l((_vm.newReminders),function(item){return _c('li',{key:item.value},[_c('div',[_c('span',{staticClass:\"name\"},[_vm._v(_vm._s(item.displayName))]),_vm._v(\" \"+_vm._s(_vm.actionMap[item.action])+\"了会议 \")])])}),0)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div :class=\"reminderClass\">\n    <div class=\"reminder-toolbar\" @click=\"onToggleReminder\">\n      <img src=\"@/assets/img/icon/icon_hide.svg\" alt=\"\" />\n      {{ !isHideContent && \"隐藏\" }}\n    </div>\n    <ul class=\"reminder-content\">\n      <li v-for=\"item in newReminders\" :key=\"item.value\">\n        <div>\n          <span class=\"name\">{{ item.displayName }}</span>\n          {{ actionMap[item.action] }}了会议\n        </div>\n      </li>\n    </ul>\n  </div>\n</template>\n<script>\n/**\n * 出入会提醒\n * 1.显示最新的3条，3秒更新一次\n * 2.无数据变化，10s之后隐藏\n * 3.超过1000人，不会收到此消息\n */\nexport default {\n  props: [\"reminders\"],\n  computed: {\n    reminderClass() {\n      return `reminder ${\n        this.isHideContent ? \"reminder-close\" : \"reminder-expand\"\n      } ${this.isHide ? \"reminder-hide\" : \"reminder-show\"}`;\n    },\n  },\n  data() {\n    return {\n      actionMap: {\n        in: \"加入\",\n        out: \"离开\",\n      },\n      newReminders: [], // 显示的消息\n      isHide: true, // 隐藏toolbar + content\n      isHideContent: false, // 隐藏content\n      remindersTemp: [], // 存储展示的消息\n      remindersQueue: [], // 存储3秒收到的消息\n      timer: null, // 隐藏定时器\n      queueTimer: null, // 存储定时器\n    };\n  },\n  methods: {\n    stopMeeting() {\n      this.$emit(\"stopMeeting\", false);\n    },\n    clearTimer() {\n      if (this.timer) {\n        clearTimeout(this.timer);\n        this.timer = null;\n      }\n    },\n    onToggleReminder() {\n      this.isHideContent = !this.isHideContent;\n    },\n  },\n  beforeDestroy() {\n    this.remindersTemp = [];\n    clearInterval(this.queueTimer);\n    this.queueTimer = null;\n  },\n  watch: {\n    reminders: {\n      handler(newValue) {\n        this.remindersQueue = this.remindersQueue.concat(newValue);\n\n        // 起一个3s定时器，每3秒取最后一个最新的数据进行展示\n        if (!this.queueTimer && this.remindersQueue.length > 0) {\n          this.queueTimer = setInterval(() => {\n            if (this.remindersQueue.length > 1) {\n              this.remindersTemp = this.remindersTemp\n                .concat(this.remindersQueue.pop() || [])\n                .slice(-3);\n\n              this.remindersQueue = [];\n            }\n          }, 2000);\n        }\n\n        // 第一条数据直接显示\n        if (this.remindersQueue.length === 1) {\n          this.remindersTemp = this.remindersTemp.concat(newValue);\n        }\n      },\n      deep: true,\n      immediate: true,\n    },\n    remindersTemp: {\n      handler(newValue) {\n        if (newValue.length !== 0) {\n          this.isHide = false;\n\n          this.newReminders = newValue;\n          this.timer = setTimeout(() => {\n            this.isHide = true;\n            this.remindersTemp = [];\n            clearInterval(this.queueTimer);\n            this.queueTimer = null;\n            this.remindersQueue = [];\n          }, 10000);\n        }\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.reminder {\n  position: absolute;\n  height: 122px;\n  bottom: 70px;\n  left: 6px;\n  color: #fff;\n  font-size: 12px;\n  overflow: hidden;\n  z-index: 99;\n  &-toolbar {\n    width: 66px;\n    height: 32px;\n    background: rgba(0, 0, 0, 0.7);\n    border-radius: 3px 16px 16px 3px;\n    margin-bottom: 6px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    overflow: hidden;\n    img {\n      margin-right: 6px;\n    }\n  }\n\n  &-content {\n    padding: 0;\n    margin: 0;\n    li {\n      margin-bottom: 6px;\n      display: flex;\n      justify-content: left;\n      div {\n        display: inline-flex;\n        align-items: center;\n        padding: 0 12px;\n        height: 22px;\n        background: rgba(0, 0, 0, 0.7);\n        border-radius: 3px;\n      }\n      .name {\n        display: inline-block;\n        max-width: 180px;\n        margin-right: 3px;\n        color: #92bfff;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n    }\n  }\n  &-hide {\n    display: none;\n  }\n  &-show {\n    display: block;\n  }\n  &-close {\n    left: 0;\n    .reminder-toolbar {\n      width: 26px;\n      img {\n        transform: rotate(180deg);\n      }\n    }\n  }\n\n  &-expand .reminder-content {\n    visibility: visible;\n  }\n  &-close .reminder-content {\n    visibility: hidden;\n  }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=0c05b1f7&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0c05b1f7&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c05b1f7\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"wrap-audio\"},[_c('audio',{ref:\"audioRef\",attrs:{\"autoPlay\":\"\",\"muted\":_vm.muted}})])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"wrap-audio\">\n    <audio autoPlay ref=\"audioRef\" :muted=\"muted\"></audio>\n  </div>\n</template>\n<script>\n\nexport default {\n  props: [\"muted\", \"streamId\", \"client\"],\n  mounted() {\n    this.renderAudio();\n  },\n  beforeDestroy() {\n    this.$refs[\"audioRef\"] && this.$refs[\"audioRef\"].pause();\n  },\n  methods: {\n    renderAudio() {\n      const audioEle = this.$refs[\"audioRef\"];\n      if (audioEle && this.client) {\n        this.client.setAudioRenderer(this.streamId, audioEle);\n      }\n    },\n  },\n};\n</script>\n<style scoped></style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=e50b3f90&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e50b3f90\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{ref:\"videoWrapRef\",class:['wrap-video', { 'video-small': _vm.item.templateConfig && _vm.item.templateConfig.isPIP }],style:(_vm.item.positionStyle),attrs:{\"id\":_vm.wrapVideoId},on:{\"click\":function($event){return _vm.toggleFullScreen($event)}}},[_c('div',{staticClass:\"video\"},[_c('div',{staticClass:\"video-content\",style:({ border: _vm.border })},[_c('div',{staticClass:\"video-model\"},[_c('div',{class:_vm.audioOnlyClass},[_c('div',{staticClass:\"center\"},[_c('div',{staticClass:\"name\"},[_vm._v(_vm._s(_vm.item.roster.displayName || ''))]),_c('div',[_vm._v(\"语音通话中\")])])]),_c('div',{class:_vm.videoMuteClass},[_c('div',{staticClass:\"center\"},[_c('FitAvatar',{attrs:{\"textAvatar\":_vm.item.textAvatar,\"avatar\":_vm.item.avatar,\"containerWidth\":_vm.containerWidth}})],1)]),_c('div',{class:_vm.videoRequestClass},[_c('div',{staticClass:\"request-loading\"})]),_c('div',{staticClass:\"video-status\"},[(!_vm.item.roster.isContent)?_c('div',{class:_vm.item.roster.audioTxMute ? 'audio-muted-status' : 'audio-unmuted-status'}):_vm._e(),_c('div',{staticClass:\"name\"},[_vm._v(\" \"+_vm._s(`${_vm.item.roster.displayName || 'Local'}`)+\" \")])])])]),_c('video',{style:(_vm.item.rotate),attrs:{\"autoPlay\":\"\"}})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.textAvatar)?_c('div',{staticClass:\"video-avatar\",style:(_vm.avatarStyle)},[_vm._v(\" \"+_vm._s(_vm.textAvatar)+\" \")]):_c('Avatar',{staticClass:\"video-avatar\",style:(_vm.avatarStyle),attrs:{\"src\":\"avatar\"}})\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('img',{attrs:{\"src\":_vm.imageSrc,\"alt\":\"Avatar\"},on:{\"error\":_vm.onImageError}})\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "/**\n * enum tools\n */\n\nimport h323 from '@/assets/img/type/h323.png';\nimport nemo from '@/assets/img/type/nemo.png';\nimport tvbox from '@/assets/img/type/tvbox.png';\nimport noicon from '@/assets/img/type/noicon.png';\nimport bruce from '@/assets/img/type/bruce.png';\n\nexport const DEFAULT_SETTING = {\n  localHide: false,\n  layoutMode: 'AUTO',\n  isThird: true,\n};\n\nexport const DEFAULT_LOCAL_USER = {\n  phone: '',\n  password: '',\n  meeting: '',\n  meetingPassword: '',\n  meetingName: '',\n  muteVideo: false,\n  muteAudio: false\n};\n\nexport const DEFAULT_CALL_INFO = {\n  avatar: '',\n  displayName: '',\n  numberType: 'CONFERENCE',\n  number: '',\n  callNumber: '',\n};\n\nexport const NEXT_DEVICE = {\n  deviceId: '',\n  label: '',\n};\n\n/**\n * 默认设备列表信息\n */\nexport const DEFAULT_DEVICE = {\n  detail: {\n    audioInputList: [],\n    audioOutputList: [],\n    videoInList: [],\n  },\n  nextDevice: {\n    audioInput: NEXT_DEVICE,\n    videoInput: NEXT_DEVICE,\n    audioOutput: NEXT_DEVICE,\n  },\n};\n\n/**\n * 参会者默认头像\n */\nexport const DEVICE_TYPE_MAP = {\n  webrtc: noicon,\n  soft: noicon,\n  hard: nemo,\n  nemono: nemo,\n  virtualnemo: nemo,\n  nemo,\n  tvbox,\n  h323,\n  bruce,\n  desk: noicon,\n  default: noicon,\n};\n\n/**\n * 参会者列表最多显示人数\n */\nexport const MAX_PARTICIPANT_COUNT = 500;\n\n/**\n * 参会者列表每页显示最大数量\n */\nexport const PARTICIPANT_PAGE_SIZE = 20;\n\n/**\n * 模板对应位置类型\n *\n * @enum\n * @property {LOCAL} LOCAL 本地\n * @property {CONTENT} CONTENT 共享画面\n */\nexport const TEMPLATE_TYPE = {\n  LOCAL: 'LOCAL',\n  CONTENT: 'CONTENT',\n};\n\n/**\n * 请流质量\n *\n * 0: low 低画面质量，帧率会自动降低到15帧接收；\n * 1: normal 普通画面质量，会基于带宽信息自动在30/15帧切换；\n * 2: high 高画面质量，会优先匹配高帧率高分辨率画面；\n */\nexport const VIDEO_QUALITY = {\n  LOW: 0,\n  NORMAL: 1,\n  HIGH: 2,\n};\n", "<template>\n  <img :src=\"imageSrc\" @error=\"onImageError\" alt=\"Avatar\" />\n</template>\n\n<script>\nimport { DEVICE_TYPE_MAP } from '@/utils/enum';\nexport default {\n  props: {\n    src: {\n      type: String,\n      required: true,\n    },\n    type: {\n      type: String,\n      default: 'default', \n    },\n  },\n  data() {\n    return {\n      imageSrc: this.src,\n      defaultSrc: DEVICE_TYPE_MAP[this.type] || DEVICE_TYPE_MAP.default,\n    };\n  },\n  created() {\n    this.imageSrc = this.src;\n  },\n  methods: {\n    onImageError() {\n      this.imageSrc = this.defaultSrc;\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=0aa56e6a\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"video-avatar\" :style=\"avatarStyle\" v-if=\"textAvatar\">\n    {{ textAvatar }}\n  </div>\n  <Avatar v-else src=\"avatar\" class=\"video-avatar\" :style=\"avatarStyle\" />\n</template>\n\n<script>\nimport Avatar from '@/components/Avatar';\n\nexport default {\n  props: ['textAvatar', 'avatar', 'containerWidth'],\n  components: { Avatar },\n  computed: {\n    avatarStyle() {\n      let size = 44;\n      let fontSize = 16;\n      if (this.containerWidth > 300) {\n        size = 72;\n        fontSize = 18;\n      }\n\n      return {\n        width: size + 'px',\n        height: size + 'px',\n        fontSize: fontSize + 'px',\n      };\n    },\n  },\n  data() {\n    return {};\n  },\n  created() {},\n  methods: {},\n};\n</script>\n<style scoped>\n.video-avatar {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(to bottom right, #3876ff, #447eff);\n  color: #fff;\n}\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=872385b6&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=872385b6&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"872385b6\",\n  null\n  \n)\n\nexport default component.exports", "\nexport const Event = {\n  lastTapTime: 0,\n  lastTapTimer: null,\n  click: function (params, doubleClick, singleClick = () => {}) {\n    const currentTime = params.timeStamp;\n    const lastTapTime = this.lastTapTime;\n    this.lastTapTime = currentTime;\n\n    if (currentTime - lastTapTime < 250) {\n      this.lastTapTime = 0;\n      if (doubleClick) doubleClick();\n      // @ts-ignore\n      clearTimeout(this.lastTapTimer);\n    } else {\n      // @ts-ignore\n      this.lastTapTimer = setTimeout(() => {\n        this.lastTapTime = 0;\n        if (singleClick) singleClick();\n      }, 250);\n    }\n  },\n};\n", "<template>\n  <div :class=\"['wrap-video', { 'video-small': item.templateConfig && item.templateConfig.isPIP }]\"\n    :style=\"item.positionStyle\" ref=\"videoWrapRef\" :id=\"wrapVideoId\" @click=\"toggleFullScreen($event)\">\n    <div class=\"video\">\n      <div class=\"video-content\" :style=\"{ border }\">\n        <div class=\"video-model\">\n          <div :class=\"audioOnlyClass\">\n            <div class=\"center\">\n              <div class=\"name\">{{ item.roster.displayName || '' }}</div>\n              <div>语音通话中</div>\n            </div>\n          </div>\n\n          <div :class=\"videoMuteClass\">\n            <div class=\"center\">\n              <FitAvatar :textAvatar=\"item.textAvatar\" :avatar=\"item.avatar\" :containerWidth=\"containerWidth\" />\n            </div>\n          </div>\n\n          <div :class=\"videoRequestClass\">\n            <div class=\"request-loading\"></div>\n          </div>\n\n          <div class=\"video-status\">\n            <div v-if=\"!item.roster.isContent\"\n              :class=\"item.roster.audioTxMute ? 'audio-muted-status' : 'audio-unmuted-status'\"></div>\n            <div class=\"name\">\n              {{ `${item.roster.displayName || 'Local'}` }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <video :style=\"item.rotate\" autoPlay></video>\n    </div>\n  </div>\n</template>\n<script>\nimport FitAvatar from '../FitAvatar';\nimport { Event } from \"@/utils/event\";\n\nexport default {\n  props: ['index', 'id', 'item', 'layoutMode', 'model', 'forceLayoutId', 'client'],\n  components: { FitAvatar },\n  computed: {\n    state () {\n      return this.item.state;\n    },\n    border () {\n      let border = '';\n      if (this.model === 'gallery' && this.item.roster.isActiveSpeaker) {\n        border = '2px solid #1483eb';\n      } else {\n        border = 'none';\n      }\n      return border;\n    },\n    audioOnlyClass () {\n      return `video-bg ${this.state === 'AUDIO_TEL' || this.state === 'AUDIO_CONTENT' || this.state === 'AUDIO_ONLY'\n        ? 'video-show'\n        : 'video-hidden'\n        }`;\n    },\n    videoMuteClass () {\n      return `video-bg ${this.state === 'MUTE' || this.state === 'INVALID' ? 'video-show' : 'video-hidden'}`;\n    },\n    videoRequestClass () {\n      return `video-bg ${this.state === 'REQUEST' ? 'video-show' : 'video-hidden'}`;\n    },\n    // 是否全屏\n    isFullScreen () {\n      return this.forceLayoutId === this.item.roster.id;\n    },\n    containerWidth () {\n      return this.item.positionInfo?.width || 0;\n    },\n  },\n  data () {\n    return {\n      wrapVideoId: 'wrap-' + this.id,\n    };\n  },\n  mounted () {\n    this.renderVideo(this.id);\n  },\n  methods: {\n    async toggleFullScreen (event) {\n      Event.click(event, () => {\n        event.stopPropagation();\n\n        this.$emit('forceFullScreen', this.isFullScreen ? '' : this.id);\n      });\n    },\n\n    renderVideo (newValue) {\n      if (newValue && this.client) {\n        this.client.setVideoRenderer(newValue, 'wrap-' + newValue);\n      }\n    },\n  },\n  watch: {\n    id: {\n      handler (newValue) {\n        this.renderVideo(newValue);\n      },\n      deep: true,\n    },\n  },\n};\n</script>\n<style scoped>\n.wrap-video {\n  position: absolute;\n  background: #000;\n  user-select: none;\n  overflow: hidden;\n  z-index: 1;\n}\n\n.video-small {\n  border: 1px solid #fff;\n  box-sizing: content-box;\n}\n\n.video {\n  width: 100%;\n  height: 100%;\n  user-select: none;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.video .video-content {\n  width: 100%;\n  height: 100%;\n\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  z-index: 10;\n}\n\n.video .video-content:hover .operate-icon {\n  opacity: 1;\n}\n\n.video video {\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  object-fit: contain;\n}\n\n.video audio {\n  position: absolute;\n}\n\n.video .video-model {\n  width: 100%;\n  height: 100%;\n  box-sizing: border-box;\n  position: relative;\n  color: #ddd;\n  font-size: 12px;\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.video .video-model .video-status {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  background-color: rgba(42, 46, 51, 0.8);\n  display: flex;\n  align-items: center;\n  max-width: 90%;\n  height: 21px;\n}\n\n.video .video-model .name {\n  flex: 1;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  margin-right: 6px;\n  margin-left: 6px;\n}\n\n.video .video-model .video-bg {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n  transition: opacity ease 0.2s;\n  background: linear-gradient(315deg, #282828 0%, #3d3d3d 100%);\n}\n\n.video .video-model .video-hidden {\n  position: absolute;\n  opacity: 0;\n}\n\n.video .video-model .video-show {\n  display: flex;\n  opacity: 1;\n}\n\n.video .video-model .center {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n  position: relative;\n  z-index: 1;\n}\n\n.video .video-model .audio-muted-status,\n.video .video-model .audio-unmuted-status {\n  width: 26px;\n  height: 24px;\n  margin: 0 -8px 0 -2px;\n}\n\n.video .video-model .audio-muted-status {\n  background: url(\"./img/audio_mute.png\") center center no-repeat;\n  background-size: 60%;\n}\n\n.video .video-model .audio-unmuted-status {\n  background: url(\"./img/audio_unmute.png\") center center no-repeat;\n  background-size: 60%;\n}\n\n.status {\n  position: absolute;\n  left: 1px;\n  top: 0px;\n  background-color: #0000002e;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  user-select: text;\n  z-index: 100;\n  padding-right: 5px;\n}\n\n.status p {\n  margin: 0;\n  line-height: 1;\n  font-size: 12px;\n}\n\n.operate-icon {\n  opacity: 0;\n\n  color: rgb(255, 255, 255);\n  font-size: 20px;\n  background-color: rgba(42, 46, 51, 0.6);\n  padding: 4px;\n  border-radius: 50%;\n\n  position: absolute;\n  top: 6px;\n  right: 6px;\n\n  cursor: pointer;\n\n  transition: all ease 0.2s;\n}\n\n.operate-icon:hover {\n  background-color: rgba(42, 46, 51, 0.8);\n}\n\n.request-loading {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  background: url(~@/assets/img/loading.png) no-repeat;\n  background-size: 100% 100%;\n  animation: circleRoate 1s infinite linear;\n}\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=4f06bbb2&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4f06bbb2&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f06bbb2\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"debug\"},[_c('div',{staticClass:\"debug__container\"},[_c('div',{staticClass:\"close-icon\",on:{\"click\":_vm.switchDebug}}),_c('h3',[_vm._v(\"总览：\")]),_c('table',{staticClass:\"table\"},[_vm._m(0),_c('tbody',[_c('tr',[_c('td',[_vm._v(_vm._s(_vm.mimeType))]),_c('td',[_vm._v(_vm._s(_vm.time || 0))]),_c('td',[_vm._v(_vm._s(_vm.bytesReceivedSecond))]),_c('td',[_vm._v(_vm._s(_vm.bytesSentSecond))])])])]),_c('br'),_c('h3',[_vm._v(\"发送：\")]),_c('table',{staticClass:\"table\"},[_vm._m(1),_c('tbody',_vm._l((_vm.sender),function(item,key){return _c('tr',{key:key},[_c('td',[_vm._v(\"本地视频\")]),_c('td',[_vm._v(_vm._s(item.frameWidth)+\"*\"+_vm._s(item.frameHeight))]),_c('td',[_vm._v(_vm._s(item.expBandwidth))]),_c('td',[_vm._v(_vm._s(item.bytesSentSecond))]),_c('td',[_vm._v(_vm._s(item.framesEncodedSecond))]),_c('td',[_vm._v(_vm._s(item.framesSentSecond))]),_c('td',[_vm._v(_vm._s(item.keyFramesEncoded))])])}),0)]),_c('br'),_c('h3',[_vm._v(\"与会者：\")]),_c('table',{staticClass:\"table\"},[_vm._m(2),_c('tbody',_vm._l((_vm.receiver),function({\n            frameWidth,\n            frameHeight,\n            bytesReceivedSecond,\n            framesReceivedSecond,\n            framesDecodedSecond,\n            type,\n            name,\n            isContent,\n            keyFramesDecoded,\n          },key){return _c('tr',{key:key},[_c('td',[_vm._v(_vm._s(name))]),_c('td',[_vm._v(_vm._s(type)+\" * \"+_vm._s(isContent ? 'Con' : 'Peo'))]),_c('td',[_vm._v(_vm._s(frameWidth)+\"*\"+_vm._s(frameHeight))]),_c('td',[_vm._v(_vm._s(framesDecodedSecond))]),_c('td',[_vm._v(_vm._s(framesReceivedSecond))]),_c('td',[_vm._v(_vm._s(bytesReceivedSecond))]),_c('td',[_vm._v(_vm._s(keyFramesDecoded))])])}),0)])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('thead',[_c('tr',{staticClass:\"table-title\"},[_c('th',[_vm._v(\"视频编码\")]),_c('th',[_vm._v(\"时间\")]),_c('th',[_vm._v(\"接收（kb/s）\")]),_c('th',[_vm._v(\"发送（kb/s）\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('thead',[_c('tr',{staticClass:\"table-title\"},[_c('th',[_vm._v(\"类型\")]),_c('th',[_vm._v(\"分辨率\")]),_c('th',[_vm._v(\"期望发送（kb/s）\")]),_c('th',[_vm._v(\"发送（kb/s）\")]),_c('th',[_vm._v(\"编码（帧/s）\")]),_c('th',[_vm._v(\"码率（帧/s）\")]),_c('th',[_vm._v(\"关键帧\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('thead',[_c('tr',{staticClass:\"table-title\"},[_c('th',[_vm._v(\"昵称\")]),_c('th',[_vm._v(\"类型\")]),_c('th',[_vm._v(\"实际分辨率\")]),_c('th',[_vm._v(\"解码（帧/s）\")]),_c('th',[_vm._v(\"码率（帧/s）\")]),_c('th',[_vm._v(\"接收（kb/s）\")]),_c('th',[_vm._v(\"关键帧\")])])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"debug\">\n    <div class=\"debug__container\">\n      <div class=\"close-icon\" @click=\"switchDebug\" />\n\n      <h3>总览：</h3>\n      <table class=\"table\">\n        <thead>\n          <tr class=\"table-title\">\n            <th>视频编码</th>\n            <th>时间</th>\n            <th>接收（kb/s）</th>\n            <th>发送（kb/s）</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr>\n            <td>{{ mimeType }}</td>\n            <td>{{ time || 0 }}</td>\n            <td>{{ bytesReceivedSecond }}</td>\n            <td>{{ bytesSentSecond }}</td>\n          </tr>\n        </tbody>\n      </table>\n\n      <br />\n      <h3>发送：</h3>\n      <table class=\"table\">\n        <thead>\n          <tr class=\"table-title\">\n            <th>类型</th>\n            <th>分辨率</th>\n            <th>期望发送（kb/s）</th>\n            <th>发送（kb/s）</th>\n            <th>编码（帧/s）</th>\n            <th>码率（帧/s）</th>\n            <th>关键帧</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr v-for=\"(item, key) in sender\" :key=\"key\">\n            <td>本地视频</td>\n            <td>{{ item.frameWidth }}*{{ item.frameHeight }}</td>\n            <td>{{ item.expBandwidth }}</td>\n            <td>{{ item.bytesSentSecond }}</td>\n            <td>{{ item.framesEncodedSecond }}</td>\n            <td>{{ item.framesSentSecond }}</td>\n            <td>{{ item.keyFramesEncoded }}</td>\n          </tr>\n        </tbody>\n      </table>\n      <br />\n\n      <h3>与会者：</h3>\n      <table class=\"table\">\n        <thead>\n          <tr class=\"table-title\">\n            <th>昵称</th>\n            <th>类型</th>\n            <th>实际分辨率</th>\n            <th>解码（帧/s）</th>\n            <th>码率（帧/s）</th>\n            <th>接收（kb/s）</th>\n            <th>关键帧</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr\n            v-for=\"({\n              frameWidth,\n              frameHeight,\n              bytesReceivedSecond,\n              framesReceivedSecond,\n              framesDecodedSecond,\n              type,\n              name,\n              isContent,\n              keyFramesDecoded,\n            },\n            key) in receiver\"\n            :key=\"key\"\n          >\n            <td>{{ name }}</td>\n            <td>{{ type }} * {{ isContent ? 'Con' : 'Peo' }}</td>\n            <td>{{ frameWidth }}*{{ frameHeight }}</td>\n            <td>{{ framesDecodedSecond }}</td>\n            <td>{{ framesReceivedSecond }}</td>\n            <td>{{ bytesReceivedSecond }}</td>\n            <td>{{ keyFramesDecoded }}</td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n  </div>\n</template>\n<script>\nimport { transformTime } from '@/utils/index';\nexport default {\n  props: ['senderStatus'],\n  computed: {\n    time() {\n      return transformTime(this.timestamp);\n    },\n  },\n  data() {\n    const { mimeType, sender = {}, timestamp, receiver = {}, bytesReceivedSecond, bytesSentSecond } = this.senderStatus;\n    return {\n      mimeType,\n      sender,\n      timestamp,\n      receiver,\n      bytesReceivedSecond,\n      bytesSentSecond,\n    };\n  },\n  methods: {\n    switchDebug() {\n      this.$emit('switchDebug');\n    },\n  },\n  watch: {\n    senderStatus: {\n      handler(newValue) {\n        const { mimeType, sender = {}, timestamp, receiver = {}, bytesReceivedSecond, bytesSentSecond } = newValue;\n        this.mimeType = mimeType;\n        this.sender = sender;\n        this.timestamp = timestamp;\n        this.receiver = receiver;\n        this.bytesReceivedSecond = bytesReceivedSecond;\n        this.bytesSentSecond = bytesSentSecond;\n      },\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2f777a1f&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2f777a1f&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f777a1f\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('el-drawer',{attrs:{\"title\":\"\",\"withHeader\":false,\"visible\":_vm.visible,\"before-close\":_vm.closeParticipant,\"size\":_vm.size,\"direction\":_vm.direction,\"custom-class\":\"xy__drawer-member\"},on:{\"update:visible\":function($event){_vm.visible=$event}}},[_c('div',{staticClass:\"member\"},[_c('div',{staticClass:\"member-header\"},[_c('span',{staticClass:\"member-hide-btn\",on:{\"click\":_vm.closeParticipant}}),_vm._v(\" 参会者 \"),(!_vm.isPc)?_c('span',[_vm._v(\"（\"+_vm._s(_vm.count)+\"）\")]):_vm._e()]),_c('div',{staticClass:\"member-navbar\"},[_c('span',{class:_vm.navbarClass('all'),on:{\"click\":function($event){return _vm.onChangeTab('all')}}},[_vm._v(\" 已入会(\"+_vm._s(_vm.count)+\") \")]),(_vm.unmuteCount)?_c('span',{class:_vm.navbarClass('unmute'),on:{\"click\":function($event){return _vm.onChangeTab('unmute')}}},[_vm._v(\" 未静音(\"+_vm._s(_vm.unmuteCount)+\") \")]):_vm._e()]),_c('div',{staticClass:\"member-content\"},_vm._l((_vm.computedRosters),function(item){return _c('div',{key:item.participantId + item.mediagroupid,staticClass:\"member-item\"},[_c('div',{staticClass:\"info\"},[_c('div',{staticClass:\"avatar\"},[_c('img',{attrs:{\"src\":item.avatar,\"alt\":\"avatar\"}}),(item.memberStatusImg)?_c('img',{staticClass:\"avatar__status\",attrs:{\"src\":item.memberStatusImg,\"alt\":\"\"}}):_vm._e()]),_c('div',{staticClass:\"name\",attrs:{\"title\":item.displayName}},[_c('span',{staticClass:\"name__info\"},[_vm._v(\" \"+_vm._s(item.displayName)+\" \"),(item.isContentOnly)?_c('span',[_vm._v(\"(仅桌面共享)\")]):_vm._e()]),((_vm.content && _vm.content.endpointId) === item.endpointId && !item.isContentOnly)?_c('span',{staticClass:\"name__status\"},[_vm._v(\"正在共享...\")]):_vm._e()])]),(!item.isContent)?_c('div',{staticClass:\"member__staus\"},[_c('div',{staticClass:\"member__staus-audio\"},[_c('img',{attrs:{\"src\":item.audioImg,\"alt\":\"unmute\"}})]),_c('div',{staticClass:\"member__staus-video\"},[_c('img',{attrs:{\"src\":item.videoImg,\"alt\":\"mute\"}})])]):_vm._e()])}),0),(_vm.pageInfo.totalPage > 1)?_c('div',{staticClass:\"member-pagination\"},[_c('el-pagination',{attrs:{\"background\":\"\",\"small\":true,\"layout\":\"prev, pager, next\",\"total\":_vm.pageInfo.totalCount,\"current-page\":_vm.pageInfo.currentPage,\"page-size\":_vm.defaultPageSize,\"pager-count\":5},on:{\"current-change\":_vm.onChangePage}})],1):_vm._e()])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <el-drawer\n    title=\"\"\n    :withHeader=\"false\"\n    :visible.sync=\"visible\"\n    :before-close=\"closeParticipant\"\n    :size=\"size\"\n    :direction=\"direction\"\n    custom-class=\"xy__drawer-member\"\n  >\n    <div class=\"member\">\n      <div class=\"member-header\">\n        <span class=\"member-hide-btn\" @click=\"closeParticipant\"></span>\n        参会者\n        <span v-if=\"!isPc\">（{{ count }}）</span>\n      </div>\n\n      <div class=\"member-navbar\">\n        <span :class=\"navbarClass('all')\" @click=\"onChangeTab('all')\"> 已入会({{ count }}) </span>\n        <span v-if=\"unmuteCount\" :class=\"navbarClass('unmute')\" @click=\"onChangeTab('unmute')\">\n          未静音({{ unmuteCount }})\n        </span>\n      </div>\n\n      <div class=\"member-content\">\n        <div class=\"member-item\" v-for=\"item in computedRosters\" :key=\"item.participantId + item.mediagroupid\">\n          <div class=\"info\">\n            <div class=\"avatar\">\n              <img :src=\"item.avatar\" alt=\"avatar\" />\n              <img v-if=\"item.memberStatusImg\" class=\"avatar__status\" :src=\"item.memberStatusImg\" alt=\"\" />\n            </div>\n            <div class=\"name\" :title=\"item.displayName\">\n              <span class=\"name__info\">\n                {{ item.displayName }}\n                <span v-if=\"item.isContentOnly\">(仅桌面共享)</span>\n              </span>\n              <span\n                v-if=\"(content && content.endpointId) === item.endpointId && !item.isContentOnly\"\n                class=\"name__status\"\n                >正在共享...</span\n              >\n            </div>\n          </div>\n          <div class=\"member__staus\" v-if=\"!item.isContent\">\n            <div class=\"member__staus-audio\">\n              <img :src=\"item.audioImg\" alt=\"unmute\" />\n            </div>\n            <div class=\"member__staus-video\">\n              <img :src=\"item.videoImg\" alt=\"mute\" />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"member-pagination\" v-if=\"pageInfo.totalPage > 1\">\n        <el-pagination\n          background\n          :small=\"true\"\n          layout=\"prev, pager, next\"\n          :total=\"pageInfo.totalCount\"\n          :current-page=\"pageInfo.currentPage\"\n          :page-size=\"defaultPageSize\"\n          :pager-count=\"5\"\n          @current-change=\"onChangePage\"\n        >\n        </el-pagination>\n      </div>\n    </div>\n  </el-drawer>\n</template>\n<script>\nimport { DEVICE_TYPE_MAP, PARTICIPANT_PAGE_SIZE } from '@/utils/enum';\nimport { isPc } from '@/utils/browser';\nimport unmuteActive from '@/assets/img/operate/icon_mic.svg';\nimport muteActive from '@/assets/img/operate/icon_mute_mic.svg';\nimport muteCamera from '@/assets/img/operate/icon_mute_camera.svg';\nimport unmuteCamera from '@/assets/img/operate/icon_camera.svg';\nimport speaker from '@/assets/img/operate/icon_speaker.gif';\nimport local from '@/assets/img/operate/icon_me.svg';\nimport './index.scss';\n\nexport default {\n  props: ['rosters', 'content', 'count', 'client'],\n  computed: {\n    navbarClass() {\n      return function(tab) {\n        return `member-navbar-item ${this.tab === tab ? 'member-navbar-item-active' : ''}`;\n      };\n    },\n    computedRosters() {\n      return this.newRosters.map((item) => {\n        const {\n          participantId,\n          endpointId,\n          mediagroupid,\n          videoTxMute,\n          videoRxMute,\n          audioTxMute,\n          audioRxMute,\n          deviceType,\n          isContent,\n          isActiveSpeaker,\n        } = item;\n\n        const key = participantId + mediagroupid;\n        const avatar = DEVICE_TYPE_MAP[deviceType] || DEVICE_TYPE_MAP.default;\n        const audioImg = audioTxMute ? muteActive : unmuteActive;\n        const videoImg = videoTxMute ? muteCamera : unmuteCamera;\n        const isContentOnly = videoTxMute && videoRxMute && audioRxMute && audioTxMute;\n\n        let memberStatusImg = '';\n\n        if (isContent) {\n          return null;\n        }\n\n        if (endpointId === this.selfRoster?.endpointId) {\n          memberStatusImg = local;\n        } else {\n          if (isActiveSpeaker && !audioTxMute) {\n            memberStatusImg = speaker;\n          }\n        }\n\n        return Object.assign(item, {\n          key,\n          avatar,\n          audioImg,\n          videoImg,\n          isContentOnly,\n          memberStatusImg,\n        });\n      });\n    },\n  },\n  data() {\n    return {\n      visible: false,\n      selfRoster: null,\n      tab: 'all',\n      size: 300,\n      unmuteCount: 0,\n      defaultPageSize: PARTICIPANT_PAGE_SIZE,\n      pageInfo: {\n        pageSize: PARTICIPANT_PAGE_SIZE,\n        currentPage: 1,\n        totalPage: 0,\n        totalCount: 0,\n      },\n      originalRosters: this.rosters,\n      newRosters: [],\n      direction: isPc ? 'rtl' : 'btt',\n      isPc,\n    };\n  },\n  beforeMount() {\n    this.selfRoster = this.client?.getSelfRoster();\n  },\n  mounted() {\n    this.visible = true;\n  },\n  methods: {\n    fetchRosters() {\n      let newData = this.originalRosters.concat();\n\n      if (this.tab === 'unmute') {\n        newData = newData.filter((roster) => !roster.audioTxMute);\n      }\n\n      this.fetch(newData);\n    },\n    fetch(newData) {\n      let totalCount = 0;\n\n      let { currentPage, pageSize } = this.pageInfo;\n\n      if (newData instanceof Array) {\n        totalCount = newData.length;\n        const startIndex = Math.max((currentPage - 1) * pageSize, 0);\n        const endIndex = startIndex + pageSize > totalCount ? totalCount : startIndex + pageSize;\n        newData = newData.slice(startIndex, endIndex);\n\n        // 当前页没有数据时，返回第一页\n        if (currentPage > 1 && newData.length === 0) {\n          currentPage -= 1;\n          this.pageInfo.currentPage = currentPage;\n\n          this.fetch(newData);\n        }\n      }\n\n      let totalPage = Math.ceil(totalCount / pageSize);\n\n      this.pageInfo = {\n        ...this.pageInfo,\n        totalPage,\n        currentPage,\n        totalCount,\n      };\n\n      this.newRosters = newData;\n    },\n    setUnmuteCount() {\n      const length = this.originalRosters.filter((item) => !item.audioTxMute).length;\n\n      if (length === 0) {\n        this.tab = 'all';\n      }\n\n      this.unmuteCount = length;\n    },\n    onChangePage(page) {\n      this.pageInfo.currentPage = page;\n      this.fetchRosters();\n    },\n    onChangeTab(tab) {\n      this.tab = tab;\n      this.pageInfo.currentPage = 1;\n      this.fetchRosters();\n    },\n    closeParticipant() {\n      this.$emit('showParticipant', false);\n    },\n  },\n  watch: {\n    rosters: {\n      handler(newValue) {\n        this.originalRosters = newValue;\n        this.setUnmuteCount();\n        this.fetchRosters();\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n};\n</script>\n<style scoped></style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=72315066&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"72315066\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.isPc)?_c('el-popover',{attrs:{\"popper-class\":\"end-call-popover\",\"placement\":\"top\",\"title\":\"\",\"trigger\":\"click\"},model:{value:(_vm.visible),callback:function ($$v) {_vm.visible=$$v},expression:\"visible\"}},[_c('div',{staticClass:\"xy-btn-box\"},_vm._l((_vm.endCallData),function(item,index){return _c('div',{key:index,class:item.className,on:{\"click\":item.onHandle}},[_vm._v(\" \"+_vm._s(item.text)+\" \")])}),0),_c('div',{staticClass:\"button button-warn end-call\",attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_c('svg-icon',{attrs:{\"icon\":\"end_call\",\"type\":\"danger\"}}),_c('div',{staticClass:\"title\"},[_vm._v(\"挂断\")])],1)]):_c('div',[_c('div',{staticClass:\"button button-warn end-call\",attrs:{\"slot\":\"reference\"},on:{\"click\":_vm.show},slot:\"reference\"},[_c('svg-icon',{attrs:{\"icon\":\"end_call\",\"type\":\"danger\"}}),_c('div',{staticClass:\"title\"},[_vm._v(\"挂断\")])],1),_c('el-drawer',{attrs:{\"visible\":_vm.visible,\"direction\":\"btt\",\"before-close\":_vm.cancel,\"append-to-body\":true,\"withHeader\":false,\"custom-class\":\"xy__drawer-end\"},on:{\"update:visible\":function($event){_vm.visible=$event}}},[_c('div',{staticClass:\"xy-btn-box\"},_vm._l((_vm.endCallData),function(item,index){return _c('div',{key:index,class:item.className,on:{\"click\":item.onHandle}},[_vm._v(\" \"+_vm._s(item.text)+\" \")])}),0)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <el-popover v-if=\"isPc\" v-model=\"visible\" popper-class=\"end-call-popover\" placement=\"top\" title=\"\" trigger=\"click\">\n    <div class=\"xy-btn-box\">\n      <div v-for=\"(item, index) in endCallData\" :key=\"index\" :class=\"item.className\" @click=\"item.onHandle\">\n        {{ item.text }}\n      </div>\n    </div>\n    <div slot=\"reference\" class=\"button button-warn end-call\">\n      <svg-icon icon=\"end_call\" type=\"danger\" />\n      <div class=\"title\">挂断</div>\n    </div>\n  </el-popover>\n\n  <div v-else>\n    <div slot=\"reference\" class=\"button button-warn end-call\" @click=\"show\">\n      <svg-icon icon=\"end_call\" type=\"danger\" />\n      <div class=\"title\">挂断</div>\n    </div>\n\n    <el-drawer :visible.sync=\"visible\" direction=\"btt\" :before-close=\"cancel\" :append-to-body=\"true\" :withHeader=\"false\"\n      custom-class=\"xy__drawer-end\">\n      <div class=\"xy-btn-box\">\n        <div v-for=\"(item, index) in endCallData\" :key=\"index\" :class=\"item.className\" @click=\"item.onHandle\">\n          {{ item.text }}\n        </div>\n      </div>\n    </el-drawer>\n  </div>\n</template>\n\n<script>\nimport { isPc } from '@/utils/browser';\n\nexport default {\n  data () {\n    return {\n      isPc,\n      visible: false,\n      endCallData: [\n        {\n          text: '离开会议',\n          className: 'xy-btn xy-end-btn',\n          onHandle: this.stop,\n        },\n        {\n          text: '取消',\n          className: 'xy-btn xy-cancel-btn',\n          onHandle: this.cancel,\n        },\n      ],\n    };\n  },\n  methods: {\n    show () {\n      this.visible = true;\n    },\n    cancel () {\n      this.visible = false;\n    },\n    stop () {\n      this.visible = false;\n      this.$emit('stop');\n    },\n  },\n};\n</script>\n<style lang=\"scss\">\n@import './index.scss';\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=fe21d51a\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=fe21d51a&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:_vm.videoValue.videoClass,on:{\"click\":_vm.videoOperate}},[_c('svg-icon',{attrs:{\"icon\":_vm.videoValue.svgIcon,\"type\":_vm.videoValue.svgType}}),_c('div',{staticClass:\"title\"},[_vm._v(\" \"+_vm._s(_vm.video === \"unmuteVideo\" ? \"关闭摄像头\" : \"开启摄像头\")+\" \")])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div :class=\"videoValue.videoClass\" @click=\"videoOperate\">\n    <svg-icon :icon=\"videoValue.svgIcon\" :type=\"videoValue.svgType\" />\n    <div class=\"title\">\n      {{ video === \"unmuteVideo\" ? \"关闭摄像头\" : \"开启摄像头\" }}\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  props: [\"permission\", \"video\"],\n  computed: {\n    videoValue() {\n      let videoClass = \"button-warn mute_camera\";\n      let svgIcon = \"camera\";\n      let svgType = \"default\";\n\n      if (this.permission.camera === \"denied\") {\n        videoClass = \"button-warn mute_camera_error\";\n        svgIcon = \"mute_camera_error\";\n        svgType = \"danger\";\n      } else if (this.video === \"unmuteVideo\") {\n        videoClass = \"camera\";\n        svgIcon = \"camera\";\n      } else {\n        videoClass = \"button-warn mute_camera\";\n        svgIcon = \"mute_camera\";\n        svgType = \"danger\";\n      }\n      return {\n        videoClass: \"button \" + videoClass,\n        svgIcon,\n        svgType,\n      };\n    },\n  },\n  data() {\n    return {};\n  },\n  methods: {\n    videoOperate() {\n      this.$emit(\"videoOperate\");\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=3eff91b0\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:_vm.audioValue.audioClass,on:{\"click\":_vm.audioOperate}},[_c('div',{staticClass:\"mic-icon\"},[(_vm.permission.microphone !== 'denied' && !_vm.disableAudio)?_c('MicLevel',{attrs:{\"stream\":_vm.stream,\"audio\":_vm.audio}}):_vm._e(),_c('svg-icon',{attrs:{\"icon\":_vm.audioValue.svgIcon,\"type\":_vm.audioValue.svgType}})],1),_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.audioValue.audioStatus))])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return (_vm.audio === 'unmuteAudio')?_c('div',{staticClass:\"aec\"},[_c('div',{staticClass:\"aec_content\",style:({ transform: `translateY(-${_vm.micLevel}%)` })})]):_vm._e()\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"aec\" v-if=\"audio === 'unmuteAudio'\">\n    <div\n      class=\"aec_content\"\n      :style=\"{ transform: `translateY(-${micLevel}%)` }\"\n    />\n  </div>\n</template>\n\n<script>\nexport default {\n  props: [\"audio\", \"stream\"],\n  data() {\n    return {\n      audioLevelTimmer: null,\n      micLevel: 0, // 音量等级\n    };\n  },\n  mounted() {\n    this.setMicLevel();\n  },\n  beforeDestroy() {\n    this.micLevel = 0;\n    // 清理定时器\n    this.clearTimmer();\n  },\n  methods: {\n    clearTimmer() {\n      this.audioLevelTimmer && clearInterval(this.audioLevelTimmer);\n      this.audioLevelTimmer = null;\n      this.micLevel = 0;\n    },\n    // 设置音量\n    setMicLevel(audio = this.audio) {\n\n      if (audio !== \"unmuteAudio\" || this.audioLevelTimmer) {\n        this.clearTimmer();\n        return;\n      }\n\n      this.audioLevelTimmer = setInterval(async () => {\n        if (this.stream) {\n          try {\n            const level = await this.stream.getAudioLevel();\n\n            // 更新Audio的实时音量显示\n            this.micLevel = level;\n          } catch (err) {\n            this.clearTimmer();\n          }\n        }\n      }, 500);\n    },\n  },\n  watch: {\n    audio: {\n      handler(newValue) {\n        this.setMicLevel(newValue);\n      },\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n@import \"./index.scss\";\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=60980d86&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=60980d86&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"60980d86\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div :class=\"audioValue.audioClass\" @click=\"audioOperate\">\n    <div class=\"mic-icon\">\n      <MicLevel\n        v-if=\"permission.microphone !== 'denied' && !disableAudio\"\n        :stream=\"stream\"\n        :audio=\"audio\"\n      />\n      <svg-icon :icon=\"audioValue.svgIcon\" :type=\"audioValue.svgType\" />\n    </div>\n    <div class=\"title\">{{ audioValue.audioStatus }}</div>\n  </div>\n</template>\n\n<script>\nimport MicLevel from \"../MicLevel/index.vue\";\n\nexport default {\n  props: [\"stream\", \"permission\", \"audio\", \"disableAudio\", \"handStatus\"],\n  components: {\n    MicLevel,\n  },\n  computed: {\n    audioValue() {\n      let audioClass = \"button-warn mute_mic\";\n      let audioStatus = \"取消静音\";\n      let svgIcon = \"mic_null\";\n      let svgType = \"default\";\n\n      if (this.permission.microphone === \"denied\") {\n        audioClass = \"button-warn mute_mic_error\";\n        svgIcon = \"mute_mic_error\";\n      } else {\n        if (this.audio === \"muteAudio\" && !this.disableAudio) {\n          audioClass = \"button-warn mute_mic\";\n          svgIcon = \"cancel_mic_mute\";\n          svgType = \"danger\";\n        }\n\n        if (this.audio === \"unmuteAudio\" && !this.disableAudio) {\n          audioStatus = \"静音\";\n          audioClass = \"mic_aec\";\n        }\n\n        if (\n          this.audio === \"muteAudio\" &&\n          this.disableAudio &&\n          !this.handStatus\n        ) {\n          audioStatus = \"举手发言\";\n          audioClass = \"hand_up\";\n          svgIcon = \"hand_up\";\n        }\n\n        if (\n          this.audio === \"muteAudio\" &&\n          this.disableAudio &&\n          this.handStatus\n        ) {\n          audioStatus = \"取消举手\";\n          audioClass = \"hand_down\";\n          svgIcon = \"hand_down\";\n        }\n\n        if (this.audio === \"unmuteAudio\" && this.disableAudio) {\n          audioStatus = \"结束举手\";\n          audioClass = \"hand_end\";\n          svgIcon = \"hand_end\";\n        }\n      }\n\n      audioClass = \"button \" + audioClass;\n\n      return {\n        audioStatus,\n        audioClass,\n        svgIcon,\n        svgType,\n      };\n    },\n  },\n  data() {\n    return {};\n  },\n  methods: {\n    audioOperate() {\n      this.$emit(\"audioOperate\");\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=67819bde\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['meeting-prompt', _vm.toolVisible ? '' : 'meeting-prompt-top']},[(_vm.forceLayoutId)?_c('div',{staticClass:\"meeting-prompt-box\"},[_vm._v(\" 主屏已被锁定 \"),_c('span',{staticClass:\"lock-btn\",on:{\"click\":_vm.toggleForceFullScreen}},[_vm._v(\" 解锁 \")])]):_vm._e(),(_vm.localHide)?_c('div',{staticClass:\"meeting-prompt-box\"},[_vm._v(\" 已开启隐藏本地画面模式 \")]):_vm._e(),(_vm.recordStatus === 1)?_c('div',{staticClass:\"meeting-prompt-box\"},[_c('Timer',{attrs:{\"before\":\"\"}},[_vm._v(\"云端录制\")])],1):_vm._e(),(_vm.recordStatus === 2 || _vm.recordStatus === 3)?_c('div',{staticClass:\"meeting-prompt-box\"},[_c('Timer',{attrs:{\"before\":\"\",\"time\":false}},[_c('div',{staticClass:\"remote-record-content\"},[_c('span',[_vm._v(\"云端录制\")]),_vm._v(\" \"+_vm._s(_vm.recordStatus === 3 ? '暂停中' : '录制中')+\" \")])])],1):_vm._e(),(_vm.chairman)?_c('div',{staticClass:\"meeting-prompt-box\"},[_vm._v(\"主会场模式\")]):_vm._e(),(_vm.isLocalShareContent)?_c('div',{staticClass:\"meeting-prompt-box\"},[_vm._v(\"本地共享中\")]):_vm._e(),(_vm.content)?_c('div',{staticClass:\"meeting-prompt-box\"},[_c('span',{staticClass:\"meeting-prompt-content-name\"},[_vm._v(_vm._s(_vm.content.displayName))]),_vm._v(\" 正在共享 \")]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"timer-content\"},[(_vm.before)?_c('i',{class:{ 'timer-circle': true, 'circle-show': _vm.isEven, 'circle-hide': !_vm.isEven }}):_vm._e(),_c('div',{staticClass:\"timer-children\"},[_vm._t(\"default\")],2),(_vm.time)?_c('div',[_vm._v(\" \"+_vm._s(_vm.timer)+\" \")]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"timer-content\">\n    <i\n      v-if=\"before\"\n      :class=\"{ 'timer-circle': true, 'circle-show': isEven, 'circle-hide': !isEven }\"\n    ></i>\n    <div class=\"timer-children\">\n      <slot></slot>\n    </div>\n    <div v-if=\"time\">\n      {{ timer }}\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  props: {\n    before: {\n      type: Boolean,\n      default: false\n    },\n    time: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      timer: '00:00:00',\n      timerCount: 0,\n      meetingTimeout: null\n    };\n  },\n  computed: {\n    isEven() {\n      return parseInt(this.timer.slice(-1)) % 2 === 0;\n    }\n  },\n  methods: {\n    secondToDate(result) {\n      const h = String(Math.floor(result / 3600)).padStart(2, '0');\n      const m = String(Math.floor((result / 60) % 60)).padStart(2, '0');\n      const s = String(Math.floor(result % 60)).padStart(2, '0');\n      return h + ':' + m + ':' + s;\n    },\n    onCreateMeetingTimeCount() {\n      this.timerCount++;\n      this.meetingTimeout = setTimeout(() => {\n        clearTimeout(this.meetingTimeout);\n        this.meetingTimeout = null;\n        const meetingTime = this.secondToDate(this.timerCount);\n        this.timer = meetingTime;\n        this.onCreateMeetingTimeCount();\n      }, 1000);\n    }\n  },\n  created() {\n    this.onCreateMeetingTimeCount();\n  },\n  beforeDestroy() {\n    clearTimeout(this.meetingTimeout);\n    this.meetingTimeout = null;\n  }\n};\n</script>\n\n<style scoped>\n.timer-content {\n  display: flex;\n  align-items: center;\n}\n.timer-children {\n  margin-right: 6px;\n}\n\n.timer-circle {\n  display: inline-block;\n  width: 7px;\n  height: 7px;\n  border-radius: 50%;\n  background: #fa6a69;\n  margin-right: 6px;\n}\n\n.circle-show {\n  opacity: 1;\n}\n\n.circle-hide {\n  opacity: 0;\n}\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./timer.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./timer.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./timer.vue?vue&type=template&id=264a9668&scoped=true\"\nimport script from \"./timer.vue?vue&type=script&lang=js\"\nexport * from \"./timer.vue?vue&type=script&lang=js\"\nimport style0 from \"./timer.vue?vue&type=style&index=0&id=264a9668&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"264a9668\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div :class=\"['meeting-prompt', toolVisible ? '' : 'meeting-prompt-top']\">\n    <div\n      v-if=\"forceLayoutId\"\n      class=\"meeting-prompt-box\"\n    >\n      主屏已被锁定\n      <span\n        class=\"lock-btn\"\n        @click=\"toggleForceFullScreen\"\n      >\n        解锁\n      </span>\n    </div>\n    <div\n      v-if=\"localHide\"\n      class=\"meeting-prompt-box\"\n    >\n      已开启隐藏本地画面模式\n    </div>\n\n    <div\n      class=\"meeting-prompt-box\"\n      v-if=\"recordStatus === 1\"\n    >\n      <Timer before>云端录制</Timer>\n    </div>\n\n    <div\n      class=\"meeting-prompt-box\"\n      v-if=\"recordStatus === 2 || recordStatus === 3\"\n    >\n      <Timer\n        before\n        :time=\"false\"\n      >\n        <div class=\"remote-record-content\">\n          <span>云端录制</span>\n          {{recordStatus === 3 ? '暂停中' : '录制中'}}\n        </div>\n      </Timer>\n    </div>\n\n    <div\n      v-if=\"chairman\"\n      class=\"meeting-prompt-box\"\n    >主会场模式</div>\n\n    <div\n      v-if=\"isLocalShareContent\"\n      class=\"meeting-prompt-box\"\n    >本地共享中</div>\n\n    <div\n      v-if=\"content\"\n      class=\"meeting-prompt-box\"\n    >\n      <span class=\"meeting-prompt-content-name\">{{ content.displayName }}</span>\n      正在共享\n    </div>\n  </div>\n</template>\n\n<script>\nimport Timer from './timer.vue';\n\nexport default {\n  props: ['forceLayoutId', 'localHide', 'isLocalShareContent', 'content', 'chairman', 'toolVisible', 'recordStatus'],\n  components: {\n    Timer\n  },\n  data() {\n    return {};\n  },\n  methods: {\n    toggleForceFullScreen() {\n      this.$emit('forceFullScreen');\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n@import \"./index.scss\";\n</style>\n", "import mod from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2547c305&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2547c305&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2547c305\",\n  null\n  \n)\n\nexport default component.exports", "/**\n * webRTC config file\n *\n * @authors <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)\n * @date  2020-01-17 12:04:01\n */\nexport const SERVER = {\n  wssServer: 'wss://cloudapi.xylink.com',\n  httpServer: 'https://cloudapi.xylink.com',\n  logServer: 'https://log.xylink.com',\n};\n\nexport const ACCOUNT = {\n  extId: '606e6604e882a63891f1471e3e1d9d32af26e918',\n  clientId: 'lleI2gfbcKaszk9dyUzFxIZ8',\n  clientSecret: 'TrXAokrTBUHRXZ2HdJsWJioZHx6ZOdKT'\n};\n", "/**\n * Meeting layout init template data\n *\n * @authors <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)\n * @date  2019-10-24 20:41:38\n */\n// 分辨率配置：\n// 0: 90P\n// 1: 180P\n// 2: 360P\n// 3: 720P\n// 4: 1080P\n\n// 9:16的比例\nexport const RATE = 0.5625;\n\n// 画面隐藏的位置\nexport const HIDE_POSITION = [-30, -30, 1, 1];\n\n// 如何自定义模版数据：\n// 1. 第一位优先大画面位置\n// 2. 第二位优先考虑 Local 的位置，剩余是其他画面\n// 3. 以容器的比值计算 position 的比例\nexport const SPEAKER_TEMPLATE = {\n  '1-1': [\n    {\n      position: [0, 0, 1, 1],\n      resolution: 3,\n    },\n  ],\n  '2-1': [\n    {\n      position: [0, 0, 0.75, 1],\n      resolution: 3,\n    },\n    {\n      position: [0.75, 0, 0.25, 0.333],\n      resolution: 1,\n    },\n  ],\n  '3-1': [\n    {\n      position: [0, 0, 0.75, 1],\n      resolution: 3,\n    },\n    {\n      position: [0.75, 0, 0.25, 0.333],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.333, 0.25, 0.333],\n      resolution: 1,\n    },\n  ],\n  '4-1': [\n    {\n      position: [0, 0, 0.75, 1],\n      resolution: 3,\n    },\n    {\n      position: [0.75, 0, 0.25, 0.333],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.333, 0.25, 0.333],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.666, 0.25, 0.333],\n      resolution: 1,\n    },\n  ],\n  '5-1': [\n    {\n      position: [0.125, 0, 0.75, 0.75],\n      resolution: 3,\n    },\n    {\n      position: [0, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.25, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.5, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n  ],\n  '6-1': [\n    {\n      position: [0, 0, 0.75, 0.75],\n      resolution: 3,\n    },\n    {\n      position: [0, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.25, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.5, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.5, 0.25, 0.25],\n      resolution: 1,\n    },\n  ],\n  '7-1': [\n    {\n      position: [0, 0, 0.75, 0.75],\n      resolution: 3,\n    },\n    {\n      position: [0, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.25, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.5, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.5, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.25, 0.25, 0.25],\n      resolution: 1,\n    },\n  ],\n  '8-1': [\n    {\n      position: [0, 0, 0.75, 0.75],\n      resolution: 3,\n    },\n    {\n      position: [0, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.25, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.5, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.75, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.5, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0.25, 0.25, 0.25],\n      resolution: 1,\n    },\n    {\n      position: [0.75, 0, 0.25, 0.25],\n      resolution: 1,\n    },\n  ],\n};\n\n// 布局模版\nexport const TEMPLATE = () => {\n  // PC端布局模版\n  // template模版数据包含自己，所以length为6是，代表有5个远端画面，一个本地画面\n  // 增加rate，方便计算容器的宽高\n  return {\n    length: 8,\n    temp: {\n      0: SPEAKER_TEMPLATE['1-1'],\n      1: SPEAKER_TEMPLATE['1-1'],\n      2: SPEAKER_TEMPLATE['2-1'],\n      3: SPEAKER_TEMPLATE['3-1'],\n      4: SPEAKER_TEMPLATE['4-1'],\n      5: SPEAKER_TEMPLATE['5-1'],\n      6: SPEAKER_TEMPLATE['6-1'],\n      7: SPEAKER_TEMPLATE['7-1'],\n      8: SPEAKER_TEMPLATE['8-1'],\n    },\n    rate: {\n      0: 0.5625,\n      1: 0.5625,\n      2: 0.4218,\n      3: 0.4218,\n      4: 0.4218,\n      5: 0.5625,\n      6: 0.5625,\n      7: 0.5625,\n      8: 0.5625,\n    },\n  };\n};\n", "import { RATE } from '@/utils/template';\nimport { TYPEOF } from '@/utils';\n\nexport const calculateBaseLayoutList = (orderLayoutList, rateWidth, rateHeight, positionInfo) => {\n  let positionStyle = { left: '0px', top: '0px', width: '0px', height: '0px' };\n\n  const layoutList = orderLayoutList.map((item, index) => {\n    let { position, customStyle = {} } = positionInfo[index] || {};\n    const [x, y, w, h] = position;\n\n    let layoutX = Math.round(rateWidth * x);\n    let layoutY = Math.round(rateHeight * y);\n    let layoutWidth = Math.round(rateWidth * w);\n    let layoutHeight = Math.min(Math.round(layoutWidth * RATE), rateHeight);\n\n    if (w === 1 && h === 1) {\n      layoutHeight = Math.round(rateHeight * h);\n    }\n\n    if (!TYPEOF.isObject(customStyle)) {\n      customStyle = {};\n    }\n\n    positionStyle = Object.assign(\n      {\n        left: `${layoutX}px`,\n        top: `${layoutY}px`,\n        width: `${layoutWidth}px`,\n        height: `${layoutHeight}px`,\n      },\n      customStyle\n    );\n\n    if (customStyle?.layoutWidth) {\n      layoutWidth = Math.min(customStyle.layoutWidth, rateWidth);\n    }\n\n    if (customStyle?.layoutHeight) {\n      layoutHeight = Math.min(customStyle?.layoutHeight, rateHeight);\n    }\n\n    const cachePositionInfo = {\n      width: layoutWidth,\n      height: layoutHeight,\n    };\n\n    return { ...item, positionStyle, positionInfo: cachePositionInfo };\n  });\n\n  return layoutList;\n};\n\n/**\n * 通过旋转信息获取在layout中的位置\n *\n * @param pid peopleId\n * @param mediagroupid type类型，0: people画面，1: content画面\n */\nexport const getLayoutIndexByRotateInfo = (nextLayoutList, pid, mid) => {\n  let index = -1;\n  let listLen = nextLayoutList.length;\n\n  for (let i = 0; i < listLen; i++) {\n    const item = nextLayoutList[i];\n    const { isContent, participantId } = item.roster;\n    // 是不是同一个人\n    const isSamePid = participantId === pid;\n    // mediagroupid： content/people设备标示，为0代表是people数据，为1代表是content数据\n    const isContentOfRotateDevice = mid === 1;\n    const match = isSamePid && isContentOfRotateDevice === isContent;\n\n    if (match) {\n      index = i;\n    }\n  }\n\n  return index;\n};\n\n// CUSTOM 自定义布局时，需要自行计算Layout容器信息\n// 此处使用CUSTOM 自定义布局实现一套SPEAKER演讲者模式的布局模式\n// 具体布局形式可参考AUTO布局的SPEAKER演讲者模式\nexport const getScreenInfo = (elementId = '', nextTemplateRate) => {\n  const { clientHeight, clientWidth } = document.getElementById(elementId) || document.body;\n\n  const rateHeight = Math.floor(clientWidth * nextTemplateRate);\n  const rateWidth = Math.floor(clientHeight / nextTemplateRate);\n  const screenInfoObj = { rateHeight: 0, rateWidth: 0 };\n\n  // 高充足，以屏幕宽计算高\n  if (clientHeight > rateHeight) {\n    screenInfoObj.rateHeight = nextTemplateRate === 1 ? clientHeight : rateHeight;\n    screenInfoObj.rateWidth = clientWidth;\n  } else {\n    // 否则，以比例宽计算高\n    screenInfoObj.rateHeight = clientHeight;\n    screenInfoObj.rateWidth = nextTemplateRate === 1 ? clientWidth : rateWidth;\n  }\n\n  return screenInfoObj;\n};\n\n// 将推送上来的custom layout数据进行排序处理\nexport const getOrderLayoutList = (layoutList) => {\n  const layoutLen = layoutList.length;\n  const baseLayout = {\n    content: [],\n    chairmain: [],\n    activeSpeaker: [],\n    audioVideoUnmute: [],\n    audioUnmute: [],\n    videoUnmute: [],\n    audioVideoMute: [],\n  };\n  // 排序数据\n  let orderedLayoutList = [];\n  let selfRoster;\n\n  for (let i = 0; i < layoutLen; i++) {\n    const item = layoutList[i];\n    const { isLocal, isContent, isForceFullScreen, isActiveSpeaker, audioTxMute, videoTxMute } = item.roster;\n\n    if (isLocal) {\n      selfRoster = item;\n\n      continue;\n    }\n\n    // 保存content\n    if (isContent) {\n      baseLayout['content'].push(item);\n      continue;\n    }\n\n    // 保存主会场数据\n    if (isForceFullScreen) {\n      baseLayout['chairmain'].push(item);\n      continue;\n    }\n\n    // 保存asp\n    if (isActiveSpeaker) {\n      baseLayout['activeSpeaker'].push(item);\n      continue;\n    }\n\n    if (!audioTxMute && !videoTxMute) {\n      baseLayout['audioVideoUnmute'].push(item);\n      continue;\n    }\n\n    if (!audioTxMute && videoTxMute) {\n      baseLayout['audioUnmute'].push(item);\n      continue;\n    }\n\n    if (audioTxMute && !videoTxMute) {\n      baseLayout['videoUnmute'].push(item);\n      continue;\n    }\n\n    if (audioTxMute && videoTxMute) {\n      baseLayout['audioVideoMute'].push(item);\n      continue;\n    }\n  }\n\n  for (let key in baseLayout) {\n    let newLayout = baseLayout[key];\n\n    if ((newLayout.length > 1 && key === 'audioVideoUnmute') || key === 'videoUnmute') {\n      newLayout.sort((a, b) => {\n        return b.roster.ExpectWidth - a.roster.ExpectWidth;\n      });\n    }\n\n    orderedLayoutList = orderedLayoutList.concat(newLayout);\n  }\n\n  return setSelfRoster(orderedLayoutList, selfRoster);\n};\n\nexport const setSelfRoster = (orderRosterList, selfRoster) => {\n  // 如果会中只有自己，则将自己的画面显示出来\n  if (orderRosterList.length === 0) {\n    selfRoster && orderRosterList.push(selfRoster);\n\n    return orderRosterList;\n  }\n\n  // 将自己的数据放置到第二位，一般来说，第一位都是content/AS/主会场等需要上大屏的数据\n  selfRoster && orderRosterList.splice(1, 0, selfRoster);\n\n  // 记录下来最终排好序的roster数据\n  return orderRosterList || [];\n};\n", "export default class Resize {\n  constructor() {\n    this.eleId = '';\n    this.element = null;\n    this.resizeObserver = null;\n    this.onResize = () => {};\n  }\n\n  init(eleId, onResize) {\n    this.eleId = eleId;\n    this.onResize = onResize;\n\n    this.resizeObserver = new ResizeObserver(() => {\n      const isHorizontal = window.innerWidth > window.innerHeight;\n\n      this.onResize({ isHorizontal });\n    });\n\n    this.element = document.getElementById(this.eleId);\n    \n    if (this.element) {\n      this.resizeObserver.observe(this.element);\n    }\n  }\n\n  /**\n   * 监听事件销毁事件\n   */\n  destroy() {\n    if (this.resizeObserver) {\n      this.resizeObserver.disconnect();\n      this.resizeObserver = null;\n    }\n\n    this.eleId = '';\n    this.element = null;\n  }\n}\n\nconst WindowResize = new Resize();\n\nexport { WindowResize };\n", "<template>\n  <div id=\"app\">\n    <div id=\"container\" class=\"container\">\n      <div class=\"loginMeetingBody\" v-if=\"(!loginMeeting && loginMeeting !== null) && !callMeeting && !callLoading\">\n        <div class=\"loginMeeting\">未检测到视频会议信息</div>\n        <div class=\"stop-btn\" @click=\"handleClose\">关闭</div>\n      </div>\n      <Login v-if=\"(loginMeeting && loginMeeting !== null) && !callMeeting && !callLoading\" :user=\"user\"\n        :isThird=\"setting.isThird\" @submitForm=\"submitForm\" @onToggleSetting=\"onToggleSetting\" />\n\n      <Loading v-if=\"callMeeting && callLoading\" :conferenceInfo=\"conferenceInfo\"\n        :audioOutputValue=\"selectedDevice.audioOutput.deviceId\" @stop=\"stop\" />\n\n      <div class=\"meeting\" v-if=\"callMeeting && !callLoading\">\n        <MeetingHeader :className=\"toolVisible ? 'xy__show' : 'xy__hide'\" :conferenceInfo=\"conferenceInfo\"\n          @switchDebug=\"switchDebug\" @stop=\"stop\" @flipCamera=\"handleFlipCamera\" />\n\n        <PromptInfo :toolVisible=\"toolVisible\" :forceLayoutId=\"forceLayoutId\" :chairman=\"chairman.hasChairman\"\n          :content=\"content\" :localHide=\"setting.localHide\" :isLocalShareContent=\"isLocalShareContent\"\n          :recordStatus=\"recordStatus\" @forceFullScreen=\"forceFullScreen\" />\n\n        <div class=\"meeting-content\" id=\"meeting\" @click.stop=\"handleToolVisible\">\n          <div v-if=\"pageStatus.previous && toolVisible\" class=\"previous-box\">\n            <div class=\"previous-button\" @click.stop=\"switchPage('previous')\">\n              <svg-icon icon=\"previous\" />\n            </div>\n            <div v-if=\"pageInfo.currentPage > 1\" class=\"home-button\" @click.stop=\"switchPage('home')\">回首页</div>\n          </div>\n          <div v-if=\"pageStatus.next && toolVisible\" class=\"next-box\">\n            <div class=\"next-button\" @click.stop=\"switchPage('next')\">\n              <svg-icon icon=\"next\" />\n              <div v-if=\"pageInfo.totalPage > 1 && pageInfo.currentPage > 0\" class=\"page-number\">\n                {{ pageInfo.currentPage }} /\n                {{ pageInfo.totalPage > 100 ? '...' : pageInfo.totalPage }}\n              </div>\n            </div>\n          </div>\n          <div class=\"meeting-layout\" :style=\"layoutStyle\">\n            <Video v-for=\"(item, index) in layout\" :key=\"item.roster.id\" :id=\"item.roster.id\" :index=\"index\"\n              :model=\"templateMode\" :layoutMode=\"setting.layoutMode\" :item=\"item\" :forceLayoutId=\"forceLayoutId\"\n              :client=\"client\" @forceFullScreen=\"forceFullScreen\"></Video>\n          </div>\n          <div class=\"audio-list\">\n            <Audio v-for=\"item in audioList\" :key=\"item.data.streams[0].id\" :muted=\"item.status === 'local'\"\n              :streamId=\"item.data.streams[0].id\" :client=\"client\" />\n          </div>\n          <Barrage v-if=\"!onhold && subTitle.content && subTitle.action === 'push'\" :subTitle=\"subTitle\" />\n          <InOutReminder v-if=\"!onhold\" :reminders=\"reminders\" />\n        </div>\n        <div :class=\"toolVisible ? 'meeting-footer xy__show' : 'meeting-footer xy__hide'\">\n          <div class=\"middle\">\n            <!-- <div class=\"button setting\" @click.stop=\"onToggleSetting\">\n              <svg-icon icon=\"setting\" />\n              <div class=\"title\">设置</div>\n            </div> -->\n\n            <div @click=\"participantVisible = true\"\n              :class=\"['button host', { 'disabled-button': conferenceInfo.numberType === 'APP' }]\">\n              <svg-icon icon=\"meeting_host\" />\n              <div class=\"title\">参会者</div>\n              <div class=\"tag\">{{ participantsCount }}</div>\n            </div>\n\n            <div v-if=\"isPc\" class=\"button layout\" @click=\"switchLayout\">\n              <svg-icon icon=\"layout\" />\n              <div class=\"title\">窗口布局</div>\n            </div>\n\n            <div v-if=\"isPc && isLocalShareContent\" @click=\"stopShareContent\" class=\"button button-warn share-stop\">\n              <svg-icon icon=\"share_stop\" type=\"danger\" />\n              <div class=\"title\">结束共享</div>\n            </div>\n\n            <div v-if=\"isPc && !isLocalShareContent\" @click=\"shareContent\"\n              :class=\"['button share', { 'disabled-button': contentIsDisabled }]\">\n              <svg-icon icon=\"share\" />\n              <div class=\"title\">共享</div>\n            </div>\n\n            <div @click=\"toggleRecord\" :class=\"['button share', { 'disabled-button': disableRecord }]\">\n              <svg-icon :icon=\"recordStatus === 0 ? 'record' : 'record_stop'\" />\n              <div class=\"title\">{{ recordStatus === 1 ? '停止录制' : '开始录制' }}</div>\n            </div>\n\n            <div v-if=\"isPc\" class=\"line\" />\n\n            <AudioButton :permission=\"permission\" :audio=\"audio\" :disableAudio=\"disableAudio\" :handStatus=\"handStatus\"\n              :stream=\"stream\" @audioOperate=\"audioOperate\" />\n\n            <VideoButton :permission=\"permission\" :video=\"video\" @videoOperate=\"videoOperate\" />\n\n            <EndCall @stop=\"stop\" />\n          </div>\n        </div>\n\n        <Participant v-if=\"participantVisible\" :client=\"client\" :content=\"content\" :rosters=\"rosters\"\n          :count=\"participantsCount\" @showParticipant=\"participantVisible = false\" />\n\n        <Internels v-if=\"debug\" :senderStatus=\"senderStatus\" @switchDebug=\"switchDebug\"></Internels>\n      </div>\n\n      <Setting v-if=\"settingVisible\" :setting=\"{ ...setting, selectedDevice }\" :visible=\"settingVisible\"\n        @cancel=\"onToggleSetting\" @setting=\"onSaveSetting\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport xyRTC, { getLayoutRotateInfo, LayoutOrientationType } from '@xylink/xy-rtc-sdk'\nimport cloneDeep from 'clone-deep'\nimport Login from './components/Login/index.vue'\nimport Loading from './components/Loading/index.vue'\nimport MeetingHeader from './components/Header/index.vue'\nimport Setting from './components/Setting/index.vue'\nimport Barrage from './components/Barrage/index.vue'\nimport InOutReminder from './components/InOutReminder/index.vue'\nimport Audio from './components/Audio/index.vue'\nimport Video from './components/Video/index.vue'\nimport Internels from './components/Internels/index.vue'\nimport Participant from './components/Participant/index.vue'\nimport EndCall from './components/EndCall/index.vue'\nimport VideoButton from './components/VideoButton/index.vue'\nimport AudioButton from './components/AudioButton/index.vue'\nimport PromptInfo from './components/PromptInfo/index.vue'\nimport store from '@/utils/store'\nimport { DEFAULT_LOCAL_USER, DEFAULT_DEVICE, DEFAULT_SETTING, DEFAULT_CALL_INFO, TEMPLATE_TYPE } from '@/utils/enum'\nimport { SERVER, ACCOUNT } from '@/utils/config'\nimport { TEMPLATE } from '@/utils/template'\nimport { message } from '@/utils/index'\nimport { getLayoutIndexByRotateInfo, getScreenInfo, calculateBaseLayoutList, getOrderLayoutList } from '@/utils/layout'\nimport { isPc, isSupportMobileJoinMeeting } from '@/utils/browser'\nimport { WindowResize } from '@/utils/resize'\n\nconst user = store.get('xy-user') || DEFAULT_LOCAL_USER\n\nconst elementId = 'container'\n\nlet rotationInfoRef = []\nlet nextLayoutListRef = []\n\nexport default {\n  name: 'App',\n  components: {\n    Login,\n    Loading,\n    Barrage,\n    InOutReminder,\n    Audio,\n    Video,\n    Internels,\n    Setting,\n    Participant,\n    EndCall,\n    VideoButton,\n    AudioButton,\n    PromptInfo,\n    MeetingHeader\n  },\n  computed: {\n    layoutStyle () {\n      const { rateWidth, rateHeight } = this.screenInfo\n      let style = { width: rateWidth + 'px', height: rateHeight + 'px' }\n      return style\n    },\n    pageStatus () {\n      const { currentPage = 0, totalPage = 0 } = this.pageInfo\n      const { participantCount = 1 } = this.confChangeInfo || {}\n      // 总页数大于当前页，则显示 \"下一页\"按钮\n      let next = currentPage < totalPage\n      // 非首页，则显示”上一页“按钮\n      let previous = currentPage !== 0\n      // 不显示分页按钮\n      // 1. 人数为1\n      // 2. 人数为2,且隐藏本地画面\n      // 3. 共享(本地)\n      // 4. 主会场(非本地、且在线)\n      if (participantCount === 1 || (participantCount === 2 && this.setting.localHide) || this.isLocalShareContent || this.chairman.chairmanUri) {\n        next = false\n        previous = false\n      }\n      return {\n        previous, // 上一页\n        next // 下一页\n      }\n    },\n    disableRecord () {\n      const { meeting, control } = this.recordPermission\n      return !meeting || !control || this.recordStatus === 2 || this.recordStatus === 3\n    }\n  },\n  data () {\n    return {\n      server: SERVER,\n      account: ACCOUNT,\n      client: null,\n      stream: null,\n      user: store.get('xy-user') || DEFAULT_LOCAL_USER, // 登录者信息\n      loginMeeting: null, // 输入入会\n      callMeeting: false, // 呼叫状态\n      callLoading: false, // 是否呼叫中\n      layout: [], // 参会成员数据，包含stream，roster，position等信息，最终依赖layout的数据进行画面布局、渲染、播放、状态显示\n      screenInfo: { rateWidth: 0, rateHeight: 0 }, // screen容器信息\n      audioList: [], // 所有声源列表\n      video: user.muteVideo ? 'muteVideo' : 'unmuteVideo', // 摄像头状态\n      audio: user.muteAudio ? 'muteAudio' : 'unmuteAudio', // 麦克风状态\n      disableAudio: false, // 是否强制静音\n      handStatus: false, // 举手状态\n      subTitle: { action: 'cancel', content: '' }, // 是否有字幕或点名\n      templateMode: 'speaker', // 桌面布局模式(语音激励模式、画廊模式)\n      participantsCount: 0, // 会议成员数量\n      isLocalShareContent: false, // 开启content的状态\n      senderStatus: { sender: {}, receiver: {} }, // 呼叫数据统计\n      debug: false, // 是否是调试模式（开启则显示所有画面的呼叫数据）\n      settingVisible: false, // 设置\n      setting: store.get('xy-setting') || DEFAULT_SETTING, // 设置信息\n      selectedDevice: DEFAULT_DEVICE.nextDevice, // 选择的设备信息\n      version: xyRTC.version,\n      permission: {\n        camera: '',\n        microphone: ''\n      },\n      onhold: false,\n      reminders: [],\n      pageInfo: {\n        pageSize: 7,\n        currentPage: 0,\n        totalPage: 0\n      },\n      confChangeInfo: {},\n      forceLayoutId: '', // 全屏 roster id\n      rosters: [], // 所有参会者列表\n      participantVisible: false, // 是否展示参会者列表\n      content: null,\n      conferenceInfo: DEFAULT_CALL_INFO, // 会议室信息\n      chairman: {\n        chairmanUri: '', // 主会场是否入会\n        hasChairman: false // 是否有设置主会场(预设主会场)\n      },\n      isPc, // 是否是PC端\n      toolVisible: true, // 操作条是否显示\n      recordStatus: 0, // 录制状态  0-未开启录制 1-本地开启录制 2-远端开启录制 3-远端录制暂停中\n      recordPermission: {\n        meeting: false, // 会议室 录制权限\n        control: false // 会控上报录制权限\n      },\n      IFacingMode: true\n    }\n  },\n  beforeMount () {\n    if (!isPc) {\n      document.body.setAttribute('data-role', 'mobile')\n    }\n  },\n  mounted () {\n    window.addEventListener('message', this.handleInit, false)\n  },\n  beforeDestroy () {\n    this.stop()\n  },\n  methods: {\n    handleInit (event) {\n      if (!event.data) return this.loginMeeting = false\n      const { wssServer, httpServer, logServer, extId, clientId, clientSecret, meeting, meetingPassword, meetingName, loginMeeting, facingMode } = event.data\n      this.IFacingMode = facingMode === 'BACK' ? false : true\n      this.loginMeeting = loginMeeting || true\n      if (wssServer) { this.server = { ...this.server, wssServer } }\n      if (httpServer) { this.server = { ...this.server, httpServer } }\n      if (logServer) { this.server = { ...this.server, logServer } }\n      if (extId && clientId && clientSecret) { this.account = { extId, clientId, clientSecret } }\n      if (meeting && meetingName) {\n        this.submitForm({ meeting, meetingPassword, meetingName, muteAudio: false, muteVideo: false })\n      }\n    },\n    handleToolVisible () {\n      this.toolVisible = !this.toolVisible\n    },\n    setUserInfo (values) {\n      this.user = values\n      this.audio = this.user.muteAudio ? 'muteAudio' : 'unmuteAudio'\n      this.video = this.user.muteVideo ? 'muteVideo' : 'unmuteVideo'\n    },\n    // 登录\n    async submitForm (values) {\n      this.setUserInfo(values)\n\n      const result = await (isPc ? xyRTC.checkSupportWebRTC() : xyRTC.checkSupportMobileWebRTC())\n      const { result: isSupport } = result\n\n      if (!isSupport) {\n        let msg = '浏览器版本太低，请升级最新的Chrome浏览器访问'\n        /**\n         * 手机端支持以下系统版本及浏览器\n         *\n         * ios 12.2+ safari\n         * ios 14.3+ 微信浏览器\n         *  android 8+ 微信浏览器 QQ浏览器等\n         */\n        if (!isPc && !isSupportMobileJoinMeeting()) {\n          msg = '请升级手机系统版本或尝试其他浏览器'\n        }\n        message.info(msg)\n        return\n      }\n      this.join()\n    },\n    // 加入会议\n    async join () {\n      this.callMeeting = true\n      this.callLoading = true\n      let callStatus = true\n      try {\n        const extUserId = Math.ceil(Math.random() * *********)\n        const { meeting, meetingPassword, meetingName, muteAudio, muteVideo } = this.user\n        const { layoutMode = 'AUTO', localHide = false } = this.setting\n        const { wssServer, httpServer, logServer } = this.server\n        const { clientId, clientSecret } = this.account\n        // 这里三方可以根据环境修改sdk log等级\n        xyRTC.logger.setLogLevel('NONE')\n        this.client = xyRTC.createClient({\n          // 注意，第三方集成时，默认是prd环境，不需要配置wss/http/log server地址；\n          wssServer,\n          httpServer,\n          logServer,\n          // 入会是否是自动静音\n          muteAudio,\n          // 入会是否是关闭摄像头\n          muteVideo,\n          // 使用哪一种布局方式：\n          // AUTO：自动布局，第三方只需要监听layout回调消息即可渲染布局和视频画面\n          // CUSTOM：自定义布局，灵活性更高，但是实现较为复杂，自定义控制参会成员的位置、大小和画面质量\n          layout: layoutMode,\n          container: {\n            elementId,\n            // AUTO布局时，Layout容器相对于elementId元素空间的偏移量，四个值分别对应：[上、下、左、右]\n            // 如果没有配置elementId元素，默认使用Body空间大小计算信息\n            offset: [0, 0, 0, 0]\n          },\n          clientId,\n          clientSecret,\n          // 隐藏本地画面\n          localHide\n        })\n\n        this.client.setFeatureConfig({\n          enableAutoResizeLayout: false,\n          enableLayoutAvatar: true,\n          enableCheckRecordPermission: true // 开启检测录制权限\n        })\n\n        this.initEventListener(this.client)\n\n        /**\n         * 重要提示\n         * 重要提示\n         * 重要提示\n         * 第三方登录，请在config配置文件里面配置企业账户信息\n         * 重要提示\n         * 重要提示\n         * 重要提示\n         */\n        let result\n        const { extId } = this.account\n        // 第三方企业用户登录\n        if (this.setting.isThird) {\n          result = await this.client.loginExternalAccount({\n            // 用户名自行填写\n            displayName: 'thirdName',\n            extId,\n            extUserId\n          })\n        } else {\n          // 小鱼登录\n          result = await this.client.loginXYlinkAccount(user.phone || '', user.password || '')\n        }\n        const { code, msg, detail } = result || {}\n        // XYSDK:950120 成功\n        // XYSDK:950104 账号密码错误\n        if (code === 'XYSDK:950104') {\n          message.info('登录密码错误')\n          this.callMeeting = false\n          this.callLoading = false\n          return\n        }\n        if (code !== 'XYSDK:950120') {\n          message.info(msg || '登录失败')\n          this.callMeeting = false\n          this.callLoading = false\n          return\n        }\n        const token = detail.access_token\n        callStatus = await this.client.makeCall({\n          token,\n          confNumber: meeting,\n          password: meetingPassword,\n          displayName: meetingName\n        })\n        if (callStatus) {\n          // 订阅全部参会者信息\n          this.client.subscribeBulkRoster()\n          this.stream = xyRTC.createStream()\n          const { audioInput, audioOutput, videoInput } = this.selectedDevice\n          await this.stream.init({\n            devices: {\n              audioInputValue: audioInput.deviceId || 'default',\n              audioOutputValue: audioOutput.deviceId || 'default',\n              videoInValue: videoInput.deviceId || ''\n            }\n          })\n          this.client.publish(this.stream, { isSharePeople: true })\n          WindowResize.init(elementId, this.onResize)\n          // 移动端 锁屏、退出后台等 需关掉本地视频、声音\n          if (!isPc && !this.onhold) {\n            document.addEventListener('visibilitychange', this.visibilitychange)\n          }\n        }\n      } catch (err) {\n        console.log('入会失败: ', err)\n        this.disconnected(err.msg || '呼叫异常，请稍后重试')\n      }\n    },\n    // 切换移动端布局方向\n    async handleOrientationChange (isHorizontal) {\n      if (isPc) return\n      if (!this.IFacingMode) {\n        await this.client.switchCamera('BACK')\n      }\n      const { MOBILE_HORIZONTAL, MOBILE_VERTICAL } = LayoutOrientationType\n      const orientation = isHorizontal ? MOBILE_HORIZONTAL : MOBILE_VERTICAL\n      this.client.setLayoutOrientation(orientation)\n      this.orientation = orientation\n    },\n    async handleFlipCamera () {\n      this.IFacingMode = !this.IFacingMode\n      if (this.IFacingMode) {\n        await this.client.switchCamera('FRONT')\n      } else {\n        await this.client.switchCamera('BACK')\n      }\n    },\n    onResize ({ isHorizontal }) {\n      if (!this.client) return\n      // CUSTOM 模式\n      if (this.setting.layoutMode === 'CUSTOM') {\n        // content模式，横竖屏显示数量不一致，需重新请流\n        // content模式，只需转换layout布局即可\n        if (this.confChangeInfo.contentUri) {\n          this.customRequestLayout()\n        } else {\n          this.createCustomLayout()\n        }\n        return\n      }\n      // AUTO 模式\n      // 通过resize监听横竖屏变化，兼容性会更好\n      // 监听手机端方向 设置layout方向\n      this.handleOrientationChange(isHorizontal)\n      this.client.updateLayoutSize()\n    },\n    async visibilitychange () {\n      if (document.visibilityState === 'hidden') {\n        if (this.video === 'unmuteVideo') {\n          await this.client.muteVideo()\n        }\n      } else {\n        if (this.video === 'unmuteVideo') {\n          await this.client.unmuteVideo()\n        }\n      }\n    },\n    // 挂断会议\n    disconnected (msg = '') {\n      message.info(msg)\n      this.stop()\n    },\n    // 结束会议操作\n    stop () {\n      WindowResize.destroy()\n      document.removeEventListener('visibilitychange', this.visibilitychange)\n      // 重置audio、video状态\n      this.audio = this.user.muteAudio ? 'muteAudio' : 'unmuteAudio'\n      this.video = this.user.muteVideo ? 'muteVideo' : 'unmuteVideo'\n      // sdk清理操作\n      this.client && this.client.destroy()\n      // 清理组件状\n      rotationInfoRef = []\n      nextLayoutListRef = []\n      this.callMeeting = false\n      this.callLoading = false\n      this.isLocalShareContent = false\n      this.debug = false\n      this.layout = []\n      this.settingVisible = false\n      this.client = null\n      this.stream = null\n      this.content = null\n      this.participantsCount = 0\n      this.conferenceInfo = DEFAULT_CALL_INFO\n      this.subTitle = { action: 'cancel', content: '' }\n      this.reminders = []\n      this.handleClose()\n    },\n    handleClose () {\n      console.log(window)\n      window.parent.postMessage('close', '*')\n      if (window.parent.xy_video_meeting) {\n        window.parent.xy_video_meeting()\n      }\n    },\n    // 监听client的内部事件\n    initEventListener (client) {\n      // 会议室信息\n      client.on('conference-info', e => {\n        this.conferenceInfo = e\n      })\n      // 退会消息监听，注意此消息很重要，内部的会议挂断都是通过此消息通知\n      client.on('disconnected', e => {\n        const showMessage = (e.detail && e.detail.message) || '呼叫异常，请稍后重试'\n        this.disconnected(showMessage)\n      })\n      // 会议成员数量数据\n      client.on('participants-count', e => {\n        this.participantsCount = e.participantsNum\n      })\n      // 从v2.0.2版本开始，需要监听conf-change-info来请求Layout数据。以前版本不兼容\n      // 接收到conf-change-info后，需要基于此列表数据计算想要请求的参会成员和共享Content画面流\n      // client.requestNewLayout请求后，会回调custom-layout数据，包含有请求的视频画面数据\n      client.on('conf-change-info', e => {\n        const { chairManUrl } = e\n        this.confChangeInfo = e\n        this.chairman = {\n          ...this.chairman,\n          chairmanUri: chairManUrl\n        }\n        // CUSTOM 模式\n        if (this.setting.layoutMode === 'CUSTOM') {\n          this.content = this.rosters.find(item => item.endpointId === this.confChangeInfo.contentUri)\n          const cacheCustomPageInfo = this.calcPageInfo()\n          this.customRequestLayout(cacheCustomPageInfo)\n        }\n      })\n      // AUTO 布局回调layout数据，使用此数据直接渲染画面即可\n      // CUSTOM 布局不需要监听此数据\n      client.on('layout', e => {\n        this.layout = e\n        if (this.forceLayoutId) {\n          const forceLayout = this.layout.find(item => item.roster.id === this.forceLayoutId)\n          if (!forceLayout) {\n            this.forceLayoutId = ''\n          }\n        }\n      })\n      // CUSTOM 自定义布局处理此数据\n      // 通过 requestLayout 请求视频流之后，会通过此回调返回所有的参会者列表数据\n      // 通过处理处理此数据，展示画面\n      client.on('custom-layout', e => {\n        this.createCustomLayout(e)\n      })\n      // 动态计算的显示容器信息\n      client.on('screen-info', e => {\n        this.screenInfo = e\n      })\n      // audio list数据\n      client.on('audio-track', e => {\n        this.audioList = e\n      })\n      // 呼叫状态\n      client.on('call-status', e => {\n        // XYSDK:950518 入会成功\n        // XYSDK:950519 正在呼叫中\n        // 呼叫失败，请将detail信息作为disconnected的第二个参数\n        const { code, msg, detail } = e\n        if (code === 'XYSDK:950518') {\n          message.info(msg)\n          // 提示\n          if (this.localHide) {\n            message.info('已开启隐藏本地画面模式', 5)\n          }\n          this.callLoading = false\n        } else if (code === 'XYSDK:950519') {\n          message.info(msg)\n        } else {\n          this.disconnected(msg, detail)\n        }\n      })\n      // 会控相关状态\n      client.on('meeting-control', e => {\n        const { disableMute, muteOperation, contentIsDisabled, chairmanUri, recordIsDisabled } = e\n        let info = ''\n        this.disableAudio = disableMute\n        this.contentIsDisabled = contentIsDisabled\n        // 会控录制权限\n        this.recordPermission = {\n          ...this.recordPermission,\n          control: !recordIsDisabled\n        }\n        this.chairman = {\n          ...this.chairman,\n          hasChairman: !!chairmanUri\n        }\n        if (muteOperation === 'muteAudio' && disableMute) {\n          info = '主持人已强制静音，如需发言，请点击“举手发言”'\n          this.handStatus = false\n        } else if (muteOperation === 'muteAudio' && !disableMute) {\n          info = '您已被主持人静音'\n        } else if (muteOperation === 'unmuteAudio' && disableMute) {\n          info = '主持人已允许您发言'\n          this.handStatus = false\n        } else if (muteOperation === 'unmuteAudio' && !disableMute) {\n          info = '您已被主持人取消静音'\n        }\n        // 在等候室时，不做提示\n        if (!this.onhold && info) {\n          message.info(info)\n        }\n      })\n      // 麦克风状态\n      client.on('audio-status', e => {\n        this.audio = e.status\n      })\n      // 摄像头状态\n      client.on('video-status', e => {\n        this.video = e.status\n      })\n      // 会议实时数据\n      client.on('meeting-stats', e => {\n        this.senderStatus = e\n      })\n      // 共享content\n      client.on('content', e => {\n        this.content = e.data\n      })\n      // 字幕、点名消息\n      client.on('sub-title', e => {\n        this.subTitle = e\n      })\n      // 清除举手\n      client.on('cancel-handup', e => {\n        if (e) {\n          this.onHand('handdown')\n        }\n      })\n      // 设备旋转信息\n      // 自定义布局需要处理\n      client.on('rotation-change', e => {\n        // 当手机竖屏入会，或者分享的竖屏的内容时\n        // 自定义布局需要手动计算视频画面的旋转信息\n        if (this.setting.layoutMode === 'CUSTOM') {\n          rotationInfoRef = e\n          // 计算屏幕旋转信息\n          nextLayoutListRef = this.calculateRotate()\n          // 更新layout布局列表数据\n          this.layout = nextLayoutListRef\n        }\n      })\n      // 设备切换\n      client.on('devices', async e => {\n        const { audioInput, videoInput, audioOutput } = e.nextDevice\n        videoInput?.label && message.info(`视频设备已自动切换至 ${videoInput.label}`)\n        audioInput?.label && message.info(`音频输入设备已自动切换至 ${audioInput.label}`)\n        setTimeout(() => {\n          audioOutput?.label && message.info(`音频输出设备已自动切换至 ${audioOutput.label}`)\n        }, 500)\n      })\n      // 设备权限\n      client.on('permission', async e => {\n        this.permission = e\n      })\n      // 被移入等候室\n      client.on('onhold', e => {\n        this.onhold = e\n        if (e) {\n          setTimeout(() => {\n            message.info('该会议室已开启等候室，请等待主持人审核')\n          }, 1000)\n        }\n      })\n      // AUTO布局 当前模板类型\n      client.on('template-mode', mode => {\n        this.templateMode = mode\n      })\n      // 出入会消息\n      client.on('in-out-reminder', e => {\n        this.reminders = e\n      })\n      // 分页信息\n      client.on('page-info', pageInfo => {\n        this.pageInfo = pageInfo\n      })\n      client.on('bulkRoster', this.handleBulkRoster)\n      // video、audio play failed, 在移动端某些浏览器，audio需要手动播放\n      client.on('play-failed', ({ type, key, error }) => {\n        if (type === 'video') {\n          console.log('[xyRTC on]video play failed:' + key, error)\n        }\n        if (type === 'audio') {\n          console.log('[xyRTC on]audio play failed:' + key, error)\n        }\n      })\n      // 录制权限\n      client.on('record-permission', data => {\n        const { authorize } = data\n        // 会议室录制权限\n        this.recordPermission = {\n          ...this.recordPermission,\n          meeting: authorize\n        }\n      })\n      // 处理 本地录制结果上报\n      client.on('recording-state-changed', data => {\n        const { reason, state, reasonText, recordInfo } = data\n        const { recordSessionId } = recordInfo\n        // 开启录制成功\n        if (state === 'RECORD_STATE_STARTED') {\n          this.recordStatus = 1\n          console.log('recordSessionId:::', recordSessionId)\n        }\n        // 停止录制成功\n        if (state === 'RECORD_STATE_IDLE') {\n          this.recordStatus = 0\n          if (reason !== 'STATE:200') {\n            message.info(reasonText)\n          } else {\n            message.info('录制完成，录制视频已保存到云会议室管理员的文件夹中')\n          }\n        }\n      })\n      // 处理 远端录制状态/会控录制状态 上报\n      client.on('record-status-notification', data => {\n        const { isStart, isLocal, status, recordInfo } = data\n        const { recordSessionId } = recordInfo\n        console.log('recordSessionId:::', recordSessionId)\n        if (isStart && !isLocal) {\n          // 录制暂停\n          this.recordStatus = status === 'RECORDING_STATE_PAUSED' ? 3 : 2\n        }\n        if (!isStart) {\n          this.recordStatus = 0\n        }\n      })\n    },\n    // 参会者信息\n    handleBulkRoster (e) {\n      // 参会者信息 是增量消息\n      // bulkRosterType: 0 - 全量roster, 1 - 增量roster\n      // addRosterInfo  新增的参会者信息  当bulkRosterType是0的时候，此参数表示全量数据\n      // changeRosterInfo  变化的参会者信息\n      // deleteRosterInfo  被删除的参会者信息\n      const { bulkRosterType = 0, addRosterInfo = [], changeRosterInfo = [], deleteRosterInfo = [] } = e\n      if (bulkRosterType === 0) {\n        this.rosters = addRosterInfo\n      } else {\n        // 新增参会者\n        let newRosters = this.rosters.concat(addRosterInfo)\n        // 删除离会的参会者\n        if (deleteRosterInfo.length > 0) {\n          deleteRosterInfo.forEach(info => {\n            const index = newRosters.findIndex(roster => {\n              return roster.participantId === info.participantId\n            })\n            if (index > -1) {\n              newRosters.splice(index, 1)\n            }\n          })\n        }\n        // 修改参会者信息，如果不存在，则添加到参会者列表中\n        if (changeRosterInfo.length > 0) {\n          changeRosterInfo.forEach(info => {\n            const index = newRosters.findIndex(roster => {\n              return roster.participantId === info.participantId\n            })\n            if (index > -1) {\n              newRosters[index] = info\n            } else {\n              newRosters.push(info)\n            }\n          })\n        }\n        this.rosters = newRosters\n      }\n    },\n    // CUSTOM布局 计算页码信息\n    calcPageInfo () {\n      const { participantCount } = this.confChangeInfo\n      let { pageSize, currentPage } = this.pageInfo\n      let cacheCustomPageInfo = JSON.parse(JSON.stringify(this.pageInfo))\n      // 会议产生变动，那么就重新计算总页数\n      // participantCount + contentPartCount 代表people + content的总个数\n      let totalPage = Math.ceil((participantCount - 1) / pageSize)\n      totalPage = totalPage > 0 ? totalPage : 0\n      // 如果当前的页码大于最新最大的页码，就更新到最后一页\n      if (currentPage > totalPage) {\n        currentPage = totalPage\n      }\n      cacheCustomPageInfo = {\n        ...cacheCustomPageInfo,\n        totalPage,\n        currentPage\n      }\n      this.pageInfo = cacheCustomPageInfo\n      return cacheCustomPageInfo\n    },\n    // CUSTOM布局 全屏请流\n    calcForceFullScreenRequestLayout (cacheCustomPageInfo = this.pageInfo) {\n      if (!this.forceLayoutId) {\n        return\n      }\n      const { pageSize } = cacheCustomPageInfo\n      const reqList = []\n      const item = this.layout.find(item => item.roster.id === this.forceLayoutId)\n      if (item) {\n        const { endpointId = '', mediagroupid = 0 } = item?.roster || {}\n        // 全屏的对象 content请1080，people请720的流，保证清晰度，其他不请流\n        let resolution = mediagroupid ? 4 : 3\n        reqList.push({\n          calluri: endpointId,\n          mediagroupid,\n          resolution,\n          quality: resolution === 4 ? 2 : 0\n        })\n        let extReqList = []\n        this.client?.requestNewLayout(reqList, pageSize, 0, extReqList, {\n          uiShowLocalWhenPageMode: false\n        })\n      } else {\n        this.forceLayoutId = ''\n      }\n      return this.forceLayoutId\n    },\n\n    // CUSTOM布局 请流\n    customRequestLayout (cacheCustomPageInfo = this.pageInfo) {\n      // forceFullScreen 请流\n      this.calcForceFullScreenRequestLayout()\n      if (this.forceLayoutId) {\n        return\n      }\n      const { chairManUrl, contentUri, participantCount } = this.confChangeInfo\n      const { pageSize, currentPage } = cacheCustomPageInfo\n      let reqList = []\n      let extReqList = []\n      const realContentLen = currentPage === 0 && contentUri ? 1 : 0\n      let realLen = participantCount + realContentLen\n      if (realLen > pageSize) {\n        if (realLen < currentPage * pageSize) {\n          realLen = realLen - (currentPage - 1) * pageSize\n        } else {\n          realLen = pageSize\n        }\n      }\n      let isRequest = currentPage > 1\n      // 移动端横屏模式下，接收content，只显示content+远端\n      if (currentPage === 0 && contentUri) {\n        realLen = 2\n        isRequest = true\n      }\n      const { temp, length } = TEMPLATE()\n      let templateLayout = temp[realLen] || temp[Math.max(1, length)]\n      templateLayout.forEach((item, index) => {\n        let { resolution = 2, quality = 1, type } = item\n        let calluri = ''\n        let mediagroupid = 0\n        if (type === TEMPLATE_TYPE.LOCAL && !isRequest) {\n          return\n        }\n        // 只在第一页显示content和主会场\n        if (currentPage === 0 && index === 0) {\n          calluri = contentUri || chairManUrl\n          mediagroupid = contentUri ? 1 : 0\n          if (contentUri) {\n            resolution = 4\n            quality = 2\n          }\n        }\n        reqList.push({\n          mediagroupid,\n          calluri,\n          resolution,\n          quality\n        })\n      })\n      this.client?.requestNewLayout(reqList, pageSize, currentPage, extReqList, {\n        uiShowLocalWhenPageMode: false\n      })\n    },\n    // CUSTOM布局\n    createCustomLayout (e = this.layout) {\n      // 如果forceFullScreen 的情况下，layout返回空，则说明当前终端已不在会，则需重新请流\n      if (this.forceLayoutId) {\n        const forceLayoutList = e.filter(item => item.roster.id === this.forceLayoutId)\n        const item = forceLayoutList[0]\n        // 当前layout为空，则说明当前终端已不在会，则需重新请流\n        // 全屏对象是content, 但是远端已取消content或者取消指定广播该content\n        if (!item || (item.roster.mediagroupid === 1 && !this.confChangeInfo.contentUri)) {\n          this.forceLayoutId = ''\n          this.customRequestLayout()\n          return\n        }\n        e = forceLayoutList\n      }\n      // 此处渲染没有排序处理，需要自行将回调数据排序并展示\n      // 此示例程序通过配置一个一组 TEMPLATE 模版数据，来计算layout container容器大小和layout item position/size/rotate 信息\n      // 如果不想通过此方式实现，第三方获取到customLayoutList数据后，自行处理数据即可\n      e = getOrderLayoutList(e)\n      // 移动端横屏模式下，首页，接收content, 只显示content+远端\n      const { contentUri, participantCount } = this.confChangeInfo\n      if (this.pageInfo.currentPage === 0 && contentUri && participantCount > 1) {\n        e = e.filter(item => !item.roster.isLocal)\n      }\n      const { rate, temp } = TEMPLATE()\n      const nextTemplateRate = rate[e.length] || 0.5625\n      const positionInfo = temp[e.length]\n      const { rateWidth, rateHeight } = getScreenInfo(elementId, nextTemplateRate)\n      // 设置layout container容器的大小\n      this.screenInfo = { rateWidth, rateHeight }\n      // 计算初始layoutList数据\n      // 包含计算每个参会成员的大小、位置\n      // 如果不需要做上述的getOrderLayoutList的排序操作，那么直接在calculateBaseLayoutList中的第一个参数配置e即可\n      nextLayoutListRef = calculateBaseLayoutList(e, rateWidth, rateHeight, positionInfo)\n      // 计算屏幕旋转信息\n      nextLayoutListRef = this.calculateRotate()\n      this.layout = nextLayoutListRef\n    },\n    // CUSTOM布局 计算 layout 成员渲染\n    calculateRotate () {\n      const rotationInfo = rotationInfoRef\n      const cacheNextLayoutList = cloneDeep(nextLayoutListRef)\n\n      rotationInfo.forEach(item => {\n        let rotateInfo = {}\n        const { participantId, mediagroupid, rotation } = item\n        const index = getLayoutIndexByRotateInfo(nextLayoutListRef, participantId, mediagroupid)\n\n        if (index >= 0) {\n          const layoutItem = cacheNextLayoutList[index]\n\n          let { width, height } = layoutItem?.positionInfo\n\n          // 调用 xy-rtc-sdk 库提供的 helper 函数【getLayoutRotateInfo】方便第三方计算旋转信息\n          // 提供 item 和 layoutItemContainerWidth 和 height 计算旋转信息\n          // 返回旋转角度和宽高样式，此数据和AUTO布局的计算结果一致\n          rotateInfo = getLayoutRotateInfo(item, width, height)\n\n          // 1和3对应需要将分辨率画面进行旋转90deg和270deg\n          const isRotate = rotation === 1 || rotation === 3\n\n          if (isRotate) {\n            rotateInfo = { ...rotateInfo, maxWidth: height + 'px' }\n          } else {\n            rotateInfo = { ...rotateInfo, maxHeight: '100%' }\n          }\n\n          cacheNextLayoutList[index]['rotate'] = rotateInfo\n        }\n      })\n\n      return cacheNextLayoutList\n    },\n    // 摄像头操作\n    async videoOperate () {\n      try {\n        let result = 'muteVideo'\n\n        if (this.video === 'unmuteVideo') {\n          result = await this.client.muteVideo()\n        } else {\n          result = await this.client.unmuteVideo()\n        }\n\n        this.video = result\n      } catch (err) {\n        const { msg = '无法访问您的摄像头设备' } = err || {}\n\n        message.error(msg)\n      }\n    },\n\n    // 麦克风操作\n    async onAudioOperate () {\n      try {\n        let result = 'muteAudio'\n\n        if (this.audio === 'unmuteAudio') {\n          result = await this.client.muteAudio()\n\n          message.info('麦克风已静音')\n        } else {\n          result = await this.client.unmuteAudio()\n        }\n        this.audio = result\n      } catch (err) {\n        const { msg = '无法访问您的麦克风设备' } = err || {}\n\n        message.error(msg)\n      }\n    },\n\n    // 取消举手\n    async onHand (type) {\n      const funcMap = {\n        handup: {\n          func: 'onHandUp',\n          msg: '发言请求已发送'\n        },\n        handdown: {\n          func: 'onHandDown',\n          msg: ''\n        },\n        mute: {\n          func: 'onMute',\n          msg: ''\n        }\n      }\n\n      try {\n        const { func, msg } = funcMap[type]\n        const handStatus = await this.client[func]()\n\n        this.handStatus = handStatus\n        if (msg) {\n          message.info(msg)\n        }\n      } catch (err) {\n        message.info('操作失败')\n      }\n    },\n\n    // 麦克风操作\n    async audioOperate () {\n      if (this.audio === 'muteAudio' && this.disableAudio && !this.handStatus) {\n        await this.onHand('handup')\n        return\n      }\n\n      if (this.audio === 'muteAudio' && this.disableAudio && this.handStatus) {\n        await this.onHand('handdown')\n        return\n      }\n\n      if (this.audio === 'unmuteAudio' && this.disableAudio) {\n        await this.onHand('mute')\n        return\n      }\n\n      this.onAudioOperate()\n    },\n\n    // 切换布局\n    async switchLayout () {\n      try {\n        await this.client.switchLayout()\n      } catch (err) {\n        console.log('switch layout error: ', err)\n      }\n    },\n\n    // 自定义布局分页\n    customSwitchPage (type) {\n      const { currentPage, totalPage } = this.pageInfo\n      let nextPage = currentPage\n\n      if (type === 'next') {\n        nextPage += 1\n      } else if (type === 'previous') {\n        nextPage -= 1\n      } else if (type === 'home') {\n        nextPage = 1\n      }\n\n      nextPage = Math.max(nextPage, 1)\n      nextPage = Math.min(nextPage, totalPage)\n\n      this.pageInfo = {\n        ...this.pageInfo,\n        currentPage: nextPage\n      }\n\n      this.customRequestLayout(this.pageInfo)\n    },\n\n    // 全屏\n    async forceFullScreen (id = '') {\n      if (this.setting.layoutMode === 'CUSTOM') {\n        if (id) {\n          const item = this.layout.find(item => item.roster.id === id)\n\n          if (!item || id === this.forceLayoutId) {\n            return\n          }\n        }\n\n        this.forceLayoutId = id\n\n        this.customRequestLayout()\n\n        return\n      }\n\n      await this.client.forceFullScreen(id)\n\n      this.forceLayoutId = id\n    },\n\n    // 分页\n    async switchPage (type) {\n      this.forceLayoutId = ''\n\n      if (this.setting.layoutMode === 'CUSTOM') {\n        this.customSwitchPage(type)\n        return\n      }\n\n      const { currentPage, totalPage } = this.pageInfo\n      let nextPage = currentPage\n\n      if (type === 'next') {\n        nextPage += 1\n      } else if (type === 'previous') {\n        nextPage -= 1\n      } else if (type === 'home') {\n        nextPage = 0\n      }\n\n      nextPage = Math.max(nextPage, 0)\n      nextPage = Math.min(nextPage, totalPage)\n\n      this.client?.setPageInfo(nextPage)\n    },\n\n    // 打开/关闭 设置弹框\n    onToggleSetting () {\n      this.settingVisible = !this.settingVisible\n    },\n\n    // 隐藏本地画面\n    async toggleLocal () {\n      const { status } = await this.client.toggleLocal()\n      const msg = status ? '已开启隐藏本地画面模式' : '已关闭隐藏本地画面模式'\n      message.info(msg)\n    },\n\n    // 切换设备\n    async switchDevice (key, device) {\n      const deviceMap = {\n        audioInput: {\n          key: 'audio',\n          zh_text: '音频输入设备'\n        },\n        audioOutput: {\n          key: 'video',\n          zh_text: '音频输出设备'\n        },\n        videoInput: {\n          key: 'video',\n          zh_text: '视频设备'\n        }\n      }\n\n      const { deviceId, label } = device\n      try {\n        if (key === 'audioOutput') {\n          await this.stream.setAudioOutput(deviceId)\n        } else if (key === 'audioInput' || key === 'videoInput') {\n          await this.stream.switchDevice(deviceMap[key]['key'], deviceId)\n        }\n        message.info(`${deviceMap[key]['zh_text']}已自动切换至 ${label}`)\n      } catch (err) {\n        message.error('设备切换失败')\n        return Promise.reject(err)\n      }\n    },\n\n    async onSwitchDevice (nextDevice) {\n      const { audioInput, videoInput, audioOutput } = nextDevice || DEFAULT_DEVICE.nextDevice\n\n      try {\n        if (audioInput?.deviceId !== this.selectedDevice?.audioInput?.deviceId) {\n          await this.switchDevice('audioInput', audioInput)\n        }\n\n        if (audioOutput?.deviceId !== this.selectedDevice?.audioOutput?.deviceId) {\n          await this.switchDevice('audioOutput', audioOutput)\n        }\n\n        if (videoInput?.deviceId !== this.selectedDevice?.videoInput?.deviceId) {\n          await this.switchDevice('videoInput', videoInput)\n        }\n      } catch (err) {\n        return Promise.reject(err)\n      }\n    },\n\n    async onSaveSetting (data) {\n      if (data['selectedDevice']) {\n        this.settingVisible = false\n\n        try {\n          if (this.stream) {\n            await this.onSwitchDevice(data['selectedDevice'])\n          }\n          this.selectedDevice = data.selectedDevice\n        } catch (err) {\n          console.log('switch device err:', err)\n        }\n\n        return\n      }\n\n      if (Object.prototype.hasOwnProperty.call(data, 'localHide')) {\n        if (this.client) {\n          this.toggleLocal()\n        }\n      }\n\n      const key = Object.keys(data)[0]\n      const value = data[key]\n\n      const xySetting = { ...this.setting, [key]: value }\n\n      store.set('xy-setting', xySetting)\n\n      this.setting = xySetting\n    },\n\n    // 停止分享content\n    stopShareContent () {\n      this.client.stopShareContent()\n      this.isLocalShareContent = false\n    },\n\n    // 分享content内容\n    async shareContent () {\n      try {\n        // screenAudio 共享时，是否采集系统音频。 true: 采集； false: 不采集\n        const result = await this.stream.createContentStream({\n          screenAudio: true\n        })\n\n        // 创建分享屏幕stream成功\n        if (result) {\n          this.isLocalShareContent = true\n\n          this.stream.on('start-share-content', () => {\n            this.client.publish(this.stream, { isShareContent: true })\n          })\n\n          this.stream.on('stop-share-content', () => {\n            this.stopShareContent()\n          })\n        } else {\n          message.info('分享屏幕失败')\n        }\n      } catch (err) {\n        console.log('share content failed:', err)\n        if (err && err.code !== 'XYSDK:950501') {\n          message.info(err.msg || '分享屏幕失败')\n        }\n      }\n    },\n\n    // debug\n    switchDebug () {\n      this.debug = !this.debug\n\n      this.client.switchDebug(this.debug)\n    },\n\n    // 录制\n    async toggleRecord () {\n      if (this.recordStatus === 0) {\n        // 开启录制\n        await this.client.startCloudRecord()\n      } else if (this.recordStatus === 1) {\n        // 结束录制\n        await this.client.stopCloudRecord()\n      }\n    }\n  }\n}\n</script>\n<style lang=\"scss\">\n@import '@/assets/style/index.scss';\n</style>\n", "import mod from \"-!../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=a782a288\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=a782a288&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('svg',{class:_vm.svgClass,attrs:{\"aria-hidden\":\"true\"}},[_c('use',{attrs:{\"xlink:href\":_vm.iconName}})])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <svg :class=\"svgClass\" aria-hidden=\"true\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n<script>\nexport default {\n  name: 'SvgIcon',\n  props: {\n    icon: {\n      type: String,\n      default: '',\n    },\n    className: {\n      type: String,\n      default: '',\n    },\n    type: {\n      type: String,\n      default: 'default',\n    },\n  },\n  computed: {\n    iconName() {\n      return `#icon-${this.icon}`;\n    },\n    svgClass() {\n      return `svg-icon ${this.type ? `svg-icon-${this.type}` : ''} ${this.className}`;\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import './index.scss';\n</style>\n", "import mod from \"-!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/_thread-loader@2.1.3@thread-loader/dist/cjs.js!../../../node_modules/_babel-loader@8.3.0@babel-loader/lib/index.js!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=74e1226c&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=74e1226c&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"74e1226c\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from \"vue\";\nimport \"./plugins/element.js\";\nimport App from \"./view/App.vue\";\nimport \"./assets/style/global.scss\";\nimport \"./assets/style/mobile.scss\";\nimport SvgIcon from \"./components/Svg/index.vue\";\n\n// 导入svg\nVue.component(\"svg-icon\", SvgIcon);\n\nconst requireAll = (requireContext) =>\n  requireContext.keys().map(requireContext);\n\nconst req = require.context(\"@/assets/img/svg\", true, /\\.svg$/);\nrequireAll(req);\n\nVue.config.productionTip = false;\n\nnew Vue({\n  render: (h) => h(App),\n}).$mount(\"#app\");\n", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-copy\",\n  \"use\": \"icon-copy-usage\",\n  \"viewBox\": \"0 0 16 16\",\n  \"content\": \"<symbol viewBox=\\\"0 0 16 16\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-copy\\\">\\n    <title></title>\\n    <defs>\\n        <linearGradient x1=\\\"4.5317396%\\\" y1=\\\"99.9829206%\\\" x2=\\\"96.0880949%\\\" y2=\\\"14.6882289%\\\" id=\\\"icon-copy_linearGradient-1\\\">\\n            <stop stop-color=\\\"#3876FF\\\" offset=\\\"0%\\\" />\\n            <stop stop-color=\\\"#69AEFF\\\" offset=\\\"100%\\\" />\\n        </linearGradient>\\n    </defs>\\n    <g id=\\\"icon-copy_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-copy_切图\\\" transform=\\\"translate(-13.000000, -217.000000)\\\">\\n            <g id=\\\"icon-copy_icon-复制\\\" transform=\\\"translate(13.000000, 217.000000)\\\">\\n                <path d=\\\"M11.9642393,12.1762405 L11.9642393,5.05353166 C11.9642393,4.4929481 11.509423,4.03788614 10.9491421,4.03788614 L3.83027924,4.03788614 C3.83027924,2.91671901 3.9555185,2 5.07608025,2 L11.9642393,2 C13.0913925,2 14.001025,2.91012391 14.001025,4.03788614 L14.001025,10.9297665 C14.001025,12.0509336 13.084801,12.1762405 11.9642393,12.1762405 Z M6.94185647,9.1139115 L6.94185647,7.58897772 C6.94185647,7.40759793 6.79414533,7.25980699 6.61286348,7.25980699 C6.43158163,7.25980699 6.28387049,7.40759793 6.28387049,7.58897772 L6.28387049,9.1139115 L4.76647425,9.1139115 C4.5851924,9.12734704 4.45090955,9.27513798 4.45762369,9.45651777 C4.46433783,9.62446202 4.59862068,9.75209965 4.76647425,9.76553519 L6.29058463,9.76553519 L6.29058463,11.290469 C6.29058463,11.4718488 6.43829577,11.6196397 6.61957762,11.6196397 C6.80085947,11.6196397 6.94857061,11.4718488 6.94857061,11.290469 L6.94857061,9.76553519 L8.47268099,9.76553519 C8.65396284,9.75209965 8.7882457,9.60430871 8.78153155,9.42292892 C8.76810327,9.26170244 8.63382042,9.12734704 8.46596685,9.1139115 L6.94185647,9.1139115 Z M2.70426972,4.85259521 L2.70426972,4.84600011 L10.4493289,4.84600011 C10.8382297,4.84600011 11.1546236,5.16256494 11.1546236,5.55167589 L11.1546236,13.3009193 C11.1480321,13.6900303 10.8382297,14 10.4493289,14 L2.70426972,14 C2.31536888,14 2.00556651,13.6834352 1.99897497,13.3009193 L1.99897497,5.55167589 C1.99897497,5.16916004 2.31536888,4.84600011 2.70426972,4.85259521 Z\\\" id=\\\"icon-copy_形状\\\" fill=\\\"url(#icon-copy_linearGradient-1)\\\" fill-rule=\\\"nonzero\\\" />\\n                <rect id=\\\"icon-copy_矩形\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"16\\\" height=\\\"16\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2f777a1f&prod&lang=scss&scoped=true\"", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=fe21d51a&prod&lang=scss\"", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./timer.vue?vue&type=style&index=0&id=264a9668&prod&scoped=true&lang=css\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-full\",\n  \"use\": \"icon-full-usage\",\n  \"viewBox\": \"0 0 30 24\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-full\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-full_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n        <rect id=\\\"icon-full_path-3\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-full_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-full_切图\\\" transform=\\\"translate(-95.000000, -107.000000)\\\">\\n            <g id=\\\"icon-full_icon-全屏--Normal\\\" transform=\\\"translate(95.000000, 108.000000)\\\">\\n                <mask id=\\\"icon-full_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-full_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-full_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-full_path-1\\\" />\\n                <path d=\\\"M25.1875,1.75 C26.2920695,1.75 27.1875,2.6454305 27.1875,3.75 L27.1875,19.25 C27.1875,20.3545695 26.2920695,21.25 25.1875,21.25 L4.8125,21.25 C3.7079305,21.25 2.8125,20.3545695 2.8125,19.25 L2.8125,3.75 C2.8125,2.6454305 3.7079305,1.75 4.8125,1.75 L25.1875,1.75 Z M10.0281057,14.0343943 C9.68542064,13.6917092 9.12981837,13.6917092 8.7871333,14.0343943 L8.7871333,14.0343943 L6.5175,16.3029081 L6.5175,14.8492814 C6.5175,14.3646515 6.12462987,13.9717814 5.64,13.9717814 C5.15537013,13.9717814 4.7625,14.3646515 4.7625,14.8492814 L4.7625,14.8492814 L4.7625,18.3 C4.7625,18.8522847 5.21021525,19.3 5.7625,19.3 L5.7625,19.3 L9.2132186,19.3 C9.69784847,19.3 10.0907186,18.9071299 10.0907186,18.4225 C10.0907186,17.9378701 9.69784847,17.545 9.2132186,17.545 L9.2132186,17.545 L7.7575,17.5449081 L10.0281057,15.2753667 C10.3707908,14.9326816 10.3707908,14.3770794 10.0281057,14.0343943 Z M21.1230061,13.9558623 C20.7790598,13.694089 20.2860223,13.7202663 19.9718943,14.0343943 C19.6292092,14.3770794 19.6292092,14.9326816 19.9718943,15.2753667 L19.9718943,15.2753667 L22.2425,17.5449081 L20.7867814,17.545 L20.6767097,17.551837 C20.2440346,17.6059845 19.9092814,17.9751494 19.9092814,18.4225 C19.9092814,18.9071299 20.3021515,19.3 20.7867814,19.3 L20.7867814,19.3 L24.2375,19.3 L24.3541211,19.2932723 C24.8514598,19.2355072 25.2375,18.8128358 25.2375,18.3 L25.2375,18.3 L25.2375,14.8492814 L25.230663,14.7392097 C25.1765155,14.3065346 24.8073506,13.9717814 24.36,13.9717814 C23.8753701,13.9717814 23.4825,14.3646515 23.4825,14.8492814 L23.4825,14.8492814 L23.4825,16.3029081 L21.2128667,14.0343943 Z M24.2375,3.7 L20.7867814,3.7 C20.3021515,3.7 19.9092814,4.09287013 19.9092814,4.5775 C19.9092814,5.06212987 20.3021515,5.455 20.7867814,5.455 L20.7867814,5.455 L22.2425,5.45509191 L19.9718943,7.7246333 C19.6292092,8.06731837 19.6292092,8.62292064 19.9718943,8.9656057 C20.3145794,9.30829077 20.8701816,9.30829077 21.2128667,8.9656057 L21.2128667,8.9656057 L23.4825,6.69709191 L23.4825,8.1507186 C23.4825,8.63534847 23.8753701,9.0282186 24.36,9.0282186 C24.8446299,9.0282186 25.2375,8.63534847 25.2375,8.1507186 L25.2375,8.1507186 L25.2375,4.7 C25.2375,4.14771525 24.7897847,3.7 24.2375,3.7 L24.2375,3.7 Z M9.2132186,3.7 L5.7625,3.7 L5.64587887,3.70672773 C5.14854019,3.76449284 4.7625,4.18716416 4.7625,4.7 L4.7625,4.7 L4.7625,8.1507186 L4.76933697,8.26079034 C4.82348452,8.69346535 5.19264935,9.0282186 5.64,9.0282186 C6.12462987,9.0282186 6.5175,8.63534847 6.5175,8.1507186 L6.5175,8.1507186 L6.5175,6.69709191 L8.7871333,8.9656057 L8.87699391,9.0441377 C9.22094016,9.30591101 9.71397773,9.27973368 10.0281057,8.9656057 C10.3707908,8.62292064 10.3707908,8.06731837 10.0281057,7.7246333 L10.0281057,7.7246333 L7.7575,5.45509191 L9.2132186,5.455 L9.32329034,5.44816303 C9.75596535,5.39401548 10.0907186,5.02485065 10.0907186,4.5775 C10.0907186,4.09287013 9.69784847,3.7 9.2132186,3.7 L9.2132186,3.7 Z\\\" id=\\\"icon-full_形状结合\\\" mask=\\\"url(#icon-full_mask-2)\\\" />\\n                <mask id=\\\"icon-full_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-full_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-full_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-full_path-3\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&id=a782a288&prod&lang=scss\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-share_stop\",\n  \"use\": \"icon-share_stop-usage\",\n  \"viewBox\": \"0 0 30 23\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 23\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-share_stop\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-share_stop_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-share_stop_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-share_stop_切图\\\" transform=\\\"translate(-155.000000, -144.000000)\\\">\\n            <g id=\\\"icon-share_stop_icon-暂停共享--Normal\\\" transform=\\\"translate(155.000000, 144.000000)\\\">\\n                <path d=\\\"M21.8232695,19.3997075 C22.3619087,19.3997075 22.798562,19.8363608 22.798562,20.375 C22.798562,20.9136392 22.3619087,21.3502925 21.8232695,21.3502925 L8.17673051,21.3502925 C7.63809134,21.3502925 7.20143801,20.9136392 7.20143801,20.375 C7.20143801,19.8363608 7.63809134,19.3997075 8.17673051,19.3997075 L21.8232695,19.3997075 Z M25.1802143,1.6497075 C26.2847838,1.6497075 27.1802143,2.545138 27.1802143,3.6497075 L27.1802143,15.2403818 C27.1802143,16.3449513 26.2847838,17.2403818 25.1802143,17.2403818 L4.81978572,17.2403818 C3.71521622,17.2403818 2.81978572,16.3449513 2.81978572,15.2403818 L2.81978572,3.6497075 C2.81978572,2.545138 3.71521622,1.6497075 4.81978572,1.6497075 L25.1802143,1.6497075 Z M18.3621266,5.93980868 C18.0180143,5.67247622 17.5206494,5.69690403 17.2045516,6.01301311 L17.2045516,6.01301311 L14.9993325,8.21722356 L12.7945841,6.01279319 L12.7041725,5.93386429 C12.3895024,5.69487099 11.9502291,5.69690758 11.6376842,5.93995798 L11.6376842,5.93995798 L11.5551226,6.01287238 L11.481969,6.09575632 C11.2146365,6.4398686 11.2390644,6.93723346 11.5551734,7.25333131 L11.5551734,7.25333131 L13.759791,9.45676509 L11.6522228,11.5655392 L11.5670543,11.6603709 C11.251543,12.0524126 11.2387151,12.5870063 11.5548507,12.9031419 L11.5548507,12.9031419 L11.6319963,12.9706333 C11.9818494,13.2375949 12.5245001,13.173723 12.8924533,12.8057698 L12.8924533,12.8057698 L15.0000215,10.6969956 L17.1076996,12.8056601 L17.2025312,12.8908286 C17.5945729,13.2063399 18.1291666,13.2191678 18.4453022,12.9030322 L18.4453022,12.9030322 L18.5127937,12.8258866 C18.7797553,12.4760335 18.7158833,11.9333828 18.3479301,11.5654296 L18.3479301,11.5654296 L16.2402521,9.45676509 L18.4450897,7.25242381 L18.5240186,7.16201221 C18.7630119,6.84734211 18.7609753,6.40806877 18.5179249,6.0955239 L18.5179249,6.0955239 L18.4447683,6.01272018 Z\\\" id=\\\"icon-share_stop_形状结合\\\" />\\n                <mask id=\\\"icon-share_stop_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-share_stop_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-share_stop_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-share_stop_path-1\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=4eaabb0c&prod&lang=scss\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-mute_mic_error\",\n  \"use\": \"icon-mute_mic_error-usage\",\n  \"viewBox\": \"0 0 31 26\",\n  \"content\": \"<symbol viewBox=\\\"0 0 31 26\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-mute_mic_error\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-mute_mic_error_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n        <rect id=\\\"icon-mute_mic_error_path-3\\\" x=\\\"0.0448224552\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n    </defs>\\n    <g id=\\\"icon-mute_mic_error_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-mute_mic_error_切图\\\" transform=\\\"translate(-438.000000, -111.000000)\\\">\\n            <g id=\\\"icon-mute_mic_error_icon-关闭话筒没权限\\\" transform=\\\"translate(438.100000, 111.000000)\\\">\\n                <mask id=\\\"icon-mute_mic_error_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-mute_mic_error_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-mute_mic_error_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-mute_mic_error_path-1\\\" />\\n                <path d=\\\"M4.26791734,-6.66133815e-16 L27.2420358,22.9741184 L25.8217276,24.3944266 L2.84760914,1.42030821 L4.26791734,-6.66133815e-16 Z M7.39633954,9.11618738 L8.61060914,10.33 L8.61130155,12.2382866 C8.61130155,15.6587677 11.4679915,18.4382866 15,18.4382866 C15.5300701,18.4382866 16.0449301,18.375684 16.5372066,18.2577073 L17.8193367,19.5400124 C17.2110796,19.7633978 16.567634,19.916596 15.8994335,19.9895162 L15.8998274,21.8980308 L16.7591726,21.8990308 C17.2562289,21.8990308 17.6591726,22.3019745 17.6591726,22.7990308 C17.6591726,23.2960871 17.2562289,23.6990308 16.7591726,23.6990308 L15.8998274,23.6980308 L15.9,23.6990308 L14.1,23.6990308 L14.0998274,23.6980308 L13.2408274,23.6990308 C12.7437711,23.6990308 12.3408274,23.2960871 12.3408274,22.7990308 C12.3408274,22.3019745 12.7437711,21.8990308 13.2408274,21.8990308 L14.0998274,21.8980308 L14.0994115,19.9893901 C10.1887876,19.5620673 7.12632887,16.3850703 7.00381063,12.4814372 L7,12.2382866 L7,9.80539585 C7,9.51194924 7.15911123,9.2554044 7.39633954,9.11618738 Z M10.521848,12.241645 L14.5517447,16.2733499 C12.4236874,16.0628974 10.7313688,14.369976 10.521848,12.241645 Z M22.1943492,9.00539585 C22.605071,9.00539585 22.9440089,9.310584 22.9937228,9.70504555 L23,9.80539585 L23,12.2382866 C23,13.2241801 22.8125292,14.1670399 22.4705402,15.0349964 L21.1950186,13.7589888 C21.3031857,13.3428282 21.3679825,12.9100064 21.3844903,12.4653457 L21.3886984,12.2382866 L21.3886984,9.80539585 C21.3886984,9.36356805 21.7494006,9.00539585 22.1943492,9.00539585 Z M15,0.695395849 C17.4852814,0.695395849 19.5,2.71011447 19.5,5.19539585 L19.5,11.7953958 C19.5,11.8830517 19.4974937,11.9701222 19.492549,12.0565396 L10.8610131,3.42644451 C11.5482372,1.82056577 13.1427198,0.695395849 15,0.695395849 Z\\\" id=\\\"icon-mute_mic_error_形状结合\\\" fill-opacity=\\\"0.8\\\" fill=\\\"#FF6666\\\" mask=\\\"url(#icon-mute_mic_error_mask-2)\\\" />\\n                <mask id=\\\"icon-mute_mic_error_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-mute_mic_error_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-mute_mic_error_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-mute_mic_error_path-3\\\" />\\n                <g id=\\\"icon-mute_mic_error_编组-11\\\" mask=\\\"url(#icon-mute_mic_error_mask-4)\\\">\\n                    <g transform=\\\"translate(20.000000, 0.000000)\\\">\\n                        <circle id=\\\"icon-mute_mic_error_椭圆形\\\" stroke-opacity=\\\"0.9\\\" stroke=\\\"#FFFFFF\\\" stroke-width=\\\"0.8\\\" fill-opacity=\\\"0.8\\\" fill=\\\"#FF6666\\\" fill-rule=\\\"evenodd\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"4.6\\\" />\\n                        <path d=\\\"M5.52767091,6.02524873 L5.73736524,2.75152348 C5.76345067,2.34428068 5.45446156,1.99299874 5.04721875,1.96691331 C5.03149494,1.96590615 5.01574294,1.96540218 4.99998691,1.96540218 C4.59190952,1.96540218 4.26109744,2.29621426 4.26109744,2.70429165 C4.26109744,2.72004768 4.26160141,2.73579968 4.26260858,2.75152348 L4.47230291,6.02524873 C4.49013136,6.30358442 4.72108082,6.520214 4.99998691,6.520214 C5.27889301,6.520214 5.50984247,6.30358442 5.52767091,6.02524873 Z M4.99643852,8.03459782 C5.15256756,8.03459782 5.28030949,7.98361404 5.39385788,7.88164647 C5.49321272,7.77967889 5.54998691,7.64129433 5.54998691,7.48105957 C5.54998691,7.32082482 5.49321272,7.18972365 5.39385788,7.08775608 C5.28740627,6.98578851 5.15256756,6.93480472 4.99643852,6.93480472 C4.84030949,6.93480472 4.71256756,6.98578851 4.61321272,7.08775608 C4.49966433,7.18972365 4.44998691,7.32082482 4.44998691,7.48105957 C4.44998691,7.64129433 4.49966433,7.7723955 4.61321272,7.87436307 C4.71256756,7.97633064 4.84030949,8.03459782 4.99643852,8.03459782 Z\\\" id=\\\"icon-mute_mic_error_！\\\" stroke=\\\"none\\\" fill=\\\"#FFFFFF\\\" fill-rule=\\\"nonzero\\\" />\\n                    </g>\\n                </g>\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-cancel_mic_mute\",\n  \"use\": \"icon-cancel_mic_mute-usage\",\n  \"viewBox\": \"0 0 31 26\",\n  \"content\": \"<symbol viewBox=\\\"0 0 31 26\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-cancel_mic_mute\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-cancel_mic_mute_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n    </defs>\\n    <g id=\\\"icon-cancel_mic_mute_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-cancel_mic_mute_切图\\\" transform=\\\"translate(-437.000000, -18.000000)\\\">\\n            <g id=\\\"icon-cancel_mic_mute_icon-关闭话筒-normal\\\" transform=\\\"translate(437.380341, 19.000000)\\\">\\n                <mask id=\\\"icon-cancel_mic_mute_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-cancel_mic_mute_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-cancel_mic_mute_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-cancel_mic_mute_path-1\\\" />\\n                <path d=\\\"M4.26791734,0.605573361 L27.2420358,23.5796918 L25.8217276,25 L2.84760914,2.02588157 L4.26791734,0.605573361 Z M7.39633954,9.72176074 L8.61060914,10.9355734 L8.61130155,12.84386 C8.61130155,16.2643411 11.4679915,19.04386 15,19.04386 C15.5300701,19.04386 16.0449301,18.9812573 16.5372066,18.8632807 L17.8193367,20.1455857 C17.2110796,20.3689711 16.567634,20.5221694 15.8994335,20.5950896 L15.8998274,22.5036042 L16.7591726,22.5046042 C17.2562289,22.5046042 17.6591726,22.9075479 17.6591726,23.4046042 C17.6591726,23.9016604 17.2562289,24.3046042 16.7591726,24.3046042 L15.8998274,24.3036042 L15.9,24.3046042 L14.1,24.3046042 L14.0998274,24.3036042 L13.2408274,24.3046042 C12.7437711,24.3046042 12.3408274,23.9016604 12.3408274,23.4046042 C12.3408274,22.9075479 12.7437711,22.5046042 13.2408274,22.5046042 L14.0998274,22.5036042 L14.0994115,20.5949634 C10.1887876,20.1676406 7.12632887,16.9906436 7.00381063,13.0870106 L7,12.84386 L7,10.4109692 C7,10.1175226 7.15911123,9.86097776 7.39633954,9.72176074 Z M10.521848,12.8472183 L14.5517447,16.8789232 C12.4236874,16.6684707 10.7313688,14.9755494 10.521848,12.8472183 Z M22.1943492,9.61096921 C22.605071,9.61096921 22.9440089,9.91615736 22.9937228,10.3106189 L23,10.4109692 L23,12.84386 C23,13.8297534 22.8125292,14.7726133 22.4705402,15.6405698 L21.1950186,14.3645621 C21.3031857,13.9484015 21.3679825,13.5155797 21.3844903,13.070919 L21.3886984,12.84386 L21.3886984,10.4109692 C21.3886984,9.96914141 21.7494006,9.61096921 22.1943492,9.61096921 Z M15,1.30096921 C17.4852814,1.30096921 19.5,3.31568784 19.5,5.80096921 L19.5,12.4009692 C19.5,12.4886251 19.4974937,12.5756956 19.492549,12.6621129 L10.8610131,4.03201787 C11.5482372,2.42613913 13.1427198,1.30096921 15,1.30096921 Z\\\" id=\\\"icon-cancel_mic_mute_形状结合\\\" mask=\\\"url(#icon-cancel_mic_mute_mask-2)\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-mic_null\",\n  \"use\": \"icon-mic_null-usage\",\n  \"viewBox\": \"0 0 40 28\",\n  \"content\": \"<symbol viewBox=\\\"0 0 40 28\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-mic_null\\\">\\n<path opacity=\\\"0.902\\\" fill-rule=\\\"evenodd\\\" clip-rule=\\\"evenodd\\\" d=\\\"M21 23.3717V24.9608H23.5C24.0525 24.9608 24.5 25.4171 24.5 25.9804C24.5 26.5437 24.0525 27 23.5 27H16.5C15.9475 27 15.5 26.5437 15.5 25.9804C15.5 25.4171 15.9475 24.9608 16.5 24.9608H19V23.3717C14.5005 22.864 11 18.9777 11 14.2549V13.7451C11 13.1818 11.4475 12.7255 12 12.7255C12.5525 12.7255 13 13.1818 13 13.7451V14.2549C13 18.1967 16.134 21.3922 20 21.3922C23.866 21.3922 27 18.1967 27 14.2549V13.7451C27 13.1818 27.4475 12.7255 28 12.7255C28.5525 12.7255 29 13.1818 29 13.7451V14.2549C29 18.9777 25.4995 22.864 21 23.3717Z\\\" fill=\\\"white\\\" />\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-camera\",\n  \"use\": \"icon-camera-usage\",\n  \"viewBox\": \"0 0 30 26\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 26\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-camera\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-camera_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n        <rect id=\\\"icon-camera_path-3\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n    </defs>\\n    <g id=\\\"icon-camera_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-camera_切图\\\" transform=\\\"translate(-334.000000, -106.000000)\\\">\\n            <g id=\\\"icon-camera_icon-摄像头--normal\\\" transform=\\\"translate(334.000000, 107.000000)\\\">\\n                <mask id=\\\"icon-camera_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-camera_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-camera_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-camera_path-1\\\" />\\n                <path d=\\\"M1.99138087,5.84719195 C1.99138087,5.84719195 1.96234653,3.5 4.28747725,3.5 C5.82320555,3.5 19.591666,3.5 19.591666,3.5 C19.591666,3.5 21.8867433,3.46796512 21.8867433,5.84719195 C21.8867433,6.24590027 21.8867433,7.41297364 21.8867433,7.41297364 L28.0086191,4.28241203 L28.0086191,20.71761 L21.8867433,17.5870484 L21.8867433,19.1518283 C21.8867433,19.1518283 21.8807326,21.5 19.591666,21.5 C18.0669572,21.5 4.28747725,21.5 4.28747725,21.5 C4.28747725,21.5 1.99138087,21.4569235 1.99138087,19.1518283 C1.99138087,17.6541677 1.99138087,5.84719195 1.99138087,5.84719195 Z\\\" id=\\\"icon-camera_路径\\\" mask=\\\"url(#icon-camera_mask-2)\\\" />\\n                <mask id=\\\"icon-camera_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-camera_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-camera_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-camera_path-3\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-hang_up_full\",\n  \"use\": \"icon-hang_up_full-usage\",\n  \"viewBox\": \"0 0 160 160\",\n  \"content\": \"<symbol viewBox=\\\"0 0 160 160\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-hang_up_full\\\">\\n    <title></title>\\n    <g id=\\\"icon-hang_up_full_页面-1\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-hang_up_full_03-语音通话接收方界面\\\" transform=\\\"translate(-110.000000, -1057.000000)\\\">\\n            <g id=\\\"icon-hang_up_full_编组-2\\\" transform=\\\"translate(110.000000, 280.000000)\\\">\\n                <g id=\\\"icon-hang_up_full_编组-3\\\" transform=\\\"translate(0.000000, 777.000000)\\\">\\n                    <image id=\\\"icon-hang_up_full_全屏-挂断@2x\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"160\\\" height=\\\"160\\\" xlink:href=\\\"data:image/png;base64,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\\\"></image>\\n                </g>\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-add\",\n  \"use\": \"icon-add-usage\",\n  \"viewBox\": \"0 0 1024 1024\",\n  \"content\": \"<symbol class=\\\"icon\\\" viewBox=\\\"0 0 1024 1024\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-add\\\">\\n<path d=\\\"M914.510769 258.835692H709.671385L665.875692 172.898462c-10.24-20.125538-36.982154-36.371692-59.746461-36.371693H400.797538c-22.784 0-49.427692 16.443077-59.608615 36.706462l-42.850461 85.602461H92.416c-22.724923 0-41.216 18.176-41.216 40.605539v530.353231c0 22.291692 18.451692 40.605538 41.216 40.605538H914.510769c22.744615 0 41.216-18.176 41.216-40.605538V299.441231c0-22.291692-18.451692-40.605538-41.216-40.605539z m-411.037538 509.636923c-113.526154 0-205.587692-91.254154-205.587693-203.854769 0-32.649846 7.739077-63.488 21.504-90.840615a30.936615 30.936615 0 0 1 52.125539-15.970462l43.638154 43.264a30.326154 30.326154 0 0 1-0.019693 43.244308 30.916923 30.916923 0 0 1-43.579076 0l-8.979693-8.861539c-1.988923 9.393231-3.012923 19.180308-3.012923 29.164308 0 78.808615 64.413538 142.710154 143.911385 142.710154 22.055385 0 42.948923-4.923077 61.636923-13.725538l45.430154 45.056a205.922462 205.922462 0 0 1-107.067077 29.814153z m167.305846-85.405538l-0.078769-0.078769a30.483692 30.483692 0 0 1-6.833231 10.121846 30.916923 30.916923 0 0 1-43.559385 0.039384L576.649846 649.846154a30.326154 30.326154 0 0 1 0-43.244308 30.956308 30.956308 0 0 1 43.618462 0l15.143384 15.025231a140.8 140.8 0 0 0 11.953231-57.028923c0-78.808615-64.413538-142.690462-143.891692-142.690462a144.088615 144.088615 0 0 0-74.988308 20.873846l-44.504615-44.110769a205.902769 205.902769 0 0 1 119.492923-37.927384c113.545846 0 205.587692 91.273846 205.587692 203.854769 0 44.169846-14.178462 85.070769-38.281846 118.449231z\\\" fill=\\\"#ffffff\\\" p-id=\\\"2332\\\" />\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "module.exports = \"data:image/svg+xml;base64,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\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-next\",\n  \"use\": \"icon-next-usage\",\n  \"viewBox\": \"0 0 24 24\",\n  \"content\": \"<symbol viewBox=\\\"0 0 24 24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-next\\\">\\n    <title></title>\\n    <g id=\\\"icon-next_通话中-视频窗口关闭-备份\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-next_icon-向右翻页\\\">\\n            <path d=\\\"M5.86516452,1.55664916 C5.34793583,2.16053099 5.38241774,3.05824619 5.96861026,3.62295023 L14.664881,12 L5.96862073,20.3770397 C5.34578071,20.9770478 5.34578071,21.9529842 5.96861026,22.5529823 C6.58731415,23.1490059 7.5875011,23.1490059 8.20620498,22.5529823 L18.0313897,13.0879663 C18.6542193,12.4879682 18.6542193,11.5120318 18.0313897,10.9120337 L8.20620498,1.44701772 C7.5875011,0.850994095 6.58731415,0.850994095 5.96861026,1.44701772 L5.86516452,1.55664916 Z\\\" id=\\\"icon-next_路径\\\" fill=\\\"#FFFFFF\\\" fill-rule=\\\"nonzero\\\" />\\n            <rect id=\\\"icon-next_矩形\\\" fill=\\\"#D8D8D8\\\" opacity=\\\"0\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" />\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=3692ac76&prod&scoped=true&lang=css\"", "module.exports = __webpack_public_path__ + \"media/ring.9fdcfe6d.wav\";", "module.exports = \"data:image/svg+xml;base64,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\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-cancel_full\",\n  \"use\": \"icon-cancel_full-usage\",\n  \"viewBox\": \"0 0 30 24\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-cancel_full\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-cancel_full_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n        <rect id=\\\"icon-cancel_full_path-3\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-cancel_full_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-cancel_full_切图\\\" transform=\\\"translate(-77.000000, -107.000000)\\\">\\n            <g id=\\\"icon-cancel_full_icon-取消全屏--Normal\\\" transform=\\\"translate(77.000000, 108.000000)\\\">\\n                <mask id=\\\"icon-cancel_full_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-cancel_full_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-cancel_full_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-cancel_full_path-1\\\" />\\n                <path d=\\\"M25.1875,1.75 C26.2920695,1.75 27.1875,2.6454305 27.1875,3.75 L27.1875,19.25 C27.1875,20.3545695 26.2920695,21.25 25.1875,21.25 L4.8125,21.25 C3.7079305,21.25 2.8125,20.3545695 2.8125,19.25 L2.8125,3.75 C2.8125,2.6454305 3.7079305,1.75 4.8125,1.75 L25.1875,1.75 Z M23.8021267,13.4139081 L20.3514081,13.4139081 L20.234787,13.4206358 C19.7374483,13.4784009 19.3514081,13.9010723 19.3514081,14.4139081 L19.3514081,14.4139081 L19.3514081,17.8646267 L19.3582451,17.9746984 C19.4123926,18.4073734 19.7815574,18.7421267 20.2289081,18.7421267 C20.713538,18.7421267 21.1064081,18.3492566 21.1064081,17.8646267 L21.1064081,17.8646267 L21.1064081,16.411 L23.3760414,18.6795138 L23.465902,18.7580458 C23.8098483,19.0198191 24.3028858,18.9936418 24.6170138,18.6795138 C24.9596989,18.3368287 24.9596989,17.7812265 24.6170138,17.4385414 L24.6170138,17.4385414 L22.3464081,15.169 L23.8021267,15.1689081 L23.9121984,15.1620711 C24.3448734,15.1079236 24.6796267,14.7387587 24.6796267,14.2914081 C24.6796267,13.8067782 24.2867566,13.4139081 23.8021267,13.4139081 L23.8021267,13.4139081 Z M9.64859191,13.4139081 L6.1978733,13.4139081 C5.71324344,13.4139081 5.3203733,13.8067782 5.3203733,14.2914081 C5.3203733,14.776038 5.71324344,15.1689081 6.1978733,15.1689081 L6.1978733,15.1689081 L7.65359191,15.169 L5.3829862,17.4385414 C5.04030113,17.7812265 5.04030113,18.3368287 5.3829862,18.6795138 C5.72567127,19.0221989 6.28127354,19.0221989 6.6239586,18.6795138 L6.6239586,18.6795138 L8.89359191,16.411 L8.89359191,17.8646267 C8.89359191,18.3492566 9.28646204,18.7421267 9.77109191,18.7421267 C10.2557218,18.7421267 10.6485919,18.3492566 10.6485919,17.8646267 L10.6485919,17.8646267 L10.6485919,14.4139081 C10.6485919,13.8616233 10.2008767,13.4139081 9.64859191,13.4139081 L9.64859191,13.4139081 Z M24.6170138,4.3204862 C24.2743287,3.97780113 23.7187265,3.97780113 23.3760414,4.3204862 L23.3760414,4.3204862 L21.1064081,6.589 L21.1064081,5.1353733 C21.1064081,4.65074344 20.713538,4.2578733 20.2289081,4.2578733 C19.7442782,4.2578733 19.3514081,4.65074344 19.3514081,5.1353733 L19.3514081,5.1353733 L19.3514081,8.58609191 C19.3514081,9.13837666 19.7991233,9.58609191 20.3514081,9.58609191 L20.3514081,9.58609191 L23.8021267,9.58609191 C24.2867566,9.58609191 24.6796267,9.19322177 24.6796267,8.70859191 C24.6796267,8.22396204 24.2867566,7.83109191 23.8021267,7.83109191 L23.8021267,7.83109191 L22.3464081,7.831 L24.6170138,5.5614586 C24.9596989,5.21877354 24.9596989,4.66317127 24.6170138,4.3204862 Z M6.53409799,4.24195421 C6.19015175,3.98018089 5.69711418,4.00635822 5.3829862,4.3204862 C5.04030113,4.66317127 5.04030113,5.21877354 5.3829862,5.5614586 L5.3829862,5.5614586 L7.65359191,7.831 L6.1978733,7.83109191 L6.08780157,7.83792888 C5.65512655,7.89207642 5.3203733,8.26124126 5.3203733,8.70859191 C5.3203733,9.19322177 5.71324344,9.58609191 6.1978733,9.58609191 L6.1978733,9.58609191 L9.64859191,9.58609191 L9.76521303,9.57936417 C10.2625517,9.52159907 10.6485919,9.09892774 10.6485919,8.58609191 L10.6485919,8.58609191 L10.6485919,5.1353733 L10.6417549,5.02530157 C10.5876074,4.59262655 10.2184426,4.2578733 9.77109191,4.2578733 C9.28646204,4.2578733 8.89359191,4.65074344 8.89359191,5.1353733 L8.89359191,5.1353733 L8.89359191,6.589 L6.6239586,4.3204862 Z\\\" id=\\\"icon-cancel_full_形状结合\\\" mask=\\\"url(#icon-cancel_full_mask-2)\\\" />\\n                <mask id=\\\"icon-cancel_full_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-cancel_full_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-cancel_full_蒙版\\\" fill-opacity=\\\"0\\\" xlink:href=\\\"#icon-cancel_full_path-3\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-mute_camera\",\n  \"use\": \"icon-mute_camera-usage\",\n  \"viewBox\": \"0 0 30 26\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 26\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-mute_camera\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-mute_camera_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n        <rect id=\\\"icon-mute_camera_path-3\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"25\\\" />\\n    </defs>\\n    <g id=\\\"icon-mute_camera_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-mute_camera_切图\\\" transform=\\\"translate(-392.000000, -50.000000)\\\">\\n            <g id=\\\"icon-mute_camera_icon-关闭摄像头备份\\\" transform=\\\"translate(392.000000, 50.000000)\\\">\\n                <mask id=\\\"icon-mute_camera_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-mute_camera_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-mute_camera_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-mute_camera_path-1\\\" />\\n                <path d=\\\"M26.6713892,19.9017012 L10.1709213,3.40121131 C14.4924486,3.40121131 19.5676727,3.40121131 19.5676727,3.40121131 C19.5676727,3.40121131 21.854718,3.36928852 21.854718,5.74118711 C21.854718,6.13850009 21.854718,7.30049082 21.854718,7.30049082 L27.9551693,4.18088514 L27.9551693,20.5585654 L26.6713892,19.9017012 Z M25.8897408,23.6482229 L25.1839612,24.3540025 C24.9882995,24.5486658 24.672845,24.5486658 24.4781816,24.3540025 L2.18173589,2.05755672 C1.9870725,1.86289333 1.9870725,1.54644053 2.18173589,1.35177714 L2.88751548,0.645997546 C3.08217888,0.451334151 3.39863168,0.451334151 3.59329507,0.645997546 L25.8897408,22.9424433 C26.0844042,23.1371067 26.0844042,23.4525612 25.8897408,23.6482229 Z M4.31704359,21.3382172 C4.31704359,21.3382172 2.029,21.2952915 2.029,18.9982634 C2.029,17.6655679 2.029,8.16399767 2.029,6.14049664 L17.2267206,21.3382172 C12.8772416,21.3382172 4.31704359,21.3382172 4.31704359,21.3382172 Z\\\" id=\\\"icon-mute_camera_形状\\\" mask=\\\"url(#icon-mute_camera_mask-2)\\\" />\\n                <mask id=\\\"icon-mute_camera_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-mute_camera_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-mute_camera_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-mute_camera_path-3\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "module.exports = \"data:image/gif;base64,R0lGODlhGAAYAOZXAD97/+Lr/zt4/0J9/0N9/1aL/zp4/+vy/3Ge/6zG/+rw/8PW/2SU/77S/4es/0F8/6rE/+Hq/z96/z56/zp3/3mj/+Ps/2KT/7/T/+rx/4Go//D0/0mB/8bY/zl3/5u6/6vF/+Ts/8/e/7/S/2OU/4et/8LV/+zy/2aV/4as/1KI/73R/6rF/0B7/1WK/zx5/8TX/0N+/3Ke/+nw/9Xi/4Kp/63H/6nE/9/p/3Of/1OI/9De/02F//7+/3qj/0iB/7XM/8HV/4Cn/9Df/+Dq/67H/3Gd/1eL/8bX/3Cd/4it/4Wr/5y7/1uO/1iM/+/0/2WV/9bj/1SJ/+jv/7zR/////zh2/////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFEQBXACwAAAAAGAAYAAAH0YBXgoNXCgkIBQMDBQgJCoSQgwcOAlaWl5YCDgeRgwsEmKGXBAudEAaiqVYGEJALqKqpBqWCB6CxqgScVw64Vh8aqQ6FlaIPHFYTVRGpAoaqIhsUAFUBqoeqAVUA1NZWFTyYiJgVHQBW2tzVVk1VQJiJmA1VDOjb3VYMVQ2YivL09tRZ08fv0iJy5gLia/fuEqNs99Z9C3epETRp+FI5KhbqWLJlzR71wgVMWK1bvkLp8gQrpaVZkE65XNUq0qeUpDrVoqRK0y6dggwhUsTIUadAACH5BAURAFcALAAAAAAYABcAAAe7gFeCg4SCBIWIiYqCJYtXJgOOklcek4MshSaVlokZkYUPHJIngimJOxsUV1ZWiY0zihZVEqytnSCEFR0AV7K0HxqKNkaEI1UkvVcSEldEgqyEMi7Fx8kSLYNWKi+ERw/FV8gWytiCOlcQhATfgyPh1uVXUFcr6tPt1ePXgzE0Nd3E8IlTxszZKluCZOAapIuXLyvAhMFK9BBapyumEKFSZbFQoyueEIUaNUgTJ0ebLGFCBOnkooyKPhIKBAAh+QQFEQBXACwAAAAAGAAYAAAHyIBXgoOCIIRXMjYZh4xXGSmNhCeRlI0wjTeVjIaEQZqWmlZWn4JLgz5IEleio0xCjUpXU4QYVRdXDz9WAFU4kQcstLZXQ08UD1URgjovhEVJtFe3IVcTD1fKUlWZgzmHGNJX1NbYVyhVK4RO3+Hj18ooV+mDMezT1e/mjPXR9+Tw6NRBGwTOX75zVAjlCEZwWLFjya7EiKLB2bdhuXb1+nXFlCBUqlhdcQWrkihSgjyhHHSJEDeUnFYeatlokaZJmmbEJIDIUaNAACH5BAURAFcALAAAAAAYABgAAAfegFeCg4IJgwNXCAkKhI2DBw6OhA4HkoILBJaNC5IQBpqOEJuflh8ajpyCB5mWE1URkpVXkYJWtoQAVQGCKi+DkYyDtlZXt4K7Kj0ghAqGgxUdAMW20rsMVQ2ECQiE2QxXDxxW1Vff2YMIBd3lVyIbFOTmhAWIg95XuwDxV+eCA/WC7uXb1+/KAHX22LmDh49dwQLcEn4LN66hPHTOBEGTNiweNm3Bat0yZvHjIEa0phEblGvXABo1fqliJckVLEeyriwgJckUqkaeQAGVhEnoJU2QQFEy2uxQoishCQUCACH5BAURAFcALAUABAAOABAAAAdTgFeCg4JWVoSIhYaJiFYfGoqEDxxXE1cRV4aahyIbFIRWKi+aVwFVAIQqPSAVHaimqIyys7Str6eMpLC5i52fgwM0NZKUlpikiY6QyI2LtJmHg4EAOw==\"", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=872385b6&prod&scoped=true&lang=css\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-mic_mute\",\n  \"use\": \"icon-mic_mute-usage\",\n  \"viewBox\": \"0 0 18 26\",\n  \"content\": \"<symbol viewBox=\\\"0 0 18 26\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-mic_mute\\\">\\n<path opacity=\\\"0.902\\\" fill-rule=\\\"evenodd\\\" clip-rule=\\\"evenodd\\\" d=\\\"M10 22.4415V24H12.5C13.0525 24 13.5 24.4475 13.5 25C13.5 25.5525 13.0525 26 12.5 26H5.5C4.9475 26 4.5 25.5525 4.5 25C4.5 24.4475 4.9475 24 5.5 24H8V22.4415C3.5005 21.9435 0 18.132 0 13.5V13C0 12.4475 0.4475 12 1 12C1.5525 12 2 12.4475 2 13V13.5C2 17.366 5.134 20.5 9 20.5C12.866 20.5 16 17.366 16 13.5V13C16 12.4475 16.4475 12 17 12C17.5525 12 18 12.4475 18 13V13.5C18 18.132 14.4995 21.9435 10 22.4415ZM9 19C5.9625 19 3.5 16.5375 3.5 13.5V6C3.5 2.9625 5.9625 0.5 9 0.5C12.0375 0.5 14.5 2.9625 14.5 6V13.5C14.5 16.5375 12.0375 19 9 19Z\\\" fill=\\\"white\\\" />\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=daca2554&prod&lang=scss\"", "module.exports = \"data:image/png;base64,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\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-hang_up\",\n  \"use\": \"icon-hang_up-usage\",\n  \"viewBox\": \"0 0 60 20\",\n  \"content\": \"<symbol viewBox=\\\"0 0 60 20\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-hang_up\\\">\\n    <title></title>\\n    <g stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <g>\\n            <path d=\\\"M59.2049907,16.0114889 C59.0444591,16.9766357 58.7782117,19.617575 52.4567937,18.8677753 C46.1334179,18.116018 42.4157428,18.0670754 40.7321195,15.2244929 C39.4654866,13.2804953 40.2152863,11.6927994 40.0880357,10.9664922 C39.9627428,10.2421426 39.731734,9.5745664 36.9087285,9.15366058 C34.0876807,8.73275477 25.9123193,8.73275477 23.0912715,9.15366058 C20.268266,9.5745664 20.0372572,10.2421426 19.9100066,10.9664922 C19.7847137,11.6927994 20.5325557,13.2804953 19.2678805,15.2244929 C17.5822995,18.0670754 13.8665821,18.116018 7.54320634,18.8677753 C1.22178827,19.617575 0.955540869,16.9766357 0.795009348,16.0114889 C0.634477827,15.0482997 0.879190511,9.97589521 2.67440277,6.49314428 C6.00249528,0.0405602039 30,1.05856497 30,1.05856497 C30,1.05856497 53.9975047,0.0405602039 57.3255972,6.49314428 C59.1208095,9.97589521 59.3655222,15.0482997 59.2049907,16.0114889 Z\\\" id=\\\"icon-hang_up_路径\\\" fill=\\\"#FFFFFF\\\" />\\n            <rect id=\\\"icon-hang_up_矩形\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"60\\\" height=\\\"20\\\" />\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=3b44c641&prod&lang=scss\"", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=4f06bbb2&prod&scoped=true&lang=css\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-hand_up\",\n  \"use\": \"icon-hand_up-usage\",\n  \"viewBox\": \"0 0 30 23\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 23\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-hand_up\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-hand_up_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-hand_up_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-hand_up_切图\\\" transform=\\\"translate(-208.000000, -232.000000)\\\">\\n            <g id=\\\"icon-hand_up_icon-举手发言--normal\\\" transform=\\\"translate(208.000000, 232.000000)\\\">\\n                <path d=\\\"M23.888965,13.5542621 L19.9600628,18.4715233 C19.2091553,20.3904118 17.4944226,21.7739392 15.4502368,21.9859807 L15.4502368,16.7716861 L12.8216225,16.7716861 L12.8216225,22 C9.9345277,21.8484166 7.63186153,19.3634998 7.63186153,16.2941545 L7.63186153,14.0002503 L5.92501462,6.36062085 C5.8119842,5.8568031 6.06170256,5.33808987 6.48228086,5.20315434 L7.24370281,4.9586932 C7.66428111,4.82375767 8.09625007,5.1225435 8.20928048,5.62723745 L9.17222954,9.93641257 C9.58229338,9.59294029 10.0519391,9.30992615 10.5785382,9.10577043 L9.59280783,3.23344599 C9.50606356,2.71385655 9.83639276,2.21792465 10.3314485,2.12592314 L11.227806,1.96032044 C11.7228617,1.86831894 12.1942598,2.21529603 12.2818803,2.73488547 L13.2746203,8.64663913 L14.0027465,8.64663913 L14.0027465,1.40655902 C14.0027465,1.18225059 14.1849971,1 14.4101817,1 L16.3264416,1 C16.55075,1 16.7330006,1.18225059 16.7330006,1.40655902 L16.7330006,8.88058581 C17.0133861,8.9524346 17.2806286,9.04268369 17.5382328,9.14782826 L18.4538668,3.69082488 C18.5414873,3.17123545 19.0137616,2.82425836 19.5088173,2.91538365 L20.4042986,3.08186256 C20.8993543,3.17386406 21.2305597,3.66891976 21.1429393,4.1893854 L19.9364053,11.3768932 C20.208905,11.9674553 20.3736315,12.6421329 20.3736315,13.4263362 L20.3736315,13.493804 L21.7974642,11.7107272 C22.1207838,11.3067968 22.6946979,11.2542246 23.0793518,11.5933158 L23.7768108,12.2075354 C24.1623409,12.5466266 24.2122846,13.1494555 23.888965,13.5542621 Z M14.148431,12.4348085 L10.1739661,16.7746508 L18.1220197,16.7746508 L14.148431,12.4348085 Z\\\" id=\\\"icon-hand_up_形状\\\" />\\n                <mask id=\\\"icon-hand_up_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-hand_up_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-hand_up_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-hand_up_path-1\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAABVlBMVEUAAADL0N7M0eDN0uDM0uDa3ujQ1eLN0uDM0eDN0uC8wtLM0eDM0eDN0uDN0uDLz93Jzt3N0uDN0uDt7/T9/f3FytrM0eDN0uDM0eDKz97M0eCyuMrN0uDN0uCorr7N0uCqsMCorr63vtHQ1eLFytrN0uC+w9LCyNnV2eW9w9Xx8vXs7vTN0uC4vczN0uDL0N/N0uDAxtfb3ujDx9LCxtHN0uDFytrKz962vNCorr7t7/S3vtGorr7Izd3L0N/N0uDQ1eK9w9Xg5Oyorr7m6PCorr7t7/Tt7/Tq7PK0u8+0u8+an6/N0uD////t7/S+xNLJzt23vdHM0d/L0N/s7vTq7PLb3ujV2eXm6PDQ1eLg5Oy0u8+6wNO9w9XAxtfCyNnFytrHzNyorr7w8fTEydj8/f6gpLTW2eK5vs24vc26vsmkqLe8wdDy8/bi5OjIzdm/xdO/w9GVC9UgAAAAS3RSTlMABPXTYAlC8B3n9Lm3bmYPCPz57Prrw5OJflhJNiYb3HA4+/Dv6+Xa1dTOw7Gxr6+WlpV9fHNzcGVgX15FQTYzJyf+7tLSf35yV1aGL+jlAAACuElEQVRYw63Y11/aUBTA8ZMlYctSFNx77727W84tNJbuYZuEJQr+/y/1ElEsIYabfN944Pc5uRmECxa4EzG5LCR85bIvISwnxRMOGMjBRQ8iaqWiqutqsaQhomcxKENXesU+bNBuVNKg3mhIRfyDvWBXeMCLBqWkErVSKFRUclVS0OAdCNubZsuDTVqRqIXMrYJKiho2TW3ZmKpnBB9UVVLJNFSIWsUHIz1gTVrFVmWdFIxQgehlbLUqgYUhAe2GUBiyOCwPUjYOjfJ0PDwxggbLxX4QEcFUCNsopavW098mBCYG2+ZpvSCvbjRsFxk0WZ8otnl8i5iJtq3TkAfNKVq1XK5qCprzPD53w6npLKPp1DDcG9uM6YSRHtscu59nI04ciG80ZzqcJ9eXGUaX12T+EAx7cUI7rCUS3wPDKCEZBwgZBcOS09ASNEg+pyGfBJSYdRrKikD1OQ/10Y6MzkMoA0DQjVAQAPxuhPwAHO9GiOcgjW6EMA0hd0Ih6Hcn1A8Bd0IBENwJCeB1J+QF3p2QB9CdEELUrdCUOyEeRtxabAGxrjt7+Ot1RIFekFre2c9RXkMM0FskV9OJA3oth9jfeCu6qOV15ky+dtF4U0rjrZxWzzKqazlaSNMHmwt4DsCvuMAPAEGFOmemUEEAkOmnnAP0+zLcWvjo2AJQ++8d2wdKijntxCRoeO009AoMYdaR/hqeh+HOmzybu9AoNHEvvjAxOi85uNfz7BsLIzQGLdY/MVuHVtLsZ0azEjxyOvGVycSpyd8sBtEeaPPuO4O3YGL3Q9d2wdTBeHeZ8QPoIOX70QVfCjo6m/tp29wZWJDWftm0JoG1o5nfNswcwZO47cmLJ0xuczY3ohJ/LCQGwt1sjeU6oFtjXZF3Avz/ET6wIwMD7lhMrghe/vyc9worSfHYamX+ATK25cGDIECFAAAAAElFTkSuQmCC\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAA3lBMVEUAAADN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDN0uDM0eDN0uDKz97N0uDN0uDM0eDN0uDN0uDKz97N0uDN0uDKz97N0uDL0N/M0d/N0uDN0uDM0eDL0N/N0uDN0uDN0uD7+/3L0d/K0N73+PrJzt3j5u7HzNzT1+TFytrCyNnM0eDAxtfu8PXs7fPp6/La3ujn6fDY2+e9w9Xd4Orf4uvV2uXy9Pi6wNP09vnx8vbQ1OK4vtK2vNCzus++xda/xde0u8/Pvk1KAAAAKHRSTlMABAcKD9PvYfdEOBzy6rqUfXNvZufwxLCJJdzYMRL8tZtOXFQW+GfQ2Sz2iwAAA8pJREFUWMO92Nd62jAYgGEZD/YeCQQy2rQVFsJsCCQESqjp/d9Qf0k2xsbG46Bfkscn8csvYZ0Y3Ujqpu/v1EYB40JDvbtPdyWUoHztTsOetLtaPp6SSrewf9PSt1RkRqkU8Y2KFSXaNFUNh6RVI0yV7eAIdbIhjFzGESvLt5y2iiOntm8sS8MxKgQu75uOY6Wn/Z06jl3dz/k+jQ9Nv/vsTwYnKHO1T20t4DNFgZLm+e5k1c/QLwvQVNkFlQMVwrIxP6t86eS8jGVsh4vPQX+wnw/XOgmico4jqT4OwcN932nwNgqgVOkMVX0YfThgt59/oOWB+EpV21E0r0PI6DdTHEtcVoT4SJpiQZVrZ80Iluc69JUqwpGLV87KgziXN+ojFWUOpb0O3dqEWJNjQUNKyJUkTm/L6+BB3zexWWufmVrMyWMn7tC5dZcDuLSpj5QHqOYaiBC6ct12jS2N69XVACp5B/rsh3TgkosqISRlXANRY90PazHSheRQGenymIED0KIfof3w4JZyqO4ZiPYjNsSXVB2V3Y6x7UdtP7rYqTJ6cu20Mf7oR26wdaQnpLocYzRn/xHpF/4OZ0lFjQuHrj4HsZpTLsH9DVRwjipeDOK2sp/NAsIs4cz3sZtTao2EMuejsfxM0MigYiRUtJ3V7yS9i+MCS+vYG72YJ+ltLEZqoF94yp3RIlFLC1LRk3XE3pfJGgvpCZWn4okeviVrIqAy+mlDyfqwoJ8oJ5yk0NqCcihV4NDk/SNReMahgoykkoC274mazMZjgEoSkqoC0ldJGpkCqgLUJhyarJM0MfkekTZAqRaHTH0bP7o5caiVYlCdQyfzMIob3mxmHKpz6LXJoNnmFFfSdxuTQ8Ueh+R7DpkbEx/iNN7trJXdyxKH8k14IAHa7cY4cnTzZQ/UzMNASJJkpWKNtPvazQw9HCHGbHe0HYNWZAbBSMrrA4w0OW1A+joe/0J/AvsLHY/HL+ac+EAPPVkSkKy88JG4BFR4wJwdmuUDAQQj9Z4NLrF9AiusHTCwLrGwZyXFBxIj5fniYJ9MmCosUPg43Hl4lc8Qk3JNg1Gz2QmskEzTnDGGOc2ucBzp+w8uwVQRmjCG7c+PF+44EEjpR8oowEIbMwXGIY9pcABySzVCgQIrLKFQSkjN7YDEV1d/JBSs8IRCHusp4XiklPzSJBANj0C69uIwXqr7oOskQjqkdiXhXFOQ8qxH7FmxmQAr25lObxNTqJOV/ttrH6gX9iKq959fjYnytVLGq2RKtTxKkJTjrw8zIPDXh7lbG/wPxQHrOkf1UdAAAAAASUVORK5CYII=\"", "module.exports = \"data:image/svg+xml;base64,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\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-hand_end\",\n  \"use\": \"icon-hand_end-usage\",\n  \"viewBox\": \"0 0 30 23\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 23\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-hand_end\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-hand_end_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-hand_end_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-hand_end_切图\\\" transform=\\\"translate(-300.000000, -232.000000)\\\">\\n            <g id=\\\"icon-hand_end_icon-speaker-normal\\\" transform=\\\"translate(300.000000, 232.000000)\\\">\\n                <path d=\\\"M12.3882683,5.98356344 L18.4837365,5.98356344 C22.7451155,5.98356344 26.5380856,0.746698556 26.5380856,0.746698556 L26.5380856,20.0182559 L12.3882683,5.98356344 Z M23.3365587,21.1747778 L22.7178722,21.7987372 C22.5465031,21.9709852 22.2687972,21.9727428 22.0965493,21.8013737 L2.12984845,1.99725381 C1.95760051,1.82676349 1.95672169,1.5481788 2.127212,1.37593085 L2.74589851,0.751971453 C2.91726764,0.579723505 3.19497351,0.578844689 3.36722146,0.749335004 L23.3339223,20.5534549 C23.5061702,20.724824 23.5070491,21.0025299 23.3365587,21.1747778 Z M14.2205998,20.7872199 C14.2205998,21.5254254 13.6194896,22.377877 12.8777688,22.377877 L10.2008951,22.377877 C9.45917437,22.377877 8.85806419,21.5254254 8.85806419,20.7872199 L8.85806419,15.4774133 L7.62596408,15.4774133 C6.3903487,15.4774133 5.38849839,14.2259792 5.38849839,12.9965156 L5.38849839,8.94341593 L14.2205998,17.7025756 L14.2205998,20.7872199 Z\\\" id=\\\"icon-hand_end_形状\\\" />\\n                <mask id=\\\"icon-hand_end_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-hand_end_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-hand_end_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-hand_end_path-1\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "module.exports = __webpack_public_path__ + \"img/noicon.e5a5e6ae.png\";", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-layout\",\n  \"use\": \"icon-layout-usage\",\n  \"viewBox\": \"0 0 32 23\",\n  \"content\": \"<symbol viewBox=\\\"0 0 32 23\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-layout\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-layout_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"32\\\" height=\\\"23\\\" />\\n        <rect id=\\\"icon-layout_path-3\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"32\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-layout_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-layout_切图\\\" transform=\\\"translate(-213.000000, -108.000000)\\\">\\n            <g id=\\\"icon-layout_icon-布局--Normal\\\" transform=\\\"translate(213.000000, 108.000000)\\\">\\n                <mask id=\\\"icon-layout_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-layout_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-layout_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-layout_path-1\\\" />\\n                <path d=\\\"M31.3657681,11.3686 L30.7168658,12.0049 C30.7168658,12.0049 30.7168658,12.0049 30.7168658,12.0049 L28.7701588,13.9138 C28.5902651,14.0893 28.3002324,14.0893 28.1203387,13.9138 L27.4714364,13.2775 C27.2924605,13.102 27.2924605,12.8167 27.4714364,12.6412 L29.0941511,11.05 L27.4714364,9.4588 C27.2924605,9.2833 27.2924605,8.998 27.4714364,8.8225 L28.1203387,8.1862 C28.3002324,8.0107 28.5902651,8.0107 28.7701588,8.1862 L30.7168658,10.0951 C30.7168658,10.0951 30.7168658,10.0951 30.7168658,10.0951 L31.3657681,10.7314 C31.544744,10.9078 31.544744,11.1922 31.3657681,11.3686 Z M24.315036,20.5 L7.79418513,20.5 C6.77998845,20.5 5.95853503,19.6945 5.95853503,18.7 L5.95853503,4.3 C5.95853503,3.3055 6.77998845,2.5 7.79418513,2.5 L24.315036,2.5 C25.3292327,2.5 26.1506861,3.3055 26.1506861,4.3 L26.1506861,18.7 C26.1506861,19.6945 25.3292327,20.5 24.315036,20.5 Z M16.9724356,12.4 C16.9724356,11.9032 16.56125,11.5 16.0546106,11.5 L8.71201018,11.5 C8.20537076,11.5 7.79418513,11.9032 7.79418513,12.4 L7.79418513,17.8 C7.79418513,18.2968 8.20537076,18.7 8.71201018,18.7 L16.0546106,18.7 C16.56125,18.7 16.9724356,18.2968 16.9724356,17.8 L16.9724356,12.4 Z M4.8470489,13.3585 L4.14674838,14.0182 C3.9530873,14.2 3.64010895,14.2 3.44644787,14.0182 L1.34554633,12.0391 C1.34554633,12.0391 1.34554633,12.0391 1.34554633,12.0391 L0.645245814,11.3794 C0.451584729,11.1976 0.451584729,10.9024 0.645245814,10.7206 L1.34554633,10.0609 C1.34554633,10.0609 1.34554633,10.0609 1.34554633,10.0609 L3.44644787,8.0818 C3.64010895,7.9 3.9530873,7.9 4.14674838,8.0818 L4.8470489,8.7415 C5.04070998,8.9233 5.04070998,9.2194 4.8470489,9.4012 L3.09675652,11.05 L4.8470489,12.6988 C5.04070998,12.8806 5.04070998,13.1758 4.8470489,13.3585 Z\\\" id=\\\"icon-layout_形状\\\" mask=\\\"url(#icon-layout_mask-2)\\\" />\\n                <mask id=\\\"icon-layout_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-layout_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-layout_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-layout_path-3\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-icon_device_message\",\n  \"use\": \"icon-icon_device_message-usage\",\n  \"viewBox\": \"0 0 12 12\",\n  \"content\": \"<symbol viewBox=\\\"0 0 12 12\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-icon_device_message\\\">\\n<g clip-path=\\\"url(#icon-icon_device_message_clip0_2356_21624)\\\">\\n<path d=\\\"M3.70253 11.5328C1.46148 10.6056 -1.43051e-05 8.41933 -1.43051e-05 5.99405C-0.00258827 2.68646 2.67646 0.00293541 5.98406 -1.43051e-05C8.40933 -0.00404835 10.598 1.45379 11.529 3.6933C12.4599 5.9328 11.9495 8.51259 10.236 10.2289C8.52252 11.9453 5.94359 12.46 3.70253 11.5328ZM5.98828 4.30078C6.45234 4.30078 6.83203 3.92109 6.83203 3.45703C6.83203 2.99297 6.45234 2.61328 5.98828 2.61328C5.52422 2.61328 5.14453 2.99297 5.14453 3.45703C5.14453 3.92109 5.52422 4.30078 5.98828 4.30078ZM5.98828 9.36328C6.45234 9.36328 6.83203 8.98359 6.83203 8.51953L6.83203 5.98828C6.83203 5.52422 6.45234 5.14453 5.98828 5.14453C5.52422 5.14453 5.14453 5.52422 5.14453 5.98828L5.14453 8.51953C5.14453 8.98359 5.52422 9.36328 5.98828 9.36328Z\\\" fill=\\\"#D8D8D8\\\" />\\n</g>\\n<defs>\\n<clipPath id=\\\"icon-icon_device_message_clip0_2356_21624\\\">\\n<rect width=\\\"12\\\" height=\\\"12\\\" fill=\\\"white\\\" />\\n</clipPath>\\n</defs>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAA5FBMVEUAAADN0uDL0N/N0uDN0uDN0uDN0uDN0uDL0N/N0uDN0uDJzt7O0+HN0uDN0uDN0uDN0uDN0uDN0uDIzt3K0N7U2eXN0uCgpbTCyNnN0uDN0uCboLDN0uCkqbjN0uDL0N+fpLP5+vzHzdy9xNb5+vydorGcobHKz97CyNm9w9X6+/zX2+b6+/zT1+TN0uDN0uDN0uCgpbSfpLPN0uDN0uCan6/6+/zKz97S1uPM0d/L0N+6wNOgpbS9w9XX2+aeo7O3vtHFytrCyNnAxtfHzNy1vNCzus+9wtC2useip7azt8Sip7fKoKL6AAAANHRSTlMA8AnTQ/xeBHBjJh326rmwln5zWQ8G+fXt59rSw5OJPOf10sO5tbWVfm5mZjY2NjMR6eDVvW9/GwAAAgdJREFUWMOt2GlT4kAQBuB3EiCBcB+CCuJ9X7vbmjUhuxzrgvr//48xUYpKIZNMz/MpHzpv1fTUfOjGBtZdca9mbgsisW3W9op3FhQ4dk1QgqjZDjIxCn1aL18tGEir3ajQBpVGG2l0moIkRLMDqdwOpbCTkzWnTinVN7aqZFJqZmnDsQRlIL49XiFPmeQLWKtImRWxRitPmeVba/qzRQq2ckgoCVIiEndnmKTol4FVdVJWx4ohMQyxZJnEYFr40iSWJj61BbGINmINYmogYlSIqWLEb5XY4tfbf2LrI+REn56y6HcHgO15LpPn2QCOHzU4BjoHOoIOOrh+1OIal3qCLjEYaTHAyYMWJ9iVlbzMXXf+Iqvaxb4sx6WQK0vaR1dSMafJYjGhuaSsi5GkwqXF6+uCPEnZCF1dQT90He2npOItbvabrNn6rv/0QYtTDP5qMcDVHy2ucPNbixtYZR05ZQuokgZVABe+Px4//1f2PB77/gUAJwiTwihF44+cwEHoKPDDKGV+mHOED60gCHyG8PdWFGT0ZgHLrGcgcj6dsUzPEWv3piy95SBoT1hsfLEO/zEcWlgaugxDrDhzlZ0lRghPkWkkh5onJaKkfcziD376R1H+cMwf1/UvEPgrDf6SRePaR+5etoi617Uay8Sxq2VKKFdtBwqs22h9WA4TovXhrYXvvQO6HmY5c47ZQAAAAABJRU5ErkJggg==\"", "module.exports = \"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+aWNvbi3lhbPpl60g6KeG6aKRPC90aXRsZT4KICAgIDxnIGlkPSJXZWItUlRDIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iaWNvbi3lhbPpl60t6KeG6aKRIj4KICAgICAgICAgICAgPHBhdGggZD0iTTIuNTYwNTE2NSw1LjY1NjU2NjUzIEwxMi4xNDgsMTUuMjQ0MzI4MyBMMy41LDE1LjI0NTIxNTMgQzIuOTQ3NzE1MjUsMTUuMjQ1MjE1MyAyLjUsMTQuNzk3NTAwMSAyLjUsMTQuMjQ1MjE1MyBMMi41LDUuOTk5OTUzOTEgQzIuNSw1Ljg3OTMwOTE5IDIuNTIxMzY0NDksNS43NjM2NTQ0IDIuNTYwNTE2NSw1LjY1NjU2NjUzIFogTTEyLjY3NjY0ODgsNC45OTk5NTM5MSBDMTMuMjI4OTMzNiw0Ljk5OTk1MzkxIDEzLjY3NjY0ODgsNS40NDc2NjkxNiAxMy42NzY2NDg4LDUuOTk5OTUzOTEgTDEzLjY3Niw3LjgzMzk1MzkxIEwxNi42MzI3MDQyLDYuMzYyOTA2NjkgQzE2LjkyOTM3NTcsNi4yMTUyODMzIDE3LjI4OTU0NzksNi4zMzYxMTA2NiAxNy40MzcxNzEzLDYuNjMyNzgyMTYgQzE3LjQ3ODQ5NCw2LjcxNTgyNjQzIDE3LjUsNi44MDczMjA2NCAxNy41LDYuOTAwMDc3OTUgTDE3LjUsMTMuMzQ1MDkxMyBDMTcuNSwxMy42NzY0NjIxIDE3LjIzMTM3MDgsMTMuOTQ1MDkxMyAxNi45LDEzLjk0NTA5MTMgQzE2LjgwNzI0MjcsMTMuOTQ1MDkxMyAxNi43MTU3NDg1LDEzLjkyMzU4NTMgMTYuNjMyNzA0MiwxMy44ODIyNjI2IEwxMy42NzYsMTIuNDEwOTUzOSBMMTMuNjc2LDEzLjA4MzMyODMgTDUuNTkxLDQuOTk5MzI4MjkgTDEyLjY3NjY0ODgsNC45OTk5NTM5MSBaIiBpZD0i5b2i54q257uT5ZCIIiBmaWxsPSIjQ0NDQ0NDIj48L3BhdGg+CiAgICAgICAgICAgIDxyZWN0IGlkPSLnn6nlvaLlpIfku70tNiIgZmlsbD0iI0Y5NEI0QiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoOC44Njg4MTgsIDEwLjE4NTc4Nikgcm90YXRlKC00NS4wMDAwMDApIHRyYW5zbGF0ZSgtOC44Njg4MTgsIC0xMC4xODU3ODYpICIgeD0iOC4zNzQyODIzNCIgeT0iLTAuNDM1NDIwOTI3IiB3aWR0aD0iMSIgaGVpZ2h0PSIyMS4yNDI0MTM2IiByeD0iMC41Ij48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSLnn6nlvaIiIGZpbGwtb3BhY2l0eT0iMCIgZmlsbD0iI0Q4RDhEOCIgeD0iMCIgeT0iMCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIj48L3JlY3Q+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-more\",\n  \"use\": \"icon-more-usage\",\n  \"viewBox\": \"0 0 30 24\",\n  \"content\": \"<symbol viewBox=\\\"0 0 30 24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-more\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-more_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n        <rect id=\\\"icon-more_path-3\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-more_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-more_切图\\\" transform=\\\"translate(-77.000000, -142.000000)\\\">\\n            <g id=\\\"icon-more_icon-更多-normal\\\" transform=\\\"translate(77.000000, 143.000000)\\\">\\n                <mask id=\\\"icon-more_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-more_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-more_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-more_path-1\\\" />\\n                <path d=\\\"M5.5,9.47670861 C7.15685425,9.47670861 8.5,10.8198544 8.5,12.4767086 C8.5,14.1335629 7.15685425,15.4767086 5.5,15.4767086 C3.84314575,15.4767086 2.5,14.1335629 2.5,12.4767086 C2.5,10.8198544 3.84314575,9.47670861 5.5,9.47670861 Z M15,9.47670861 C16.6568542,9.47670861 18,10.8198544 18,12.4767086 C18,14.1335629 16.6568542,15.4767086 15,15.4767086 C13.3431458,15.4767086 12,14.1335629 12,12.4767086 C12,10.8198544 13.3431458,9.47670861 15,9.47670861 Z M24.5,9.47670861 C26.1568542,9.47670861 27.5,10.8198544 27.5,12.4767086 C27.5,14.1335629 26.1568542,15.4767086 24.5,15.4767086 C22.8431458,15.4767086 21.5,14.1335629 21.5,12.4767086 C21.5,10.8198544 22.8431458,9.47670861 24.5,9.47670861 Z\\\" id=\\\"icon-more_形状结合\\\" mask=\\\"url(#icon-more_mask-2)\\\" />\\n                <mask id=\\\"icon-more_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-more_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-more_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-more_path-3\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-home\",\n  \"use\": \"icon-home-usage\",\n  \"viewBox\": \"0 0 10 10\",\n  \"content\": \"<symbol viewBox=\\\"0 0 10 10\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-home\\\">\\n<path d=\\\"M9.77922 3.20966L5.40779 0.160207C5.17143 -0.0542941 4.78442 -0.0529535 4.54935 0.161548L0.218182 3.20966C0.0793379 3.33689 0 3.51941 0 3.71106V8.65937C0 9.3994 0.581818 10 1.2987 10H3.33766V7.44743C3.33766 6.49961 4.08182 5.73142 5 5.73142C5.91818 5.73142 6.66234 6.49961 6.66234 7.44743V10H8.7013C9.41818 10 10 9.3994 10 8.65937V3.7124C10 3.51935 9.91948 3.33568 9.77922 3.20966Z\\\" fill=\\\"white\\\" fill-opacity=\\\"0.8\\\" />\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-end_call\",\n  \"use\": \"icon-end_call-usage\",\n  \"viewBox\": \"0 0 31 24\",\n  \"content\": \"<symbol viewBox=\\\"0 0 31 24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-end_call\\\">\\n    <title></title>\\n    <defs>\\n        <rect id=\\\"icon-end_call_path-1\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n        <rect id=\\\"icon-end_call_path-3\\\" x=\\\"0.0448224552\\\" y=\\\"0\\\" width=\\\"30\\\" height=\\\"23\\\" />\\n    </defs>\\n    <g id=\\\"icon-end_call_Web-RTC\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-end_call_切图\\\" transform=\\\"translate(-438.000000, -197.000000)\\\">\\n            <g id=\\\"icon-end_call_icon-挂断-normal\\\" transform=\\\"translate(438.100000, 198.000000)\\\">\\n                <mask id=\\\"icon-end_call_mask-2\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-end_call_path-1\\\" />\\n                </mask>\\n                <use id=\\\"icon-end_call_蒙版\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" xlink:href=\\\"#icon-end_call_path-1\\\" />\\n                <path d=\\\"M28.3204526,14.908412 C28.2474803,15.3907415 28.1264529,16.7113633 25.2529438,16.3358226 C22.3785447,15.9602819 20.6886116,15.9353645 19.9232917,14.5141833 C19.3484119,13.5432949 19.6883563,12.749498 19.6305124,12.3864161 C19.5735583,12.0233341 19.4685493,11.689619 18.1853037,11.4787111 C16.9029479,11.2686931 15.0448225,11.2599281 15.0448225,11.2599281 C15.0448225,11.2599281 13.186697,11.2686931 11.9043412,11.4787111 C10.6210956,11.689619 10.5160866,12.0233341 10.4582426,12.3864161 C10.4012886,12.749498 10.741233,13.5432949 10.1663532,14.5141833 C9.40014339,15.9353645 7.71110022,15.9602819 4.83670114,16.3358226 C1.96319197,16.7113633 1.84216464,15.3907415 1.76919228,14.908412 C1.69621992,14.4269724 1.80745827,11.889848 2.62350284,10.1491903 C4.13634446,6.92238808 15.0448225,7.43141479 15.0448225,7.43141479 C15.0448225,7.43141479 25.9533005,6.92238808 27.4661421,10.1491903 C28.2821866,11.889848 28.393425,14.4269724 28.3204526,14.908412 Z\\\" id=\\\"icon-end_call_路径\\\" mask=\\\"url(#icon-end_call_mask-2)\\\" />\\n                <mask id=\\\"icon-end_call_mask-4\\\" fill=\\\"white\\\">\\n                    <use xlink:href=\\\"#icon-end_call_path-3\\\" />\\n                </mask>\\n                <use id=\\\"icon-end_call_蒙版\\\" fill-opacity=\\\"0\\\" xlink:href=\\\"#icon-end_call_path-3\\\" />\\n            </g>\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "module.exports = \"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+aWNvbi3op4bpopE8L3RpdGxlPgogICAgPGcgaWQ9IldlYi1SVEMiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJpY29uLeinhumikSI+CiAgICAgICAgICAgIDxwYXRoIGQ9Ik0xMi42NzY2NDg4LDUgQzEzLjIyODkzMzYsNSAxMy42NzY2NDg4LDUuNDQ3NzE1MjUgMTMuNjc2NjQ4OCw2IEwxMy42NzYsNy44MzQgTDE2LjYzMjcwNDIsNi4zNjI5NTI3OCBDMTYuOTI5Mzc1Nyw2LjIxNTMyOTM5IDE3LjI4OTU0NzksNi4zMzYxNTY3NSAxNy40MzcxNzEzLDYuNjMyODI4MjUgQzE3LjQ3ODQ5NCw2LjcxNTg3MjUxIDE3LjUsNi44MDczNjY3MyAxNy41LDYuOTAwMTI0MDQgTDE3LjUsMTMuMzQ1MTM3NCBDMTcuNSwxMy42NzY1MDgyIDE3LjIzMTM3MDgsMTMuOTQ1MTM3NCAxNi45LDEzLjk0NTEzNzQgQzE2LjgwNzI0MjcsMTMuOTQ1MTM3NCAxNi43MTU3NDg1LDEzLjkyMzYzMTQgMTYuNjMyNzA0MiwxMy44ODIzMDg2IEwxMy42NzYsMTIuNDExIEwxMy42NzY2NDg4LDE0LjI0NTI2MTQgQzEzLjY3NjY0ODgsMTQuNzk3NTQ2MiAxMy4yMjg5MzM2LDE1LjI0NTI2MTQgMTIuNjc2NjQ4OCwxNS4yNDUyNjE0IEwzLjUsMTUuMjQ1MjYxNCBDMi45NDc3MTUyNSwxNS4yNDUyNjE0IDIuNSwxNC43OTc1NDYyIDIuNSwxNC4yNDUyNjE0IEwyLjUsNiBDMi41LDUuNDQ3NzE1MjUgMi45NDc3MTUyNSw1IDMuNSw1IEwxMi42NzY2NDg4LDUgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iI0Q4RDhEOCI+PC9wYXRoPgogICAgICAgICAgICA8cmVjdCBpZD0i55+p5b2iIiB4PSIwIiB5PSIwIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiPjwvcmVjdD4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==\"", "export * from \"-!../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=74e1226c&prod&lang=scss&scoped=true\"", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-previous\",\n  \"use\": \"icon-previous-usage\",\n  \"viewBox\": \"0 0 24 24\",\n  \"content\": \"<symbol viewBox=\\\"0 0 24 24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-previous\\\">\\n    <title></title>\\n    <g id=\\\"icon-previous_通话中-视频窗口关闭-备份\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-previous_icon-向左翻页\\\">\\n            <path d=\\\"M5.86516452,1.55664916 C5.34793583,2.16053099 5.38241774,3.05824619 5.96861026,3.62295023 L14.664881,12 L5.96862073,20.3770397 C5.34578071,20.9770478 5.34578071,21.9529842 5.96861026,22.5529823 C6.58731415,23.1490059 7.5875011,23.1490059 8.20620498,22.5529823 L18.0313897,13.0879663 C18.6542193,12.4879682 18.6542193,11.5120318 18.0313897,10.9120337 L8.20620498,1.44701772 C7.5875011,0.850994095 6.58731415,0.850994095 5.96861026,1.44701772 L5.86516452,1.55664916 Z\\\" id=\\\"icon-previous_路径\\\" fill=\\\"#FFFFFF\\\" fill-rule=\\\"nonzero\\\" transform=\\\"translate(12.000000, 12.000000) rotate(-180.000000) translate(-12.000000, -12.000000) \\\" />\\n            <rect id=\\\"icon-previous_矩形\\\" fill=\\\"#D8D8D8\\\" opacity=\\\"0\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"24\\\" height=\\\"24\\\" />\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "import SpriteSymbol from \"../../../../node_modules/_svg-baker-runtime@1.4.7@svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../../node_modules/_svg-sprite-loader@6.0.11@svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-signal\",\n  \"use\": \"icon-signal-usage\",\n  \"viewBox\": \"0 0 20 21\",\n  \"content\": \"<symbol viewBox=\\\"0 0 20 21\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-signal\\\">\\n    <title></title>\\n    <g id=\\\"icon-signal_【通话中网络质量提示\\\" stroke=\\\"none\\\" stroke-width=\\\"1\\\" fill-rule=\\\"evenodd\\\">\\n        <g id=\\\"icon-signal_icon-信号强度-常态备份\\\" transform=\\\"translate(0.000000, 0.800000)\\\">\\n            <path d=\\\"M4,10 C4.55228475,10 5,10.4477153 5,11 L5,16 <PERSON>5,16.5522847 4.55228475,17 4,17 C3.44771525,17 3,16.5522847 3,16 L3,11 C3,10.4477153 3.44771525,10 4,10 Z M8,8 C8.55228475,8 9,8.44771525 9,9 L9,16 C9,16.5522847 8.55228475,17 8,17 C7.44771525,17 7,16.5522847 7,16 L7,9 C7,8.44771525 7.44771525,8 8,8 Z M12,7 C12.5522847,7 13,7.44771525 13,8 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,8 C11,7.44771525 11.4477153,7 12,7 Z M16,4 C16.5522847,4 17,4.44771525 17,5 L17,16 C17,16.5522847 16.5522847,17 16,17 C15.4477153,17 15,16.5522847 15,16 L15,5 C15,4.44771525 15.4477153,4 16,4 Z\\\" id=\\\"icon-signal_信号\\\" />\\n            <rect id=\\\"icon-signal_矩形备份-4\\\" fill-opacity=\\\"0\\\" fill=\\\"#D8D8D8\\\" x=\\\"0\\\" y=\\\"0\\\" width=\\\"20\\\" height=\\\"20\\\" />\\n        </g>\\n    </g>\\n</symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/_mini-css-extract-plugin@0.9.0@mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/_css-loader@3.6.0@css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/_postcss-loader@3.0.0@postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/_sass-loader@10.5.2@sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/_cache-loader@4.1.0@cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/_vue-loader@15.11.1@vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=60980d86&prod&lang=scss&scoped=true\""], "sourceRoot": ""}