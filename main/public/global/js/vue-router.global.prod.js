/*!
  * vue-router v4.3.2
  * (c) 2024 <PERSON>
  * @license MIT
  */
var VueRouter=function(e,t){"use strict";const n="undefined"!=typeof document;function r(e){return e.__esModule||"Module"===e[Symbol.toStringTag]}const o=Object.assign;function c(e,t){const n={};for(const r in t){const o=t[r];n[r]=s(o)?o.map(e):e(o)}return n}const a=()=>{},s=Array.isArray,i=/#/g,l=/&/g,u=/\//g,f=/=/g,p=/\?/g,h=/\+/g,d=/%5B/g,m=/%5D/g,g=/%5E/g,v=/%60/g,y=/%7B/g,b=/%7C/g,w=/%7D/g,E=/%20/g;function R(e){return encodeURI(""+e).replace(b,"|").replace(d,"[").replace(m,"]")}function k(e){return R(e).replace(h,"%2B").replace(E,"+").replace(i,"%23").replace(l,"%26").replace(v,"`").replace(y,"{").replace(w,"}").replace(g,"^")}function O(e){return null==e?"":function(e){return R(e).replace(i,"%23").replace(p,"%3F")}(e).replace(u,"%2F")}function j(e){try{return decodeURIComponent(""+e)}catch(e){}return""+e}const P=/\/$/,C=e=>e.replace(P,"");function x(e,t,n="/"){let r,o={},c="",a="";const s=t.indexOf("#");let i=t.indexOf("?");return s<i&&s>=0&&(i=-1),i>-1&&(r=t.slice(0,i),c=t.slice(i+1,s>-1?s:t.length),o=e(c)),s>-1&&(r=r||t.slice(0,s),a=t.slice(s,t.length)),r=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];".."!==o&&"."!==o||r.push("");let c,a,s=n.length-1;for(c=0;c<r.length;c++)if(a=r[c],"."!==a){if(".."!==a)break;s>1&&s--}return n.slice(0,s).join("/")+"/"+r.slice(c).join("/")}(null!=r?r:t,n),{fullPath:r+(c&&"?")+c+a,path:r,query:o,hash:j(a)}}function $(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function S(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function A(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!L(e[n],t[n]))return!1;return!0}function L(e,t){return s(e)?M(e,t):s(t)?M(t,e):e===t}function M(e,t){return s(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var q,B;!function(e){e.pop="pop",e.push="push"}(q||(q={})),function(e){e.back="back",e.forward="forward",e.unknown=""}(B||(B={}));function T(e){if(!e)if(n){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),C(e)}const G=/^[^#]+#/;function _(e,t){return e.replace(G,"#")+t}const F=()=>({left:window.scrollX,top:window.scrollY});function W(e){let t;if("el"in e){const n=e.el,r="string"==typeof n&&n.startsWith("#"),o="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function D(e,t){return(history.state?history.state.position-t:-1)+e}const I=new Map;let K=()=>location.protocol+"//"+location.host;function U(e,t){const{pathname:n,search:r,hash:o}=t,c=e.indexOf("#");if(c>-1){let t=o.includes(e.slice(c))?e.slice(c).length:1,n=o.slice(t);return"/"!==n[0]&&(n="/"+n),$(n,"")}return $(n,e)+r+o}function V(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?F():null}}function H(e){const t=function(e){const{history:t,location:n}=window,r={value:U(e,n)},c={value:t.state};function a(r,o,a){const s=e.indexOf("#"),i=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+r:K()+e+r;try{t[a?"replaceState":"pushState"](o,"",i),c.value=o}catch(e){console.error(e),n[a?"replace":"assign"](i)}}return c.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:c,push:function(e,n){const s=o({},c.value,t.state,{forward:e,scroll:F()});a(s.current,s,!0),a(e,o({},V(r.value,e,null),{position:s.position+1},n),!1),r.value=e},replace:function(e,n){a(e,o({},t.state,V(c.value.back,e,c.value.forward,!0),n,{position:c.value.position}),!0),r.value=e}}}(e=T(e)),n=function(e,t,n,r){let c=[],a=[],s=null;const i=({state:o})=>{const a=U(e,location),i=n.value,l=t.value;let u=0;if(o){if(n.value=a,t.value=o,s&&s===i)return void(s=null);u=l?o.position-l.position:0}else r(a);c.forEach((e=>{e(n.value,i,{delta:u,type:q.pop,direction:u?u>0?B.forward:B.back:B.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(o({},e.state,{scroll:F()}),"")}return window.addEventListener("popstate",i),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){c.push(e);const t=()=>{const t=c.indexOf(e);t>-1&&c.splice(t,1)};return a.push(t),t},destroy:function(){for(const e of a)e();a=[],window.removeEventListener("popstate",i),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const r=o({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:_.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function N(e){return"string"==typeof e||"symbol"==typeof e}const z={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},Q=Symbol("");var X;function Y(e,t){return o(new Error,{type:e,[Q]:!0},t)}function Z(e,t){return e instanceof Error&&Q in e&&(null==t||!!(e.type&t))}e.NavigationFailureType=void 0,(X=e.NavigationFailureType||(e.NavigationFailureType={}))[X.aborted=4]="aborted",X[X.cancelled=8]="cancelled",X[X.duplicated=16]="duplicated";const J="[^/]+?",ee={sensitive:!1,strict:!1,start:!0,end:!0},te=/[.+*?^${}()[\]/\\]/g;function ne(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function re(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const e=ne(r[n],o[n]);if(e)return e;n++}if(1===Math.abs(o.length-r.length)){if(oe(r))return 1;if(oe(o))return-1}return o.length-r.length}function oe(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ce={type:0,value:""},ae=/[a-zA-Z0-9_]/;function se(e,t,n){const r=function(e,t){const n=o({},ee,t),r=[];let c=n.start?"^":"";const a=[];for(const t of e){const e=t.length?[]:[90];n.strict&&!t.length&&(c+="/");for(let r=0;r<t.length;r++){const o=t[r];let s=40+(n.sensitive?.25:0);if(0===o.type)r||(c+="/"),c+=o.value.replace(te,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:i,regexp:l}=o;a.push({name:e,repeatable:n,optional:i});const u=l||J;if(u!==J){s+=10;try{new RegExp(`(${u})`)}catch(t){throw new Error(`Invalid custom RegExp for param "${e}" (${u}): `+t.message)}}let f=n?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;r||(f=i&&t.length<2?`(?:/${f})`:"/"+f),i&&(f+="?"),c+=f,s+=20,i&&(s+=-8),n&&(s+=-20),".*"===u&&(s+=-50)}e.push(s)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(c+="/?"),n.end?c+="$":n.strict&&(c+="(?:/|$)");const i=new RegExp(c,n.sensitive?"":"i");return{re:i,score:r,keys:a,parse:function(e){const t=e.match(i),n={};if(!t)return null;for(let e=1;e<t.length;e++){const r=t[e]||"",o=a[e-1];n[o.name]=r&&o.repeatable?r.split("/"):r}return n},stringify:function(t){let n="",r=!1;for(const o of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of o)if(0===e.type)n+=e.value;else if(1===e.type){const{value:c,repeatable:a,optional:i}=e,l=c in t?t[c]:"";if(s(l)&&!a)throw new Error(`Provided param "${c}" is an array but it is not repeatable (* or + modifiers)`);const u=s(l)?l.join("/"):l;if(!u){if(!i)throw new Error(`Missing required param "${c}"`);o.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=u}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[ce]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${l}": ${e}`)}let n=0,r=n;const o=[];let c;function a(){c&&o.push(c),c=[]}let s,i=0,l="",u="";function f(){l&&(0===n?c.push({type:0,value:l}):1===n||2===n||3===n?(c.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),c.push({type:1,value:l,regexp:u,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),l="")}function p(){l+=s}for(;i<e.length;)if(s=e[i++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(l&&f(),a()):":"===s?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:"("===s?n=2:ae.test(s)?p():(f(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&i--);break;case 2:")"===s?"\\"==u[u.length-1]?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:f(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&i--,u="";break;default:t("Unknown state")}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${l}"`),f(),a(),o}(e.path),n),c=o(r,{record:e,parent:t,children:[],alias:[]});return t&&!c.record.aliasOf==!t.record.aliasOf&&t.children.push(c),c}function ie(e,t){const n=[],r=new Map;function c(e,n,r){const l=!r,u=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:ue(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);u.aliasOf=r&&r.record;const f=he(t,e),p=[u];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)p.push(o({},u,{components:r?r.record.components:u.components,path:e,aliasOf:r?r.record:u}))}let h,d;for(const t of p){const{path:o}=t;if(n&&"/"!==o[0]){const e=n.record.path;t.path=n.record.path+(o&&("/"===e[e.length-1]?"":"/")+o)}if(h=se(t,n,f),r?r.alias.push(h):(d=d||h,d!==h&&d.alias.push(h),l&&e.name&&!fe(h)&&s(e.name)),u.children){const e=u.children;for(let t=0;t<e.length;t++)c(e[t],h,r&&r.children[t])}r=r||h,(h.record.components&&Object.keys(h.record.components).length||h.record.name||h.record.redirect)&&i(h)}return d?()=>{s(d)}:a}function s(e){if(N(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(s),t.alias.forEach(s))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(s),e.alias.forEach(s))}}function i(e){let t=0;for(;t<n.length&&re(e,n[t])>=0&&(e.record.path!==n[t].record.path||!de(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!fe(e)&&r.set(e.record.name,e)}return t=he({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>c(e))),{addRoute:c,resolve:function(e,t){let c,a,s,i={};if("name"in e&&e.name){if(c=r.get(e.name),!c)throw Y(1,{location:e});s=c.record.name,i=o(le(t.params,c.keys.filter((e=>!e.optional)).concat(c.parent?c.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&le(e.params,c.keys.map((e=>e.name)))),a=c.stringify(i)}else if(null!=e.path)a=e.path,c=n.find((e=>e.re.test(a))),c&&(i=c.parse(a),s=c.record.name);else{if(c=t.name?r.get(t.name):n.find((e=>e.re.test(t.path))),!c)throw Y(1,{location:e,currentLocation:t});s=c.record.name,i=o({},t.params,e.params),a=c.stringify(i)}const l=[];let u=c;for(;u;)l.unshift(u.record),u=u.parent;return{name:s,path:a,params:i,matched:l,meta:pe(l)}},removeRoute:s,getRoutes:function(){return n},getRecordMatcher:function(e){return r.get(e)}}}function le(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function ue(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="object"==typeof n?n[r]:n;return t}function fe(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function pe(e){return e.reduce(((e,t)=>o(e,t.meta)),{})}function he(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function de(e,t){return t.children.some((t=>t===e||de(e,t)))}function me(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let e=0;e<n.length;++e){const r=n[e].replace(h," "),o=r.indexOf("="),c=j(o<0?r:r.slice(0,o)),a=o<0?null:j(r.slice(o+1));if(c in t){let e=t[c];s(e)||(e=t[c]=[e]),e.push(a)}else t[c]=a}return t}function ge(e){let t="";for(let n in e){const r=e[n];if(n=k(n).replace(f,"%3D"),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}(s(r)?r.map((e=>e&&k(e))):[r&&k(r)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function ve(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=s(r)?r.map((e=>null==e?null:""+e)):null==r?r:""+r)}return t}const ye=Symbol(""),be=Symbol(""),we=Symbol(""),Ee=Symbol(""),Re=Symbol("");function ke(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Oe(e,n,r){const o=()=>{e[n].delete(r)};t.onUnmounted(o),t.onDeactivated(o),t.onActivated((()=>{e[n].add(r)})),e[n].add(r)}function je(e,t,n,r,o,c=(e=>e())){const a=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise(((s,i)=>{const l=e=>{var c;!1===e?i(Y(4,{from:n,to:t})):e instanceof Error?i(e):"string"==typeof(c=e)||c&&"object"==typeof c?i(Y(2,{from:t,to:e})):(a&&r.enterCallbacks[o]===a&&"function"==typeof e&&a.push(e),s())},u=c((()=>e.call(r&&r.instances[o],t,n,l)));let f=Promise.resolve(u);e.length<3&&(f=f.then(l)),f.catch((e=>i(e)))}))}function Pe(e,t,n,o,c=(e=>e())){const a=[];for(const i of e)for(const e in i.components){let l=i.components[e];if("beforeRouteEnter"===t||i.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const r=(l.__vccOpts||l)[t];r&&a.push(je(r,n,o,i,e,c))}else{let s=l();a.push((()=>s.then((a=>{if(!a)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${i.path}"`));const s=r(a)?a.default:a;i.components[e]=s;const l=(s.__vccOpts||s)[t];return l&&je(l,n,o,i,e,c)()}))))}}var s;return a}function Ce(e){const n=t.inject(we),r=t.inject(Ee),o=t.computed((()=>{const r=t.unref(e.to);return n.resolve(r)})),c=t.computed((()=>{const{matched:e}=o.value,{length:t}=e,n=e[t-1],c=r.matched;if(!n||!c.length)return-1;const a=c.findIndex(S.bind(null,n));if(a>-1)return a;const s=$e(e[t-2]);return t>1&&$e(n)===s&&c[c.length-1].path!==s?c.findIndex(S.bind(null,e[t-2])):a})),i=t.computed((()=>c.value>-1&&function(e,t){for(const n in t){const r=t[n],o=e[n];if("string"==typeof r){if(r!==o)return!1}else if(!s(o)||o.length!==r.length||r.some(((e,t)=>e!==o[t])))return!1}return!0}(r.params,o.value.params))),l=t.computed((()=>c.value>-1&&c.value===r.matched.length-1&&A(r.params,o.value.params)));return{route:o,href:t.computed((()=>o.value.href)),isActive:i,isExactActive:l,navigate:function(r={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(r)?n[t.unref(e.replace)?"replace":"push"](t.unref(e.to)).catch(a):Promise.resolve()}}}const xe=t.defineComponent({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Ce,setup(e,{slots:n}){const r=t.reactive(Ce(e)),{options:o}=t.inject(we),c=t.computed((()=>({[Se(e.activeClass,o.linkActiveClass,"router-link-active")]:r.isActive,[Se(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive})));return()=>{const o=n.default&&n.default(r);return e.custom?o:t.h("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:c.value},o)}}});function $e(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Se=(e,t,n)=>null!=e?e:null!=t?t:n;function Ae(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Le=t.defineComponent({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:n,slots:r}){const c=t.inject(Re),a=t.computed((()=>e.route||c.value)),s=t.inject(be,0),i=t.computed((()=>{let e=t.unref(s);const{matched:n}=a.value;let r;for(;(r=n[e])&&!r.components;)e++;return e})),l=t.computed((()=>a.value.matched[i.value]));t.provide(be,t.computed((()=>i.value+1))),t.provide(ye,l),t.provide(Re,a);const u=t.ref();return t.watch((()=>[u.value,l.value,e.name]),(([e,t,n],[r,o,c])=>{t&&(t.instances[n]=e,o&&o!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=o.leaveGuards),t.updateGuards.size||(t.updateGuards=o.updateGuards))),!e||!t||o&&S(t,o)&&r||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const c=a.value,s=e.name,i=l.value,f=i&&i.components[s];if(!f)return Ae(r.default,{Component:f,route:c});const p=i.props[s],h=p?!0===p?c.params:"function"==typeof p?p(c):p:null,d=t.h(f,o({},h,n,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(i.instances[s]=null)},ref:u}));return Ae(r.default,{Component:d,route:c})||d}}});return e.RouterLink=xe,e.RouterView=Le,e.START_LOCATION=z,e.createMemoryHistory=function(e=""){let t=[],n=[""],r=0;function o(e){r++,r!==n.length&&n.splice(r),n.push(e)}const c={location:"",state:{},base:e=T(e),createHref:_.bind(null,e),replace(e){n.splice(r--,1),o(e)},push(e,t){o(e)},listen:e=>(t.push(e),()=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)}),destroy(){t=[],n=[""],r=0},go(e,o=!0){const c=this.location,a=e<0?B.back:B.forward;r=Math.max(0,Math.min(r+e,n.length-1)),o&&function(e,n,{direction:r,delta:o}){const c={direction:r,delta:o,type:q.pop};for(const r of t)r(e,n,c)}(this.location,c,{direction:a,delta:e})}};return Object.defineProperty(c,"location",{enumerable:!0,get:()=>n[r]}),c},e.createRouter=function(e){const r=ie(e.routes,e),i=e.parseQuery||me,l=e.stringifyQuery||ge,u=e.history,f=ke(),p=ke(),h=ke(),d=t.shallowRef(z);let m=z;n&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const v=c.bind(null,(e=>""+e)),b=c.bind(null,O),E=c.bind(null,j);function k(e,t){if(t=o({},t||d.value),"string"==typeof e){const n=x(i,e,t.path),c=r.resolve({path:n.path},t),a=u.createHref(n.fullPath);return o(n,c,{params:E(c.params),hash:j(n.hash),redirectedFrom:void 0,href:a})}let n;if(null!=e.path)n=o({},e,{path:x(i,e.path,t.path).path});else{const r=o({},e.params);for(const e in r)null==r[e]&&delete r[e];n=o({},e,{params:b(r)}),t.params=b(t.params)}const c=r.resolve(n,t),a=e.hash||"";c.params=v(E(c.params));const s=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(l,o({},e,{hash:(f=a,R(f).replace(y,"{").replace(w,"}").replace(g,"^")),path:c.path}));var f;const p=u.createHref(s);return o({fullPath:s,hash:a,query:l===ge?ve(e.query):e.query||{}},c,{redirectedFrom:void 0,href:p})}function P(e){return"string"==typeof e?x(i,e,d.value.path):o({},e)}function C(e,t){if(m!==e)return Y(8,{from:t,to:e})}function $(e){return M(e)}function L(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"==typeof n?n(e):n;return"string"==typeof r&&(r=r.includes("?")||r.includes("#")?r=P(r):{path:r},r.params={}),o({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function M(e,t){const n=m=k(e),r=d.value,c=e.state,a=e.force,s=!0===e.replace,i=L(n);if(i)return M(o(P(i),{state:"object"==typeof i?o({},c,i.state):c,force:a,replace:s}),t||n);const u=n;let f;return u.redirectedFrom=t,!a&&function(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&S(t.matched[r],n.matched[o])&&A(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(l,r,n)&&(f=Y(16,{to:u,from:r}),te(r,r,!0,!1)),(f?Promise.resolve(f):G(u,r)).catch((e=>Z(e)?Z(e,2)?e:ee(e):J(e,u,r))).then((e=>{if(e){if(Z(e,2))return M(o({replace:s},P(e.to),{state:"object"==typeof e.to?o({},c,e.to.state):c,force:a}),t||u)}else e=K(u,r,!0,s,c);return _(u,r,e),e}))}function B(e,t){const n=C(e,t);return n?Promise.reject(n):Promise.resolve()}function T(e){const t=oe.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function G(e,t){let n;const[r,o,c]=function(e,t){const n=[],r=[],o=[],c=Math.max(t.matched.length,e.matched.length);for(let a=0;a<c;a++){const c=t.matched[a];c&&(e.matched.find((e=>S(e,c)))?r.push(c):n.push(c));const s=e.matched[a];s&&(t.matched.find((e=>S(e,s)))||o.push(s))}return[n,r,o]}(e,t);n=Pe(r.reverse(),"beforeRouteLeave",e,t);for(const o of r)o.leaveGuards.forEach((r=>{n.push(je(r,e,t))}));const a=B.bind(null,e,t);return n.push(a),ae(n).then((()=>{n=[];for(const r of f.list())n.push(je(r,e,t));return n.push(a),ae(n)})).then((()=>{n=Pe(o,"beforeRouteUpdate",e,t);for(const r of o)r.updateGuards.forEach((r=>{n.push(je(r,e,t))}));return n.push(a),ae(n)})).then((()=>{n=[];for(const r of c)if(r.beforeEnter)if(s(r.beforeEnter))for(const o of r.beforeEnter)n.push(je(o,e,t));else n.push(je(r.beforeEnter,e,t));return n.push(a),ae(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Pe(c,"beforeRouteEnter",e,t,T),n.push(a),ae(n)))).then((()=>{n=[];for(const r of p.list())n.push(je(r,e,t));return n.push(a),ae(n)})).catch((e=>Z(e,8)?e:Promise.reject(e)))}function _(e,t,n){h.list().forEach((r=>T((()=>r(e,t,n)))))}function K(e,t,r,c,a){const s=C(e,t);if(s)return s;const i=t===z,l=n?history.state:{};r&&(c||i?u.replace(e.fullPath,o({scroll:i&&l&&l.scroll},a)):u.push(e.fullPath,a)),d.value=e,te(e,t,r,i),ee()}let U;function V(){U||(U=u.listen(((e,t,r)=>{if(!ce.listening)return;const c=k(e),s=L(c);if(s)return void M(o(s,{replace:!0}),c).catch(a);m=c;const i=d.value;var l,f;n&&(l=D(i.fullPath,r.delta),f=F(),I.set(l,f)),G(c,i).catch((e=>Z(e,12)?e:Z(e,2)?(M(e.to,c).then((e=>{Z(e,20)&&!r.delta&&r.type===q.pop&&u.go(-1,!1)})).catch(a),Promise.reject()):(r.delta&&u.go(-r.delta,!1),J(e,c,i)))).then((e=>{(e=e||K(c,i,!1))&&(r.delta&&!Z(e,8)?u.go(-r.delta,!1):r.type===q.pop&&Z(e,20)&&u.go(-1,!1)),_(c,i,e)})).catch(a)})))}let H,Q=ke(),X=ke();function J(e,t,n){ee(e);const r=X.list();return r.length?r.forEach((r=>r(e,t,n))):console.error(e),Promise.reject(e)}function ee(e){return H||(H=!e,V(),Q.list().forEach((([t,n])=>e?n(e):t())),Q.reset()),e}function te(r,o,c,a){const{scrollBehavior:s}=e;if(!n||!s)return Promise.resolve();const i=!c&&function(e){const t=I.get(e);return I.delete(e),t}(D(r.fullPath,0))||(a||!c)&&history.state&&history.state.scroll||null;return t.nextTick().then((()=>s(r,o,i))).then((e=>e&&W(e))).catch((e=>J(e,r,o)))}const ne=e=>u.go(e);let re;const oe=new Set,ce={currentRoute:d,listening:!0,addRoute:function(e,t){let n,o;return N(e)?(n=r.getRecordMatcher(e),o=t):o=e,r.addRoute(o,n)},removeRoute:function(e){const t=r.getRecordMatcher(e);t&&r.removeRoute(t)},hasRoute:function(e){return!!r.getRecordMatcher(e)},getRoutes:function(){return r.getRoutes().map((e=>e.record))},resolve:k,options:e,push:$,replace:function(e){return $(o(P(e),{replace:!0}))},go:ne,back:()=>ne(-1),forward:()=>ne(1),beforeEach:f.add,beforeResolve:p.add,afterEach:h.add,onError:X.add,isReady:function(){return H&&d.value!==z?Promise.resolve():new Promise(((e,t)=>{Q.add([e,t])}))},install(e){e.component("RouterLink",xe),e.component("RouterView",Le),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>t.unref(d)}),n&&!re&&d.value===z&&(re=!0,$(u.location).catch((e=>{})));const r={};for(const e in z)Object.defineProperty(r,e,{get:()=>d.value[e],enumerable:!0});e.provide(we,this),e.provide(Ee,t.shallowReactive(r)),e.provide(Re,d);const o=e.unmount;oe.add(e),e.unmount=function(){oe.delete(e),oe.size<1&&(m=z,U&&U(),U=null,d.value=z,re=!1,H=!1),o()}}};function ae(e){return e.reduce(((e,t)=>e.then((()=>T(t)))),Promise.resolve())}return ce},e.createRouterMatcher=ie,e.createWebHashHistory=function(e){return(e=location.host?e||location.pathname+location.search:"").includes("#")||(e+="#"),H(e)},e.createWebHistory=H,e.isNavigationFailure=Z,e.loadRouteLocation=function(e){return e.matched.every((e=>e.redirect))?Promise.reject(new Error("Cannot load a route that redirects.")):Promise.all(e.matched.map((e=>e.components&&Promise.all(Object.keys(e.components).reduce(((t,n)=>{const o=e.components[n];return"function"!=typeof o||"displayName"in o||t.push(o().then((t=>{if(!t)return Promise.reject(new Error(`Couldn't resolve component "${n}" at "${e.path}". Ensure you passed a function that returns a promise.`));const o=r(t)?t.default:t;e.components[n]=o}))),t}),[]))))).then((()=>e))},e.matchedRouteKey=ye,e.onBeforeRouteLeave=function(e){const n=t.inject(ye,{}).value;n&&Oe(n,"leaveGuards",e)},e.onBeforeRouteUpdate=function(e){const n=t.inject(ye,{}).value;n&&Oe(n,"updateGuards",e)},e.parseQuery=me,e.routeLocationKey=Ee,e.routerKey=we,e.routerViewLocationKey=Re,e.stringifyQuery=ge,e.useLink=Ce,e.useRoute=function(){return t.inject(Ee)},e.useRouter=function(){return t.inject(we)},e.viewDepthKey=be,e}({},Vue);
