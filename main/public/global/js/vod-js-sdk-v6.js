/*! For license information please see vod-js-sdk-v6.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.TcVod=t():e.TcVod=t()}(self,(()=>(()=>{var __webpack_modules__={669:(e,t,r)=>{e.exports=r(609)},448:(e,t,r)=>{"use strict";var n=r(867),o=r(26),i=r(372),a=r(327),s=r(97),c=r(109),u=r(985),l=r(61);e.exports=function(e){return new Promise((function(t,r){var d=e.data,p=e.headers,f=e.responseType;n.isFormData(d)&&delete p["Content-Type"];var h=new XMLHttpRequest;if(e.auth){var m=e.auth.username||"",g=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";p.Authorization="Basic "+btoa(m+":"+g)}var y=s(e.baseURL,e.url);function v(){if(h){var n="getAllResponseHeaders"in h?c(h.getAllResponseHeaders()):null,i={data:f&&"text"!==f&&"json"!==f?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:n,config:e,request:h};o(t,r,i),h=null}}if(h.open(e.method.toUpperCase(),a(y,e.params,e.paramsSerializer),!0),h.timeout=e.timeout,"onloadend"in h?h.onloadend=v:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(v)},h.onabort=function(){h&&(r(l("Request aborted",e,"ECONNABORTED",h)),h=null)},h.onerror=function(){r(l("Network Error",e,null,h)),h=null},h.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),r(l(t,e,e.transitional&&e.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",h)),h=null},n.isStandardBrowserEnv()){var b=(e.withCredentials||u(y))&&e.xsrfCookieName?i.read(e.xsrfCookieName):void 0;b&&(p[e.xsrfHeaderName]=b)}"setRequestHeader"in h&&n.forEach(p,(function(e,t){void 0===d&&"content-type"===t.toLowerCase()?delete p[t]:h.setRequestHeader(t,e)})),n.isUndefined(e.withCredentials)||(h.withCredentials=!!e.withCredentials),f&&"json"!==f&&(h.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&h.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&h.upload&&h.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){h&&(h.abort(),r(e),h=null)})),d||(d=null),h.send(d)}))}},609:(e,t,r)=>{"use strict";var n=r(867),o=r(849),i=r(321),a=r(185);function s(e){var t=new i(e),r=o(i.prototype.request,t);return n.extend(r,i.prototype,t),n.extend(r,t),r}var c=s(r(655));c.Axios=i,c.create=function(e){return s(a(c.defaults,e))},c.Cancel=r(263),c.CancelToken=r(972),c.isCancel=r(502),c.all=function(e){return Promise.all(e)},c.spread=r(713),c.isAxiosError=r(268),e.exports=c,e.exports.default=c},263:e=>{"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},972:(e,t,r)=>{"use strict";var n=r(263);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var r=this;e((function(e){r.reason||(r.reason=new n(e),t(r.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},502:e=>{"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},321:(e,t,r)=>{"use strict";var n=r(867),o=r(327),i=r(782),a=r(572),s=r(185),c=r(875),u=c.validators;function l(e){this.defaults=e,this.interceptors={request:new i,response:new i}}l.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=s(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&c.assertOptions(t,{silentJSONParsing:u.transitional(u.boolean,"1.0.0"),forcedJSONParsing:u.transitional(u.boolean,"1.0.0"),clarifyTimeoutError:u.transitional(u.boolean,"1.0.0")},!1);var r=[],n=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(n=n&&t.synchronous,r.unshift(t.fulfilled,t.rejected))}));var o,i=[];if(this.interceptors.response.forEach((function(e){i.push(e.fulfilled,e.rejected)})),!n){var l=[a,void 0];for(Array.prototype.unshift.apply(l,r),l=l.concat(i),o=Promise.resolve(e);l.length;)o=o.then(l.shift(),l.shift());return o}for(var d=e;r.length;){var p=r.shift(),f=r.shift();try{d=p(d)}catch(e){f(e);break}}try{o=a(d)}catch(e){return Promise.reject(e)}for(;i.length;)o=o.then(i.shift(),i.shift());return o},l.prototype.getUri=function(e){return e=s(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},n.forEach(["delete","get","head","options"],(function(e){l.prototype[e]=function(t,r){return this.request(s(r||{},{method:e,url:t,data:(r||{}).data}))}})),n.forEach(["post","put","patch"],(function(e){l.prototype[e]=function(t,r,n){return this.request(s(n||{},{method:e,url:t,data:r}))}})),e.exports=l},782:(e,t,r)=>{"use strict";var n=r(867);function o(){this.handlers=[]}o.prototype.use=function(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){n.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},97:(e,t,r)=>{"use strict";var n=r(793),o=r(303);e.exports=function(e,t){return e&&!n(t)?o(e,t):t}},61:(e,t,r)=>{"use strict";var n=r(481);e.exports=function(e,t,r,o,i){var a=new Error(e);return n(a,t,r,o,i)}},572:(e,t,r)=>{"use strict";var n=r(867),o=r(527),i=r(502),a=r(655);function s(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return s(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),n.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||a.adapter)(e).then((function(t){return s(e),t.data=o.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(s(e),t&&t.response&&(t.response.data=o.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},481:e=>{"use strict";e.exports=function(e,t,r,n,o){return e.config=t,r&&(e.code=r),e.request=n,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},185:(e,t,r)=>{"use strict";var n=r(867);e.exports=function(e,t){t=t||{};var r={},o=["url","method","data"],i=["headers","auth","proxy","params"],a=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],s=["validateStatus"];function c(e,t){return n.isPlainObject(e)&&n.isPlainObject(t)?n.merge(e,t):n.isPlainObject(t)?n.merge({},t):n.isArray(t)?t.slice():t}function u(o){n.isUndefined(t[o])?n.isUndefined(e[o])||(r[o]=c(void 0,e[o])):r[o]=c(e[o],t[o])}n.forEach(o,(function(e){n.isUndefined(t[e])||(r[e]=c(void 0,t[e]))})),n.forEach(i,u),n.forEach(a,(function(o){n.isUndefined(t[o])?n.isUndefined(e[o])||(r[o]=c(void 0,e[o])):r[o]=c(void 0,t[o])})),n.forEach(s,(function(n){n in t?r[n]=c(e[n],t[n]):n in e&&(r[n]=c(void 0,e[n]))}));var l=o.concat(i).concat(a).concat(s),d=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===l.indexOf(e)}));return n.forEach(d,u),r}},26:(e,t,r)=>{"use strict";var n=r(61);e.exports=function(e,t,r){var o=r.config.validateStatus;r.status&&o&&!o(r.status)?t(n("Request failed with status code "+r.status,r.config,null,r.request,r)):e(r)}},527:(e,t,r)=>{"use strict";var n=r(867),o=r(655);e.exports=function(e,t,r){var i=this||o;return n.forEach(r,(function(r){e=r.call(i,e,t)})),e}},655:(e,t,r)=>{"use strict";var n=r(867),o=r(16),i=r(481),a={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!n.isUndefined(e)&&n.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var c,u={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(c=r(448)),c),transformRequest:[function(e,t){return o(t,"Accept"),o(t,"Content-Type"),n.isFormData(e)||n.isArrayBuffer(e)||n.isBuffer(e)||n.isStream(e)||n.isFile(e)||n.isBlob(e)?e:n.isArrayBufferView(e)?e.buffer:n.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):n.isObject(e)||t&&"application/json"===t["Content-Type"]?(s(t,"application/json"),function(e,t,r){if(n.isString(e))try{return(0,JSON.parse)(e),n.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional,r=t&&t.silentJSONParsing,o=t&&t.forcedJSONParsing,a=!r&&"json"===this.responseType;if(a||o&&n.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(a){if("SyntaxError"===e.name)throw i(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};n.forEach(["delete","get","head"],(function(e){u.headers[e]={}})),n.forEach(["post","put","patch"],(function(e){u.headers[e]=n.merge(a)})),e.exports=u},849:e=>{"use strict";e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}},327:(e,t,r)=>{"use strict";var n=r(867);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,r){if(!t)return e;var i;if(r)i=r(t);else if(n.isURLSearchParams(t))i=t.toString();else{var a=[];n.forEach(t,(function(e,t){null!=e&&(n.isArray(e)?t+="[]":e=[e],n.forEach(e,(function(e){n.isDate(e)?e=e.toISOString():n.isObject(e)&&(e=JSON.stringify(e)),a.push(o(t)+"="+o(e))})))})),i=a.join("&")}if(i){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}},303:e=>{"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},372:(e,t,r)=>{"use strict";var n=r(867);e.exports=n.isStandardBrowserEnv()?{write:function(e,t,r,o,i,a){var s=[];s.push(e+"="+encodeURIComponent(t)),n.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),n.isString(o)&&s.push("path="+o),n.isString(i)&&s.push("domain="+i),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},793:e=>{"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},268:e=>{"use strict";e.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},985:(e,t,r)=>{"use strict";var n=r(867);e.exports=n.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function o(e){var n=e;return t&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return e=o(window.location.href),function(t){var r=n.isString(t)?o(t):t;return r.protocol===e.protocol&&r.host===e.host}}():function(){return!0}},16:(e,t,r)=>{"use strict";var n=r(867);e.exports=function(e,t){n.forEach(e,(function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])}))}},109:(e,t,r)=>{"use strict";var n=r(867),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,r,i,a={};return e?(n.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=n.trim(e.substr(0,i)).toLowerCase(),r=n.trim(e.substr(i+1)),t){if(a[t]&&o.indexOf(t)>=0)return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([r]):a[t]?a[t]+", "+r:r}})),a):a}},713:e=>{"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},875:(e,t,r)=>{"use strict";var n=r(593),o={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){o[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}}));var i={},a=n.version.split(".");function s(e,t){for(var r=t?t.split("."):a,n=e.split("."),o=0;o<3;o++){if(r[o]>n[o])return!0;if(r[o]<n[o])return!1}return!1}o.transitional=function(e,t,r){var o=t&&s(t);function a(e,t){return"[Axios v"+n.version+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return function(r,n,s){if(!1===e)throw new Error(a(n," has been removed in "+t));return o&&!i[n]&&(i[n]=!0,console.warn(a(n," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,n,s)}},e.exports={isOlderVersion:s,assertOptions:function(e,t,r){if("object"!=typeof e)throw new TypeError("options must be an object");for(var n=Object.keys(e),o=n.length;o-- >0;){var i=n[o],a=t[i];if(a){var s=e[i],c=void 0===s||a(s,i,e);if(!0!==c)throw new TypeError("option "+i+" must be "+c)}else if(!0!==r)throw Error("Unknown option "+i)}},validators:o}},867:(e,t,r)=>{"use strict";var n=r(849),o=Object.prototype.toString;function i(e){return"[object Array]"===o.call(e)}function a(e){return void 0===e}function s(e){return null!==e&&"object"==typeof e}function c(e){if("[object Object]"!==o.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function u(e){return"[object Function]"===o.call(e)}function l(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),i(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:i,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:function(e){return null!==e&&!a(e)&&null!==e.constructor&&!a(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isPlainObject:c,isUndefined:a,isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:u,isStream:function(e){return s(e)&&u(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:l,merge:function e(){var t={};function r(r,n){c(t[n])&&c(r)?t[n]=e(t[n],r):c(r)?t[n]=e({},r):i(r)?t[n]=r.slice():t[n]=r}for(var n=0,o=arguments.length;n<o;n++)l(arguments[n],r);return t},extend:function(e,t,r){return l(t,(function(t,o){e[o]=r&&"function"==typeof t?n(t,r):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},729:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var s=new o(n,i||e,a),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,o,i,a){var s=r?r+e:e;if(!this._events[s])return!1;var c,u,l=this._events[s],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,o),!0;case 5:return l.fn.call(l.context,t,n,o,i),!0;case 6:return l.fn.call(l.context,t,n,o,i,a),!0}for(u=1,c=new Array(d-1);u<d;u++)c[u-1]=arguments[u];l.fn.apply(l.context,c)}else{var p,f=l.length;for(u=0;u<f;u++)switch(l[u].once&&this.removeListener(e,l[u].fn,void 0,!0),d){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,t);break;case 3:l[u].fn.call(l[u].context,t,n);break;case 4:l[u].fn.call(l[u].context,t,n,o);break;default:if(!c)for(p=1,c=new Array(d-1);p<d;p++)c[p-1]=arguments[p];l[u].fn.apply(l[u].context,c)}}return!0},s.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var s=this._events[i];if(s.fn)s.fn!==t||o&&!s.once||n&&s.context!==n||a(this,i);else{for(var c=0,u=[],l=s.length;c<l;c++)(s[c].fn!==t||o&&!s[c].once||n&&s[c].context!==n)&&u.push(s[c]);u.length?this._events[i]=1===u.length?u[0]:u:a(this,i)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},810:(module,exports,__webpack_require__)=>{var __WEBPACK_AMD_DEFINE_RESULT__;(function(){"use strict";var root="object"==typeof window?window:{},NODE_JS=!root.JS_SHA1_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node;NODE_JS&&(root=__webpack_require__.g);var COMMON_JS=!root.JS_SHA1_NO_COMMON_JS&&module.exports,AMD=__webpack_require__.amdO,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[-2147483648,8388608,32768,128],SHIFT=[24,16,8,0],OUTPUT_TYPES=["hex","array","digest","arrayBuffer"],blocks=[],createOutputMethod=function(e){return function(t){return new Sha1(!0).update(t)[e]()}},createMethod=function(){var e=createOutputMethod("hex");NODE_JS&&(e=nodeWrap(e)),e.create=function(){return new Sha1},e.update=function(t){return e.create().update(t)};for(var t=0;t<OUTPUT_TYPES.length;++t){var r=OUTPUT_TYPES[t];e[r]=createOutputMethod(r)}return e},nodeWrap=function(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(e){if("string"==typeof e)return crypto.createHash("sha1").update(e,"utf8").digest("hex");if(e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(void 0===e.length)return method(e);return crypto.createHash("sha1").update(new Buffer(e)).digest("hex")};return nodeMethod};function Sha1(e){e?(blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Sha1.prototype.update=function(e){if(!this.finalized){var t="string"!=typeof e;t&&e.constructor===root.ArrayBuffer&&(e=new Uint8Array(e));for(var r,n,o=0,i=e.length||0,a=this.blocks;o<i;){if(this.hashed&&(this.hashed=!1,a[0]=this.block,a[16]=a[1]=a[2]=a[3]=a[4]=a[5]=a[6]=a[7]=a[8]=a[9]=a[10]=a[11]=a[12]=a[13]=a[14]=a[15]=0),t)for(n=this.start;o<i&&n<64;++o)a[n>>2]|=e[o]<<SHIFT[3&n++];else for(n=this.start;o<i&&n<64;++o)(r=e.charCodeAt(o))<128?a[n>>2]|=r<<SHIFT[3&n++]:r<2048?(a[n>>2]|=(192|r>>6)<<SHIFT[3&n++],a[n>>2]|=(128|63&r)<<SHIFT[3&n++]):r<55296||r>=57344?(a[n>>2]|=(224|r>>12)<<SHIFT[3&n++],a[n>>2]|=(128|r>>6&63)<<SHIFT[3&n++],a[n>>2]|=(128|63&r)<<SHIFT[3&n++]):(r=65536+((1023&r)<<10|1023&e.charCodeAt(++o)),a[n>>2]|=(240|r>>18)<<SHIFT[3&n++],a[n>>2]|=(128|r>>12&63)<<SHIFT[3&n++],a[n>>2]|=(128|r>>6&63)<<SHIFT[3&n++],a[n>>2]|=(128|63&r)<<SHIFT[3&n++]);this.lastByteIndex=n,this.bytes+=n-this.start,n>=64?(this.block=a[16],this.start=n-64,this.hash(),this.hashed=!0):this.start=n}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Sha1.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=EXTRA[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},Sha1.prototype.hash=function(){var e,t,r=this.h0,n=this.h1,o=this.h2,i=this.h3,a=this.h4,s=this.blocks;for(e=16;e<80;++e)t=s[e-3]^s[e-8]^s[e-14]^s[e-16],s[e]=t<<1|t>>>31;for(e=0;e<20;e+=5)r=(t=(n=(t=(o=(t=(i=(t=(a=(t=r<<5|r>>>27)+(n&o|~n&i)+a+1518500249+s[e]<<0)<<5|a>>>27)+(r&(n=n<<30|n>>>2)|~r&o)+i+1518500249+s[e+1]<<0)<<5|i>>>27)+(a&(r=r<<30|r>>>2)|~a&n)+o+1518500249+s[e+2]<<0)<<5|o>>>27)+(i&(a=a<<30|a>>>2)|~i&r)+n+1518500249+s[e+3]<<0)<<5|n>>>27)+(o&(i=i<<30|i>>>2)|~o&a)+r+1518500249+s[e+4]<<0,o=o<<30|o>>>2;for(;e<40;e+=5)r=(t=(n=(t=(o=(t=(i=(t=(a=(t=r<<5|r>>>27)+(n^o^i)+a+1859775393+s[e]<<0)<<5|a>>>27)+(r^(n=n<<30|n>>>2)^o)+i+1859775393+s[e+1]<<0)<<5|i>>>27)+(a^(r=r<<30|r>>>2)^n)+o+1859775393+s[e+2]<<0)<<5|o>>>27)+(i^(a=a<<30|a>>>2)^r)+n+1859775393+s[e+3]<<0)<<5|n>>>27)+(o^(i=i<<30|i>>>2)^a)+r+1859775393+s[e+4]<<0,o=o<<30|o>>>2;for(;e<60;e+=5)r=(t=(n=(t=(o=(t=(i=(t=(a=(t=r<<5|r>>>27)+(n&o|n&i|o&i)+a-1894007588+s[e]<<0)<<5|a>>>27)+(r&(n=n<<30|n>>>2)|r&o|n&o)+i-1894007588+s[e+1]<<0)<<5|i>>>27)+(a&(r=r<<30|r>>>2)|a&n|r&n)+o-1894007588+s[e+2]<<0)<<5|o>>>27)+(i&(a=a<<30|a>>>2)|i&r|a&r)+n-1894007588+s[e+3]<<0)<<5|n>>>27)+(o&(i=i<<30|i>>>2)|o&a|i&a)+r-1894007588+s[e+4]<<0,o=o<<30|o>>>2;for(;e<80;e+=5)r=(t=(n=(t=(o=(t=(i=(t=(a=(t=r<<5|r>>>27)+(n^o^i)+a-899497514+s[e]<<0)<<5|a>>>27)+(r^(n=n<<30|n>>>2)^o)+i-899497514+s[e+1]<<0)<<5|i>>>27)+(a^(r=r<<30|r>>>2)^n)+o-899497514+s[e+2]<<0)<<5|o>>>27)+(i^(a=a<<30|a>>>2)^r)+n-899497514+s[e+3]<<0)<<5|n>>>27)+(o^(i=i<<30|i>>>2)^a)+r-899497514+s[e+4]<<0,o=o<<30|o>>>2;this.h0=this.h0+r<<0,this.h1=this.h1+n<<0,this.h2=this.h2+o<<0,this.h3=this.h3+i<<0,this.h4=this.h4+a<<0},Sha1.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,n=this.h3,o=this.h4;return HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[n>>28&15]+HEX_CHARS[n>>24&15]+HEX_CHARS[n>>20&15]+HEX_CHARS[n>>16&15]+HEX_CHARS[n>>12&15]+HEX_CHARS[n>>8&15]+HEX_CHARS[n>>4&15]+HEX_CHARS[15&n]+HEX_CHARS[o>>28&15]+HEX_CHARS[o>>24&15]+HEX_CHARS[o>>20&15]+HEX_CHARS[o>>16&15]+HEX_CHARS[o>>12&15]+HEX_CHARS[o>>8&15]+HEX_CHARS[o>>4&15]+HEX_CHARS[15&o]},Sha1.prototype.toString=Sha1.prototype.hex,Sha1.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,n=this.h3,o=this.h4;return[e>>24&255,e>>16&255,e>>8&255,255&e,t>>24&255,t>>16&255,t>>8&255,255&t,r>>24&255,r>>16&255,r>>8&255,255&r,n>>24&255,n>>16&255,n>>8&255,255&n,o>>24&255,o>>16&255,o>>8&255,255&o]},Sha1.prototype.array=Sha1.prototype.digest,Sha1.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(20),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),e};var exports=createMethod();COMMON_JS?module.exports=exports:(root.sha1=exports,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))})()},754:function(e,t,r){"use strict";var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var o=r(119),i=r(376),a=function(){function e(e){this.allowReport=!0,this.enableResume=!0,this.enableRaceRegion=!0,this.getSignature=e.getSignature,void 0!==e.allowReport&&(this.allowReport=e.allowReport),void 0!==e.enableResume&&(this.enableResume=e.enableResume),this.appId=e.appId,this.reportId=e.reportId,!1===e.enableRaceRegion&&(this.enableRaceRegion=e.enableRaceRegion)}return e.prototype.upload=function(e){var t=n({getSignature:this.getSignature,appId:this.appId,reportId:this.reportId,enableResume:this.enableResume,enableRaceRegion:this.enableRaceRegion},e),r=new o.default(t);return this.allowReport&&this.initReporter(r),r.start(),r},e.prototype.initReporter=function(e){new i.VodReporter(e)},e}();t.default=a},119:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)},a=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{c(n.next(e))}catch(e){i(e)}}function s(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))},s=this&&this.__generator||function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.UploaderEvent=t.vodAxios=void 0;var c=r(810),u=r(996),l=r(729),d=r(669),p=r(882),f=r(376),h=r(171);t.vodAxios=d.default.create();var m,g=["vod-upload-accelerate2","vod-upload-accelerate","vod-upload-proxy","vod-quic"],y=["vod-upload-accelerate2","vod-upload-proxy","vod-quic"],v=2;t.vodAxios.interceptors.response.use((function(e){return e}),(function(e){return isNaN(e.code)&&(e.code=500),Promise.reject(e)})),function(e){e.video_progress="video_progress",e.media_progress="media_progress",e.video_upload="video_upload",e.media_upload="media_upload",e.cover_progress="cover_progress",e.cover_upload="cover_upload"}(m||(t.UploaderEvent=m={}));var b=function(e){function r(t){var r=e.call(this)||this;return r.sessionName="",r.vodSessionKey="",r.appId=0,r.reqKey=h(),r.reportId="",r.enableResume=!0,r.enableRaceRegion=!0,r.applyRequestTimeout=5e3,r.applyRequestRetryCount=3,r.commitRequestTimeout=5e3,r.commitRequestRetryCount=3,r.retryDelay=1e3,r.targetAccelerate=!1,r.validateInitParams(t),r.videoFile=t.mediaFile||t.videoFile,r.getSignature=t.getSignature,r.cosStrategy=r.getCosStrategy(t),r.enableResume=t.enableResume,r.videoName=t.mediaName||t.videoName,r.coverFile=t.coverFile,r.fileId=t.fileId,r.applyRequestTimeout=t.applyRequestTimeout||r.applyRequestTimeout,r.commitRequestTimeout=t.commitRequestTimeout||r.commitRequestTimeout,r.retryDelay=t.retryDelay||r.retryDelay,r.appId=t.appId||r.appId,r.reportId=t.reportId||r.reportId,r.enableRaceRegion=t.enableRaceRegion,r.cosAuthTime=0,r.genFileInfo(),r}return o(r,e),r.prototype.getCosStrategy=function(e){var t={FileParallelLimit:e.fileParallelLimit,ChunkParallelLimit:e.chunkParallelLimit||6,ChunkRetryTimes:e.chunkRetryTimes||5,ChunkSize:e.chunkSize||8388608,SliceSize:e.sliceSize,CopyChunkParallelLimit:e.copyChunkParallelLimit,CopyChunkSize:e.copyChunkSize,CopySliceSize:e.copySliceSize,ProgressInterval:e.progressInterval,DynamicAccelerate:e.dynamicAccelerate};return Object.keys(t).filter((function(e){return void 0!==t[e]})).reduce((function(e,r){var n;return i(i({},e),((n={})[r]=t[r],n))}),{})},r.prototype.setStorage=function(e,t){if(e){var r="webugc_"+c(e);try{localStorage.setItem(r,t)}catch(e){}}},r.prototype.getStorage=function(e){if(e){var t="webugc_"+c(e),r=null;try{r=localStorage.getItem(t)}catch(e){}return r}},r.prototype.delStorage=function(e){if(e){var t="webugc_"+c(e);try{localStorage.removeItem(t)}catch(e){}}},r.prototype.validateInitParams=function(e){if(!p.default.isFunction(e.getSignature))throw new Error("getSignature must be a function");if(e.videoFile&&!p.default.isFile(e.videoFile))throw new Error("videoFile must be a File")},r.prototype.genFileInfo=function(){var e=this.videoFile;if(e){var t=e.name.lastIndexOf("."),r="";if(this.videoName){if(!p.default.isString(this.videoName))throw new Error("mediaName must be a string");if(/[:*?<>\"\\/|]/g.test(this.videoName))throw new Error('Cant use these chars in filename: \\ / : * ? " < > |');r=this.videoName}else r=e.name.substring(0,t);this.videoInfo={name:r,type:e.name.substring(t+1).toLowerCase(),size:e.size},this.sessionName+="".concat(e.name,"_").concat(e.size,";")}var n=this.coverFile;if(n){var o=n.name,i=o.lastIndexOf(".");this.coverInfo={name:o.substring(0,i),type:o.substring(i+1).toLowerCase(),size:n.size},this.sessionName+="".concat(n.name,"_").concat(n.size,";")}},r.prototype.requestRegion=function(e){return a(this,void 0,void 0,(function(){var r,n,o,i,a,c;return s(this,(function(s){switch(s.label){case 0:return r=this,[4,this.getSignature()];case 1:n=s.sent(),o={signature:n},i=Date.now(),s.label=2;case 2:return s.trys.push([2,4,,5]),[4,t.vodAxios.post("https://vod2.qcloud.com/v3/index.php?Action=PrepareUploadUGC",o,{timeout:this.applyRequestTimeout,withCredentials:!1})];case 3:return a=s.sent(),[3,5];case 4:return s.sent(),[2,e()];case 5:return 0===(c=a.data).code?(r.appId=r.appId||c.data.appId,!c.data.cosRegionList||c.data.cosRegionList&&c.data.cosRegionList.length<=1?[2,e()]:[2,r.regionRace(c.data.cosRegionList,(function(t){return r.emit(f.VodReportEvent.report_prepare,{data:{region:t},requestStartTime:i}),e(t)}))]):[2,e()]}}))}))},r.prototype.regionRace=function(e,t){return a(this,void 0,void 0,(function(){var r=this;return s(this,(function(n){return[2,Promise.race(e.map((function(e){return r.raceRequest(e)}))).then((function(e){return t(e)}))]}))}))},r.prototype.raceRequest=function(e){return a(this,void 0,void 0,(function(){var r=this;return s(this,(function(n){return[2,new Promise((function(n,o){return a(r,void 0,void 0,(function(){return s(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,t.vodAxios.head("https://"+e.domain,{timeout:this.applyRequestTimeout,withCredentials:!1})];case 1:return r.sent(),[3,3];case 2:return r.sent(),n(e.region),[3,3];case 3:return n(e.region),[2]}}))}))}))]}))}))},r.prototype.applyUploadUGC=function(e,n){return void 0===e&&(e=0),a(this,void 0,void 0,(function(){function o(t){return a(this,void 0,void 0,(function(){return s(this,(function(n){switch(n.label){case 0:if(500===t.code&&(r.host=r.host===p.HOST.MAIN?p.HOST.BACKUP:p.HOST.MAIN),i.emit(f.VodReportEvent.report_apply,{err:t,requestStartTime:m}),i.delStorage(i.sessionName),i.applyRequestRetryCount==e){if(t)throw t;throw new Error("apply upload failed")}return[4,p.default.delay(i.retryDelay)];case 1:return n.sent(),[2,i.applyUploadUGC(e+1)]}}))}))}var i,c,u,l,d,h,m,y,v,b,C,S;return s(this,(function(e){switch(e.label){case 0:return i=this,[4,this.getSignature()];case 1:if(c=e.sent(),l=this.videoInfo,d=this.coverInfo,h=this.vodSessionKey||this.enableResume&&this.getStorage(this.sessionName))u={signature:c,vodSessionKey:h};else if(l)u={signature:c,videoName:l.name,videoType:l.type,videoSize:l.size},d&&(u.coverName=d.name,u.coverType=d.type,u.coverSize=d.size);else{if(!this.fileId||!d)throw"Wrong params, please check and try again";u={signature:c,fileId:this.fileId,coverName:d.name,coverType:d.type,coverSize:d.size}}u.uploadFromWeb=!0,m=new Date,n&&(u.storageRegion=n),e.label=2;case 2:return e.trys.push([2,4,,5]),[4,t.vodAxios.post("https://".concat(r.host,"/v3/index.php?Action=ApplyUploadUGC"),u,{timeout:this.applyRequestTimeout,withCredentials:!1})];case 3:return y=e.sent(),[3,5];case 4:return[2,o(e.sent())];case 5:return 0==(v=y.data).code?(b=v.data,C=b.vodSessionKey,this.setStorage(this.sessionName,C),this.vodSessionKey=C,this.appId=b.appId,this.targetAccelerate=g.some((function(e){return b.StorageRegionV5===e})),this.emit(f.VodReportEvent.report_apply,{data:b,requestStartTime:m}),[2,b]):((S=new Error(v.message)).code=v.code,[2,o(S)])}}))}))},r.prototype.uploadToCos=function(e){return a(this,void 0,void 0,(function(){var t,r,n,o,c,l,d,h;return s(this,(function(g){switch(g.label){case 0:return t=this,r={bucket:e.storageBucket+"-"+e.storageAppId,region:e.storageRegionV5},this.targetAccelerate||(this.cosStrategy.DynamicAccelerate=!1),n=new u(Object.assign({ForceSignHost:!1,getAuthorization:function(r,n){return a(this,void 0,void 0,(function(){var r,o;return s(this,(function(i){switch(i.label){case 0:return r=p.default.getUnix(),o=.9*(e.tempCertificate.expiredTime-e.timestamp),0!==t.cosAuthTime?[3,1]:(t.cosAuthTime=r,[3,3]);case 1:return t.cosAuthTime&&r-t.cosAuthTime>=o?[4,t.applyUploadUGC()]:[3,3];case 2:e=i.sent(),t.cosAuthTime=p.default.getUnix(),i.label=3;case 3:return n({TmpSecretId:e.tempCertificate.secretId,TmpSecretKey:e.tempCertificate.secretKey,XCosSecurityToken:e.tempCertificate.token,StartTime:e.timestamp,ExpiredTime:e.tempCertificate.expiredTime}),[2]}}))}))}},this.cosStrategy)),this.cos=n,n.on("before-send",(function(e){var r=e.url;if(!t.targetAccelerate)return!1;if(!t.cosStrategy.DynamicAccelerate)return!1;-1===r.indexOf("https")&&(r=r.replace("http","https"));var n=r.match(/^(https?:\/\/([^\/]+)\/)([^\/]*\/?)(.*)$/),o=r.match(/cos\.(.+)\.myqcloud/);-1===r.indexOf("uploads")&&(e.url=r.replace(o[1],y[v]),e.headers["Vod-Forward-Cos"]=n[2]),v<=y.length-2?v++:v=0})),o=[],this.videoFile&&(c=i(i({},r),{file:this.videoFile,key:e.video.storagePath,onProgress:function(e){t.emit(m.video_progress,e),t.emit(m.media_progress,e)},onUpload:function(e){t.emit(m.video_upload,e),t.emit(m.media_upload,e)},onTaskReady:function(e){t.taskId=e}}),o.push(c)),this.coverFile&&(l=i(i({},r),{file:this.coverFile,key:e.cover.storagePath,onProgress:function(e){t.emit(m.cover_progress,e)},onUpload:function(e){t.emit(m.cover_upload,e)},onTaskReady:p.default.noop}),o.push(l)),d=new Date,h=o.map((function(e){return new Promise((function(r,o){n.sliceUploadFile({Bucket:e.bucket,Region:e.region,Key:e.key,Body:e.file,onTaskReady:e.onTaskReady,onProgress:e.onProgress},(function(n,i){return e.file===t.videoFile&&t.emit(f.VodReportEvent.report_cos_upload,{err:n,requestStartTime:d}),n?(t.delStorage(t.sessionName),'{"error":"error","headers":{}}'===JSON.stringify(n)?o(new Error("cors error")):void o(n)):(e.onUpload(i),r())}))}))})),[4,Promise.all(h)];case 1:return[2,g.sent()]}}))}))},r.prototype.commitUploadUGC=function(e){return void 0===e&&(e=0),a(this,void 0,void 0,(function(){function n(t){return a(this,void 0,void 0,(function(){return s(this,(function(n){switch(n.label){case 0:if(500===t.code&&(r.host=r.host===p.HOST.MAIN?p.HOST.BACKUP:p.HOST.MAIN),o.emit(f.VodReportEvent.report_commit,{err:t,requestStartTime:u}),o.commitRequestRetryCount==e){if(t)throw t;throw new Error("commit upload failed")}return[4,p.default.delay(o.retryDelay)];case 1:return n.sent(),[2,o.commitUploadUGC(e+1)]}}))}))}var o,i,c,u,l,d,h;return s(this,(function(e){switch(e.label){case 0:return o=this,[4,this.getSignature()];case 1:i=e.sent(),this.delStorage(this.sessionName),c=this.vodSessionKey,u=new Date,e.label=2;case 2:return e.trys.push([2,4,,5]),[4,t.vodAxios.post("https://".concat(r.host,"/v3/index.php?Action=CommitUploadUGC"),{signature:i,vodSessionKey:c},{timeout:this.commitRequestTimeout,withCredentials:!1})];case 3:return l=e.sent(),[3,5];case 4:return[2,n(e.sent())];case 5:return 0==(d=l.data).code?(this.emit(f.VodReportEvent.report_commit,{data:d.data,requestStartTime:u}),[2,d.data]):((h=new Error(d.message)).code=d.code,[2,n(h)])}}))}))},r.prototype.start=function(){var e=this,t=new Date;this.donePromise=this._start().then((function(r){return e.emit(f.VodReportEvent.report_done,{err:{code:0},requestStartTime:t}),r})).catch((function(r){throw e.emit(f.VodReportEvent.report_done,{err:{code:r&&r.code||p.default.CLIENT_ERROR_CODE.UPLOAD_FAIL},requestStartTime:t}),r}))},r.prototype._start=function(){return a(this,void 0,void 0,(function(){var e,t=this;return s(this,(function(r){switch(r.label){case 0:return e=function(e){return a(t,void 0,void 0,(function(){var t;return s(this,(function(r){switch(r.label){case 0:return[4,this.applyUploadUGC(null,e)];case 1:return t=r.sent(),[4,this.uploadToCos(t)];case 2:return r.sent(),[4,this.commitUploadUGC()];case 3:return[2,r.sent()]}}))}))},this.enableRaceRegion?[4,this.requestRegion(e)]:[3,2];case 1:case 3:return[2,r.sent()];case 2:return[4,e()]}}))}))},r.prototype.done=function(){return this.donePromise},r.prototype.cancel=function(){this.cos.cancelTask(this.taskId)},r.host=p.HOST.MAIN,r}(l.EventEmitter);t.default=b},882:(e,t)=>{"use strict";var r,n;Object.defineProperty(t,"__esModule",{value:!0}),t.HOST=void 0,function(e){e[e.UPLOAD_FAIL=1]="UPLOAD_FAIL"}(r||(r={})),function(e){e.MAIN="vod2.qcloud.com",e.BACKUP="vod2.dnsv1.com"}(n||(t.HOST=n={})),t.default={isFile:function(e){return"[object File]"==Object.prototype.toString.call(e)},isFunction:function(e){return"function"==typeof e},isString:function(e){return"string"==typeof e},noop:function(){},delay:function(e){return new Promise((function(t){setTimeout((function(){t()}),e)}))},getUnix:function(){return Math.floor(Date.now()/1e3)},isTest:!1,isDev:!1,CLIENT_ERROR_CODE:r}},376:function(e,t,r){"use strict";var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.VodReporter=t.VodReportEvent=void 0;var o,i,a=r(119),s=r(147),c=r(882);!function(e){e.report_prepare="report_prepare",e.report_apply="report_apply",e.report_cos_upload="report_cos_upload",e.report_commit="report_commit",e.report_done="report_done"}(o||(t.VodReportEvent=o={})),function(e){e[e.apply=10001]="apply",e[e.cos_upload=20001]="cos_upload",e[e.commit=10002]="commit",e[e.done=40001]="done"}(i||(i={}));var u=function(){function e(e,t){this.baseReportData={version:s.version,platform:3e3,device:navigator.userAgent},this.reportUrl="https://vodreport.qcloud.com/ugcupload_new",this.uploader=e,this.options=t,this.init()}return e.prototype.init=function(){this.uploader.on(o.report_apply,this.onApply.bind(this)),this.uploader.on(o.report_cos_upload,this.onCosUpload.bind(this)),this.uploader.on(o.report_commit,this.onCommit.bind(this)),this.uploader.on(o.report_done,this.onDone.bind(this))},e.prototype.onApply=function(e){try{var t=this.uploader;if(!t.videoFile)return;Object.assign(this.baseReportData,{appId:t.appId,fileSize:t.videoFile.size,fileName:t.videoFile.name,fileType:t.videoFile.type,vodSessionKey:t.vodSessionKey,reqKey:t.reqKey,reportId:t.reportId});var r={reqType:i.apply,errCode:0,vodErrCode:0,errMsg:"",reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};e.err&&(r.errCode=1,r.vodErrCode=e.err.code,r.errMsg=e.err.message),e.data&&(this.baseReportData.cosRegion=e.data.storageRegionV5),this.report(r)}catch(e){if(console.error("onApply",e),c.default.isTest)throw e}},e.prototype.onCosUpload=function(e){console.log("reportObj",e),console.log("timecost",Number(new Date)-Number(e.requestStartTime));try{var t={reqType:i.cos_upload,errCode:0,cosErrCode:"",errMsg:"",reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};e.err&&(t.errCode=1,t.cosErrCode=e.err.error?e.err.error.Code:e.err,e.err&&"error"===e.err.error&&(t.cosErrCode="cors error"),t.errMsg=JSON.stringify(e.err)),this.report(t)}catch(e){if(console.error("onCosUpload",e),c.default.isTest)throw e}},e.prototype.onCommit=function(e){try{var t={reqType:i.commit,errCode:0,vodErrCode:0,errMsg:"",reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};e.err&&(t.errCode=1,t.vodErrCode=e.err.code,t.errMsg=e.err.message),e.data&&(this.baseReportData.fileId=e.data.fileId),this.report(t)}catch(e){if(console.error("onCommit",e),c.default.isTest)throw e}},e.prototype.onDone=function(e){try{var t={reqType:i.done,errCode:e.err&&e.err.code,reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};this.report(t)}catch(e){if(console.error("onDone",e),c.default.isTest)throw e}},e.prototype.report=function(e){e=n(n({},this.baseReportData),e),this.send(e)},e.prototype.send=function(e){c.default.isDev||c.default.isTest?console.log("send reportData",e):a.vodAxios.post(this.reportUrl,e,{withCredentials:!1})},e}();t.VodReporter=u},75:e=>{for(var t=[],r=0;r<256;++r)t[r]=(r+256).toString(16).substr(1);e.exports=function(e,r){var n=r||0,o=t;return[o[e[n++]],o[e[n++]],o[e[n++]],o[e[n++]],"-",o[e[n++]],o[e[n++]],"-",o[e[n++]],o[e[n++]],"-",o[e[n++]],o[e[n++]],"-",o[e[n++]],o[e[n++]],o[e[n++]],o[e[n++]],o[e[n++]],o[e[n++]]].join("")}},217:e=>{var t="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto);if(t){var r=new Uint8Array(16);e.exports=function(){return t(r),r}}else{var n=new Array(16);e.exports=function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),n[t]=e>>>((3&t)<<3)&255;return n}}},171:(e,t,r)=>{var n=r(217),o=r(75);e.exports=function(e,t,r){var i=t&&r||0;"string"==typeof e&&(t="binary"===e?new Array(16):null,e=null);var a=(e=e||{}).random||(e.rng||n)();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t)for(var s=0;s<16;++s)t[i+s]=a[s];return t||o(a)}},996:function(module){var t;t=function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/dist/",r(r.s=15)}([function(e,t){function r(t){return e.exports=r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,r(t)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,r){"use strict";(function(t){var n=r(0),o=r(17),i=r(20),a=r(21),s=r(26),c=r(8);function u(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}function l(e,t){var r=[];for(var n in e)e.hasOwnProperty(n)&&r.push(t?u(n).toLowerCase():n);return r.sort((function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1}))}var d,p=["cache-control","content-disposition","content-encoding","content-length","content-md5","expect","expires","host","if-match","if-modified-since","if-none-match","if-unmodified-since","origin","range","transfer-encoding"],f=function(e,t,r){var n=t/8,o=e.slice(r,r+n);return new Uint8Array(o).reverse(),new{8:Uint8Array,16:Uint16Array,32:Uint32Array}[t](o)[0]},h=function(e,t,r,n){var o=e.slice(t,r),i="";return new Uint8Array(o).forEach((function(e){i+=String.fromCharCode(e)})),n&&(i=decodeURIComponent(escape(i))),i},m=function(){},g=function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&void 0!==e[r]&&null!==e[r]&&(t[r]=e[r]);return t},y=(d=function(e,t){e=e.split("."),t=t.split(".");for(var r=0;r<t.length;r++)if(e[r]!==t[r])return parseInt(e[r])>parseInt(t[r])?1:-1;return 0},function(e){if(!e)return!1;var t=(e.match(/Chrome\/([.\d]+)/)||[])[1],r=(e.match(/QBCore\/([.\d]+)/)||[])[1],n=(e.match(/QQBrowser\/([.\d]+)/)||[])[1];return t&&d(t,"53.0.2785.116")<0&&r&&d(r,"3.53.991.400")<0&&n&&d(n,"9.0.2524.400")<=0||!1}("undefined"!=typeof navigator&&navigator.userAgent)),v=1048576;function b(e){return w(e,(function(e){return"object"===n(e)&&null!==e?b(e):e}))}function C(e,t){return k(t,(function(r,n){e[n]=t[n]})),e}function S(e){return e instanceof Array}function k(e,t){for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)}function w(e,t){var r=S(e)?[]:{};for(var n in e)e.hasOwnProperty(n)&&(r[n]=t(e[n],n));return r}var T=function(e,t){var r=t.Bucket,n=t.Region,o=t.Key,i=this.options.Domain,a=!i||"string"==typeof i&&i.indexOf("{Bucket}")>-1,s=!i||"string"==typeof i&&i.indexOf("{Region}")>-1;if(e.indexOf("Bucket")>-1||"deleteMultipleObject"===e||"multipartList"===e||"listObjectVersions"===e){if(a&&!r)return"Bucket";if(s&&!n)return"Region"}else if(e.indexOf("Object")>-1||e.indexOf("multipart")>-1||"sliceUploadFile"===e||"abortUploadTask"===e){if(a&&!r)return"Bucket";if(s&&!n)return"Region";if(!o)return"Key"}return!1},R=function(e,t){if(t=C({},t),"getAuth"!==e&&"getV4Auth"!==e&&"getObjectUrl"!==e){var r=t.Headers||{};t&&"object"===n(t)&&(function(){for(var e in t)t.hasOwnProperty(e)&&e.indexOf("x-cos-")>-1&&(r[e]=t[e])}(),A.each({"x-cos-mfa":"MFA","Content-MD5":"ContentMD5","Content-Length":"ContentLength","Content-Type":"ContentType",Expect:"Expect",Expires:"Expires","Cache-Control":"CacheControl","Content-Disposition":"ContentDisposition","Content-Encoding":"ContentEncoding",Range:"Range","If-Modified-Since":"IfModifiedSince","If-Unmodified-Since":"IfUnmodifiedSince","If-Match":"IfMatch","If-None-Match":"IfNoneMatch","x-cos-copy-source":"CopySource","x-cos-copy-source-Range":"CopySourceRange","x-cos-metadata-directive":"MetadataDirective","x-cos-copy-source-If-Modified-Since":"CopySourceIfModifiedSince","x-cos-copy-source-If-Unmodified-Since":"CopySourceIfUnmodifiedSince","x-cos-copy-source-If-Match":"CopySourceIfMatch","x-cos-copy-source-If-None-Match":"CopySourceIfNoneMatch","x-cos-acl":"ACL","x-cos-grant-read":"GrantRead","x-cos-grant-write":"GrantWrite","x-cos-grant-full-control":"GrantFullControl","x-cos-grant-read-acp":"GrantReadAcp","x-cos-grant-write-acp":"GrantWriteAcp","x-cos-storage-class":"StorageClass","x-cos-traffic-limit":"TrafficLimit","x-cos-mime-limit":"MimeLimit","x-cos-server-side-encryption-customer-algorithm":"SSECustomerAlgorithm","x-cos-server-side-encryption-customer-key":"SSECustomerKey","x-cos-server-side-encryption-customer-key-MD5":"SSECustomerKeyMD5","x-cos-server-side-encryption":"ServerSideEncryption","x-cos-server-side-encryption-cos-kms-key-id":"SSEKMSKeyId","x-cos-server-side-encryption-context":"SSEContext","Pic-Operations":"PicOperations"},(function(e,n){void 0!==t[e]&&(r[n]=t[e])})),t.Headers=g(r))}return t},_=function(e){return Date.now()+(e||0)},E="object"===("undefined"==typeof navigator?"undefined":n(navigator))&&!!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),x="object"===("undefined"==typeof navigator?"undefined":n(navigator))&&/\sQQ/i.test(navigator.userAgent),A={noop:m,formatParams:R,apiWrapper:function(e,t){return function(r,n){var o,i=this;if("function"==typeof r&&(n=r,r={}),r=R(e,r),i.options.EnableTracker)if("sliceUploadFile"===r.calledBySdk)o=r.tracker&&r.tracker.generateSubTracker({apiName:e});else if(["uploadFile","uploadFiles"].includes(e))o=null;else{var a=-1;r.Body&&(a="string"==typeof r.Body?r.Body.length:r.Body.size||r.Body.byteLength||-1),o=new c({bucket:r.Bucket,region:r.Region,apiName:e,fileKey:r.Key,fileSize:a,deepTracker:i.options.DeepTracker,customId:i.options.CustomId,delay:i.options.TrackerDelay})}r.tracker=o;var s=function(e){return e&&e.headers&&(e.headers["x-cos-request-id"]&&(e.RequestId=e.headers["x-cos-request-id"]),e.headers["x-ci-request-id"]&&(e.RequestId=e.headers["x-ci-request-id"]),e.headers["x-cos-version-id"]&&(e.VersionId=e.headers["x-cos-version-id"]),e.headers["x-cos-delete-marker"]&&(e.DeleteMarker=e.headers["x-cos-delete-marker"])),e},u=function(e,t){o&&o.formatResult(e,t),n&&n(s(e),s(t))},l=function(){if("getService"!==e&&"abortUploadTask"!==e){var t=T.call(i,e,r);if(t)return"missing param "+t;if(r.Region){if(i.options.CompatibilityMode){if(!/^([a-z\d-.]+)$/.test(r.Region))return"Region format error."}else{if(r.Region.indexOf("cos.")>-1)return'param Region should not be start with "cos."';if(!/^([a-z\d-]+)$/.test(r.Region))return"Region format error."}!i.options.CompatibilityMode&&-1===r.Region.indexOf("-")&&"yfb"!==r.Region&&"default"!==r.Region&&r.Region}if(r.Bucket){if(!/^([a-z\d-]+)-(\d+)$/.test(r.Bucket))if(r.AppId)r.Bucket=r.Bucket+"-"+r.AppId;else{if(!i.options.AppId)return'Bucket should format as "test-1250000000".';r.Bucket=r.Bucket+"-"+i.options.AppId}r.AppId&&delete r.AppId}!i.options.UseRawKey&&r.Key&&"/"===r.Key.substr(0,1)&&(r.Key=r.Key.substr(1))}}(),d=["getAuth","getObjectUrl"].includes(e);if("function"==typeof Promise&&!d&&!n)return new Promise((function(e,o){if(n=function(t,r){t?o(t):e(r)},l)return u(A.error(new Error(l)));t.call(i,r,u)}));if(l)return u(A.error(new Error(l)));var p=t.call(i,r,u);return d?p:void 0}},xml2json:a,json2xml:s,md5:o,clearKey:g,fileSlice:function(e,t,r,n,o){var i;if(e.slice?i=e.slice(t,r):e.mozSlice?i=e.mozSlice(t,r):e.webkitSlice&&(i=e.webkitSlice(t,r)),n&&y){var a=new FileReader;a.onload=function(e){i=null,o(new Blob([a.result]))},a.readAsArrayBuffer(i)}else o(i)},getBodyMd5:function(e,t,r,n){r=r||m,e?"string"==typeof t?r(A.md5(t,!0)):Blob&&t instanceof Blob?A.getFileMd5(t,(function(e,t){r(t)}),n):r():r()},getFileMd5:function(e,t,r){var n=e.size,i=0,a=o.getCtx();!function o(s){if(s>=n){var c=a.digest("hex");t(null,c)}else{var u=Math.min(n,s+v);A.fileSlice(e,s,u,!1,(function(e){!function(e,t){var r,n=new FileReader;FileReader.prototype.readAsBinaryString?(r=FileReader.prototype.readAsBinaryString,n.onload=function(){t(this.result)}):FileReader.prototype.readAsArrayBuffer&&(r=function(e){var r="",n=new FileReader;n.onload=function(e){for(var o=new Uint8Array(n.result),i=o.byteLength,a=0;a<i;a++)r+=String.fromCharCode(o[a]);t(r)},n.readAsArrayBuffer(e)}),r.call(n,e)}(e,(function(t){e=null,a=a.update(t,!0),i+=t.length,t=null,r&&r({loaded:i,total:n,percent:Math.round(i/n*1e4)/1e4}),o(s+v)}))}))}}(0)},binaryBase64:function(e){var t,r,n,o="";for(t=0,r=e.length/2;t<r;t++)n=parseInt(e[2*t]+e[2*t+1],16),o+=String.fromCharCode(n);return btoa(o)},extend:C,isArray:S,isInArray:function(e,t){for(var r=!1,n=0;n<e.length;n++)if(t===e[n]){r=!0;break}return r},makeArray:function(e){return S(e)?e:[e]},each:k,map:w,filter:function(e,t){var r=S(e),n=r?[]:{};for(var o in e)e.hasOwnProperty(o)&&t(e[o],o)&&(r?n.push(e[o]):n[o]=e[o]);return n},clone:b,attr:function(e,t,r){return e&&t in e?e[t]:r},uuid:function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},camSafeUrlEncode:u,throttleOnProgress:function(e,t){var r,n,o=this,i=0,a=0,s=Date.now();function c(){if(n=0,t&&"function"==typeof t){r=Date.now();var o,c=Math.max(0,Math.round((a-i)/((r-s)/1e3)*100)/100)||0;o=0===a&&0===e?1:Math.floor(a/e*100)/100||0,s=r,i=a;try{t({loaded:a,total:e,speed:c,percent:o})}catch(e){}}}return function(t,r){if(t&&(a=t.loaded,e=t.total),r)clearTimeout(n),c();else{if(n)return;n=setTimeout(c,o.options.ProgressInterval)}}},getFileSize:function(e,t,r){var n;"string"==typeof t.Body?t.Body=new Blob([t.Body],{type:"text/plain"}):t.Body instanceof ArrayBuffer&&(t.Body=new Blob([t.Body])),t.Body&&(t.Body instanceof Blob||"[object File]"===t.Body.toString()||"[object Blob]"===t.Body.toString())?(n=t.Body.size,t.ContentLength=n,r(null,n)):r(A.error(new Error("params body format error, Only allow File|Blob|String.")))},getSkewTime:_,error:function(e,t){var r=e;return e.message=e.message||null,"string"==typeof t?(e.error=t,e.message=t):"object"===n(t)&&null!==t&&(C(e,t),(t.code||t.name)&&(e.code=t.code||t.name),t.message&&(e.message=t.message),t.stack&&(e.stack=t.stack)),"function"==typeof Object.defineProperty&&(Object.defineProperty(e,"name",{writable:!0,enumerable:!1}),Object.defineProperty(e,"message",{enumerable:!0})),e.name=t&&t.name||e.name||e.code||"Error",e.code||(e.code=e.name),e.error||(e.error=b(r)),e},obj2str:function(e,t){var r,n,o,i=[],a=l(e);for(r=0;r<a.length;r++)o=void 0===e[n=a[r]]||null===e[n]?"":""+e[n],n=t?u(n).toLowerCase():u(n),o=u(o)||"",i.push(n+"="+o);return i.join("&")},getAuth:function(e){var t,r=(e=e||{}).SecretId,n=e.SecretKey,o=e.KeyTime,a=(e.method||e.Method||"get").toLowerCase(),s=b(e.Query||e.params||{}),c=function(e){var t={};for(var r in e){var n=r.toLowerCase();(n.indexOf("x-cos-")>-1||p.indexOf(n)>-1)&&(t[r]=e[r])}return t}(b(e.Headers||e.headers||{})),u=e.Key||"";e.UseRawKey?t=e.Pathname||e.pathname||"/"+u:0!==(t=e.Pathname||e.pathname||u).indexOf("/")&&(t="/"+t);var d=!1!==e.ForceSignHost;if(!c.Host&&!c.host&&e.Bucket&&e.Region&&d&&(c.Host=e.Bucket+".cos."+e.Region+".myqcloud.com"),!r)throw new Error("missing param SecretId");if(!n)throw new Error("missing param SecretKey");var f=Math.round(_(e.SystemClockOffset)/1e3)-1,h=f,m=e.Expires||e.expires;h+=void 0===m?900:1*m||0;var g=r,y=o||f+";"+h,v=o||f+";"+h,C=l(c,!0).join(";").toLowerCase(),S=l(s,!0).join(";").toLowerCase(),k=i.HmacSHA1(v,n).toString(),w=[a,t,A.obj2str(s,!0),A.obj2str(c,!0),""].join("\n"),T=["sha1",y,i.SHA1(w).toString(),""].join("\n");return["q-sign-algorithm=sha1","q-ak="+g,"q-sign-time="+y,"q-key-time="+v,"q-header-list="+C,"q-url-param-list="+S,"q-signature="+i.HmacSHA1(T,k).toString()].join("&")},parseSelectPayload:function(e){for(var t={},r=h(e),n={records:[]};e.byteLength;){var o,i=f(e,32,0),a=f(e,32,4),s=i-a-16,c=0;for(e=e.slice(12);c<a;){var u=f(e,8,c),l=h(e,c+1,c+1+u),d=f(e,16,c+u+2),p=h(e,c+u+4,c+u+4+d);t[l]=p,c+=u+4+d}if("Records"===t[":event-type"])o=h(e,c,c+s,!0),n.records.push(o);else if("Stats"===t[":event-type"])o=h(e,c,c+s,!0),n.stats=A.xml2json(o).Stats;else if("error"===t[":event-type"]){var m=t[":error-code"],g=t[":error-message"],y=new Error(g);y.message=g,y.name=y.code=m,n.error=y}else["Progress","Continuation","End"].includes(t[":event-type"]);e=e.slice(c+s+4)}return{payload:n.records.join(""),body:r}},getSourceParams:function(e){var t=this.options.CopySourceParser;if(t)return t(e);var r=e.match(/^([^.]+-\d+)\.cos(v6|-cdc|-cdz|-internal)?\.([^.]+)\.((myqcloud\.com)|(tencentcos\.cn))\/(.+)$/);return r?{Bucket:r[1],Region:r[3],Key:r[7]}:null},isBrowser:!0,isNode:function(){return"object"!==("undefined"==typeof window?"undefined":n(window))&&"object"===(void 0===t?"undefined":n(t))&&!("object"===("undefined"==typeof globalThis?"undefined":n(globalThis))&&("DedicatedWorkerGlobalScope"===globalThis.constructor.name||globalThis.FileReaderSync))},isCIHost:function(e){return/^https?:\/\/([^/]+\.)?ci\.[^/]+/.test(e)},isIOS_QQ:E&&x};e.exports=A}).call(this,r(5))},function(e,t,r){"use strict";function n(e,t){return void 0===t&&(t=Object),t&&"function"==typeof t.freeze?t.freeze(e):e}var o=n({HTML:"text/html",isHTML:function(e){return e===o.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),i=n({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(e){return e===i.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});t.assign=function(e,t){if(null===e||"object"!=typeof e)throw new TypeError("target is not an object");for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},t.find=function(e,t,r){if(void 0===r&&(r=Array.prototype),e&&"function"==typeof r.find)return r.find.call(e,t);for(var n=0;n<e.length;n++)if(Object.prototype.hasOwnProperty.call(e,n)){var o=e[n];if(t.call(void 0,o,n,e))return o}},t.freeze=n,t.MIME_TYPE=o,t.NAMESPACE=i},function(e){e.exports=JSON.parse('{"name":"cos-js-sdk-v5","version":"1.4.16","description":"JavaScript SDK for [腾讯云对象存储](https://cloud.tencent.com/product/cos)","main":"dist/cos-js-sdk-v5.js","types":"index.d.ts","scripts":{"server":"node server/sts.js","dev":"cross-env NODE_ENV=development webpack -w --mode=development","build":"cross-env NODE_ENV=production webpack --mode=production","cos-auth.min.js":"uglifyjs ./demo/common/cos-auth.js -o ./demo/common/cos-auth.min.js -c -m","test":"jest --coverage"},"repository":{"type":"git","url":"git+https://github.com/tencentyun/cos-js-sdk-v5.git"},"keywords":[],"author":"carsonxu","license":"ISC","bugs":{"url":"https://github.com/tencentyun/cos-js-sdk-v5/issues"},"homepage":"https://github.com/tencentyun/cos-js-sdk-v5#readme","dependencies":{"@xmldom/xmldom":"^0.8.6"},"devDependencies":{"@babel/core":"7.17.9","@babel/plugin-transform-runtime":"7.18.10","@babel/preset-env":"7.16.11","babel-loader":"8.2.5","body-parser":"^1.18.3","cross-env":"^5.2.0","express":"^4.16.4","jest":"^29.3.1","jest-environment-jsdom":"^29.3.1","qcloud-cos-sts":"^3.0.2","request":"^2.87.0","terser-webpack-plugin":"4.2.3","uglifyjs":"^2.4.11","webpack":"4.46.0","webpack-cli":"4.10.0"}}')},function(e,t){var r=function(e){var t={},r=function(e){return!t[e]&&(t[e]=[]),t[e]};e.on=function(e,t){r(e).push(t)},e.off=function(e,t){for(var n=r(e),o=n.length-1;o>=0;o--)t===n[o]&&n.splice(o,1)},e.emit=function(e,t){for(var n=r(e).map((function(e){return e})),o=0;o<n.length;o++)n[o](t)}};e.exports.init=r,e.exports.EventProxy=function(){r(this)}},function(e,t){var r,n,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(r===setTimeout)return setTimeout(e,0);if((r===i||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:i}catch(e){r=i}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var c,u=[],l=!1,d=-1;function p(){l&&c&&(l=!1,c.length?u=c.concat(u):d=-1,u.length&&f())}function f(){if(!l){var e=s(p);l=!0;for(var t=u.length;t;){for(c=u,u=[];++d<t;)c&&c[d].run();d=-1,t=u.length}c=null,l=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function m(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];u.push(new h(e,t)),1!==u.length||l||s(f)},h.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=m,o.addListener=m,o.once=m,o.off=m,o.removeListener=m,o.removeAllListeners=m,o.emit=m,o.prependListener=m,o.prependOnceListener=m,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,r){var n=r(2),o=n.find,i=n.NAMESPACE;function a(e){return""!==e}function s(e,t){return e.hasOwnProperty(t)||(e[t]=!0),e}function c(e){if(!e)return[];var t=function(e){return e?e.split(/[\t\n\f\r ]+/).filter(a):[]}(e);return Object.keys(t.reduce(s,{}))}function u(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function l(e,t){var r=e.prototype;if(!(r instanceof t)){function n(){}n.prototype=t.prototype,u(r,n=new n),e.prototype=r=n}r.constructor!=e&&(r.constructor=e)}var d={},p=d.ELEMENT_NODE=1,f=d.ATTRIBUTE_NODE=2,h=d.TEXT_NODE=3,m=d.CDATA_SECTION_NODE=4,g=d.ENTITY_REFERENCE_NODE=5,y=d.ENTITY_NODE=6,v=d.PROCESSING_INSTRUCTION_NODE=7,b=d.COMMENT_NODE=8,C=d.DOCUMENT_NODE=9,S=d.DOCUMENT_TYPE_NODE=10,k=d.DOCUMENT_FRAGMENT_NODE=11,w=d.NOTATION_NODE=12,T={},R={},_=(T.INDEX_SIZE_ERR=(R[1]="Index size error",1),T.DOMSTRING_SIZE_ERR=(R[2]="DOMString size error",2),T.HIERARCHY_REQUEST_ERR=(R[3]="Hierarchy request error",3)),E=(T.WRONG_DOCUMENT_ERR=(R[4]="Wrong document",4),T.INVALID_CHARACTER_ERR=(R[5]="Invalid character",5),T.NO_DATA_ALLOWED_ERR=(R[6]="No data allowed",6),T.NO_MODIFICATION_ALLOWED_ERR=(R[7]="No modification allowed",7),T.NOT_FOUND_ERR=(R[8]="Not found",8)),x=(T.NOT_SUPPORTED_ERR=(R[9]="Not supported",9),T.INUSE_ATTRIBUTE_ERR=(R[10]="Attribute in use",10));function A(e,t){if(t instanceof Error)var r=t;else r=this,Error.call(this,R[e]),this.message=R[e],Error.captureStackTrace&&Error.captureStackTrace(this,A);return r.code=e,t&&(this.message=this.message+": "+t),r}function O(){}function I(e,t){this._node=e,this._refresh=t,B(this)}function B(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!=t){var r=e._refresh(e._node);ve(e,"length",r.length),u(r,e),e._inc=t}}function N(){}function P(e,t){for(var r=e.length;r--;)if(e[r]===t)return r}function D(e,t,r,n){if(n?t[P(t,n)]=r:t[t.length++]=r,e){r.ownerElement=e;var o=e.ownerDocument;o&&(n&&q(o,e,n),function(e,t,r){e&&e._inc++,r.namespaceURI===i.XMLNS&&(t._nsMap[r.prefix?r.localName:""]=r.value)}(o,e,r))}}function U(e,t,r){var n=P(t,r);if(!(n>=0))throw new A(E,new Error(e.tagName+"@"+r));for(var o=t.length-1;n<o;)t[n]=t[++n];if(t.length=o,e){var i=e.ownerDocument;i&&(q(i,e,r),r.ownerElement=null)}}function M(){}function H(){}function j(e){return("<"==e?"&lt;":">"==e&&"&gt;")||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function L(e,t){if(t(e))return!0;if(e=e.firstChild)do{if(L(e,t))return!0}while(e=e.nextSibling)}function F(){this.ownerDocument=this}function q(e,t,r,n){e&&e._inc++,r.namespaceURI===i.XMLNS&&delete t._nsMap[r.prefix?r.localName:""]}function K(e,t,r){if(e&&e._inc){e._inc++;var n=t.childNodes;if(r)n[n.length++]=r;else{for(var o=t.firstChild,i=0;o;)n[i++]=o,o=o.nextSibling;n.length=i,delete n[n.length]}}}function z(e,t){var r=t.previousSibling,n=t.nextSibling;return r?r.nextSibling=n:e.firstChild=n,n?n.previousSibling=r:e.lastChild=r,t.parentNode=null,t.previousSibling=null,t.nextSibling=null,K(e.ownerDocument,e),t}function V(e){return e&&e.nodeType===H.DOCUMENT_TYPE_NODE}function X(e){return e&&e.nodeType===H.ELEMENT_NODE}function G(e){return e&&e.nodeType===H.TEXT_NODE}function W(e,t){var r=e.childNodes||[];if(o(r,X)||V(t))return!1;var n=o(r,V);return!(t&&n&&r.indexOf(n)>r.indexOf(t))}function J(e,t){var r=e.childNodes||[];if(o(r,(function(e){return X(e)&&e!==t})))return!1;var n=o(r,V);return!(t&&n&&r.indexOf(n)>r.indexOf(t))}function Q(e,t,r){var n=e.childNodes||[],i=t.childNodes||[];if(t.nodeType===H.DOCUMENT_FRAGMENT_NODE){var a=i.filter(X);if(a.length>1||o(i,G))throw new A(_,"More than one element or text in fragment");if(1===a.length&&!W(e,r))throw new A(_,"Element in fragment can not be inserted before doctype")}if(X(t)&&!W(e,r))throw new A(_,"Only one element can be added and only after doctype");if(V(t)){if(o(n,V))throw new A(_,"Only one doctype is allowed");var s=o(n,X);if(r&&n.indexOf(s)<n.indexOf(r))throw new A(_,"Doctype can only be inserted before an element");if(!r&&s)throw new A(_,"Doctype can not be appended since element is present")}}function $(e,t,r){var n=e.childNodes||[],i=t.childNodes||[];if(t.nodeType===H.DOCUMENT_FRAGMENT_NODE){var a=i.filter(X);if(a.length>1||o(i,G))throw new A(_,"More than one element or text in fragment");if(1===a.length&&!J(e,r))throw new A(_,"Element in fragment can not be inserted before doctype")}if(X(t)&&!J(e,r))throw new A(_,"Only one element can be added and only after doctype");if(V(t)){if(o(n,(function(e){return V(e)&&e!==r})))throw new A(_,"Only one doctype is allowed");var s=o(n,X);if(r&&n.indexOf(s)<n.indexOf(r))throw new A(_,"Doctype can only be inserted before an element")}}function Y(e,t,r,n){(function(e,t,r){if(!function(e){return e&&(e.nodeType===H.DOCUMENT_NODE||e.nodeType===H.DOCUMENT_FRAGMENT_NODE||e.nodeType===H.ELEMENT_NODE)}(e))throw new A(_,"Unexpected parent node type "+e.nodeType);if(r&&r.parentNode!==e)throw new A(E,"child not in parent");if(!function(e){return e&&(X(e)||G(e)||V(e)||e.nodeType===H.DOCUMENT_FRAGMENT_NODE||e.nodeType===H.COMMENT_NODE||e.nodeType===H.PROCESSING_INSTRUCTION_NODE)}(t)||V(t)&&e.nodeType!==H.DOCUMENT_NODE)throw new A(_,"Unexpected node type "+t.nodeType+" for parent node type "+e.nodeType)})(e,t,r),e.nodeType===H.DOCUMENT_NODE&&(n||Q)(e,t,r);var o=t.parentNode;if(o&&o.removeChild(t),t.nodeType===k){var i=t.firstChild;if(null==i)return t;var a=t.lastChild}else i=a=t;var s=r?r.previousSibling:e.lastChild;i.previousSibling=s,a.nextSibling=r,s?s.nextSibling=i:e.firstChild=i,null==r?e.lastChild=a:r.previousSibling=a;do{i.parentNode=e}while(i!==a&&(i=i.nextSibling));return K(e.ownerDocument||e,e),t.nodeType==k&&(t.firstChild=t.lastChild=null),t}function Z(){this._nsMap={}}function ee(){}function te(){}function re(){}function ne(){}function oe(){}function ie(){}function ae(){}function se(){}function ce(){}function ue(){}function le(){}function de(){}function pe(e,t){var r=[],n=9==this.nodeType&&this.documentElement||this,o=n.prefix,i=n.namespaceURI;if(i&&null==o&&null==(o=n.lookupPrefix(i)))var a=[{namespace:i,prefix:null}];return me(this,r,e,t,a),r.join("")}function fe(e,t,r){var n=e.prefix||"",o=e.namespaceURI;if(!o)return!1;if("xml"===n&&o===i.XML||o===i.XMLNS)return!1;for(var a=r.length;a--;){var s=r[a];if(s.prefix===n)return s.namespace!==o}return!0}function he(e,t,r){e.push(" ",t,'="',r.replace(/[<>&"\t\n\r]/g,j),'"')}function me(e,t,r,n,o){if(o||(o=[]),n){if(!(e=n(e)))return;if("string"==typeof e)return void t.push(e)}switch(e.nodeType){case p:var a=e.attributes,s=a.length,c=e.firstChild,u=e.tagName,l=u;if(!(r=i.isHTML(e.namespaceURI)||r)&&!e.prefix&&e.namespaceURI){for(var d,y=0;y<a.length;y++)if("xmlns"===a.item(y).name){d=a.item(y).value;break}if(!d)for(var w=o.length-1;w>=0;w--)if(""===(T=o[w]).prefix&&T.namespace===e.namespaceURI){d=T.namespace;break}if(d!==e.namespaceURI)for(w=o.length-1;w>=0;w--){var T;if((T=o[w]).namespace===e.namespaceURI){T.prefix&&(l=T.prefix+":"+u);break}}}t.push("<",l);for(var R=0;R<s;R++)"xmlns"==(_=a.item(R)).prefix?o.push({prefix:_.localName,namespace:_.value}):"xmlns"==_.nodeName&&o.push({prefix:"",namespace:_.value});for(R=0;R<s;R++){var _,E,x;fe(_=a.item(R),0,o)&&(he(t,(E=_.prefix||"")?"xmlns:"+E:"xmlns",x=_.namespaceURI),o.push({prefix:E,namespace:x})),me(_,t,r,n,o)}if(u===l&&fe(e,0,o)&&(he(t,(E=e.prefix||"")?"xmlns:"+E:"xmlns",x=e.namespaceURI),o.push({prefix:E,namespace:x})),c||r&&!/^(?:meta|link|img|br|hr|input)$/i.test(u)){if(t.push(">"),r&&/^script$/i.test(u))for(;c;)c.data?t.push(c.data):me(c,t,r,n,o.slice()),c=c.nextSibling;else for(;c;)me(c,t,r,n,o.slice()),c=c.nextSibling;t.push("</",l,">")}else t.push("/>");return;case C:case k:for(c=e.firstChild;c;)me(c,t,r,n,o.slice()),c=c.nextSibling;return;case f:return he(t,e.name,e.value);case h:return t.push(e.data.replace(/[<&>]/g,j));case m:return t.push("<![CDATA[",e.data,"]]>");case b:return t.push("\x3c!--",e.data,"--\x3e");case S:var A=e.publicId,O=e.systemId;if(t.push("<!DOCTYPE ",e.name),A)t.push(" PUBLIC ",A),O&&"."!=O&&t.push(" ",O),t.push(">");else if(O&&"."!=O)t.push(" SYSTEM ",O,">");else{var I=e.internalSubset;I&&t.push(" [",I,"]"),t.push(">")}return;case v:return t.push("<?",e.target," ",e.data,"?>");case g:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function ge(e,t,r){var n;switch(t.nodeType){case p:(n=t.cloneNode(!1)).ownerDocument=e;case k:break;case f:r=!0}if(n||(n=t.cloneNode(!1)),n.ownerDocument=e,n.parentNode=null,r)for(var o=t.firstChild;o;)n.appendChild(ge(e,o,r)),o=o.nextSibling;return n}function ye(e,t,r){var n=new t.constructor;for(var o in t)if(Object.prototype.hasOwnProperty.call(t,o)){var i=t[o];"object"!=typeof i&&i!=n[o]&&(n[o]=i)}switch(t.childNodes&&(n.childNodes=new O),n.ownerDocument=e,n.nodeType){case p:var a=t.attributes,s=n.attributes=new N,c=a.length;s._ownerElement=n;for(var u=0;u<c;u++)n.setAttributeNode(ye(e,a.item(u),!0));break;case f:r=!0}if(r)for(var l=t.firstChild;l;)n.appendChild(ye(e,l,r)),l=l.nextSibling;return n}function ve(e,t,r){e[t]=r}T.INVALID_STATE_ERR=(R[11]="Invalid state",11),T.SYNTAX_ERR=(R[12]="Syntax error",12),T.INVALID_MODIFICATION_ERR=(R[13]="Invalid modification",13),T.NAMESPACE_ERR=(R[14]="Invalid namespace",14),T.INVALID_ACCESS_ERR=(R[15]="Invalid access",15),A.prototype=Error.prototype,u(T,A),O.prototype={length:0,item:function(e){return this[e]||null},toString:function(e,t){for(var r=[],n=0;n<this.length;n++)me(this[n],r,e,t);return r.join("")},filter:function(e){return Array.prototype.filter.call(this,e)},indexOf:function(e){return Array.prototype.indexOf.call(this,e)}},I.prototype.item=function(e){return B(this),this[e]},l(I,O),N.prototype={length:0,item:O.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var r=this[t];if(r.nodeName==e)return r}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new A(x);var r=this.getNamedItem(e.nodeName);return D(this._ownerElement,this,e,r),r},setNamedItemNS:function(e){var t,r=e.ownerElement;if(r&&r!=this._ownerElement)throw new A(x);return t=this.getNamedItemNS(e.namespaceURI,e.localName),D(this._ownerElement,this,e,t),t},removeNamedItem:function(e){var t=this.getNamedItem(e);return U(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var r=this.getNamedItemNS(e,t);return U(this._ownerElement,this,r),r},getNamedItemNS:function(e,t){for(var r=this.length;r--;){var n=this[r];if(n.localName==t&&n.namespaceURI==e)return n}return null}},M.prototype={hasFeature:function(e,t){return!0},createDocument:function(e,t,r){var n=new F;if(n.implementation=this,n.childNodes=new O,n.doctype=r||null,r&&n.appendChild(r),t){var o=n.createElementNS(e,t);n.appendChild(o)}return n},createDocumentType:function(e,t,r){var n=new ie;return n.name=e,n.nodeName=e,n.publicId=t||"",n.systemId=r||"",n}},H.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return Y(this,e,t)},replaceChild:function(e,t){Y(this,e,t,$),t&&this.removeChild(t)},removeChild:function(e){return z(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return ye(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==h&&e.nodeType==h?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(e){for(var t=this;t;){var r=t._nsMap;if(r)for(var n in r)if(Object.prototype.hasOwnProperty.call(r,n)&&r[n]===e)return n;t=t.nodeType==f?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var r=t._nsMap;if(r&&Object.prototype.hasOwnProperty.call(r,e))return r[e];t=t.nodeType==f?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},u(d,H),u(d,H.prototype),F.prototype={nodeName:"#document",nodeType:C,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType==k){for(var r=e.firstChild;r;){var n=r.nextSibling;this.insertBefore(r,t),r=n}return e}return Y(this,e,t),e.ownerDocument=this,null===this.documentElement&&e.nodeType===p&&(this.documentElement=e),e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),z(this,e)},replaceChild:function(e,t){Y(this,e,t,$),e.ownerDocument=this,t&&this.removeChild(t),X(e)&&(this.documentElement=e)},importNode:function(e,t){return ge(this,e,t)},getElementById:function(e){var t=null;return L(this.documentElement,(function(r){if(r.nodeType==p&&r.getAttribute("id")==e)return t=r,!0})),t},getElementsByClassName:function(e){var t=c(e);return new I(this,(function(r){var n=[];return t.length>0&&L(r.documentElement,(function(o){if(o!==r&&o.nodeType===p){var i=o.getAttribute("class");if(i){var a=e===i;if(!a){var s=c(i);a=t.every((u=s,function(e){return u&&-1!==u.indexOf(e)}))}a&&n.push(o)}}var u})),n}))},createElement:function(e){var t=new Z;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.localName=e,t.childNodes=new O,(t.attributes=new N)._ownerElement=t,t},createDocumentFragment:function(){var e=new ue;return e.ownerDocument=this,e.childNodes=new O,e},createTextNode:function(e){var t=new re;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new ne;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new oe;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var r=new le;return r.ownerDocument=this,r.tagName=r.target=e,r.nodeValue=r.data=t,r},createAttribute:function(e){var t=new ee;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new ce;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var r=new Z,n=t.split(":"),o=r.attributes=new N;return r.childNodes=new O,r.ownerDocument=this,r.nodeName=t,r.tagName=t,r.namespaceURI=e,2==n.length?(r.prefix=n[0],r.localName=n[1]):r.localName=t,o._ownerElement=r,r},createAttributeNS:function(e,t){var r=new ee,n=t.split(":");return r.ownerDocument=this,r.nodeName=t,r.name=t,r.namespaceURI=e,r.specified=!0,2==n.length?(r.prefix=n[0],r.localName=n[1]):r.localName=t,r}},l(F,H),Z.prototype={nodeType:p,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var t=this.getAttributeNode(e);return t&&t.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){var r=this.ownerDocument.createAttribute(e);r.value=r.nodeValue=""+t,this.setAttributeNode(r)},removeAttribute:function(e){var t=this.getAttributeNode(e);t&&this.removeAttributeNode(t)},appendChild:function(e){return e.nodeType===k?this.insertBefore(e,null):function(e,t){return t.parentNode&&t.parentNode.removeChild(t),t.parentNode=e,t.previousSibling=e.lastChild,t.nextSibling=null,t.previousSibling?t.previousSibling.nextSibling=t:e.firstChild=t,e.lastChild=t,K(e.ownerDocument,e,t),t}(this,e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){var r=this.getAttributeNodeNS(e,t);r&&this.removeAttributeNode(r)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){var r=this.getAttributeNodeNS(e,t);return r&&r.value||""},setAttributeNS:function(e,t,r){var n=this.ownerDocument.createAttributeNS(e,t);n.value=n.nodeValue=""+r,this.setAttributeNode(n)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(e){return new I(this,(function(t){var r=[];return L(t,(function(n){n===t||n.nodeType!=p||"*"!==e&&n.tagName!=e||r.push(n)})),r}))},getElementsByTagNameNS:function(e,t){return new I(this,(function(r){var n=[];return L(r,(function(o){o===r||o.nodeType!==p||"*"!==e&&o.namespaceURI!==e||"*"!==t&&o.localName!=t||n.push(o)})),n}))}},F.prototype.getElementsByTagName=Z.prototype.getElementsByTagName,F.prototype.getElementsByTagNameNS=Z.prototype.getElementsByTagNameNS,l(Z,H),ee.prototype.nodeType=f,l(ee,H),te.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw new Error(R[_])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,r){r=this.data.substring(0,e)+r+this.data.substring(e+t),this.nodeValue=this.data=r,this.length=r.length}},l(te,H),re.prototype={nodeName:"#text",nodeType:h,splitText:function(e){var t=this.data,r=t.substring(e);t=t.substring(0,e),this.data=this.nodeValue=t,this.length=t.length;var n=this.ownerDocument.createTextNode(r);return this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling),n}},l(re,te),ne.prototype={nodeName:"#comment",nodeType:b},l(ne,te),oe.prototype={nodeName:"#cdata-section",nodeType:m},l(oe,te),ie.prototype.nodeType=S,l(ie,H),ae.prototype.nodeType=w,l(ae,H),se.prototype.nodeType=y,l(se,H),ce.prototype.nodeType=g,l(ce,H),ue.prototype.nodeName="#document-fragment",ue.prototype.nodeType=k,l(ue,H),le.prototype.nodeType=v,l(le,H),de.prototype.serializeToString=function(e,t,r){return pe.call(e,t,r)},H.prototype.toString=pe;try{if(Object.defineProperty){function be(e){switch(e.nodeType){case p:case k:var t=[];for(e=e.firstChild;e;)7!==e.nodeType&&8!==e.nodeType&&t.push(be(e)),e=e.nextSibling;return t.join("");default:return e.nodeValue}}Object.defineProperty(I.prototype,"length",{get:function(){return B(this),this.$$length}}),Object.defineProperty(H.prototype,"textContent",{get:function(){return be(this)},set:function(e){switch(this.nodeType){case p:case k:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),ve=function(e,t,r){e["$$"+t]=r}}}catch(Ce){}t.DocumentType=ie,t.DOMException=A,t.DOMImplementation=M,t.Element=Z,t.Node=H,t.NodeList=O,t.XMLSerializer=de},function(e,t,r){var n=r(9),o=r(10),i=r(0),a=r(3),s=null,c=function(e){if(!s){var t=r(29);s=new t({appkey:"0AND0VEVB24UBGDU",versionCode:a.version,channelID:"js_sdk",openid:"openid",unionid:"unid",strictMode:!1,delay:e,sessionDuration:6e4})}return s},u=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},l=function(){if("object"===("undefined"==typeof navigator?"undefined":i(navigator))){var e=navigator.connection||navigator.mozConnection||navigator.webkitConnection;return(null==e?void 0:e.type)||(null==e?void 0:e.effectiveType)||"unknown"}return"unknown"},d=function(){return!("object"!==("undefined"==typeof navigator?"undefined":i(navigator))||!navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))},p={isMobile:d(),isBrowser:!d(),mobileOsType:"object"===("undefined"==typeof navigator?"undefined":i(navigator))&&navigator.userAgent.match(/(Android|Adr|Linux)/i)?"android":function(){return!("object"!==("undefined"==typeof navigator?"undefined":i(navigator))||!navigator.userAgent.match(/(iPhone|iPod|iPad|iOS)/i))}?"ios":"other_mobile",pcOsType:function(){if("object"!==("undefined"==typeof navigator?"undefined":i(navigator)))return"unknown os";var e=navigator.userAgent.toLowerCase(),t=/macintosh|mac os x/i.test(navigator.userAgent);return e.indexOf("win32")>=0||e.indexOf("wow32")>=0?"win32":e.indexOf("win64")>=0||e.indexOf("wow64")>=0?"win64":t?"mac":"unknown os"}()},f={deviceType:p.isMobile?"mobile":p.isBrowser?"browser":"unknown",devicePlatform:p.isMobile?p.mobileOsType:p.pcOsType,deviceName:function(){if("object"!==("undefined"==typeof navigator?"undefined":i(navigator)))return"unknown device";var e=navigator.userAgent.toLowerCase();if(e.includes("app/tencent_wemeet"))return"tencent_wemeet";if(e.indexOf("maxthon")>=0){var t=e.match(/maxthon\/([\d.]+)/),r=t&&t[1]||"";return"傲游浏览器 ".concat(r).trim()}if(e.indexOf("qqbrowser")>=0){var n=e.match(/qqbrowser\/([\d.]+)/),o=n&&n[1]||"";return"QQ浏览器 ".concat(o).trim()}if(e.indexOf("se 2.x")>=0)return"搜狗浏览器";if(e.indexOf("wxwork")>=0)return"微信内置浏览器";if(e.indexOf("msie")>=0){var a=e.match(/msie ([\d.]+)/),s=a&&a[1]||"";return"IE ".concat(s).trim()}if(e.indexOf("firefox")>=0){var c=e.match(/firefox\/([\d.]+)/),u=c&&c[1]||"";return"Firefox ".concat(u).trim()}if(e.indexOf("chrome")>=0){var l=e.match(/chrome\/([\d.]+)/),d=l&&l[1]||"";return"Chrome ".concat(d).trim()}if(e.indexOf("opera")>=0){var p=e.match(/opera.([\d.]+)/),f=p&&p[1]||"";return"Opera ".concat(f).trim()}if(e.indexOf("safari")>=0){var h=e.match(/version\/([\d.]+)/),m=h&&h[1]||"";return"Safari ".concat(m).trim()}if(e.indexOf("edge")>=0){var g=e.match(/edge\/([\d.]+)/),y=g&&g[1]||"";return"edge ".concat(y).trim()}return e.substr(0,200)}()},h=["multipartInit","multipartUpload","multipartComplete","multipartList","multipartListPart","multipartAbort"],m=["putObject","postObject","appendObject","sliceUploadFile","uploadFile","uploadFiles"].concat(h),g=["getObject"];function y(e){return e.replace(/([A-Z])/g,"_$1").toLowerCase()}var v=function(){"use strict";function e(t){n(this,e);var r=t.parent,o=t.traceId,i=t.bucket,s=t.region,l=t.apiName,d=t.fileKey,p=t.fileSize,h=t.accelerate,m=t.customId,g=t.delay,y=t.deepTracker,v=i&&i.substr(i.lastIndexOf("-")+1)||"";this.parent=r,this.deepTracker=y,this.delay=g,this.params={cossdkVersion:a.version,region:s,networkType:"",host:"",accelerate:h?"Y":"N",requestPath:d||"",size:p||-1,httpMd5:0,httpSign:0,httpFull:0,name:l||"",result:"",tookTime:0,errorNode:"",errorCode:"",errorMessage:"",errorRequestId:"",errorStatusCode:0,errorServiceName:"",tracePlatform:"cos-js-sdk-v5",traceId:o||u(),bucket:i,appid:v,partNumber:0,retryTimes:0,reqUrl:"",customId:m||"",deviceType:f.deviceType,devicePlatform:f.devicePlatform,deviceName:f.deviceName,md5StartTime:0,md5EndTime:0,signStartTime:0,signEndTime:0,httpStartTime:0,httpEndTime:0,startTime:(new Date).getTime(),endTime:0},this.beacon=c(g)}return o(e,[{key:"formatResult",value:function(e,t){var r,n,o,i,a,s,c=(new Date).getTime()-this.params.startTime,u=l(),d=e?(null==e?void 0:e.code)||(null==e||null===(r=e.error)||void 0===r?void 0:r.code)||(null==e||null===(n=e.error)||void 0===n?void 0:n.Code):"",p=e?(null==e?void 0:e.message)||(null==e||null===(o=e.error)||void 0===o?void 0:o.message)||(null==e||null===(i=e.error)||void 0===i?void 0:i.Message):"",f=e?(null==e?void 0:e.resource)||(null==e||null===(a=e.error)||void 0===a?void 0:a.resource)||(null==e||null===(s=e.error)||void 0===s?void 0:s.Resource):"",h=e?null==e?void 0:e.statusCode:t.statusCode,m=e?(null==e?void 0:e.headers)&&(null==e?void 0:e.headers["x-cos-request-id"]):(null==t?void 0:t.headers)&&(null==t?void 0:t.headers["x-cos-request-id"]),g=e?m?"Server":"Client":"";if(Object.assign(this.params,{tookTime:c,networkType:u,httpMd5:this.params.md5EndTime-this.params.md5StartTime,httpSign:this.params.signEndTime-this.params.signStartTime,httpFull:this.params.httpEndTime-this.params.httpStartTime,result:e?"Fail":"Success",errorType:g,errorCode:d,errorStatusCode:h,errorMessage:p,errorServiceName:f,errorRequestId:m}),!e||d&&p||(this.params.fullError=e?JSON.stringify(e):""),"getObject"===this.params.name&&(this.params.size=t?t.headers&&t.headers["content-length"]:-1),this.params.reqUrl)try{var y=/^http(s)?:\/\/(.*?)\//.exec(this.params.reqUrl);this.params.host=y[2]}catch(e){this.params.host=this.params.reqUrl}this.sendEvents()}},{key:"setParams",value:function(e){Object.assign(this.params,e)}},{key:"sendEvents",value:function(){if(!h.includes(this.params.name)||this.deepTracker){var e,t=(e=this.params.name,m.includes(e)?"cos_upload":g.includes(e)?"cos_download":"base_service"),r=function(e){var t={},r="Success"===e.result?["tracePlatform","cossdkVersion","region","bucket","appid","networkType","host","accelerate","requestPath","partNumber","size","name","result","tookTime","errorRequestId","retryTimes","reqUrl","customId","deviceType","devicePlatform","deviceName"]:["tracePlatform","cossdkVersion","region","networkType","host","accelerate","requestPath","size","httpMd5","httpSign","httpFull","name","result","tookTime","errorNode","errorCode","errorMessage","errorRequestId","errorStatusCode","errorServiceName","errorType","traceId","bucket","appid","partNumber","retryTimes","reqUrl","customId","fullError","deviceType","devicePlatform","deviceName"];for(var n in e)r.includes(n)&&(t[y(n)]=e[n]);return t}(this.params);this.beacon||(this.beacon=c(this.delay||5e3)),0===this.delay?this.beacon&&this.beacon.onDirectUserAction(t,r):this.beacon&&this.beacon.onUserAction(t,r)}}},{key:"generateSubTracker",value:function(t){return Object.assign(t,{parent:this,deepTracker:this.deepTracker,traceId:this.params.traceId,bucket:this.params.bucket,region:this.params.region,fileKey:this.params.requestPath,customId:this.params.customId,delay:this.delay}),new e(t)}}]),e}();e.exports=v},function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,r){var n=r(27);function o(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,r){var n=r(12),o=r(1),i={};e.exports.transferToTaskMethod=function(e,t){i[t]=e[t],e[t]=function(e,r){e.SkipTask?i[t].call(this,e,r):this._addTask(t,e,r)}},e.exports.init=function(e){var t,r,a=[],s={},c=0,u=0,l=function(e){var t={id:e.id,Bucket:e.Bucket,Region:e.Region,Key:e.Key,FilePath:e.FilePath,state:e.state,loaded:e.loaded,size:e.size,speed:e.speed,percent:e.percent,hashPercent:e.hashPercent,error:e.error};return e.FilePath&&(t.FilePath=e.FilePath),e._custom&&(t._custom=e._custom),t},d=(r=function(){t=0,e.emit("task-list-update",{list:o.map(a,l)}),e.emit("list-update",{list:o.map(a,l)})},function(){t||(t=setTimeout(r))}),p=function(){if(!(a.length<=e.options.UploadQueueSize)){for(var t=0;t<u&&t<a.length&&a.length>e.options.UploadQueueSize;){var r="waiting"===a[t].state||"checking"===a[t].state||"uploading"===a[t].state;a[t]&&r?t++:(s[a[t].id]&&delete s[a[t].id],a.splice(t,1),u--)}d()}},f=function t(){if(!(c>=e.options.FileParallelLimit)){for(;a[u]&&"waiting"!==a[u].state;)u++;if(!(u>=a.length)){var r=a[u];u++,c++,r.state="checking",r.params.onTaskStart&&r.params.onTaskStart(l(r)),!r.params.UploadData&&(r.params.UploadData={});var n=o.formatParams(r.api,r.params);i[r.api].call(e,n,(function(n,o){e._isRunningTask(r.id)&&("checking"!==r.state&&"uploading"!==r.state||(r.state=n?"error":"success",n&&(r.error=n),c--,d(),t(),r.callback&&r.callback(n,o),"success"===r.state&&(r.params&&(delete r.params.UploadData,delete r.params.Body,delete r.params),delete r.callback)),p())})),d(),setTimeout(t)}}},h=function(t,r){var o=s[t];if(o){var i=o&&"waiting"===o.state,a=o&&("checking"===o.state||"uploading"===o.state);if("canceled"===r&&"canceled"!==o.state||"paused"===r&&i||"paused"===r&&a){if("paused"===r&&o.params.Body&&"function"==typeof o.params.Body.pipe)return;o.state=r,e.emit("inner-kill-task",{TaskId:t,toState:r});try{var u=o&&o.params&&o.params.UploadData.UploadId}catch(e){}"canceled"===r&&u&&n.removeUsing(u),d(),a&&(c--,f()),"canceled"===r&&(o.params&&(delete o.params.UploadData,delete o.params.Body,delete o.params),delete o.callback)}p()}};e._addTasks=function(t){o.each(t,(function(t){e._addTask(t.api,t.params,t.callback,!0)})),d()},e._addTask=function(t,r,n,i){r=o.formatParams(t,r);var c=o.uuid();r.TaskId=c,r.onTaskReady&&r.onTaskReady(c),r.TaskReady&&r.TaskReady(c);var u={params:r,callback:n,api:t,index:a.length,id:c,Bucket:r.Bucket,Region:r.Region,Key:r.Key,FilePath:r.FilePath||"",state:"waiting",loaded:0,size:0,speed:0,percent:0,hashPercent:0,error:null,_custom:r._custom},l=r.onHashProgress;r.onHashProgress=function(t){e._isRunningTask(u.id)&&(u.hashPercent=t.percent,l&&l(t),d())};var h=r.onProgress;return r.onProgress=function(t){e._isRunningTask(u.id)&&("checking"===u.state&&(u.state="uploading"),u.loaded=t.loaded,u.speed=t.speed,u.percent=t.percent,h&&h(t),d())},o.getFileSize(t,r,(function(e,t){if(e)return n(o.error(e));s[c]=u,a.push(u),u.size=t,!i&&d(),f(),p()})),c},e._isRunningTask=function(e){var t=s[e];return!(!t||"checking"!==t.state&&"uploading"!==t.state)},e.getTaskList=function(){return o.map(a,l)},e.cancelTask=function(e){h(e,"canceled")},e.pauseTask=function(e){h(e,"paused")},e.restartTask=function(e){var t=s[e];!t||"paused"!==t.state&&"error"!==t.state||(t.state="waiting",d(),u=Math.min(u,t.index),f())},e.isUploadRunning=function(){return c||u<a.length}}},function(e,t,r){var n,o,i=r(1),a="cos_sdk_upload_cache",s=function(){try{var e=JSON.parse(localStorage.getItem(a))}catch(e){}e||(e=[]),n=e},c=function(){try{n.length?localStorage.setItem(a,JSON.stringify(n)):localStorage.removeItem(a)}catch(e){}},u=function(){if(!n){s.call(this);for(var e=!1,t=Math.round(Date.now()/1e3),r=n.length-1;r>=0;r--){var o=n[r][2];(!o||o+2592e3<t)&&(n.splice(r,1),e=!0)}e&&c()}},l=function(){o||(o=setTimeout((function(){c(),o=null}),400))},d={using:{},setUsing:function(e){d.using[e]=!0},removeUsing:function(e){delete d.using[e]},getFileId:function(e,t,r,n){return e.name&&e.size&&e.lastModifiedDate&&t?i.md5([e.name,e.size,e.lastModifiedDate,t,r,n].join("::")):null},getCopyFileId:function(e,t,r,n,o){var a=t["content-length"],s=t.etag||"",c=t["last-modified"];return e&&r?i.md5([e,a,s,c,r,n,o].join("::")):null},getUploadIdList:function(e){if(!e)return null;u.call(this);for(var t=[],r=0;r<n.length;r++)n[r][0]===e&&t.push(n[r][1]);return t.length?t:null},saveUploadId:function(e,t,r){if(u.call(this),e){for(var o=n.length-1;o>=0;o--){var i=n[o];i[0]===e&&i[1]===t&&n.splice(o,1)}n.unshift([e,t,Math.round(Date.now()/1e3)]),n.length>r&&n.splice(r),l()}},removeUploadId:function(e){u.call(this),delete d.using[e];for(var t=n.length-1;t>=0;t--)n[t][1]===e&&n.splice(t,1);l()}};e.exports=d},function(e,t,r){var n=r(0),o=r(30),i=r(1);function a(e){var t={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},r={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},n=(e&&e.AccessControlList||{}).Grant;n&&(n=i.isArray(n)?n:[n]);var o={READ:0,WRITE:0,FULL_CONTROL:0};return n&&n.length&&i.each(n,(function(n){"qcs::cam::anyone:anyone"===n.Grantee.ID||"http://cam.qcloud.com/groups/global/AllUsers"===n.Grantee.URI?o[n.Permission]=1:n.Grantee.ID!==e.Owner.ID&&t[r[n.Permission]].push('id="'+n.Grantee.ID+'"')})),o.FULL_CONTROL||o.WRITE&&o.READ?t.ACL="public-read-write":o.READ?t.ACL="public-read":t.ACL="private",i.each(r,(function(e){t[e]=s(t[e].join(","))})),t}function s(e){var t,r,n=e.split(","),o={};for(t=0;t<n.length;)o[r=n[t].trim()]?n.splice(t,1):(o[r]=!0,n[t]=r,t++);return n.join(",")}function c(e){var t=e.region||"",r=e.bucket||"",o=r.substr(0,r.lastIndexOf("-")),a=r.substr(r.lastIndexOf("-")+1),s=e.domain,c=e.object;"function"==typeof s&&(s=s({Bucket:r,Region:t})),["http","https"].includes(e.protocol)&&(e.protocol=e.protocol+":");var u=e.protocol||(i.isBrowser&&"object"===("undefined"==typeof location?"undefined":n(location))&&"http:"===location.protocol?"http:":"https:");s||(s=["cn-south","cn-south-2","cn-north","cn-east","cn-southwest","sg"].indexOf(t)>-1?"{Region}.myqcloud.com":"cos.{Region}.myqcloud.com",e.ForcePathStyle||(s="{Bucket}."+s)),s=(s=s.replace(/\{\{AppId\}\}/gi,a).replace(/\{\{Bucket\}\}/gi,o).replace(/\{\{Region\}\}/gi,t).replace(/\{\{.*?\}\}/gi,"")).replace(/\{AppId\}/gi,a).replace(/\{BucketName\}/gi,o).replace(/\{Bucket\}/gi,r).replace(/\{Region\}/gi,t).replace(/\{.*?\}/gi,""),/^[a-zA-Z]+:\/\//.test(s)||(s=u+"//"+s),"/"===s.slice(-1)&&(s=s.slice(0,-1));var l=s;return e.ForcePathStyle&&(l+="/"+r),l+="/",c&&(l+=i.camSafeUrlEncode(c).replace(/%2F/g,"/")),e.isLocation&&(l=l.replace(/^https?:\/\//,"")),l}var u=function(e){if(!e.Bucket||!e.Region)return"";var t=void 0===e.UseAccelerate?this.options.UseAccelerate:e.UseAccelerate,r=(e.Url||c({ForcePathStyle:this.options.ForcePathStyle,protocol:this.options.Protocol,domain:this.options.Domain,bucket:e.Bucket,region:t?"accelerate":e.Region})).replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1");return new RegExp("^([a-z\\d-]+-\\d+\\.)?(cos|cosv6|ci|pic)\\.([a-z\\d-]+)\\.myqcloud\\.com$").test(r)?r:""};function l(e,t){var r=i.clone(e.Headers),n="";i.each(r,(function(e,t){(""===e||["content-type","cache-control","expires"].indexOf(t.toLowerCase())>-1)&&delete r[t],"host"===t.toLowerCase()&&(n=e)}));var o=!1!==e.ForceSignHost;!n&&e.SignHost&&o&&(r.Host=e.SignHost);var a=!1,s=function(e,r){a||(a=!0,r&&r.XCosSecurityToken&&!r.SecurityToken&&((r=i.clone(r)).SecurityToken=r.XCosSecurityToken,delete r.XCosSecurityToken),t&&t(e,r))},c=this,u=e.Bucket||"",l=e.Region||"",d=e.Key||"";c.options.ForcePathStyle&&u&&(d=u+"/"+d);var p="/"+d,f={},h=e.Scope;if(!h){var m=e.Action||"",g=e.ResourceKey||e.Key||"";h=e.Scope||[{action:m,bucket:u,region:l,prefix:g}]}var y=i.md5(JSON.stringify(h));c._StsCache=c._StsCache||[],function(){var e,t;for(e=c._StsCache.length-1;e>=0;e--){t=c._StsCache[e];var r=Math.round(i.getSkewTime(c.options.SystemClockOffset)/1e3)+30;if(t.StartTime&&r<t.StartTime||r>=t.ExpiredTime)c._StsCache.splice(e,1);else if(!t.ScopeLimit||t.ScopeLimit&&t.ScopeKey===y){f=t;break}}}();var v,b=function(){var t="";f.StartTime&&e.Expires?t=f.StartTime+";"+(f.StartTime+1*e.Expires):f.StartTime&&f.ExpiredTime&&(t=f.StartTime+";"+f.ExpiredTime);var n={Authorization:i.getAuth({SecretId:f.TmpSecretId,SecretKey:f.TmpSecretKey,Method:e.Method,Pathname:p,Query:e.Query,Headers:r,Expires:e.Expires,UseRawKey:c.options.UseRawKey,SystemClockOffset:c.options.SystemClockOffset,KeyTime:t,ForceSignHost:o}),SecurityToken:f.SecurityToken||f.XCosSecurityToken||"",Token:f.Token||"",ClientIP:f.ClientIP||"",ClientUA:f.ClientUA||""};s(null,n)},C=function(e){if(e.Authorization){var t=!1,r=e.Authorization;if(r)if(r.indexOf(" ")>-1)t=!1;else if(r.indexOf("q-sign-algorithm=")>-1&&r.indexOf("q-ak=")>-1&&r.indexOf("q-sign-time=")>-1&&r.indexOf("q-key-time=")>-1&&r.indexOf("q-url-param-list=")>-1)t=!0;else try{(r=atob(r)).indexOf("a=")>-1&&r.indexOf("k=")>-1&&r.indexOf("t=")>-1&&r.indexOf("r=")>-1&&r.indexOf("b=")>-1&&(t=!0)}catch(e){}if(!t)return i.error(new Error("getAuthorization callback params format error"))}else{if(!e.TmpSecretId)return i.error(new Error('getAuthorization callback params missing "TmpSecretId"'));if(!e.TmpSecretKey)return i.error(new Error('getAuthorization callback params missing "TmpSecretKey"'));if(!e.SecurityToken&&!e.XCosSecurityToken)return i.error(new Error('getAuthorization callback params missing "SecurityToken"'));if(!e.ExpiredTime)return i.error(new Error('getAuthorization callback params missing "ExpiredTime"'));if(e.ExpiredTime&&10!==e.ExpiredTime.toString().length)return i.error(new Error('getAuthorization callback params "ExpiredTime" should be 10 digits'));if(e.StartTime&&10!==e.StartTime.toString().length)return i.error(new Error('getAuthorization callback params "StartTime" should be 10 StartTime'))}return!1};if(f.ExpiredTime&&f.ExpiredTime-i.getSkewTime(c.options.SystemClockOffset)/1e3>60)b();else if(c.options.getAuthorization)c.options.getAuthorization.call(c,{Bucket:u,Region:l,Method:e.Method,Key:d,Pathname:p,Query:e.Query,Headers:r,Scope:h,SystemClockOffset:c.options.SystemClockOffset,ForceSignHost:o},(function(e){"string"==typeof e&&(e={Authorization:e});var t=C(e);if(t)return s(t);e.Authorization?s(null,e):((f=e||{}).Scope=h,f.ScopeKey=y,c._StsCache.push(f),b())}));else{if(!c.options.getSTS)return v={Authorization:i.getAuth({SecretId:e.SecretId||c.options.SecretId,SecretKey:e.SecretKey||c.options.SecretKey,Method:e.Method,Pathname:p,Query:e.Query,Headers:r,Expires:e.Expires,UseRawKey:c.options.UseRawKey,SystemClockOffset:c.options.SystemClockOffset,ForceSignHost:o}),SecurityToken:c.options.SecurityToken||c.options.XCosSecurityToken},s(null,v),v;c.options.getSTS.call(c,{Bucket:u,Region:l},(function(e){(f=e||{}).Scope=h,f.ScopeKey=y,f.TmpSecretId||(f.TmpSecretId=f.SecretId),f.TmpSecretKey||(f.TmpSecretKey=f.SecretKey);var t=C(f);if(t)return s(t);c._StsCache.push(f),b()}))}return""}function d(e){var t=!1,r=!1,n=e.headers&&(e.headers.date||e.headers.Date)||e.error&&e.error.ServerTime;try{var o=e.error.Code,a=e.error.Message;("RequestTimeTooSkewed"===o||"AccessDenied"===o&&"Request has expired"===a)&&(r=!0)}catch(e){}if(e)if(r&&n){var s=Date.parse(n);this.options.CorrectClockSkew&&Math.abs(i.getSkewTime(this.options.SystemClockOffset)-s)>=3e4&&(this.options.SystemClockOffset=s-Date.now(),t=!0)}else 5===Math.floor(e.statusCode/100)&&(t=!0);return t}function p(e,t){var r=this;!e.headers&&(e.headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=i.clearKey(e.qs),e.headers&&(e.headers=i.clearKey(e.headers)),e.qs&&(e.qs=i.clearKey(e.qs));var n=i.clone(e.qs);e.action&&(n[e.action]="");var o=e.url||e.Url,a=e.SignHost||u.call(this,{Bucket:e.Bucket,Region:e.Region,Url:o}),s=e.tracker;!function o(i){var c=r.options.SystemClockOffset;s&&s.setParams({signStartTime:(new Date).getTime(),retryTimes:i-1}),l.call(r,{Bucket:e.Bucket||"",Region:e.Region||"",Method:e.method,Key:e.Key,Query:n,Headers:e.headers,SignHost:a,Action:e.Action,ResourceKey:e.ResourceKey,Scope:e.Scope,ForceSignHost:r.options.ForceSignHost},(function(n,a){n?t(n):(s&&s.setParams({signEndTime:(new Date).getTime(),httpStartTime:(new Date).getTime()}),e.AuthData=a,f.call(r,e,(function(n,a){s&&s.setParams({httpEndTime:(new Date).getTime()}),n&&i<2&&(c!==r.options.SystemClockOffset||d.call(r,n))?(e.headers&&(delete e.headers.Authorization,delete e.headers.token,delete e.headers.clientIP,delete e.headers.clientUA,e.headers["x-cos-security-token"]&&delete e.headers["x-cos-security-token"],e.headers["x-ci-security-token"]&&delete e.headers["x-ci-security-token"]),o(i+1)):t(n,a)})))}))}(1)}function f(e,t){var r=this,n=e.TaskId;if(!n||r._isRunningTask(n)){var a=e.Bucket,s=e.Region,u=e.Key,l=e.method||"GET",d=e.Url||e.url,p=e.body,f=e.rawBody;r.options.UseAccelerate&&(s="accelerate"),d=d||c({ForcePathStyle:r.options.ForcePathStyle,protocol:r.options.Protocol,domain:r.options.Domain,bucket:a,region:s,object:u}),e.action&&(d=d+"?"+(i.isIOS_QQ?"".concat(e.action,"="):e.action)),e.qsStr&&(d=d.indexOf("?")>-1?d+"&"+e.qsStr:d+"?"+e.qsStr);var h={method:l,url:d,headers:e.headers,qs:e.qs,body:p},m="x-cos-security-token";if(i.isCIHost(d)&&(m="x-ci-security-token"),h.headers.Authorization=e.AuthData.Authorization,e.AuthData.Token&&(h.headers.token=e.AuthData.Token),e.AuthData.ClientIP&&(h.headers.clientIP=e.AuthData.ClientIP),e.AuthData.ClientUA&&(h.headers.clientUA=e.AuthData.ClientUA),e.AuthData.SecurityToken&&(h.headers[m]=e.AuthData.SecurityToken),h.headers&&(h.headers=i.clearKey(h.headers)),h=i.clearKey(h),e.onProgress&&"function"==typeof e.onProgress){var g=p&&(p.size||p.length)||0;h.onProgress=function(t){if(!n||r._isRunningTask(n)){var o=t?t.loaded:0;e.onProgress({loaded:o,total:g})}}}e.onDownloadProgress&&(h.onDownloadProgress=e.onDownloadProgress),e.DataType&&(h.dataType=e.DataType),this.options.Timeout&&(h.timeout=this.options.Timeout),r.options.ForcePathStyle&&(h.pathStyle=r.options.ForcePathStyle),r.emit("before-send",h);var y=h.url.includes("accelerate."),v=h.qs?Object.keys(h.qs).map((function(e){return"".concat(e,"=").concat(h.qs[e])})).join("&"):"",b=v?h.url+"?"+v:h.url;e.tracker&&e.tracker.setParams({reqUrl:b,accelerate:y?"Y":"N"}),e.tracker&&e.tracker.parent&&e.tracker.parent.setParams({reqUrl:b,accelerate:y?"Y":"N"});var C=(r.options.Request||o)(h,(function(e){if(!e||"abort"!==e.error){var o={options:h,error:e&&e.error,statusCode:e&&e.statusCode||0,statusMessage:e&&e.statusMessage||"",headers:e&&e.headers||{},body:e&&e.body};r.emit("after-receive",o);var a,s=o.error,c=o.body,u={statusCode:o.statusCode,statusMessage:o.statusMessage,headers:o.headers},l=function(e,o){if(n&&r.off("inner-kill-task",S),!a){a=!0;var s={};u&&u.statusCode&&(s.statusCode=u.statusCode),u&&u.headers&&(s.headers=u.headers),e?(e=i.extend(e||{},s),t(e,null)):(o=i.extend(o||{},s),t(null,o)),C=null}};if(s)return l(i.error(s));var d,p=u.statusCode,m=2===Math.floor(p/100);if(f&&m)return l(null,{body:c});try{d=c&&c.indexOf("<")>-1&&c.indexOf(">")>-1&&i.xml2json(c)||{}}catch(e){d={}}var g=d&&d.Error;m?l(null,d):g?l(i.error(new Error(g.Message),{code:g.Code,error:g})):p?l(i.error(new Error(u.statusMessage),{code:""+p})):p&&l(i.error(new Error("statusCode error")))}})),S=function e(t){t.TaskId===n&&(C&&C.abort&&C.abort(),r.off("inner-kill-task",e))};n&&r.on("inner-kill-task",S)}}var h={getService:function(e,t){"function"==typeof e&&(t=e,e={});var r=this.options.Protocol||(i.isBrowser&&"object"===("undefined"==typeof location?"undefined":n(location))&&"http:"===location.protocol?"http:":"https:"),o=this.options.ServiceDomain,a=e.AppId||this.options.appId,s=e.Region;o?(o=o.replace(/\{\{AppId\}\}/gi,a||"").replace(/\{\{Region\}\}/gi,s||"").replace(/\{\{.*?\}\}/gi,""),/^[a-zA-Z]+:\/\//.test(o)||(o=r+"//"+o),"/"===o.slice(-1)&&(o=o.slice(0,-1))):o=s?r+"//cos."+s+".myqcloud.com":r+"//service.cos.myqcloud.com";var c="",u=s?"cos."+s+".myqcloud.com":"service.cos.myqcloud.com";u===o.replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1")&&(c=u),p.call(this,{Action:"name/cos:GetService",url:o,method:"GET",headers:e.Headers,SignHost:c},(function(e,r){if(e)return t(e);var n=r&&r.ListAllMyBucketsResult&&r.ListAllMyBucketsResult.Buckets&&r.ListAllMyBucketsResult.Buckets.Bucket||[];n=i.isArray(n)?n:[n];var o=r&&r.ListAllMyBucketsResult&&r.ListAllMyBucketsResult.Owner||{};t(null,{Buckets:n,Owner:o,statusCode:r.statusCode,headers:r.headers})}))},putBucket:function(e,t){var r=this,n="";if(e.BucketAZConfig){var o={BucketAZConfig:e.BucketAZConfig};n=i.json2xml({CreateBucketConfiguration:o})}p.call(this,{Action:"name/cos:PutBucket",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,body:n},(function(n,o){if(n)return t(n);var i=c({protocol:r.options.Protocol,domain:r.options.Domain,bucket:e.Bucket,region:e.Region,isLocation:!0});t(null,{Location:i,statusCode:o.statusCode,headers:o.headers})}))},headBucket:function(e,t){p.call(this,{Action:"name/cos:HeadBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"HEAD"},t)},getBucket:function(e,t){var r={};r.prefix=e.Prefix||"",r.delimiter=e.Delimiter,r.marker=e.Marker,r["max-keys"]=e.MaxKeys,r["encoding-type"]=e.EncodingType,p.call(this,{Action:"name/cos:GetBucket",ResourceKey:r.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:r},(function(e,r){if(e)return t(e);var n=r.ListBucketResult||{},o=n.Contents||[],a=n.CommonPrefixes||[];o=i.isArray(o)?o:[o],a=i.isArray(a)?a:[a];var s=i.clone(n);i.extend(s,{Contents:o,CommonPrefixes:a,statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},deleteBucket:function(e,t){p.call(this,{Action:"name/cos:DeleteBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"DELETE"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketAcl:function(e,t){var r=e.Headers,n="";if(e.AccessControlPolicy){var o=i.clone(e.AccessControlPolicy||{}),a=o.Grants||o.Grant;a=i.isArray(a)?a:[a],delete o.Grant,delete o.Grants,o.AccessControlList={Grant:a},n=i.json2xml({AccessControlPolicy:o}),r["Content-Type"]="application/xml",r["Content-MD5"]=i.binaryBase64(i.md5(n))}i.each(r,(function(e,t){0===t.indexOf("x-cos-grant-")&&(r[t]=s(r[t]))})),p.call(this,{Action:"name/cos:PutBucketACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:r,action:"acl",body:n},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketAcl:function(e,t){p.call(this,{Action:"name/cos:GetBucketACL",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"acl"},(function(e,r){if(e)return t(e);var n=r.AccessControlPolicy||{},o=n.Owner||{},s=n.AccessControlList.Grant||[];s=i.isArray(s)?s:[s];var c=a(n);r.headers&&r.headers["x-cos-acl"]&&(c.ACL=r.headers["x-cos-acl"]),c=i.extend(c,{Owner:o,Grants:s,statusCode:r.statusCode,headers:r.headers}),t(null,c)}))},putBucketCors:function(e,t){var r=(e.CORSConfiguration||{}).CORSRules||e.CORSRules||[];r=i.clone(i.isArray(r)?r:[r]),i.each(r,(function(e){i.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],(function(t){var r=t+"s",n=e[r]||e[t]||[];delete e[r],e[t]=i.isArray(n)?n:[n]}))}));var n={CORSRule:r};e.ResponseVary&&(n.ResponseVary=e.ResponseVary);var o=i.json2xml({CORSConfiguration:n}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=i.binaryBase64(i.md5(o)),p.call(this,{Action:"name/cos:PutBucketCORS",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"cors",headers:a},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketCors:function(e,t){p.call(this,{Action:"name/cos:GetBucketCORS",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors"},(function(e,r){if(e)if(404===e.statusCode&&e.error&&"NoSuchCORSConfiguration"===e.error.Code){var n={CORSRules:[],statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var o=r.CORSConfiguration||{},a=o.CORSRules||o.CORSRule||[];a=i.clone(i.isArray(a)?a:[a]);var s=o.ResponseVary;i.each(a,(function(e){i.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],(function(t){var r=t+"s",n=e[r]||e[t]||[];delete e[t],e[r]=i.isArray(n)?n:[n]}))})),t(null,{CORSRules:a,ResponseVary:s,statusCode:r.statusCode,headers:r.headers})}}))},deleteBucketCors:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketCORS",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode||e.statusCode,headers:r.headers})}))},getBucketLocation:function(e,t){p.call(this,{Action:"name/cos:GetBucketLocation",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"location"},t)},getBucketPolicy:function(e,t){p.call(this,{Action:"name/cos:GetBucketPolicy",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",rawBody:!0},(function(e,r){if(e)return e.statusCode&&403===e.statusCode?t(i.error(e,{ErrorStatus:"Access Denied"})):e.statusCode&&405===e.statusCode?t(i.error(e,{ErrorStatus:"Method Not Allowed"})):e.statusCode&&404===e.statusCode?t(i.error(e,{ErrorStatus:"Policy Not Found"})):t(e);var n={};try{n=JSON.parse(r.body)}catch(e){}t(null,{Policy:n,statusCode:r.statusCode,headers:r.headers})}))},putBucketPolicy:function(e,t){var r=e.Policy;try{"string"==typeof r&&(r=JSON.parse(r))}catch(e){}if(!r||"string"==typeof r)return t(i.error(new Error("Policy format error")));var n=JSON.stringify(r);r.version||(r.version="2.0");var o=e.Headers;o["Content-Type"]="application/json",o["Content-MD5"]=i.binaryBase64(i.md5(n)),p.call(this,{Action:"name/cos:PutBucketPolicy",method:"PUT",Bucket:e.Bucket,Region:e.Region,action:"policy",body:n,headers:o},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},deleteBucketPolicy:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketPolicy",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode||e.statusCode,headers:r.headers})}))},putBucketTagging:function(e,t){var r=e.Tagging||{},n=r.TagSet||r.Tags||e.Tags||[];n=i.clone(i.isArray(n)?n:[n]);var o=i.json2xml({Tagging:{TagSet:{Tag:n}}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=i.binaryBase64(i.md5(o)),p.call(this,{Action:"name/cos:PutBucketTagging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"tagging",headers:a},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketTagging:function(e,t){p.call(this,{Action:"name/cos:GetBucketTagging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging"},(function(e,r){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var n={Tags:[],statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else{var o=[];try{o=r.Tagging.TagSet.Tag||[]}catch(e){}o=i.clone(i.isArray(o)?o:[o]),t(null,{Tags:o,statusCode:r.statusCode,headers:r.headers})}}))},deleteBucketTagging:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketLifecycle:function(e,t){var r=(e.LifecycleConfiguration||{}).Rules||e.Rules||[];r=i.clone(r);var n=i.json2xml({LifecycleConfiguration:{Rule:r}}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=i.binaryBase64(i.md5(n)),p.call(this,{Action:"name/cos:PutBucketLifecycle",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"lifecycle",headers:o},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketLifecycle:function(e,t){p.call(this,{Action:"name/cos:GetBucketLifecycle",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle"},(function(e,r){if(e)if(404===e.statusCode&&e.error&&"NoSuchLifecycleConfiguration"===e.error.Code){var n={Rules:[],statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var o=[];try{o=r.LifecycleConfiguration.Rule||[]}catch(e){}o=i.clone(i.isArray(o)?o:[o]),t(null,{Rules:o,statusCode:r.statusCode,headers:r.headers})}}))},deleteBucketLifecycle:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketLifecycle",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketVersioning:function(e,t){if(e.VersioningConfiguration){var r=e.VersioningConfiguration||{},n=i.json2xml({VersioningConfiguration:r}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=i.binaryBase64(i.md5(n)),p.call(this,{Action:"name/cos:PutBucketVersioning",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"versioning",headers:o},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))}else t(i.error(new Error("missing param VersioningConfiguration")))},getBucketVersioning:function(e,t){p.call(this,{Action:"name/cos:GetBucketVersioning",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"versioning"},(function(e,r){e||!r.VersioningConfiguration&&(r.VersioningConfiguration={}),t(e,r)}))},putBucketReplication:function(e,t){var r=i.clone(e.ReplicationConfiguration),n=i.json2xml({ReplicationConfiguration:r});n=(n=n.replace(/<(\/?)Rules>/gi,"<$1Rule>")).replace(/<(\/?)Tags>/gi,"<$1Tag>");var o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=i.binaryBase64(i.md5(n)),p.call(this,{Action:"name/cos:PutBucketReplication",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"replication",headers:o},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketReplication:function(e,t){p.call(this,{Action:"name/cos:GetBucketReplication",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication"},(function(e,r){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"ReplicationConfigurationnotFoundError"!==e.error.Code)t(e);else{var n={ReplicationConfiguration:{Rules:[]},statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else!r.ReplicationConfiguration&&(r.ReplicationConfiguration={}),r.ReplicationConfiguration.Rule&&(r.ReplicationConfiguration.Rules=i.makeArray(r.ReplicationConfiguration.Rule),delete r.ReplicationConfiguration.Rule),t(e,r)}))},deleteBucketReplication:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketWebsite:function(e,t){if(e.WebsiteConfiguration){var r=i.clone(e.WebsiteConfiguration||{}),n=r.RoutingRules||r.RoutingRule||[];n=i.isArray(n)?n:[n],delete r.RoutingRule,delete r.RoutingRules,n.length&&(r.RoutingRules={RoutingRule:n});var o=i.json2xml({WebsiteConfiguration:r}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=i.binaryBase64(i.md5(o)),p.call(this,{Action:"name/cos:PutBucketWebsite",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"website",headers:a},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))}else t(i.error(new Error("missing param WebsiteConfiguration")))},getBucketWebsite:function(e,t){p.call(this,{Action:"name/cos:GetBucketWebsite",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"website"},(function(e,r){if(e)if(404===e.statusCode&&"NoSuchWebsiteConfiguration"===e.error.Code){var n={WebsiteConfiguration:{},statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var o=r.WebsiteConfiguration||{};if(o.RoutingRules){var a=i.clone(o.RoutingRules.RoutingRule||[]);a=i.makeArray(a),o.RoutingRules=a}t(null,{WebsiteConfiguration:o,statusCode:r.statusCode,headers:r.headers})}}))},deleteBucketWebsite:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketWebsite",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"website"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketReferer:function(e,t){if(e.RefererConfiguration){var r=i.clone(e.RefererConfiguration||{}),n=r.DomainList||{},o=n.Domains||n.Domain||[];(o=i.isArray(o)?o:[o]).length&&(r.DomainList={Domain:o});var a=i.json2xml({RefererConfiguration:r}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=i.binaryBase64(i.md5(a)),p.call(this,{Action:"name/cos:PutBucketReferer",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:a,action:"referer",headers:s},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))}else t(i.error(new Error("missing param RefererConfiguration")))},getBucketReferer:function(e,t){p.call(this,{Action:"name/cos:GetBucketReferer",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"referer"},(function(e,r){if(e)if(404===e.statusCode&&"NoSuchRefererConfiguration"===e.error.Code){var n={WebsiteConfiguration:{},statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var o=r.RefererConfiguration||{};if(o.DomainList){var a=i.makeArray(o.DomainList.Domain||[]);o.DomainList={Domains:a}}t(null,{RefererConfiguration:o,statusCode:r.statusCode,headers:r.headers})}}))},putBucketDomain:function(e,t){var r=(e.DomainConfiguration||{}).DomainRule||e.DomainRule||[];r=i.clone(r);var n=i.json2xml({DomainConfiguration:{DomainRule:r}}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=i.binaryBase64(i.md5(n)),p.call(this,{Action:"name/cos:PutBucketDomain",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"domain",headers:o},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketDomain:function(e,t){p.call(this,{Action:"name/cos:GetBucketDomain",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain"},(function(e,r){if(e)return t(e);var n=[];try{n=r.DomainConfiguration.DomainRule||[]}catch(e){}n=i.clone(i.isArray(n)?n:[n]),t(null,{DomainRule:n,statusCode:r.statusCode,headers:r.headers})}))},deleteBucketDomain:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketDomain",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketOrigin:function(e,t){var r=(e.OriginConfiguration||{}).OriginRule||e.OriginRule||[];r=i.clone(r);var n=i.json2xml({OriginConfiguration:{OriginRule:r}}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=i.binaryBase64(i.md5(n)),p.call(this,{Action:"name/cos:PutBucketOrigin",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"origin",headers:o},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketOrigin:function(e,t){p.call(this,{Action:"name/cos:GetBucketOrigin",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin"},(function(e,r){if(e)return t(e);var n=[];try{n=r.OriginConfiguration.OriginRule||[]}catch(e){}n=i.clone(i.isArray(n)?n:[n]),t(null,{OriginRule:n,statusCode:r.statusCode,headers:r.headers})}))},deleteBucketOrigin:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketOrigin",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketLogging:function(e,t){var r=i.json2xml({BucketLoggingStatus:e.BucketLoggingStatus||""}),n=e.Headers;n["Content-Type"]="application/xml",n["Content-MD5"]=i.binaryBase64(i.md5(r)),p.call(this,{Action:"name/cos:PutBucketLogging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"logging",headers:n},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketLogging:function(e,t){p.call(this,{Action:"name/cos:GetBucketLogging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"logging"},(function(e,r){if(e)return t(e);t(null,{BucketLoggingStatus:r.BucketLoggingStatus,statusCode:r.statusCode,headers:r.headers})}))},putBucketInventory:function(e,t){var r=i.clone(e.InventoryConfiguration);if(r.OptionalFields){var n=r.OptionalFields||[];r.OptionalFields={Field:n}}if(r.Destination&&r.Destination.COSBucketDestination&&r.Destination.COSBucketDestination.Encryption){var o=r.Destination.COSBucketDestination.Encryption;Object.keys(o).indexOf("SSECOS")>-1&&(o["SSE-COS"]=o.SSECOS,delete o.SSECOS)}var a=i.json2xml({InventoryConfiguration:r}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=i.binaryBase64(i.md5(a)),p.call(this,{Action:"name/cos:PutBucketInventory",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:a,action:"inventory",qs:{id:e.Id},headers:s},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketInventory:function(e,t){p.call(this,{Action:"name/cos:GetBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e.Id}},(function(e,r){if(e)return t(e);var n=r.InventoryConfiguration;if(n&&n.OptionalFields&&n.OptionalFields.Field){var o=n.OptionalFields.Field;i.isArray(o)||(o=[o]),n.OptionalFields=o}if(n.Destination&&n.Destination.COSBucketDestination&&n.Destination.COSBucketDestination.Encryption){var a=n.Destination.COSBucketDestination.Encryption;Object.keys(a).indexOf("SSE-COS")>-1&&(a.SSECOS=a["SSE-COS"],delete a["SSE-COS"])}t(null,{InventoryConfiguration:n,statusCode:r.statusCode,headers:r.headers})}))},listBucketInventory:function(e,t){p.call(this,{Action:"name/cos:ListBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{"continuation-token":e.ContinuationToken}},(function(e,r){if(e)return t(e);var n=r.ListInventoryConfigurationResult,o=n.InventoryConfiguration||[];o=i.isArray(o)?o:[o],delete n.InventoryConfiguration,i.each(o,(function(e){if(e&&e.OptionalFields&&e.OptionalFields.Field){var t=e.OptionalFields.Field;i.isArray(t)||(t=[t]),e.OptionalFields=t}if(e.Destination&&e.Destination.COSBucketDestination&&e.Destination.COSBucketDestination.Encryption){var r=e.Destination.COSBucketDestination.Encryption;Object.keys(r).indexOf("SSE-COS")>-1&&(r.SSECOS=r["SSE-COS"],delete r["SSE-COS"])}})),n.InventoryConfigurations=o,i.extend(n,{statusCode:r.statusCode,headers:r.headers}),t(null,n)}))},deleteBucketInventory:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketInventory",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e.Id}},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketAccelerate:function(e,t){if(e.AccelerateConfiguration){var r={AccelerateConfiguration:e.AccelerateConfiguration||{}},n=i.json2xml(r),o={"Content-Type":"application/xml"};o["Content-MD5"]=i.binaryBase64(i.md5(n)),p.call(this,{Action:"name/cos:PutBucketAccelerate",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"accelerate",headers:o},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))}else t(i.error(new Error("missing param AccelerateConfiguration")))},getBucketAccelerate:function(e,t){p.call(this,{Action:"name/cos:GetBucketAccelerate",method:"GET",Bucket:e.Bucket,Region:e.Region,action:"accelerate"},(function(e,r){e||!r.AccelerateConfiguration&&(r.AccelerateConfiguration={}),t(e,r)}))},putBucketEncryption:function(e,t){var r=e.ServerSideEncryptionConfiguration||{},n=r.Rule||r.Rules||[],o=i.json2xml({ServerSideEncryptionConfiguration:{Rule:n}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=i.binaryBase64(i.md5(o)),p.call(this,{Action:"name/cos:PutBucketEncryption",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"encryption",headers:a},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketEncryption:function(e,t){p.call(this,{Action:"name/cos:GetBucketEncryption",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"encryption"},(function(e,r){if(e)if(404===e.statusCode&&"NoSuchEncryptionConfiguration"===e.code){var n={EncryptionConfiguration:{Rules:[]},statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var o=i.makeArray(r.EncryptionConfiguration&&r.EncryptionConfiguration.Rule||[]);r.EncryptionConfiguration={Rules:o},t(e,r)}}))},deleteBucketEncryption:function(e,t){p.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"encryption"},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getObject:function(e,t){var r=e.Query||{},n=e.QueryString||"",o=i.throttleOnProgress.call(this,0,e.onProgress),a=e.tracker;a&&a.setParams({signStartTime:(new Date).getTime()}),r["response-content-type"]=e.ResponseContentType,r["response-content-language"]=e.ResponseContentLanguage,r["response-expires"]=e.ResponseExpires,r["response-cache-control"]=e.ResponseCacheControl,r["response-content-disposition"]=e.ResponseContentDisposition,r["response-content-encoding"]=e.ResponseContentEncoding,p.call(this,{Action:"name/cos:GetObject",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,DataType:e.DataType,headers:e.Headers,qs:r,qsStr:n,rawBody:!0,onDownloadProgress:o,tracker:a},(function(r,n){if(o(null,!0),r){var a=r.statusCode;return e.Headers["If-Modified-Since"]&&a&&304===a?t(null,{NotModified:!0}):t(r)}t(null,{Body:n.body,ETag:i.attr(n.headers,"etag",""),statusCode:n.statusCode,headers:n.headers})}))},headObject:function(e,t){p.call(this,{Action:"name/cos:HeadObject",method:"HEAD",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers},(function(r,n){if(r){var o=r.statusCode;return e.Headers["If-Modified-Since"]&&o&&304===o?t(null,{NotModified:!0,statusCode:o}):t(r)}n.ETag=i.attr(n.headers,"etag",""),t(null,n)}))},listObjectVersions:function(e,t){var r={};r.prefix=e.Prefix||"",r.delimiter=e.Delimiter,r["key-marker"]=e.KeyMarker,r["version-id-marker"]=e.VersionIdMarker,r["max-keys"]=e.MaxKeys,r["encoding-type"]=e.EncodingType,p.call(this,{Action:"name/cos:GetBucketObjectVersions",ResourceKey:r.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:r,action:"versions"},(function(e,r){if(e)return t(e);var n=r.ListVersionsResult||{},o=n.DeleteMarker||[];o=i.isArray(o)?o:[o];var a=n.Version||[];a=i.isArray(a)?a:[a];var s=i.clone(n);delete s.DeleteMarker,delete s.Version,i.extend(s,{DeleteMarkers:o,Versions:a,statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},putObject:function(e,t){var r=this,n=e.ContentLength,o=i.throttleOnProgress.call(r,n,e.onProgress),a=e.Headers;a["Cache-Control"]||a["cache-control"]||(a["Cache-Control"]=""),a["Content-Type"]||a["content-type"]||(a["Content-Type"]=e.Body&&e.Body.type||"");var s=e.UploadAddMetaMd5||r.options.UploadAddMetaMd5||r.options.UploadCheckContentMd5,u=e.tracker;s&&u&&u.setParams({md5StartTime:(new Date).getTime()}),i.getBodyMd5(s,e.Body,(function(s){s&&(u&&u.setParams({md5EndTime:(new Date).getTime()}),r.options.UploadCheckContentMd5&&(a["Content-MD5"]=i.binaryBase64(s)),(e.UploadAddMetaMd5||r.options.UploadAddMetaMd5)&&(a["x-cos-meta-md5"]=s)),void 0!==e.ContentLength&&(a["Content-Length"]=e.ContentLength),o(null,!0),p.call(r,{Action:"name/cos:PutObject",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:e.Query,body:e.Body,onProgress:o,tracker:u},(function(a,s){if(a)return o(null,!0),t(a);o({loaded:n,total:n},!0);var u=c({ForcePathStyle:r.options.ForcePathStyle,protocol:r.options.Protocol,domain:r.options.Domain,bucket:e.Bucket,region:r.options.UseAccelerate?"accelerate":e.Region,object:e.Key});u=u.substr(u.indexOf("://")+3),s.Location=u,s.ETag=i.attr(s.headers,"etag",""),t(null,s)}))}),e.onHashProgress)},deleteObject:function(e,t){p.call(this,{Action:"name/cos:DeleteObject",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,VersionId:e.VersionId,action:e.Recursive?"recursive":""},(function(e,r){if(e){var n=e.statusCode;return n&&404===n?t(null,{BucketNotFound:!0,statusCode:n}):t(e)}t(null,{statusCode:r.statusCode,headers:r.headers})}))},getObjectAcl:function(e,t){var r={};e.VersionId&&(r.versionId=e.VersionId),p.call(this,{Action:"name/cos:GetObjectACL",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:r,action:"acl"},(function(e,r){if(e)return t(e);var n=r.AccessControlPolicy||{},o=n.Owner||{},s=n.AccessControlList&&n.AccessControlList.Grant||[];s=i.isArray(s)?s:[s];var c=a(n);delete c.GrantWrite,r.headers&&r.headers["x-cos-acl"]&&(c.ACL=r.headers["x-cos-acl"]),c=i.extend(c,{Owner:o,Grants:s,statusCode:r.statusCode,headers:r.headers}),t(null,c)}))},putObjectAcl:function(e,t){var r=e.Headers,n="";if(e.AccessControlPolicy){var o=i.clone(e.AccessControlPolicy||{}),a=o.Grants||o.Grant;a=i.isArray(a)?a:[a],delete o.Grant,delete o.Grants,o.AccessControlList={Grant:a},n=i.json2xml({AccessControlPolicy:o}),r["Content-Type"]="application/xml",r["Content-MD5"]=i.binaryBase64(i.md5(n))}i.each(r,(function(e,t){0===t.indexOf("x-cos-grant-")&&(r[t]=s(r[t]))})),p.call(this,{Action:"name/cos:PutObjectACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"acl",headers:r,body:n},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))},optionsObject:function(e,t){var r=e.Headers;r.Origin=e.Origin,r["Access-Control-Request-Method"]=e.AccessControlRequestMethod,r["Access-Control-Request-Headers"]=e.AccessControlRequestHeaders,p.call(this,{Action:"name/cos:OptionsObject",method:"OPTIONS",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:r},(function(e,r){if(e)return e.statusCode&&403===e.statusCode?t(null,{OptionsForbidden:!0,statusCode:e.statusCode}):t(e);var n=r.headers||{};t(null,{AccessControlAllowOrigin:n["access-control-allow-origin"],AccessControlAllowMethods:n["access-control-allow-methods"],AccessControlAllowHeaders:n["access-control-allow-headers"],AccessControlExposeHeaders:n["access-control-expose-headers"],AccessControlMaxAge:n["access-control-max-age"],statusCode:r.statusCode,headers:r.headers})}))},putObjectCopy:function(e,t){var r=this,n=e.Headers;n["Cache-Control"]||n["cache-control"]||(n["Cache-Control"]="");var o=e.CopySource||"",a=i.getSourceParams.call(this,o);if(a){var s=a.Bucket,u=a.Region,l=decodeURIComponent(a.Key);p.call(this,{Scope:[{action:"name/cos:GetObject",bucket:s,region:u,prefix:l},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers},(function(n,o){if(n)return t(n);var a=i.clone(o.CopyObjectResult||{}),s=c({ForcePathStyle:r.options.ForcePathStyle,protocol:r.options.Protocol,domain:r.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key,isLocation:!0});i.extend(a,{Location:s,statusCode:o.statusCode,headers:o.headers}),t(null,a)}))}else t(i.error(new Error("CopySource format error")))},deleteMultipleObject:function(e,t){var r=e.Objects||[],n=e.Quiet;r=i.isArray(r)?r:[r];var o=i.json2xml({Delete:{Object:r,Quiet:n||!1}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=i.binaryBase64(i.md5(o));var s=i.map(r,(function(t){return{action:"name/cos:DeleteObject",bucket:e.Bucket,region:e.Region,prefix:t.Key}}));p.call(this,{Scope:s,method:"POST",Bucket:e.Bucket,Region:e.Region,body:o,action:"delete",headers:a},(function(e,r){if(e)return t(e);var n=r.DeleteResult||{},o=n.Deleted||[],a=n.Error||[];o=i.isArray(o)?o:[o],a=i.isArray(a)?a:[a];var s=i.clone(n);i.extend(s,{Error:a,Deleted:o,statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},restoreObject:function(e,t){var r=e.Headers;if(e.RestoreRequest){var n=e.RestoreRequest||{},o=i.json2xml({RestoreRequest:n});r["Content-Type"]="application/xml",r["Content-MD5"]=i.binaryBase64(i.md5(o)),p.call(this,{Action:"name/cos:RestoreObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,body:o,action:"restore",headers:r},t)}else t(i.error(new Error("missing param RestoreRequest")))},putObjectTagging:function(e,t){var r=e.Tagging||{},n=r.TagSet||r.Tags||e.Tags||[];n=i.clone(i.isArray(n)?n:[n]);var o=i.json2xml({Tagging:{TagSet:{Tag:n}}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=i.binaryBase64(i.md5(o)),p.call(this,{Action:"name/cos:PutObjectTagging",method:"PUT",Bucket:e.Bucket,Key:e.Key,Region:e.Region,body:o,action:"tagging",headers:a,VersionId:e.VersionId},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getObjectTagging:function(e,t){p.call(this,{Action:"name/cos:GetObjectTagging",method:"GET",Key:e.Key,Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",VersionId:e.VersionId},(function(e,r){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var n={Tags:[],statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else{var o=[];try{o=r.Tagging.TagSet.Tag||[]}catch(e){}o=i.clone(i.isArray(o)?o:[o]),t(null,{Tags:o,statusCode:r.statusCode,headers:r.headers})}}))},deleteObjectTagging:function(e,t){p.call(this,{Action:"name/cos:DeleteObjectTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"tagging",VersionId:e.VersionId},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},selectObjectContent:function(e,t){if(!e.SelectType)return t(i.error(new Error("missing param SelectType")));var r=e.SelectRequest||{},n=i.json2xml({SelectRequest:r}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=i.binaryBase64(i.md5(n)),p.call(this,{Action:"name/cos:GetObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"select",qs:{"select-type":e.SelectType},VersionId:e.VersionId,body:n,DataType:"arraybuffer",rawBody:!0},(function(e,r){if(e&&204===e.statusCode)return t(null,{statusCode:e.statusCode});if(e)return t(e);var n=i.parseSelectPayload(r.body);t(null,{statusCode:r.statusCode,headers:r.headers,Body:n.body,Payload:n.payload})}))},appendObject:function(e,t){var r=e.Headers;r["Cache-Control"]||r["cache-control"]||(r["Cache-Control"]=""),r["Content-Type"]||r["content-type"]||(r["Content-Type"]=e.Body&&e.Body.type||""),p.call(this,{Action:"name/cos:AppendObject",method:"POST",Bucket:e.Bucket,Region:e.Region,action:"append",Key:e.Key,body:e.Body,qs:{position:e.Position},headers:e.Headers},(function(e,r){if(e)return t(e);t(null,r)}))},uploadPartCopy:function(e,t){var r=e.CopySource||"",n=i.getSourceParams.call(this,r);if(n){var o=n.Bucket,a=n.Region,s=decodeURIComponent(n.Key);p.call(this,{Scope:[{action:"name/cos:GetObject",bucket:o,region:a,prefix:s},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers},(function(e,r){if(e)return t(e);var n=i.clone(r.CopyPartResult||{});i.extend(n,{statusCode:r.statusCode,headers:r.headers}),t(null,n)}))}else t(i.error(new Error("CopySource format error")))},multipartInit:function(e,t){var r=this,n=e.Headers,o=e.tracker;n["Cache-Control"]||n["cache-control"]||(n["Cache-Control"]=""),n["Content-Type"]||n["content-type"]||(n["Content-Type"]=e.Body&&e.Body.type||"");var a=e.Body&&(e.UploadAddMetaMd5||r.options.UploadAddMetaMd5);a&&o&&o.setParams({md5StartTime:(new Date).getTime()}),i.getBodyMd5(a,e.Body,(function(n){n&&(e.Headers["x-cos-meta-md5"]=n),a&&o&&o.setParams({md5EndTime:(new Date).getTime()}),p.call(r,{Action:"name/cos:InitiateMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"uploads",headers:e.Headers,qs:e.Query,tracker:o},(function(e,r){return e?(o&&o.parent&&o.parent.setParams({errorNode:"multipartInit"}),t(e)):(r=i.clone(r||{}))&&r.InitiateMultipartUploadResult?t(null,i.extend(r.InitiateMultipartUploadResult,{statusCode:r.statusCode,headers:r.headers})):void t(null,r)}))}),e.onHashProgress)},multipartUpload:function(e,t){var r=this;i.getFileSize("multipartUpload",e,(function(){var n=e.tracker,o=r.options.UploadCheckContentMd5;o&&n&&n.setParams({md5StartTime:(new Date).getTime()}),i.getBodyMd5(o,e.Body,(function(a){a&&(e.Headers["Content-MD5"]=i.binaryBase64(a)),o&&n&&n.setParams({md5EndTime:(new Date).getTime()}),n&&n.setParams({partNumber:e.PartNumber}),p.call(r,{Action:"name/cos:UploadPart",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers,onProgress:e.onProgress,body:e.Body||null,tracker:n},(function(e,r){if(e)return n&&n.parent&&n.parent.setParams({errorNode:"multipartUpload"}),t(e);t(null,{ETag:i.attr(r.headers,"etag",""),statusCode:r.statusCode,headers:r.headers})}))}))}))},multipartComplete:function(e,t){for(var r=this,n=e.UploadId,o=e.Parts,a=e.tracker,s=0,u=o.length;s<u;s++)o[s].ETag&&0===o[s].ETag.indexOf('"')||(o[s].ETag='"'+o[s].ETag+'"');var l=i.json2xml({CompleteMultipartUpload:{Part:o}});l=l.replace(/\n\s*/g,"");var d=e.Headers;d["Content-Type"]="application/xml",d["Content-MD5"]=i.binaryBase64(i.md5(l)),p.call(this,{Action:"name/cos:CompleteMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{uploadId:n},body:l,headers:d,tracker:a},(function(n,o){if(n)return a&&a.parent&&a.parent.setParams({errorNode:"multipartComplete"}),t(n);var s=c({ForcePathStyle:r.options.ForcePathStyle,protocol:r.options.Protocol,domain:r.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key,isLocation:!0}),u=o.CompleteMultipartUploadResult||{};u.ProcessResults&&u&&u.ProcessResults&&(u.UploadResult={OriginalInfo:{Key:u.Key,Location:s,ETag:u.ETag,ImageInfo:u.ImageInfo},ProcessResults:u.ProcessResults},delete u.ImageInfo,delete u.ProcessResults);var l=i.extend(u,{Location:s,statusCode:o.statusCode,headers:o.headers});t(null,l)}))},multipartList:function(e,t){var r={};r.delimiter=e.Delimiter,r["encoding-type"]=e.EncodingType,r.prefix=e.Prefix||"",r["max-uploads"]=e.MaxUploads,r["key-marker"]=e.KeyMarker,r["upload-id-marker"]=e.UploadIdMarker,r=i.clearKey(r);var n=e.tracker;n&&n.setParams({signStartTime:(new Date).getTime()}),p.call(this,{Action:"name/cos:ListMultipartUploads",ResourceKey:r.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:r,action:"uploads",tracker:n},(function(e,r){if(e)return n&&n.parent&&n.parent.setParams({errorNode:"multipartList"}),t(e);if(r&&r.ListMultipartUploadsResult){var o=r.ListMultipartUploadsResult.Upload||[];o=i.isArray(o)?o:[o],r.ListMultipartUploadsResult.Upload=o}var a=i.clone(r.ListMultipartUploadsResult||{});i.extend(a,{statusCode:r.statusCode,headers:r.headers}),t(null,a)}))},multipartListPart:function(e,t){var r={},n=e.tracker;r.uploadId=e.UploadId,r["encoding-type"]=e.EncodingType,r["max-parts"]=e.MaxParts,r["part-number-marker"]=e.PartNumberMarker,p.call(this,{Action:"name/cos:ListParts",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:r},(function(e,r){if(e)return n&&n.parent&&n.parent.setParams({errorNode:"multipartListPart"}),t(e);var o=r.ListPartsResult||{},a=o.Part||[];a=i.isArray(a)?a:[a],o.Part=a;var s=i.clone(o);i.extend(s,{statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},multipartAbort:function(e,t){var r={};r.uploadId=e.UploadId,p.call(this,{Action:"name/cos:AbortMultipartUpload",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:r},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))},request:function(e,t){p.call(this,{method:e.Method,Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:e.Action,headers:e.Headers,qs:e.Query,body:e.Body,Url:e.Url,rawBody:e.RawBody,DataType:e.DataType},(function(e,r){if(e)return t(e);r&&r.body&&(r.Body=r.body,delete r.body),t(e,r)}))},getObjectUrl:function(e,t){var r=this,n=void 0===e.UseAccelerate?r.options.UseAccelerate:e.UseAccelerate,o=c({ForcePathStyle:r.options.ForcePathStyle,protocol:e.Protocol||r.options.Protocol,domain:e.Domain||r.options.Domain,bucket:e.Bucket,region:n?"accelerate":e.Region,object:e.Key}),a="";e.Query&&(a+=i.obj2str(e.Query)),e.QueryString&&(a+=(a?"&":"")+e.QueryString);var s=o;if(void 0!==e.Sign&&!e.Sign)return a&&(s+="?"+a),t(null,{Url:s}),s;var d=u.call(this,{Bucket:e.Bucket,Region:e.Region,UseAccelerate:e.UseAccelerate,Url:o}),p=l.call(this,{Action:"PUT"===(e.Method||"").toUpperCase()?"name/cos:PutObject":"name/cos:GetObject",Bucket:e.Bucket||"",Region:e.Region||"",Method:e.Method||"get",Key:e.Key,Expires:e.Expires,Headers:e.Headers,Query:e.Query,SignHost:d,ForceSignHost:!1!==e.ForceSignHost&&r.options.ForceSignHost},(function(e,r){if(t)if(e)t(e);else{var n=o;n+="?"+(r.Authorization.indexOf("q-signature")>-1?function(e){var t=e.match(/q-url-param-list.*?(?=&)/g)[0],r="q-url-param-list="+encodeURIComponent(t.replace(/q-url-param-list=/,"")).toLowerCase(),n=new RegExp(t,"g");return e.replace(n,r)}(r.Authorization):"sign="+encodeURIComponent(r.Authorization)),r.SecurityToken&&(n+="&x-cos-security-token="+r.SecurityToken),r.ClientIP&&(n+="&clientIP="+r.ClientIP),r.ClientUA&&(n+="&clientUA="+r.ClientUA),r.Token&&(n+="&token="+r.Token),a&&(n+="&"+a),setTimeout((function(){t(null,{Url:n})}))}}));return p?(s+="?"+p.Authorization+(p.SecurityToken?"&x-cos-security-token="+p.SecurityToken:""),a&&(s+="&"+a)):a&&(s+="?"+a),s},getAuth:function(e){return i.getAuth({SecretId:e.SecretId||this.options.SecretId||"",SecretKey:e.SecretKey||this.options.SecretKey||"",Bucket:e.Bucket,Region:e.Region,Method:e.Method,Key:e.Key,Query:e.Query,Headers:e.Headers,Expires:e.Expires,UseRawKey:this.options.UseRawKey,SystemClockOffset:this.options.SystemClockOffset})}};e.exports.init=function(e,t){t.transferToTaskMethod(h,"putObject"),i.each(h,(function(t,r){e.prototype[r]=i.apiWrapper(r,t),function(e,t,r){i.each(["Cors","Acl"],(function(n){if(e.slice(-n.length)===n){var o=e.slice(0,-n.length)+n.toUpperCase(),a=i.apiWrapper(e,t);r[o]=function(){a.apply(this,arguments)}}}))}(r,t,e.prototype)}))}},function(e,t,r){var n=r(0),o=r(12),i=r(31),a=r(4).EventProxy,s=r(1),c=r(8),u=new(r(32));function l(e,t){var r=e.TaskId,n=e.Bucket,c=e.Region,u=e.Key,l=e.StorageClass,f=this,h={},m=e.FileSize,g=e.SliceSize,y=Math.ceil(m/g),v=0,b=s.throttleOnProgress.call(f,m,e.onHashProgress),C=function(t,r){var n=t.length;return 0===n?r(null,!0):n>y||n>1&&Math.max(t[0].Size,t[1].Size)!==g?r(null,!1):void function o(i){if(i<n){var a=t[i];!function(t,r){var n=g*(t-1),o=Math.min(n+g,m),i=o-n;h[t]?r(0,{PartNumber:t,ETag:h[t],Size:i}):s.fileSlice(e.Body,n,o,!1,(function(e){s.getFileMd5(e,(function(e,n){if(e)return r(s.error(e));var o='"'+n+'"';h[t]=o,b({loaded:v+=i,total:m}),r(0,{PartNumber:t,ETag:o,Size:i})}))}))}(a.PartNumber,(function(e,t){t&&t.ETag===a.ETag&&t.Size===a.Size?o(i+1):r(null,!1)}))}else r(null,!0)}(0)},S=new a;S.on("error",(function(e){if(f._isRunningTask(r))return t(e)})),S.on("upload_id_available",(function(e){var r={},n=[];s.each(e.PartList,(function(e){r[e.PartNumber]=e}));for(var o=1;o<=y;o++){var i=r[o];i?(i.PartNumber=o,i.Uploaded=!0):i={PartNumber:o,ETag:null,Uploaded:!1},n.push(i)}e.PartList=n,t(null,e)})),S.on("no_available_upload_id",(function(){if(f._isRunningTask(r)){var o=s.extend({Bucket:n,Region:c,Key:u,Query:s.clone(e.Query),StorageClass:l,Body:e.Body,calledBySdk:"sliceUploadFile",tracker:e.tracker},e),i=s.clone(e.Headers);delete i["x-cos-mime-limit"],o.Headers=i,f.multipartInit(o,(function(e,n){if(f._isRunningTask(r)){if(e)return S.emit("error",e);var o=n.UploadId;if(!o)return t(s.error(new Error("no such upload id")));S.emit("upload_id_available",{UploadId:o,PartList:[]})}}))}})),S.on("has_and_check_upload_id",(function(t){t=t.reverse(),i.eachLimit(t,1,(function(t,i){f._isRunningTask(r)&&(o.using[t]?i():p.call(f,{Bucket:n,Region:c,Key:u,UploadId:t,tracker:e.tracker},(function(e,n){if(f._isRunningTask(r)){if(e)return o.removeUsing(t),S.emit("error",e);var a=n.PartList;a.forEach((function(e){e.PartNumber*=1,e.Size*=1,e.ETag=e.ETag||""})),C(a,(function(e,n){if(f._isRunningTask(r))return e?S.emit("error",e):void(n?i({UploadId:t,PartList:a}):i())}))}})))}),(function(e){f._isRunningTask(r)&&(b(null,!0),e&&e.UploadId?S.emit("upload_id_available",e):S.emit("no_available_upload_id"))}))})),S.on("seek_local_avail_upload_id",(function(t){var i=o.getFileId(e.Body,e.ChunkSize,n,u),a=o.getUploadIdList.call(f,i);i&&a?function i(l){if(l>=a.length)S.emit("has_and_check_upload_id",t);else{var d=a[l];if(!s.isInArray(t,d))return o.removeUploadId.call(f,d),void i(l+1);o.using[d]?i(l+1):p.call(f,{Bucket:n,Region:c,Key:u,UploadId:d,tracker:e.tracker},(function(e,t){f._isRunningTask(r)&&(e?(o.removeUploadId.call(f,d),i(l+1)):S.emit("upload_id_available",{UploadId:d,PartList:t.PartList}))}))}}(0):S.emit("has_and_check_upload_id",t)})),S.on("get_remote_upload_id_list",(function(){d.call(f,{Bucket:n,Region:c,Key:u,tracker:e.tracker},(function(t,i){if(f._isRunningTask(r)){if(t)return S.emit("error",t);var a=s.filter(i.UploadList,(function(e){return e.Key===u&&(!l||e.StorageClass.toUpperCase()===l.toUpperCase())})).reverse().map((function(e){return e.UploadId||e.UploadID}));if(a.length)S.emit("seek_local_avail_upload_id",a);else{var c,d=o.getFileId(e.Body,e.ChunkSize,n,u);d&&(c=o.getUploadIdList.call(f,d))&&s.each(c,(function(e){o.removeUploadId.call(f,e)})),S.emit("no_available_upload_id")}}}))})),S.emit("get_remote_upload_id_list")}function d(e,t){var r=this,n=[],o={Bucket:e.Bucket,Region:e.Region,Prefix:e.Key,calledBySdk:e.calledBySdk||"sliceUploadFile",tracker:e.tracker};!function e(){r.multipartList(o,(function(r,i){if(r)return t(r);n.push.apply(n,i.Upload||[]),"true"===i.IsTruncated?(o.KeyMarker=i.NextKeyMarker,o.UploadIdMarker=i.NextUploadIdMarker,e()):t(null,{UploadList:n})}))}()}function p(e,t){var r=this,n=[],o={Bucket:e.Bucket,Region:e.Region,Key:e.Key,UploadId:e.UploadId,calledBySdk:"sliceUploadFile",tracker:e.tracker};!function e(){r.multipartListPart(o,(function(r,i){if(r)return t(r);n.push.apply(n,i.Part||[]),"true"===i.IsTruncated?(o.PartNumberMarker=i.NextPartNumberMarker,e()):t(null,{PartList:n})}))}()}function f(e,t){var r,n=this,o=e.TaskId,a=e.Bucket,c=e.Region,l=e.Key,d=e.UploadData,p=e.FileSize,f=e.SliceSize;r=n.options.ChunkParallelLimit&&!n.options.DynamicAccelerate?Math.min(e.AsyncLimit||n.options.ChunkParallelLimit||1,256):u.getScheduledParallelLimit.bind(u);var m=e.Body,g=Math.ceil(p/f),y=0,v=e.ServerSideEncryption,b=e.Headers,C=s.filter(d.PartList,(function(e){return e.Uploaded&&(y+=e.PartNumber>=g&&p%f||f),!e.Uploaded})),S=e.onProgress;i.eachLimit(C,r,(function(t,r){if(n._isRunningTask(o)){var i=t.PartNumber,s=Math.min(p,t.PartNumber*f)-(t.PartNumber-1)*f,u=0;h.call(n,{TaskId:o,Bucket:a,Region:c,Key:l,SliceSize:f,FileSize:p,PartNumber:i,ServerSideEncryption:v,Body:m,UploadData:d,Headers:b,onProgress:function(e){y+=e.loaded-u,u=e.loaded,S({loaded:y,total:p})},tracker:e.tracker},(function(e,i){n._isRunningTask(o)&&(e||i.ETag||(e='get ETag error, please add "ETag" to CORS ExposeHeader setting.( 获取ETag失败，请在CORS ExposeHeader设置中添加ETag，请参考文档：https://cloud.tencent.com/document/product/436/13318 )'),e?y-=u:(y+=s-u,t.ETag=i.ETag),S({loaded:y,total:p}),r(e||null,i))}))}}),(function(e){if(n._isRunningTask(o))return e?t(e):void t(null,{UploadId:d.UploadId,SliceList:d.PartList})}))}function h(e,t){var r=this,n=e.TaskId,o=e.Bucket,a=e.Region,c=e.Key,l=e.FileSize,d=e.Body,p=1*e.PartNumber,f=e.SliceSize,h=e.ServerSideEncryption,m=e.UploadData,g=e.Headers||{},y=r.options.ChunkRetryTimes+1,v=f*(p-1),b=f,C=v+f;C>l&&(b=(C=l)-v);var S=["x-cos-traffic-limit","x-cos-mime-limit"],k={};s.each(g,(function(e,t){S.indexOf(t)>-1&&(k[t]=e)}));var w=m.PartList[p-1];i.retry(y,(function(t){r._isRunningTask(n)&&s.fileSlice(d,v,C,!0,(function(i){u.markStart(p),r.multipartUpload({TaskId:n,Bucket:o,Region:a,Key:c,ContentLength:b,PartNumber:p,UploadId:m.UploadId,ServerSideEncryption:h,Body:i,Headers:k,onProgress:e.onProgress,calledBySdk:"sliceUploadFile",tracker:e.tracker},(function(e,o){if(u.markEnd(p),r._isRunningTask(n))return e?(u.triggerFallback(),t(e)):(w.Uploaded=!0,t(null,o))}))}))}),(function(e,o){if(r._isRunningTask(n))return t(e,o)}))}function m(e,t){var r=e.Bucket,n=e.Region,o=e.Key,a=e.UploadId,s=e.SliceList,c=this,u=this.options.ChunkRetryTimes+1,l=e.Headers,d=s.map((function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}}));i.retry(u,(function(t){c.multipartComplete({Bucket:r,Region:n,Key:o,UploadId:a,Parts:d,Headers:l,calledBySdk:"sliceUploadFile",tracker:e.tracker},t)}),(function(e,r){t(e,r)}))}function g(e,t){var r=e.Bucket,n=e.Region,o=e.Key,a=e.AbortArray,s=e.AsyncLimit||1,c=this,u=0,l=new Array(a.length);i.eachLimit(a,s,(function(t,i){var a=u;if(o&&o!==t.Key)return l[a]={error:{KeyNotMatch:!0}},void i(null);var s=t.UploadId||t.UploadID;c.multipartAbort({Bucket:r,Region:n,Key:t.Key,Headers:e.Headers,UploadId:s},(function(e){var o={Bucket:r,Region:n,Key:t.Key,UploadId:s};l[a]={error:e,task:o},i(null)})),u++}),(function(e){if(e)return t(e);for(var r=[],n=[],o=0,i=l.length;o<i;o++){var a=l[o];a.task&&(a.error?n.push(a.task):r.push(a.task))}return t(null,{successList:r,errorList:n})}))}function y(e,t){var r=e.TaskId,n=e.Bucket,o=e.Region,a=e.Key,s=e.CopySource,c=e.UploadId,u=1*e.PartNumber,l=e.CopySourceRange,d=this.options.ChunkRetryTimes+1,p=this;i.retry(d,(function(e){p.uploadPartCopy({TaskId:r,Bucket:n,Region:o,Key:a,CopySource:s,UploadId:c,PartNumber:u,CopySourceRange:l},(function(t,r){e(t||null,r)}))}),(function(e,r){return t(e,r)}))}var v={sliceUploadFile:function(e,t){var r,n,i=this,c=new a,u=e.TaskId,d=e.Bucket,p=e.Region,h=e.Key,g=e.Body,y=e.ChunkSize||e.SliceSize||i.options.ChunkSize,v=e.AsyncLimit,b=e.StorageClass,C=e.ServerSideEncryption,S=e.onHashProgress,k=e.tracker;k&&k.setParams({chunkSize:y}),c.on("error",(function(r){if(i._isRunningTask(u))return r.UploadId=e.UploadData.UploadId||"",t(r)})),c.on("upload_complete",(function(r){var n=s.extend({UploadId:e.UploadData.UploadId||""},r);t(null,n)})),c.on("upload_slice_complete",(function(t){var a={};s.each(e.Headers,(function(e,t){var r=t.toLowerCase();0!==r.indexOf("x-cos-meta-")&&"pic-operations"!==r||(a[t]=e)})),m.call(i,{Bucket:d,Region:p,Key:h,UploadId:t.UploadId,SliceList:t.SliceList,Headers:a,tracker:k},(function(e,a){if(i._isRunningTask(u)){if(o.removeUsing(t.UploadId),e)return n(null,!0),c.emit("error",e);o.removeUploadId.call(i,t.UploadId),n({loaded:r,total:r},!0),c.emit("upload_complete",a)}}))})),c.on("get_upload_data_finish",(function(t){var a=o.getFileId(g,e.ChunkSize,d,h);a&&o.saveUploadId.call(i,a,t.UploadId,i.options.UploadIdCacheLimit),o.setUsing(t.UploadId),n(null,!0),f.call(i,{TaskId:u,Bucket:d,Region:p,Key:h,Body:g,FileSize:r,SliceSize:y,AsyncLimit:v,ServerSideEncryption:C,UploadData:t,Headers:e.Headers,onProgress:n,tracker:k},(function(e,t){if(i._isRunningTask(u))return e?(n(null,!0),c.emit("error",e)):void c.emit("upload_slice_complete",t)}))})),c.on("get_file_size_finish",(function(){if(n=s.throttleOnProgress.call(i,r,e.onProgress),e.UploadData.UploadId)c.emit("get_upload_data_finish",e.UploadData);else{var t=s.extend({TaskId:u,Bucket:d,Region:p,Key:h,Headers:e.Headers,StorageClass:b,Body:g,FileSize:r,SliceSize:y,onHashProgress:S,tracker:k},e);l.call(i,t,(function(t,r){if(i._isRunningTask(u)){if(t)return c.emit("error",t);e.UploadData.UploadId=r.UploadId,e.UploadData.PartList=r.PartList,c.emit("get_upload_data_finish",e.UploadData)}}))}})),r=e.ContentLength,delete e.ContentLength,!e.Headers&&(e.Headers={}),s.each(e.Headers,(function(t,r){"content-length"===r.toLowerCase()&&delete e.Headers[r]})),function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],n=1048576,o=0;o<t.length&&!(r/(n=1024*t[o]*1024)<=i.options.MaxPartNumber);o++);e.ChunkSize=e.SliceSize=y=Math.max(y,n)}(),0===r?(e.Body="",e.ContentLength=0,e.SkipTask=!0,i.putObject(e,t)):c.emit("get_file_size_finish")},abortUploadTask:function(e,t){var r=e.Bucket,n=e.Region,o=e.Key,i=e.UploadId,c=e.Level||"task",u=e.AsyncLimit,l=this,p=new a;if(p.on("error",(function(e){return t(e)})),p.on("get_abort_array",(function(i){g.call(l,{Bucket:r,Region:n,Key:o,Headers:e.Headers,AsyncLimit:u,AbortArray:i},t)})),"bucket"===c)d.call(l,{Bucket:r,Region:n,calledBySdk:"abortUploadTask"},(function(e,r){if(e)return t(e);p.emit("get_abort_array",r.UploadList||[])}));else if("file"===c){if(!o)return t(s.error(new Error("abort_upload_task_no_key")));d.call(l,{Bucket:r,Region:n,Key:o,calledBySdk:"abortUploadTask"},(function(e,r){if(e)return t(e);p.emit("get_abort_array",r.UploadList||[])}))}else{if("task"!==c)return t(s.error(new Error("abort_unknown_level")));if(!i)return t(s.error(new Error("abort_upload_task_no_id")));if(!o)return t(s.error(new Error("abort_upload_task_no_key")));p.emit("get_abort_array",[{Key:o,UploadId:i}])}},uploadFile:function(e,t){var r=this,o=void 0===e.SliceSize?r.options.SliceSize:e.SliceSize,i=[],a=e.Body,u=a.size||a.length||0,l={TaskId:""};if(r.options.EnableTracker){var d=r.options.UseAccelerate||"string"==typeof r.options.Domain&&r.options.Domain.includes("accelerate.");e.tracker=new c({bucket:e.Bucket,region:e.Region,apiName:"uploadFile",fileKey:e.Key,fileSize:u,accelerate:d,deepTracker:r.options.DeepTracker,customId:r.options.CustomId,delay:r.options.TrackerDelay})}s.each(e,(function(e,t){"object"!==n(e)&&"function"!=typeof e&&(l[t]=e)}));var p=e.onTaskReady;e.onTaskReady=function(e){l.TaskId=e,p&&p(e)};var f=u>o?"sliceUploadFile":"putObject",h=e.onFileFinish;i.push({api:f,params:e,callback:function(r,n){e.tracker&&e.tracker.formatResult(r,n),h&&h(r,n,l),t&&t(r,n)}}),r._addTasks(i)},uploadFiles:function(e,t){var r=this,o=void 0===e.SliceSize?r.options.SliceSize:e.SliceSize,i=0,a=0,u=s.throttleOnProgress.call(r,a,e.onProgress),l=e.files.length,d=e.onFileFinish,p=Array(l),f=function(e,r,n){u(null,!0),d&&d(e,r,n),p[n.Index]={options:n,error:e,data:r},--l<=0&&t&&t(null,{files:p})},h=[];s.each(e.files,(function(e,t){!function(){var l=e.Body,d=l.size||l.length||0,p={Index:t,TaskId:""};if(i+=d,r.options.EnableTracker){var m=r.options.UseAccelerate||"string"==typeof r.options.Domain&&r.options.Domain.includes("accelerate.");e.tracker=new c({bucket:e.Bucket,region:e.Region,apiName:"uploadFiles",fileKey:e.Key,fileSize:d,accelerate:m,deepTracker:r.options.DeepTracker,customId:r.options.CustomId,delay:r.options.TrackerDelay})}s.each(e,(function(e,t){"object"!==n(e)&&"function"!=typeof e&&(p[t]=e)}));var g=e.onTaskReady;e.onTaskReady=function(e){p.TaskId=e,g&&g(e)};var y=0,v=e.onProgress;e.onProgress=function(e){a=a-y+e.loaded,y=e.loaded,v&&v(e),u({loaded:a,total:i})};var b=d>o?"sliceUploadFile":"putObject",C=e.onFileFinish;h.push({api:b,params:e,callback:function(t,r){e.tracker&&e.tracker.formatResult(t,r),C&&C(t,r),f&&f(t,r,p)}})}()})),r._addTasks(h)},sliceCopyFile:function(e,t){var r=new a,n=this,c=e.Bucket,u=e.Region,l=e.Key,d=e.CopySource,f=s.getSourceParams.call(this,d);if(f){var h=f.Bucket,m=f.Region,g=decodeURIComponent(f.Key),v=void 0===e.CopySliceSize?n.options.CopySliceSize:e.CopySliceSize;v=Math.max(0,v);var b,C,S=e.CopyChunkSize||this.options.CopyChunkSize,k=this.options.CopyChunkParallelLimit,w=this.options.ChunkRetryTimes+1,T=0,R=0,_={},E={},x={};r.on("copy_slice_complete",(function(r){var a={};s.each(e.Headers,(function(e,t){0===t.toLowerCase().indexOf("x-cos-meta-")&&(a[t]=e)}));var d=s.map(r.PartList,(function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}}));i.retry(w,(function(e){n.multipartComplete({Bucket:c,Region:u,Key:l,UploadId:r.UploadId,Parts:d,calledBySdk:"sliceCopyFile"},e)}),(function(e,n){if(o.removeUsing(r.UploadId),e)return C(null,!0),t(e);o.removeUploadId(r.UploadId),C({loaded:b,total:b},!0),t(null,n)}))})),r.on("get_copy_data_finish",(function(e){var a=o.getCopyFileId(d,_,S,c,l);a&&o.saveUploadId(a,e.UploadId,n.options.UploadIdCacheLimit),o.setUsing(e.UploadId);var p=s.filter(e.PartList,(function(e){return e.Uploaded&&(R+=e.PartNumber>=T&&b%S||S),!e.Uploaded}));i.eachLimit(p,k,(function(t,r){var o=t.PartNumber,a=t.CopySourceRange,s=t.end-t.start;i.retry(w,(function(t){y.call(n,{Bucket:c,Region:u,Key:l,CopySource:d,UploadId:e.UploadId,PartNumber:o,CopySourceRange:a},t)}),(function(e,n){if(e)return r(e);C({loaded:R+=s,total:b}),t.ETag=n.ETag,r(e||null,n)}))}),(function(n){if(n)return o.removeUsing(e.UploadId),C(null,!0),t(n);r.emit("copy_slice_complete",e)}))})),r.on("get_chunk_size_finish",(function(){var i=function(){n.multipartInit({Bucket:c,Region:u,Key:l,Headers:x},(function(n,o){if(n)return t(n);e.UploadId=o.UploadId,r.emit("get_copy_data_finish",{UploadId:e.UploadId,PartList:e.PartList})}))},a=o.getCopyFileId(d,_,S,c,l),f=o.getUploadIdList(a);if(!a||!f)return i();!function t(a){if(a>=f.length)return i();var d=f[a];if(o.using[d])return t(a+1);p.call(n,{Bucket:c,Region:u,Key:l,UploadId:d},(function(n,i){if(n)o.removeUploadId(d),t(a+1);else{if(o.using[d])return t(a+1);var c={},u=0;s.each(i.PartList,(function(e){var t=parseInt(e.Size),r=u+t-1;c[e.PartNumber+"|"+u+"|"+r]=e.ETag,u+=t})),s.each(e.PartList,(function(e){var t=c[e.PartNumber+"|"+e.start+"|"+e.end];t&&(e.ETag=t,e.Uploaded=!0)})),r.emit("get_copy_data_finish",{UploadId:d,PartList:e.PartList})}}))}(0)})),r.on("get_file_size_finish",(function(){if(function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],r=1048576,o=0;o<t.length&&!(b/(r=1024*t[o]*1024)<=n.options.MaxPartNumber);o++);e.ChunkSize=S=Math.max(S,r),T=Math.ceil(b/S);for(var i=[],a=1;a<=T;a++){var s=(a-1)*S,c=a*S<b?a*S-1:b-1,u={PartNumber:a,start:s,end:c,CopySourceRange:"bytes="+s+"-"+c};i.push(u)}e.PartList=i}(),(x="Replaced"===e.Headers["x-cos-metadata-directive"]?e.Headers:E)["x-cos-storage-class"]=e.Headers["x-cos-storage-class"]||E["x-cos-storage-class"],x=s.clearKey(x),"ARCHIVE"===E["x-cos-storage-class"]||"DEEP_ARCHIVE"===E["x-cos-storage-class"]){var o=E["x-cos-restore"];if(!o||'ongoing-request="true"'===o)return void t(s.error(new Error("Unrestored archive object is not allowed to be copied")))}delete x["x-cos-copy-source"],delete x["x-cos-metadata-directive"],delete x["x-cos-copy-source-If-Modified-Since"],delete x["x-cos-copy-source-If-Unmodified-Since"],delete x["x-cos-copy-source-If-Match"],delete x["x-cos-copy-source-If-None-Match"],r.emit("get_chunk_size_finish")})),n.headObject({Bucket:h,Region:m,Key:g},(function(o,i){if(o)o.statusCode&&404===o.statusCode?t(s.error(o,{ErrorStatus:g+" Not Exist"})):t(o);else if(void 0!==(b=e.FileSize=i.headers["content-length"])&&b)if(C=s.throttleOnProgress.call(n,b,e.onProgress),b<=v)e.Headers["x-cos-metadata-directive"]||(e.Headers["x-cos-metadata-directive"]="Copy"),n.putObjectCopy(e,(function(e,r){if(e)return C(null,!0),t(e);C({loaded:b,total:b},!0),t(e,r)}));else{var a=i.headers;_=a,E={"Cache-Control":a["cache-control"],"Content-Disposition":a["content-disposition"],"Content-Encoding":a["content-encoding"],"Content-Type":a["content-type"],Expires:a.expires,"x-cos-storage-class":a["x-cos-storage-class"]},s.each(a,(function(e,t){0===t.indexOf("x-cos-meta-")&&t.length>11&&(E[t]=e)})),r.emit("get_file_size_finish")}else t(s.error(new Error('get Content-Length error, please add "Content-Length" to CORS ExposeHeader setting.（ 获取Content-Length失败，请在CORS ExposeHeader设置中添加Content-Length，请参考文档：https://cloud.tencent.com/document/product/436/13318 ）')))}))}else t(s.error(new Error("CopySource format error")))}};e.exports.init=function(e,t){t.transferToTaskMethod(v,"sliceUploadFile"),s.each(v,(function(t,r){e.prototype[r]=s.apiWrapper(r,t)}))}},function(e,t,r){var n=r(16);e.exports=n},function(e,t,r){"use strict";var n=r(1),o=r(4),i=r(11),a=r(13),s=r(14),c=r(3),u={AppId:"",SecretId:"",SecretKey:"",SecurityToken:"",ChunkRetryTimes:2,FileParallelLimit:3,ChunkParallelLimit:3,ChunkSize:1048576,SliceSize:1048576,CopyChunkParallelLimit:20,CopyChunkSize:10485760,CopySliceSize:10485760,MaxPartNumber:1e4,ProgressInterval:1e3,Domain:"",ServiceDomain:"",Protocol:"",CompatibilityMode:!1,ForcePathStyle:!1,UseRawKey:!1,Timeout:0,CorrectClockSkew:!0,SystemClockOffset:0,UploadCheckContentMd5:!1,UploadQueueSize:1e4,UploadAddMetaMd5:!1,UploadIdCacheLimit:50,UseAccelerate:!1,ForceSignHost:!0,EnableTracker:!1,DeepTracker:!1,TrackerDelay:5e3,CustomId:""},l=function(e){this.options=n.extend(n.clone(u),e||{}),this.options.FileParallelLimit=Math.max(1,this.options.FileParallelLimit),this.options.ChunkParallelLimit=Math.max(1,this.options.ChunkParallelLimit),this.options.ChunkRetryTimes=Math.max(0,this.options.ChunkRetryTimes),this.options.ChunkSize=Math.max(1048576,this.options.ChunkSize),this.options.CopyChunkParallelLimit=Math.max(1,this.options.CopyChunkParallelLimit),this.options.CopyChunkSize=Math.max(1048576,this.options.CopyChunkSize),this.options.CopySliceSize=Math.max(0,this.options.CopySliceSize),this.options.MaxPartNumber=Math.max(1024,Math.min(1e4,this.options.MaxPartNumber)),this.options.Timeout=Math.max(0,this.options.Timeout),this.options.AppId,this.options.SecretId&&this.options.SecretId.indexOf(" "),this.options.SecretKey&&this.options.SecretKey.indexOf(" "),n.isNode(),o.init(this),i.init(this)};a.init(l,i),s.init(l,i),l.util={md5:n.md5,xml2json:n.xml2json,json2xml:n.json2xml},l.getAuthorization=n.getAuth,l.version=c.version,e.exports=l},function(module,exports,__nested_webpack_require_119206__){(function(process,global,module){var __WEBPACK_AMD_DEFINE_RESULT__,_typeof=__nested_webpack_require_119206__(0);(function(){"use strict";var ERROR="input is invalid type",WINDOW="object"===("undefined"==typeof window?"undefined":_typeof(window)),root=WINDOW?window:{};root.JS_MD5_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"===("undefined"==typeof self?"undefined":_typeof(self)),NODE_JS=!root.JS_MD5_NO_NODE_JS&&"object"===(void 0===process?"undefined":_typeof(process))&&process.versions&&process.versions.node;NODE_JS?root=global:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_MD5_NO_COMMON_JS&&"object"===_typeof(module)&&module.exports,AMD=__nested_webpack_require_119206__(19),ARRAY_BUFFER=!root.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[128,32768,8388608,-2147483648],SHIFT=[0,8,16,24],OUTPUT_TYPES=["hex","array","digest","buffer","arrayBuffer","base64"],BASE64_ENCODE_CHAR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),blocks=[],buffer8;if(ARRAY_BUFFER){var buffer=new ArrayBuffer(68);buffer8=new Uint8Array(buffer),blocks=new Uint32Array(buffer)}!root.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),!ARRAY_BUFFER||!root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(e){return"object"===_typeof(e)&&e.buffer&&e.buffer.constructor===ArrayBuffer});var createOutputMethod=function(e){return function(t,r){return new Md5(!0).update(t,r)[e]()}},createMethod=function(){var e=createOutputMethod("hex");NODE_JS&&(e=nodeWrap(e)),e.getCtx=e.create=function(){return new Md5},e.update=function(t){return e.create().update(t)};for(var t=0;t<OUTPUT_TYPES.length;++t){var r=OUTPUT_TYPES[t];e[r]=createOutputMethod(r)}return e},nodeWrap=function nodeWrap(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(e){if("string"==typeof e)return crypto.createHash("md5").update(e,"utf8").digest("hex");if(null==e)throw ERROR;return e.constructor===ArrayBuffer&&(e=new Uint8Array(e)),Array.isArray(e)||ArrayBuffer.isView(e)||e.constructor===Buffer?crypto.createHash("md5").update(new Buffer(e)).digest("hex"):method(e)};return nodeMethod};function Md5(e){if(e)blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks,this.buffer8=buffer8;else if(ARRAY_BUFFER){var t=new ArrayBuffer(68);this.buffer8=new Uint8Array(t),this.blocks=new Uint32Array(t)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Md5.prototype.update=function(e,t){if(!this.finalized){for(var r,n,o=0,i=e.length,a=this.blocks,s=this.buffer8;o<i;){if(this.hashed&&(this.hashed=!1,a[0]=a[16],a[16]=a[1]=a[2]=a[3]=a[4]=a[5]=a[6]=a[7]=a[8]=a[9]=a[10]=a[11]=a[12]=a[13]=a[14]=a[15]=0),ARRAY_BUFFER)for(n=this.start;o<i&&n<64;++o)r=e.charCodeAt(o),t||r<128?s[n++]=r:r<2048?(s[n++]=192|r>>6,s[n++]=128|63&r):r<55296||r>=57344?(s[n++]=224|r>>12,s[n++]=128|r>>6&63,s[n++]=128|63&r):(r=65536+((1023&r)<<10|1023&e.charCodeAt(++o)),s[n++]=240|r>>18,s[n++]=128|r>>12&63,s[n++]=128|r>>6&63,s[n++]=128|63&r);else for(n=this.start;o<i&&n<64;++o)r=e.charCodeAt(o),t||r<128?a[n>>2]|=r<<SHIFT[3&n++]:r<2048?(a[n>>2]|=(192|r>>6)<<SHIFT[3&n++],a[n>>2]|=(128|63&r)<<SHIFT[3&n++]):r<55296||r>=57344?(a[n>>2]|=(224|r>>12)<<SHIFT[3&n++],a[n>>2]|=(128|r>>6&63)<<SHIFT[3&n++],a[n>>2]|=(128|63&r)<<SHIFT[3&n++]):(r=65536+((1023&r)<<10|1023&e.charCodeAt(++o)),a[n>>2]|=(240|r>>18)<<SHIFT[3&n++],a[n>>2]|=(128|r>>12&63)<<SHIFT[3&n++],a[n>>2]|=(128|r>>6&63)<<SHIFT[3&n++],a[n>>2]|=(128|63&r)<<SHIFT[3&n++]);this.lastByteIndex=n,this.bytes+=n-this.start,n>=64?(this.start=n-64,this.hash(),this.hashed=!0):this.start=n}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Md5.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[t>>2]|=EXTRA[3&t],t>=56&&(this.hashed||this.hash(),e[0]=e[16],e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.bytes<<3,e[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},Md5.prototype.hash=function(){var e,t,r,n,o,i,a=this.blocks;this.first?t=((t=((e=((e=a[0]-680876937)<<7|e>>>25)-271733879<<0)^(r=((r=(-271733879^(n=((n=(-1732584194^2004318071&e)+a[1]-117830708)<<12|n>>>20)+e<<0)&(-271733879^e))+a[2]-1126478375)<<17|r>>>15)+n<<0)&(n^e))+a[3]-1316259209)<<22|t>>>10)+r<<0:(e=this.h0,t=this.h1,r=this.h2,t=((t+=((e=((e+=((n=this.h3)^t&(r^n))+a[0]-680876936)<<7|e>>>25)+t<<0)^(r=((r+=(t^(n=((n+=(r^e&(t^r))+a[1]-389564586)<<12|n>>>20)+e<<0)&(e^t))+a[2]+606105819)<<17|r>>>15)+n<<0)&(n^e))+a[3]-1044525330)<<22|t>>>10)+r<<0),t=((t+=((e=((e+=(n^t&(r^n))+a[4]-176418897)<<7|e>>>25)+t<<0)^(r=((r+=(t^(n=((n+=(r^e&(t^r))+a[5]+1200080426)<<12|n>>>20)+e<<0)&(e^t))+a[6]-1473231341)<<17|r>>>15)+n<<0)&(n^e))+a[7]-45705983)<<22|t>>>10)+r<<0,t=((t+=((e=((e+=(n^t&(r^n))+a[8]+1770035416)<<7|e>>>25)+t<<0)^(r=((r+=(t^(n=((n+=(r^e&(t^r))+a[9]-1958414417)<<12|n>>>20)+e<<0)&(e^t))+a[10]-42063)<<17|r>>>15)+n<<0)&(n^e))+a[11]-1990404162)<<22|t>>>10)+r<<0,t=((t+=((e=((e+=(n^t&(r^n))+a[12]+1804603682)<<7|e>>>25)+t<<0)^(r=((r+=(t^(n=((n+=(r^e&(t^r))+a[13]-40341101)<<12|n>>>20)+e<<0)&(e^t))+a[14]-1502002290)<<17|r>>>15)+n<<0)&(n^e))+a[15]+1236535329)<<22|t>>>10)+r<<0,t=((t+=((n=((n+=(t^r&((e=((e+=(r^n&(t^r))+a[1]-165796510)<<5|e>>>27)+t<<0)^t))+a[6]-1069501632)<<9|n>>>23)+e<<0)^e&((r=((r+=(e^t&(n^e))+a[11]+643717713)<<14|r>>>18)+n<<0)^n))+a[0]-373897302)<<20|t>>>12)+r<<0,t=((t+=((n=((n+=(t^r&((e=((e+=(r^n&(t^r))+a[5]-701558691)<<5|e>>>27)+t<<0)^t))+a[10]+38016083)<<9|n>>>23)+e<<0)^e&((r=((r+=(e^t&(n^e))+a[15]-660478335)<<14|r>>>18)+n<<0)^n))+a[4]-405537848)<<20|t>>>12)+r<<0,t=((t+=((n=((n+=(t^r&((e=((e+=(r^n&(t^r))+a[9]+568446438)<<5|e>>>27)+t<<0)^t))+a[14]-1019803690)<<9|n>>>23)+e<<0)^e&((r=((r+=(e^t&(n^e))+a[3]-187363961)<<14|r>>>18)+n<<0)^n))+a[8]+1163531501)<<20|t>>>12)+r<<0,t=((t+=((n=((n+=(t^r&((e=((e+=(r^n&(t^r))+a[13]-1444681467)<<5|e>>>27)+t<<0)^t))+a[2]-51403784)<<9|n>>>23)+e<<0)^e&((r=((r+=(e^t&(n^e))+a[7]+1735328473)<<14|r>>>18)+n<<0)^n))+a[12]-1926607734)<<20|t>>>12)+r<<0,t=((t+=((i=(n=((n+=((o=t^r)^(e=((e+=(o^n)+a[5]-378558)<<4|e>>>28)+t<<0))+a[8]-2022574463)<<11|n>>>21)+e<<0)^e)^(r=((r+=(i^t)+a[11]+1839030562)<<16|r>>>16)+n<<0))+a[14]-35309556)<<23|t>>>9)+r<<0,t=((t+=((i=(n=((n+=((o=t^r)^(e=((e+=(o^n)+a[1]-1530992060)<<4|e>>>28)+t<<0))+a[4]+1272893353)<<11|n>>>21)+e<<0)^e)^(r=((r+=(i^t)+a[7]-155497632)<<16|r>>>16)+n<<0))+a[10]-1094730640)<<23|t>>>9)+r<<0,t=((t+=((i=(n=((n+=((o=t^r)^(e=((e+=(o^n)+a[13]+681279174)<<4|e>>>28)+t<<0))+a[0]-358537222)<<11|n>>>21)+e<<0)^e)^(r=((r+=(i^t)+a[3]-722521979)<<16|r>>>16)+n<<0))+a[6]+76029189)<<23|t>>>9)+r<<0,t=((t+=((i=(n=((n+=((o=t^r)^(e=((e+=(o^n)+a[9]-640364487)<<4|e>>>28)+t<<0))+a[12]-421815835)<<11|n>>>21)+e<<0)^e)^(r=((r+=(i^t)+a[15]+530742520)<<16|r>>>16)+n<<0))+a[2]-995338651)<<23|t>>>9)+r<<0,t=((t+=((n=((n+=(t^((e=((e+=(r^(t|~n))+a[0]-198630844)<<6|e>>>26)+t<<0)|~r))+a[7]+1126891415)<<10|n>>>22)+e<<0)^((r=((r+=(e^(n|~t))+a[14]-1416354905)<<15|r>>>17)+n<<0)|~e))+a[5]-57434055)<<21|t>>>11)+r<<0,t=((t+=((n=((n+=(t^((e=((e+=(r^(t|~n))+a[12]+1700485571)<<6|e>>>26)+t<<0)|~r))+a[3]-1894986606)<<10|n>>>22)+e<<0)^((r=((r+=(e^(n|~t))+a[10]-1051523)<<15|r>>>17)+n<<0)|~e))+a[1]-2054922799)<<21|t>>>11)+r<<0,t=((t+=((n=((n+=(t^((e=((e+=(r^(t|~n))+a[8]+1873313359)<<6|e>>>26)+t<<0)|~r))+a[15]-30611744)<<10|n>>>22)+e<<0)^((r=((r+=(e^(n|~t))+a[6]-1560198380)<<15|r>>>17)+n<<0)|~e))+a[13]+1309151649)<<21|t>>>11)+r<<0,t=((t+=((n=((n+=(t^((e=((e+=(r^(t|~n))+a[4]-145523070)<<6|e>>>26)+t<<0)|~r))+a[11]-1120210379)<<10|n>>>22)+e<<0)^((r=((r+=(e^(n|~t))+a[2]+718787259)<<15|r>>>17)+n<<0)|~e))+a[9]-343485551)<<21|t>>>11)+r<<0,this.first?(this.h0=e+1732584193<<0,this.h1=t-271733879<<0,this.h2=r-1732584194<<0,this.h3=n+271733878<<0,this.first=!1):(this.h0=this.h0+e<<0,this.h1=this.h1+t<<0,this.h2=this.h2+r<<0,this.h3=this.h3+n<<0)},Md5.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,n=this.h3;return HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]+HEX_CHARS[n>>4&15]+HEX_CHARS[15&n]+HEX_CHARS[n>>12&15]+HEX_CHARS[n>>8&15]+HEX_CHARS[n>>20&15]+HEX_CHARS[n>>16&15]+HEX_CHARS[n>>28&15]+HEX_CHARS[n>>24&15]},Md5.prototype.toString=Md5.prototype.hex,Md5.prototype.digest=function(e){if("hex"===e)return this.hex();this.finalize();var t=this.h0,r=this.h1,n=this.h2,o=this.h3;return[255&t,t>>8&255,t>>16&255,t>>24&255,255&r,r>>8&255,r>>16&255,r>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255,255&o,o>>8&255,o>>16&255,o>>24&255]},Md5.prototype.array=Md5.prototype.digest,Md5.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(16),t=new Uint32Array(e);return t[0]=this.h0,t[1]=this.h1,t[2]=this.h2,t[3]=this.h3,e},Md5.prototype.buffer=Md5.prototype.arrayBuffer,Md5.prototype.base64=function(){for(var e,t,r,n="",o=this.array(),i=0;i<15;)e=o[i++],t=o[i++],r=o[i++],n+=BASE64_ENCODE_CHAR[e>>>2]+BASE64_ENCODE_CHAR[63&(e<<4|t>>>4)]+BASE64_ENCODE_CHAR[63&(t<<2|r>>>6)]+BASE64_ENCODE_CHAR[63&r];return e=o[i],n+(BASE64_ENCODE_CHAR[e>>>2]+BASE64_ENCODE_CHAR[e<<4&63]+"==")};var exports=createMethod();COMMON_JS?module.exports=exports:(root.md5=exports,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__nested_webpack_require_119206__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))})()}).call(this,__nested_webpack_require_119206__(5),__nested_webpack_require_119206__(18),__nested_webpack_require_119206__(6)(module))},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t){(function(t){e.exports=t}).call(this,{})},function(e,t,r){(function(e){var t,n,o,i,a,s,c,u=r(0),l=l||function(e,t){var r={},n=r.lib={},o=function(){},i=n.Base={extend:function(e){o.prototype=this;var t=new o;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=n.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes;if(e=e.sigBytes,this.clamp(),n%4)for(var o=0;o<e;o++)t[n+o>>>2]|=(r[o>>>2]>>>24-o%4*8&255)<<24-(n+o)%4*8;else if(65535<r.length)for(o=0;o<e;o+=4)t[n+o>>>2]=r[o>>>2];else t.push.apply(t,r);return this.sigBytes+=e,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var r=[],n=0;n<t;n+=4)r.push(4294967296*e.random()|0);return new a.init(r,t)}}),s=r.enc={},c=s.Hex={stringify:function(e){var t=e.words;e=e.sigBytes;for(var r=[],n=0;n<e;n++){var o=t[n>>>2]>>>24-n%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new a.init(r,t/2)}},u=s.Latin1={stringify:function(e){var t=e.words;e=e.sigBytes;for(var r=[],n=0;n<e;n++)r.push(String.fromCharCode(t[n>>>2]>>>24-n%4*8&255));return r.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new a.init(r,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},d=n.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r=this._data,n=r.words,o=r.sigBytes,i=this.blockSize,s=o/(4*i);if(t=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,o=e.min(4*t,o),t){for(var c=0;c<t;c+=i)this._doProcessBlock(n,c);c=n.splice(0,t),r.sigBytes-=o}return new a.init(c,o)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});n.Hasher=d.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new p.HMAC.init(e,r).finalize(t)}}});var p=r.algo={};return r}(Math);n=(a=(t=l).lib).WordArray,o=a.Hasher,i=[],a=t.algo.SHA1=o.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],o=r[1],a=r[2],s=r[3],c=r[4],u=0;80>u;u++){if(16>u)i[u]=0|e[t+u];else{var l=i[u-3]^i[u-8]^i[u-14]^i[u-16];i[u]=l<<1|l>>>31}l=(n<<5|n>>>27)+c+i[u],l=20>u?l+(1518500249+(o&a|~o&s)):40>u?l+(1859775393+(o^a^s)):60>u?l+((o&a|o&s|a&s)-1894007588):l+((o^a^s)-899497514),c=s,s=a,a=o<<30|o>>>2,o=n,n=l}r[0]=r[0]+n|0,r[1]=r[1]+o|0,r[2]=r[2]+a|0,r[3]=r[3]+s|0,r[4]=r[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(n+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}}),t.SHA1=o._createHelper(a),t.HmacSHA1=o._createHmacHelper(a),function(){var e=l,t=e.enc.Utf8;e.algo.HMAC=e.lib.Base.extend({init:function(e,r){e=this._hasher=new e.init,"string"==typeof r&&(r=t.parse(r));var n=e.blockSize,o=4*n;r.sigBytes>o&&(r=e.finalize(r)),r.clamp();for(var i=this._oKey=r.clone(),a=this._iKey=r.clone(),s=i.words,c=a.words,u=0;u<n;u++)s[u]^=1549556828,c[u]^=909522486;i.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher;return e=t.finalize(e),t.reset(),t.finalize(this._oKey.clone().concat(e))}})}(),c=(s=l).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var o=[],i=0;i<r;i+=3)for(var a=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<r;s++)o.push(n.charAt(a>>>6*(3-s)&63));var c=n.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,r=this._map,n=r.charAt(64);if(n){var o=e.indexOf(n);-1!=o&&(t=o)}for(var i=[],a=0,s=0;s<t;s++)if(s%4){var u=r.indexOf(e.charAt(s-1))<<s%4*2,l=r.indexOf(e.charAt(s))>>>6-s%4*2;i[a>>>2]|=(u|l)<<24-a%4*8,a++}return c.create(i,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},"object"===u(e)?e.exports=l:window.CryptoJS=l}).call(this,r(6)(e))},function(e,t,r){var n=r(22).DOMParser,o=function(){this.version="1.3.5";var e={mergeCDATA:!0,normalize:!0,stripElemPrefix:!0},t=new RegExp(/(?!xmlns)^.*:/);return new RegExp(/^\s+|\s+$/g),this.grokType=function(e){return/^\s*$/.test(e)?null:/^(?:true|false)$/i.test(e)?"true"===e.toLowerCase():isFinite(e)?parseFloat(e):e},this.parseString=function(e,t){if(e){var r=this.stringToXML(e);return r.getElementsByTagName("parsererror").length?null:this.parseXML(r,t)}return null},this.parseXML=function(r,n){for(var i in n)e[i]=n[i];var a={},s=0,c="";if(r.childNodes.length)for(var u,l,d,p=0;p<r.childNodes.length;p++)4===(u=r.childNodes.item(p)).nodeType?e.mergeCDATA&&(c+=u.nodeValue):3===u.nodeType?c+=u.nodeValue:1===u.nodeType&&(0===s&&(a={}),l=e.stripElemPrefix?u.nodeName.replace(t,""):u.nodeName,d=o.parseXML(u),a.hasOwnProperty(l)?(a[l].constructor!==Array&&(a[l]=[a[l]]),a[l].push(d)):(a[l]=d,s++));return Object.keys(a).length||(a=c||""),a},this.xmlToString=function(e){try{return e.xml?e.xml:(new XMLSerializer).serializeToString(e)}catch(e){return null}},this.stringToXML=function(e){try{var t=null;return window.DOMParser?t=(new n).parseFromString(e,"text/xml"):((t=new ActiveXObject("Microsoft.XMLDOM")).async=!1,t.loadXML(e),t)}catch(e){return null}},this}.call({});e.exports=function(e){return o.parseString(e)}},function(e,t,r){var n=r(7);t.DOMImplementation=n.DOMImplementation,t.XMLSerializer=n.XMLSerializer,t.DOMParser=r(23).DOMParser},function(e,t,r){var n=r(2),o=r(7),i=r(24),a=r(25),s=o.DOMImplementation,c=n.NAMESPACE,u=a.ParseError,l=a.XMLReader;function d(e){return e.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028]/g,"\n")}function p(e){this.options=e||{locator:{}}}function f(){this.cdata=!1}function h(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function m(e,t,r){return"string"==typeof e?e.substr(t,r):e.length>=t+r||t?new java.lang.String(e,t,r)+"":e}function g(e,t){e.currentElement?e.currentElement.appendChild(t):e.doc.appendChild(t)}p.prototype.parseFromString=function(e,t){var r=this.options,n=new l,o=r.domBuilder||new f,a=r.errorHandler,s=r.locator,u=r.xmlns||{},p=/\/x?html?$/.test(t),h=p?i.HTML_ENTITIES:i.XML_ENTITIES;s&&o.setDocumentLocator(s),n.errorHandler=function(e,t,r){if(!e){if(t instanceof f)return t;e=t}var n={},o=e instanceof Function;function i(t){var i=e[t];!i&&o&&(i=2==e.length?function(r){e(t,r)}:e),n[t]=i&&function(e){i("[xmldom "+t+"]\t"+e+function(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}(r))}||function(){}}return r=r||{},i("warning"),i("error"),i("fatalError"),n}(a,o,s),n.domBuilder=r.domBuilder||o,p&&(u[""]=c.HTML),u.xml=u.xml||c.XML;var m=r.normalizeLineEndings||d;return e&&"string"==typeof e?n.parse(m(e),u,h):n.errorHandler.error("invalid doc source"),o.doc},f.prototype={startDocument:function(){this.doc=(new s).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,r,n){var o=this.doc,i=o.createElementNS(e,r||t),a=n.length;g(this,i),this.currentElement=i,this.locator&&h(this.locator,i);for(var s=0;s<a;s++){e=n.getURI(s);var c=n.getValue(s),u=(r=n.getQName(s),o.createAttributeNS(e,r));this.locator&&h(n.getLocator(s),u),u.value=u.nodeValue=c,i.setAttributeNode(u)}},endElement:function(e,t,r){var n=this.currentElement;n.tagName,this.currentElement=n.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){var r=this.doc.createProcessingInstruction(e,t);this.locator&&h(this.locator,r),g(this,r)},ignorableWhitespace:function(e,t,r){},characters:function(e,t,r){if(e=m.apply(this,arguments)){if(this.cdata)var n=this.doc.createCDATASection(e);else n=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(n):/^\s*$/.test(e)&&this.doc.appendChild(n),this.locator&&h(this.locator,n)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,r){e=m.apply(this,arguments);var n=this.doc.createComment(e);this.locator&&h(this.locator,n),g(this,n)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,r){var n=this.doc.implementation;if(n&&n.createDocumentType){var o=n.createDocumentType(e,t,r);this.locator&&h(this.locator,o),g(this,o),this.doc.doctype=o}},warning:function(e){},error:function(e){},fatalError:function(e){throw new u(e,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,(function(e){f.prototype[e]=function(){return null}})),t.__DOMHandler=f,t.normalizeLineEndings=d,t.DOMParser=p},function(e,t,r){var n=r(2).freeze;t.XML_ENTITIES=n({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),t.HTML_ENTITIES=n({lt:"<",gt:">",amp:"&",quot:'"',apos:"'",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",times:"×",divide:"÷",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",euro:"€",trade:"™",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"}),t.entityMap=t.HTML_ENTITIES},function(e,t,r){var n=r(2).NAMESPACE,o=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,i=new RegExp("[\\-\\.0-9"+o.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),a=new RegExp("^"+o.source+i.source+"*(?::"+o.source+i.source+"*)?$");function s(e,t){this.message=e,this.locator=t,Error.captureStackTrace&&Error.captureStackTrace(this,s)}function c(){}function u(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function l(e,t,r,o,i,a){function s(e,t,n){r.attributeNames.hasOwnProperty(e)&&a.fatalError("Attribute "+e+" redefined"),r.addValue(e,t.replace(/[\t\n\r]/g," ").replace(/&#?\w+;/g,i),n)}for(var c,u=++t,l=0;;){var d=e.charAt(u);switch(d){case"=":if(1===l)c=e.slice(t,u),l=3;else{if(2!==l)throw new Error("attribute equal must after attrName");l=3}break;case"'":case'"':if(3===l||1===l){if(1===l&&(a.warning('attribute value must after "="'),c=e.slice(t,u)),t=u+1,!((u=e.indexOf(d,t))>0))throw new Error("attribute value no end '"+d+"' match");s(c,p=e.slice(t,u),t-1),l=5}else{if(4!=l)throw new Error('attribute value must after "="');s(c,p=e.slice(t,u),t),a.warning('attribute "'+c+'" missed start quot('+d+")!!"),t=u+1,l=5}break;case"/":switch(l){case 0:r.setTagName(e.slice(t,u));case 5:case 6:case 7:l=7,r.closed=!0;case 4:case 1:break;case 2:r.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":return a.error("unexpected end of input"),0==l&&r.setTagName(e.slice(t,u)),u;case">":switch(l){case 0:r.setTagName(e.slice(t,u));case 5:case 6:case 7:break;case 4:case 1:"/"===(p=e.slice(t,u)).slice(-1)&&(r.closed=!0,p=p.slice(0,-1));case 2:2===l&&(p=c),4==l?(a.warning('attribute "'+p+'" missed quot(")!'),s(c,p,t)):(n.isHTML(o[""])&&p.match(/^(?:disabled|checked|selected)$/i)||a.warning('attribute "'+p+'" missed value!! "'+p+'" instead!!'),s(p,p,t));break;case 3:throw new Error("attribute value missed!!")}return u;case"":d=" ";default:if(d<=" ")switch(l){case 0:r.setTagName(e.slice(t,u)),l=6;break;case 1:c=e.slice(t,u),l=2;break;case 4:var p=e.slice(t,u);a.warning('attribute "'+p+'" missed quot(")!!'),s(c,p,t);case 5:l=6}else switch(l){case 2:r.tagName,n.isHTML(o[""])&&c.match(/^(?:disabled|checked|selected)$/i)||a.warning('attribute "'+c+'" missed value!! "'+c+'" instead2!!'),s(c,c,t),t=u,l=1;break;case 5:a.warning('attribute space is required"'+c+'"!!');case 6:l=1,t=u;break;case 3:l=4,t=u;break;case 7:throw new Error("elements closed character '/' and '>' must be connected to")}}u++}}function d(e,t,r){for(var o=e.tagName,i=null,a=e.length;a--;){var s=e[a],c=s.qName,u=s.value;if((f=c.indexOf(":"))>0)var l=s.prefix=c.slice(0,f),d=c.slice(f+1),p="xmlns"===l&&d;else d=c,l=null,p="xmlns"===c&&"";s.localName=d,!1!==p&&(null==i&&(i={},h(r,r={})),r[p]=i[p]=u,s.uri=n.XMLNS,t.startPrefixMapping(p,u))}for(a=e.length;a--;)(l=(s=e[a]).prefix)&&("xml"===l&&(s.uri=n.XML),"xmlns"!==l&&(s.uri=r[l||""]));var f;(f=o.indexOf(":"))>0?(l=e.prefix=o.slice(0,f),d=e.localName=o.slice(f+1)):(l=null,d=e.localName=o);var m=e.uri=r[l||""];if(t.startElement(m,d,o,e),!e.closed)return e.currentNSMap=r,e.localNSMap=i,!0;if(t.endElement(m,d,o),i)for(l in i)Object.prototype.hasOwnProperty.call(i,l)&&t.endPrefixMapping(l)}function p(e,t,r,n,o){if(/^(?:script|textarea)$/i.test(r)){var i=e.indexOf("</"+r+">",t),a=e.substring(t+1,i);if(/[&<]/.test(a))return/^script$/i.test(r)?(o.characters(a,0,a.length),i):(a=a.replace(/&#?\w+;/g,n),o.characters(a,0,a.length),i)}return t+1}function f(e,t,r,n){var o=n[r];return null==o&&((o=e.lastIndexOf("</"+r+">"))<t&&(o=e.lastIndexOf("</"+r)),n[r]=o),o<t}function h(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function m(e,t,r,n){if("-"===e.charAt(t+2))return"-"===e.charAt(t+3)?(o=e.indexOf("--\x3e",t+4))>t?(r.comment(e,t+4,o-t-4),o+3):(n.error("Unclosed comment"),-1):-1;if("CDATA["==e.substr(t+3,6)){var o=e.indexOf("]]>",t+9);return r.startCDATA(),r.characters(e,t+9,o-t-9),r.endCDATA(),o+3}var i=function(e,t){var r,n=[],o=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;for(o.lastIndex=t,o.exec(e);r=o.exec(e);)if(n.push(r),r[1])return n}(e,t),a=i.length;if(a>1&&/!doctype/i.test(i[0][0])){var s=i[1][0],c=!1,u=!1;a>3&&(/^public$/i.test(i[2][0])?(c=i[3][0],u=a>4&&i[4][0]):/^system$/i.test(i[2][0])&&(u=i[3][0]));var l=i[a-1];return r.startDTD(s,c,u),r.endDTD(),l.index+l[0].length}return-1}function g(e,t,r){var n=e.indexOf("?>",t);if(n){var o=e.substring(t,n).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);return o?(o[0].length,r.processingInstruction(o[1],o[2]),n+2):-1}return-1}function y(){this.attributeNames={}}s.prototype=new Error,s.prototype.name=s.name,c.prototype={parse:function(e,t,r){var o=this.domBuilder;o.startDocument(),h(t,t={}),function(e,t,r,o,i){function a(e){var t=e.slice(1,-1);return Object.hasOwnProperty.call(r,t)?r[t]:"#"===t.charAt(0)?function(e){if(e>65535){var t=55296+((e-=65536)>>10),r=56320+(1023&e);return String.fromCharCode(t,r)}return String.fromCharCode(e)}(parseInt(t.substr(1).replace("x","0x"))):(i.error("entity not found:"+e),e)}function c(t){if(t>T){var r=e.substring(T,t).replace(/&#?\w+;/g,a);S&&h(T),o.characters(r,0,t-T),T=t}}function h(t,r){for(;t>=b&&(r=C.exec(e));)v=r.index,b=v+r[0].length,S.lineNumber++;S.columnNumber=t-v+1}for(var v=0,b=0,C=/.*(?:\r\n?|\n)|.*$/g,S=o.locator,k=[{currentNSMap:t}],w={},T=0;;){try{var R=e.indexOf("<",T);if(R<0){if(!e.substr(T).match(/^\s*$/)){var _=o.doc,E=_.createTextNode(e.substr(T));_.appendChild(E),o.currentElement=E}return}switch(R>T&&c(R),e.charAt(R+1)){case"/":var x=e.indexOf(">",R+3),A=e.substring(R+2,x).replace(/[ \t\n\r]+$/g,""),O=k.pop();x<0?(A=e.substring(R+2).replace(/[\s<].*/,""),i.error("end tag name: "+A+" is not complete:"+O.tagName),x=R+1+A.length):A.match(/\s</)&&(A=A.replace(/[\s<].*/,""),i.error("end tag name: "+A+" maybe not complete"),x=R+1+A.length);var I=O.localNSMap,B=O.tagName==A;if(B||O.tagName&&O.tagName.toLowerCase()==A.toLowerCase()){if(o.endElement(O.uri,O.localName,A),I)for(var N in I)Object.prototype.hasOwnProperty.call(I,N)&&o.endPrefixMapping(N);B||i.fatalError("end tag name: "+A+" is not match the current start tagName:"+O.tagName)}else k.push(O);x++;break;case"?":S&&h(R),x=g(e,R,o);break;case"!":S&&h(R),x=m(e,R,o,i);break;default:S&&h(R);var P=new y,D=k[k.length-1].currentNSMap,U=(x=l(e,R,P,D,a,i),P.length);if(!P.closed&&f(e,x,P.tagName,w)&&(P.closed=!0,r.nbsp||i.warning("unclosed xml attribute")),S&&U){for(var M=u(S,{}),H=0;H<U;H++){var j=P[H];h(j.offset),j.locator=u(S,{})}o.locator=M,d(P,o,D)&&k.push(P),o.locator=S}else d(P,o,D)&&k.push(P);n.isHTML(P.uri)&&!P.closed?x=p(e,x,P.tagName,a,o):x++}}catch(e){if(e instanceof s)throw e;i.error("element parse error: "+e),x=-1}x>T?T=x:c(Math.max(R,T)+1)}}(e,t,r,o,this.errorHandler),o.endDocument()}},y.prototype={setTagName:function(e){if(!a.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},addValue:function(e,t,r){if(!a.test(e))throw new Error("invalid attribute:"+e);this.attributeNames[e]=this.length,this[this.length++]={qName:e,value:t,offset:r}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},t.XMLReader=c,t.ParseError=s},function(e,t,r){var n=r(0),o="a-zA-Z_À-ÖØ-öø-ÿͰ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿿、-퟿豈-﷏ﷰ-�",i=new RegExp("^([^"+o+"])|^((x|X)(m|M)(l|L))|([^"+o+"-.0-9·̀-ͯ‿⁀])","g"),a=/[^\x09\x0A\x0D\x20-\xFF\x85\xA0-\uD7FF\uE000-\uFDCF\uFDE0-\uFFFD]/gm,s=function(e){var t=[];if(e instanceof Object)for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t},c=function(e,t){var r=function(e,r,n,o,a){var s=void 0!==t.indent?t.indent:"\t",c=t.prettyPrint?"\n"+new Array(o).join(s):"";t.removeIllegalNameCharacters&&(e=e.replace(i,"_"));var u=[c,"<",e,n||""];return r&&r.length>0?(u.push(">"),u.push(r),a&&u.push(c),u.push("</"),u.push(e),u.push(">")):u.push("/>"),u.join("")};return function e(o,i,c){var u=n(o);switch((Array.isArray?Array.isArray(o):o instanceof Array)?u="array":o instanceof Date&&(u="date"),u){case"array":var l=[];return o.map((function(t){l.push(e(t,0,c+1))})),t.prettyPrint&&l.push("\n"),l.join("");case"date":return o.toJSON?o.toJSON():o+"";case"object":var d=[];for(var p in o)if(o.hasOwnProperty(p))if(o[p]instanceof Array)for(var f=0;f<o[p].length;f++)o[p].hasOwnProperty(f)&&d.push(r(p,e(o[p][f],0,c+1),null,c+1,s(o[p][f]).length));else d.push(r(p,e(o[p],0,c+1),null,c+1));return t.prettyPrint&&d.length>0&&d.push("\n"),d.join("");case"function":return o();default:return t.escape?(""+o).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&apos;").replace(/"/g,"&quot;").replace(a,""):""+o}}(e,0,0)},u=function(e){var t=['<?xml version="1.0" encoding="UTF-8"'];return e&&t.push(' standalone="yes"'),t.push("?>"),t.join("")};e.exports=function(e,t){if(t||(t={xmlHeader:{standalone:!0},prettyPrint:!0,indent:"  ",escape:!0}),"string"==typeof e)try{e=JSON.parse(e.toString())}catch(e){return!1}var r="",o="";return t&&("object"==n(t)?(t.xmlHeader&&(r=u(!!t.xmlHeader.standalone)),void 0!==t.docType&&(o="<!DOCTYPE "+t.docType+">")):r=u()),[r,(t=t||{}).prettyPrint&&o?"\n":"",o,c(e,t)].join("").replace(/\n{2,}/g,"\n").replace(/\s+$/g,"")}},function(e,t,r){var n=r(0).default,o=r(28);e.exports=function(e){var t=o(e,"string");return"symbol"===n(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,r){var n=r(0).default;e.exports=function(e,t){if("object"!==n(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,r){var n,o,i;i=function(){"use strict";var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(){return t=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},t.apply(this,arguments)};function r(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{c(n.next(e))}catch(e){i(e)}}function s(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function n(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}var o="__BEACON_",i="__BEACON_deviceId",a="last_report_time",s="sending_event_ids",c="beacon_config",u="beacon_config_request_time",l=function(){function e(){var e=this;this.emit=function(t,r){if(e){var n,o=e.__EventsList[t];if(null==o?void 0:o.length){o=o.slice();for(var i=0;i<o.length;i++){n=o[i];try{var a=n.callback.apply(e,[r]);if(1===n.type&&e.remove(t,n.callback),!1===a)break}catch(e){throw e}}}return e}},this.__EventsList={}}return e.prototype.indexOf=function(e,t){for(var r=0;r<e.length;r++)if(e[r].callback===t)return r;return-1},e.prototype.on=function(e,t,r){if(void 0===r&&(r=0),this){var n=this.__EventsList[e];if(n||(n=this.__EventsList[e]=[]),-1===this.indexOf(n,t)){var o={name:e,type:r||0,callback:t};return n.push(o),this}return this}},e.prototype.one=function(e,t){this.on(e,t,1)},e.prototype.remove=function(e,t){if(this){var r=this.__EventsList[e];if(!r)return null;if(!t){try{delete this.__EventsList[e]}catch(e){}return null}if(r.length){var n=this.indexOf(r,t);r.splice(n,1)}return this}},e}();function d(e,t){for(var r={},n=0,o=Object.keys(e);n<o.length;n++){var i=o[n],a=e[i];if("string"==typeof a)r[p(i)]=p(a);else{if(t)throw new Error("value mast be string  !!!!");r[p(String(i))]=p(String(a))}}return r}function p(e){if("string"!=typeof e)return e;try{return e.replace(new RegExp("\\|","g"),"%7C").replace(new RegExp("\\&","g"),"%26").replace(new RegExp("\\=","g"),"%3D").replace(new RegExp("\\+","g"),"%2B")}catch(e){return""}}function f(e){return String(e.A99)+String(e.A100)}var h=function(){},m=function(){function e(e){var r=this;this.lifeCycle=new l,this.uploadJobQueue=[],this.additionalParams={},this.delayTime=0,this._normalLogPipeline=function(e){if(!e||!e.reduce||!e.length)throw new TypeError("createPipeline 方法需要传入至少有一个 pipe 的数组");return 1===e.length?function(t,r){e[0](t,r||h)}:e.reduce((function(e,t){return function(r,n){return void 0===n&&(n=h),e(r,(function(e){return null==t?void 0:t(e,n)}))}}))}([function(e){r.send({url:r.strategy.getUploadUrl(),data:e,method:"post",contentType:"application/json;charset=UTF-8"},(function(){var t=r.config.onReportSuccess;"function"==typeof t&&t(JSON.stringify(e.events))}),(function(){var t=r.config.onReportFail;"function"==typeof t&&t(JSON.stringify(e.events))}))}]),function(e,t){if(!e)throw t instanceof Error?t:new Error(t)}(Boolean(e.appkey),"appkey must be initial"),this.config=t({},e)}return e.prototype.onUserAction=function(e,t){this.preReport(e,t,!1)},e.prototype.onDirectUserAction=function(e,t){this.preReport(e,t,!0)},e.prototype.preReport=function(e,t,r){e?this.strategy.isEventUpOnOff()&&(this.strategy.isBlackEvent(e)||this.strategy.isSampleEvent(e)||this.onReport(e,t,r)):this.errorReport.reportError("602"," no eventCode")},e.prototype.addAdditionalParams=function(e){for(var t=0,r=Object.keys(e);t<r.length;t++){var n=r[t];this.additionalParams[n]=e[n]}},e.prototype.setChannelId=function(e){this.commonInfo.channelID=String(e)},e.prototype.setOpenId=function(e){this.commonInfo.openid=String(e)},e.prototype.setUnionid=function(e){this.commonInfo.unid=String(e)},e.prototype.getDeviceId=function(){return this.commonInfo.deviceId},e.prototype.getCommonInfo=function(){return this.commonInfo},e.prototype.removeSendingId=function(e){try{var t=JSON.parse(this.storage.getItem(s)),r=t.indexOf(e);-1!=r&&(t.splice(r,1),this.storage.setItem(s,JSON.stringify(t)))}catch(e){}},e}(),g=function(){function e(e,t,r,n){this.requestParams={},this.network=n,this.requestParams.attaid="00400014144",this.requestParams.token="6478159937",this.requestParams.product_id=e.appkey,this.requestParams.platform=r,this.requestParams.uin=t.deviceId,this.requestParams.model="",this.requestParams.os=r,this.requestParams.app_version=e.appVersion,this.requestParams.sdk_version=t.sdkVersion,this.requestParams.error_stack="",this.uploadUrl=e.isOversea?"https://htrace.wetvinfo.com/kv":"https://h.trace.qq.com/kv"}return e.prototype.reportError=function(e,t){this.requestParams._dc=Math.random(),this.requestParams.error_msg=t,this.requestParams.error_code=e,this.network.get(this.uploadUrl,{params:this.requestParams}).catch((function(e){}))},e}(),y=function(){function e(e,t,r,n,o){this.strategy={isEventUpOnOff:!0,httpsUploadUrl:"https://otheve.beacon.qq.com/analytics/v2_upload",requestInterval:30,blacklist:[],samplelist:[]},this.realSample={},this.appkey="",this.needQueryConfig=!0,this.appkey=t.appkey,this.storage=n,this.needQueryConfig=e;try{var i=JSON.parse(this.storage.getItem(c));i&&this.processData(i)}catch(e){}t.isOversea&&(this.strategy.httpsUploadUrl="https://svibeacon.onezapp.com/analytics/v2_upload"),!t.isOversea&&this.needRequestConfig()&&this.requestConfig(t.appVersion,r,o)}return e.prototype.requestConfig=function(e,t,r){var n=this;this.storage.setItem(u,Date.now().toString()),r.post("https://oth.str.beacon.qq.com/trpc.beacon.configserver.BeaconConfigService/QueryConfig",{platformId:"undefined"==typeof wx?"3":"4",mainAppKey:this.appkey,appVersion:e,sdkVersion:t.sdkVersion,osVersion:t.userAgent,model:"",packageName:"",params:{A3:t.deviceId}}).then((function(e){if(0==e.data.ret)try{var t=JSON.parse(e.data.beaconConfig);t&&(n.processData(t),n.storage.setItem(c,e.data.beaconConfig))}catch(e){}else n.processData(null),n.storage.setItem(c,"")})).catch((function(e){}))},e.prototype.processData=function(e){var t,r,n,o,i;this.strategy.isEventUpOnOff=null!==(t=null==e?void 0:e.isEventUpOnOff)&&void 0!==t?t:this.strategy.isEventUpOnOff,this.strategy.httpsUploadUrl=null!==(r=null==e?void 0:e.httpsUploadUrl)&&void 0!==r?r:this.strategy.httpsUploadUrl,this.strategy.requestInterval=null!==(n=null==e?void 0:e.requestInterval)&&void 0!==n?n:this.strategy.requestInterval,this.strategy.blacklist=null!==(o=null==e?void 0:e.blacklist)&&void 0!==o?o:this.strategy.blacklist,this.strategy.samplelist=null!==(i=null==e?void 0:e.samplelist)&&void 0!==i?i:this.strategy.samplelist;for(var a=0,s=this.strategy.samplelist;a<s.length;a++){var c=s[a].split(",");2==c.length&&(this.realSample[c[0]]=c[1])}},e.prototype.needRequestConfig=function(){if(!this.needQueryConfig)return!1;var e=Number(this.storage.getItem(u));return Date.now()-e>60*this.strategy.requestInterval*1e3},e.prototype.getUploadUrl=function(){return this.strategy.httpsUploadUrl+"?appkey="+this.appkey},e.prototype.isBlackEvent=function(e){return-1!=this.strategy.blacklist.indexOf(e)},e.prototype.isEventUpOnOff=function(){return this.strategy.isEventUpOnOff},e.prototype.isSampleEvent=function(e){return!!Object.prototype.hasOwnProperty.call(this.realSample,e)&&this.realSample[e]<Math.floor(Math.random()*Math.floor(1e4))},e}(),v="session_storage_key",b=function(){function e(e,t,r){this.getSessionStackDepth=0,this.beacon=r,this.storage=e,this.duration=t,this.appkey=r.config.appkey}return e.prototype.getSession=function(){this.getSessionStackDepth+=1;var e=this.storage.getItem(v);if(!e)return this.createSession();var t="",r=0;try{var n=JSON.parse(e)||{sessionId:void 0,sessionStart:void 0};if(!n.sessionId||!n.sessionStart)return this.createSession();var o=Number(this.storage.getItem(a));if(Date.now()-o>this.duration)return this.createSession();t=n.sessionId,r=n.sessionStart,this.getSessionStackDepth=0}catch(e){}return{sessionId:t,sessionStart:r}},e.prototype.createSession=function(){var e=Date.now(),t={sessionId:this.appkey+"_"+e.toString(),sessionStart:e};this.storage.setItem(v,JSON.stringify(t)),this.storage.setItem(a,e.toString());var r="is_new_user",n=this.storage.getItem(r);return this.getSessionStackDepth<=1&&this.beacon.onDirectUserAction("rqd_applaunched",{A21:n?"N":"Y"}),this.storage.setItem(r,JSON.stringify(!1)),t},e}();function C(){var e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,r=e.indexOf("Edge")>-1&&!t,n=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);var o=parseFloat(RegExp.$1);return 7==o?7:8==o?8:9==o?9:10==o?10:6}return r?-2:n?11:-1}var S,k,w=function(){return(w=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},T=function(){function e(e,t){void 0===t&&(t={}),this.reportOptions={},this.config=e,this.reportOptions=t}return e.canUseDB=function(){return!!(null===window||void 0===window?void 0:window.indexedDB)},e.prototype.openDB=function(){var t=this;return new Promise((function(r,n){if(!e.canUseDB())return n({message:"当前不支持 indexeddb"});var o=t.config,i=o.name,a=o.version,s=o.stores,c=indexedDB.open(i,a);c.onsuccess=function(){t.db=c.result,r(),w({result:1,func:"open",params:JSON.stringify(t.config)},t.reportOptions)},c.onerror=function(e){var r,o;n(e),w({result:0,func:"open",params:JSON.stringify(t.config),error_msg:null===(o=null===(r=e.target)||void 0===r?void 0:r.error)||void 0===o?void 0:o.message},t.reportOptions)},c.onupgradeneeded=function(){t.db=c.result;try{null==s||s.forEach((function(e){t.createStore(e)}))}catch(e){w({result:0,func:"open",params:JSON.stringify(t.config),error_msg:e.message},t.reportOptions),n(e)}}}))},e.prototype.useStore=function(e){return this.storeName=e,this},e.prototype.deleteDB=function(){var e=this;return this.closeDB(),new Promise((function(t,r){var n=indexedDB.deleteDatabase(e.config.name);n.onsuccess=function(){return t()},n.onerror=r}))},e.prototype.closeDB=function(){var e;null===(e=this.db)||void 0===e||e.close(),this.db=null},e.prototype.getStoreCount=function(){var e=this;return new Promise((function(t,r){var n=e.getStore("readonly").count();n.onsuccess=function(){return t(n.result)},n.onerror=r}))},e.prototype.clearStore=function(){var e=this;return new Promise((function(t,r){var n=e.getStore("readwrite").clear();n.onsuccess=function(){return t()},n.onerror=r}))},e.prototype.add=function(e,t){var r=this;return new Promise((function(n,o){var i=r.getStore("readwrite").add(e,t);i.onsuccess=function(){n(i.result)},i.onerror=o}))},e.prototype.put=function(e,t){var r=this;return new Promise((function(n,o){var i=r.getStore("readwrite").put(e,t);i.onsuccess=function(){n(i.result)},i.onerror=o}))},e.prototype.getStoreAllData=function(){var e=this;return new Promise((function(t,r){var n=e.getStore("readonly").openCursor(),o=[];n.onsuccess=function(){var e;if(null===(e=n.result)||void 0===e?void 0:e.value){var r=n.result.value;o.push(r),n.result.continue()}else t(o)},n.onerror=r}))},e.prototype.getDataRangeByIndex=function(e,t,r,n,o){var i=this;return new Promise((function(a,s){var c=i.getStore().index(e),u=IDBKeyRange.bound(t,r,n,o),l=[],d=c.openCursor(u);d.onsuccess=function(){var e;(null===(e=null==d?void 0:d.result)||void 0===e?void 0:e.value)?(l.push(null==d?void 0:d.result.value),null==d||d.result.continue()):a(l)},d.onerror=s}))},e.prototype.removeDataByIndex=function(e,t,r,n,o){var i=this;return new Promise((function(a,s){var c=i.getStore("readwrite").index(e),u=IDBKeyRange.bound(t,r,n,o),l=c.openCursor(u),d=0;l.onsuccess=function(e){var t=e.target.result;t?(d+=1,t.delete(),t.continue()):a(d)},l.onerror=s}))},e.prototype.createStore=function(e){var t=e.name,r=e.indexes,n=void 0===r?[]:r,o=e.options;if(this.db){this.db.objectStoreNames.contains(t)&&this.db.deleteObjectStore(t);var i=this.db.createObjectStore(t,o);n.forEach((function(e){i.createIndex(e.indexName,e.keyPath,e.options)}))}},e.prototype.getStore=function(e){var t;return void 0===e&&(e="readonly"),null===(t=this.db)||void 0===t?void 0:t.transaction(this.storeName,e).objectStore(this.storeName)},e}(),R="event_table_v3",_="eventId",E=function(){function e(e){this.isReady=!1,this.taskQueue=Promise.resolve(),this.db=new T({name:"Beacon_"+e+"_V3",version:1,stores:[{name:R,options:{keyPath:_},indexes:[{indexName:_,keyPath:_,options:{unique:!0}}]}]}),this.open()}return e.prototype.getCount=function(){var e=this;return this.readyExec((function(){return e.db.getStoreCount()}))},e.prototype.setItem=function(e,t){var r=this;return this.readyExec((function(){return r.db.add({eventId:e,value:t})}))},e.prototype.getItem=function(e){return r(this,void 0,void 0,(function(){var t=this;return n(this,(function(r){return[2,this.readyExec((function(){return t.db.getDataRangeByIndex(_,e,e)}))]}))}))},e.prototype.removeItem=function(e){var t=this;return this.readyExec((function(){return t.db.removeDataByIndex(_,e,e)}))},e.prototype.updateItem=function(e,t){var r=this;return this.readyExec((function(){return r.db.put({eventId:e,value:t})}))},e.prototype.iterate=function(e){var t=this;return this.readyExec((function(){return t.db.getStoreAllData().then((function(t){t.forEach((function(t){e(t.value)}))}))}))},e.prototype.open=function(){return r(this,void 0,void 0,(function(){var e=this;return n(this,(function(t){switch(t.label){case 0:return this.taskQueue=this.taskQueue.then((function(){return e.db.openDB()})),[4,this.taskQueue];case 1:return t.sent(),this.isReady=!0,this.db.useStore(R),[2]}}))}))},e.prototype.readyExec=function(e){return this.isReady?e():(this.taskQueue=this.taskQueue.then((function(){return e()})),this.taskQueue)},e}(),x=function(){function e(e){this.keyObject={},this.storage=e}return e.prototype.getCount=function(){return this.storage.getStoreCount()},e.prototype.removeItem=function(e){this.storage.removeItem(e),delete this.keyObject[e]},e.prototype.setItem=function(e,t){var r=JSON.stringify(t);this.storage.setItem(e,r),this.keyObject[e]=t},e.prototype.iterate=function(e){for(var t=Object.keys(this.keyObject),r=0;r<t.length;r++){var n=this.storage.getItem(t[r]);e(JSON.parse(n))}},e}(),A=function(){function e(e,t){var r=this;this.dbEventCount=0,C()>0||!window.indexedDB||/X5Lite/.test(navigator.userAgent)?(this.store=new x(t),this.dbEventCount=this.store.getCount()):(this.store=new E(e),this.getCount().then((function(e){r.dbEventCount=e})).catch((function(e){})))}return e.prototype.getCount=function(){return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.store.getCount()];case 1:return[2,e.sent()];case 2:return e.sent(),[2,Promise.reject()];case 3:return[2]}}))}))},e.prototype.insertEvent=function(e,t){return r(this,void 0,void 0,(function(){var r,o;return n(this,(function(n){switch(n.label){case 0:if(this.dbEventCount>=1e4)return[2,Promise.reject()];r=f(e.mapValue),n.label=1;case 1:return n.trys.push([1,3,,4]),this.dbEventCount++,[4,this.store.setItem(r,e)];case 2:return[2,n.sent()];case 3:return o=n.sent(),t&&t(o,e),this.dbEventCount--,[2,Promise.reject()];case 4:return[2]}}))}))},e.prototype.getEvents=function(){return r(this,void 0,void 0,(function(){var e;return n(this,(function(t){switch(t.label){case 0:e=[],t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.store.iterate((function(t){e.push(t)}))];case 2:case 3:return t.sent(),[2,Promise.all(e)];case 4:return[2]}}))}))},e.prototype.removeEvent=function(e){return r(this,void 0,void 0,(function(){var t;return n(this,(function(r){switch(r.label){case 0:t=f(e.mapValue),r.label=1;case 1:return r.trys.push([1,3,,4]),this.dbEventCount--,[4,this.store.removeItem(t)];case 2:return[2,r.sent()];case 3:return r.sent(),this.dbEventCount++,[2,Promise.reject()];case 4:return[2]}}))}))},e}(),O=function(){return(O=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function I(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}function B(e,t){var r=[null,void 0,"",NaN].includes(e);if(t.isSkipEmpty&&r)return null;var n=!t.isSkipEmpty&&r?"":e;try{return t.encode?encodeURIComponent(n):n}catch(e){return null}}function N(e,t){return new Promise((function(r,n){if(t&&document.querySelectorAll("script[data-tag="+t+"]").length)return r();var o=document.createElement("script"),i=O({type:"text/javascript",charset:"utf-8"},e);Object.keys(i).forEach((function(e){return function(e,t,r){if(e)return void 0===r?e.getAttribute(t):e.setAttribute(t,r)}(o,e,i[e])})),t&&(o.dataset.tag=t),o.onload=function(){return r()},o.onreadystatechange=function(){var e=o.readyState;["complete","loaded"].includes(e)&&(o.onreadystatechange=null,r())},o.onerror=n,document.body.appendChild(o)}))}(k=S||(S={}))[k.equal=0]="equal",k[k.low=-1]="low",k[k.high=1]="high";var P=function(){return(P=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function D(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{c(n.next(e))}catch(e){i(e)}}function s(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function U(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}var M=function(){function e(){this.interceptors=[]}return e.prototype.use=function(e,t){return this.interceptors.push({resolved:e,rejected:t}),this.interceptors.length-1},e.prototype.traverse=function(e,t){void 0===t&&(t=!1);var r=Promise.resolve(e);return(t?Array.prototype.reduceRight:Array.prototype.reduce).call(this.interceptors,(function(e,t){if(t){var n=t.resolved,o=t.rejected;r=r.then(n,o)}return e}),""),r},e.prototype.eject=function(e){this.interceptors[e]&&(this.interceptors[e]=null)},e}(),H={defaults:{timeout:0,method:"GET",mode:"cors",redirect:"follow",credentials:"same-origin"},headers:{common:{Accept:"application/json, text/plain, */*"},POST:{"Content-Type":"application/x-www-form-urlencoded"},PUT:{"Content-Type":"application/x-www-form-urlencoded"},PATCH:{"Content-Type":"application/x-www-form-urlencoded"}},baseURL:"",polyfillUrl:"https://vm.gtimg.cn/comps/script/fetch.min.js",interceptors:{request:new M,response:new M}},j=/^([a-z][a-z\d+\-.]*:)?\/\//i,L=Object.prototype.toString;function F(e){return D(this,void 0,void 0,(function(){var t;return U(this,(function(r){switch(r.label){case 0:if(window.fetch)return[2];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,N({src:e})];case 2:return r.sent(),[3,4];case 3:throw t=r.sent(),new Error("加载 polyfill "+e+" 失败: "+t.message);case 4:return[2]}}))}))}function q(e){return D(this,void 0,void 0,(function(){var t,r,n,o,i,a,s,c,u,l,d,p,f,h,m,g,y;return U(this,(function(v){switch(v.label){case 0:return t=H.baseURL,r=H.defaults,n=H.interceptors,[4,F(H.polyfillUrl)];case 1:return v.sent(),(o=P(P({},r),e)).headers||(o.headers=function(e){void 0===e&&(e="GET");var t=H.headers[e]||{};return P(P({},H.headers.common),t)}(o.method)),function(e){["Accept","Content-Type"].forEach((function(t){return r=t,void((n=e.headers)&&Object.keys(n).forEach((function(e){e!==r&&e.toUpperCase()===r.toUpperCase()&&(n[r]=n[e],delete n[e])})));var r,n})),function(e){if("[object Object]"!==L.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}(e.body)&&(e.body=JSON.stringify(e.body),e.headers&&(e.headers["Content-Type"]="application/json;charset=utf-8"))}(o),[4,n.request.traverse(o,!0)];case 2:if((i=v.sent())instanceof Error)throw i;return i.url=function(e,t){return!e||j.test(t)?t:e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,"")}(t,i.url),a=i.url,s=i.timeout,c=i.params,u=i.method,l=["GET","DELETE","OPTIONS","HEAD"].includes(void 0===u?"GET":u)&&!!c,d=l?function(e,t){void 0===t&&(t={encode:!0,isSkipEmpty:!1});var r=e.url,n=e.query,o=void 0===n?{}:n,i=e.hash,a=r.split("#"),s=a[0],c=a[1],u=void 0===c?"":c,l=s.split("?")[0],d=[],p=B(i||u,t),f=O(O({},function(e){var t=e.split("#"),r=t[0],n=t[1],o=void 0===n?"":n,i=r.split("?"),a=i[0],s=i[1],c=void 0===s?"":s,u=I(o),l=Object.create(null);return c.split("&").forEach((function(e){var t=e.split("="),r=t[0],n=t[1],o=void 0===n?"":n,i=I(r),a=I(o);null===i||null===a||""===i&&""===a||l[i]||(l[i]=a)})),{url:a,query:l,hash:u}}(r).query),o);return Object.keys(f).forEach((function(e){var r=B(e,t),n=B(f[e],t);null!==r&&null!==n&&d.push(r+"="+n)})),l+(d.length?"?"+d.join("&"):"")+(p?"#"+p:"")}({url:a,query:c}):a,p=[],s&&!i.signal&&(m=new Promise((function(e){f=setTimeout((function(){e(new Error("timeout"))}),s)})),p.push(m),h=new AbortController,i.signal=h.signal),p.push(fetch(d,i).catch((function(e){return e}))),[4,Promise.race(p)];case 3:return g=v.sent(),f&&clearTimeout(f),[4,n.response.traverse(g)];case 4:if((y=v.sent())instanceof Error)throw null==h||h.abort(),y;return[2,y]}}))}))}var K=function(){function e(e){H.interceptors.request.use((function(r){var n=r.url,o=r.method,i=r.body,a=i;if(e.onReportBeforeSend){var s=e.onReportBeforeSend({url:n,method:o,data:i?JSON.parse(i):null});a=(null==s?void 0:s.data)?JSON.stringify(s.data):null}return"GET"!=o&&a?t(t({},r),{body:a}):r}))}return e.prototype.get=function(e,o){return r(this,void 0,void 0,(function(){var r,i;return n(this,(function(n){switch(n.label){case 0:return[4,q(t({url:e},o))];case 1:return[4,(r=n.sent()).json()];case 2:return i=n.sent(),[2,Promise.resolve({data:i,status:r.status,statusText:r.statusText,headers:r.headers})]}}))}))},e.prototype.post=function(e,o,i){return r(this,void 0,void 0,(function(){var r,a;return n(this,(function(n){switch(n.label){case 0:return[4,q(t({url:e,body:o,method:"POST"},i))];case 1:return[4,(r=n.sent()).json()];case 2:return a=n.sent(),[2,Promise.resolve({data:a,status:r.status,statusText:r.statusText,headers:r.headers})]}}))}))},e}(),z=function(){function e(e){this.appkey=e}return e.prototype.getItem=function(e){try{return window.localStorage.getItem(this.getStoreKey(e))}catch(e){return""}},e.prototype.removeItem=function(e){try{window.localStorage.removeItem(this.getStoreKey(e))}catch(e){}},e.prototype.setItem=function(e,t){try{window.localStorage.setItem(this.getStoreKey(e),t)}catch(e){}},e.prototype.setSessionItem=function(e,t){try{window.sessionStorage.setItem(this.getStoreKey(e),t)}catch(e){}},e.prototype.getSessionItem=function(e){try{return window.sessionStorage.getItem(this.getStoreKey(e))}catch(e){return""}},e.prototype.getStoreKey=function(e){return o+this.appkey+"_"+e},e.prototype.createDeviceId=function(){try{var e=window.localStorage.getItem(i);return e||(e=function(e){for(var t="",r=0;r<32;r++)t+="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz0123456789".charAt(Math.floor(51*Math.random()));return t}(),window.localStorage.setItem(i,e)),e}catch(e){return""}},e.prototype.clear=function(){try{for(var e=window.localStorage.length,t=0;t<e;t++){var r=window.localStorage.key(t);(null==r?void 0:r.substr(0,9))==o&&window.localStorage.removeItem(r)}}catch(e){}},e.prototype.getStoreCount=function(){var e=0;try{e=window.localStorage.length}catch(e){}return e},e}(),V="logid_start",X="4.5.14-web";return function(r){function n(e){var t=r.call(this,e)||this;t.qimei36="",t.uselessCycleTaskNum=0,t.underWeakNet=!1,t.pauseSearching=!1,t.send=function(e,r,n){t.storage.setItem(a,Date.now().toString()),t.network.post(t.uploadUrl||t.strategy.getUploadUrl(),e.data).then((function(n){var o;100==(null===(o=null==n?void 0:n.data)||void 0===o?void 0:o.result)?t.delayTime=1e3*n.data.delayTime:t.delayTime=0,r&&r(e.data),e.data.events.forEach((function(e){t.store.removeEvent(e).then((function(){t.removeSendingId(f(e.mapValue))}))})),t.doCustomCycleTask()})).catch((function(r){var o=e.data.events;t.errorReport.reportError(r.code?r.code.toString():"600",r.message),n&&n(e.data);var i=JSON.parse(t.storage.getItem(s));o.forEach((function(e){i&&-1!=i.indexOf(f(e))&&t.store.insertEvent(e,(function(e,r){e&&t.errorReport.reportError("604","insertEvent fail!")})),t.removeSendingId(f(e))})),t.monitorUploadFailed()}))};var n,o,i=C();return t.isUnderIE8=i>0&&i<8,t.isUnderIE8||(t.isUnderIE=i>0,e.needInitQimei&&function(e,t){var r;(void 0===r&&(r=Date.now()+"-"+Math.random()),new Promise((function(e,t){if(document.getElementById(r))e(void 0);else{var n=document.getElementsByTagName("head")[0],o=document.createElement("script");o.onload=function(){return function(){o.onload=null,e(void 0)}},o.onerror=function(e){o.onerror=null,n.removeChild(o),t(e)},o.src="https://tun-cos-1258344701.file.myqcloud.com/fp.js",o.id=r,n.appendChild(o)}}))).then((function(){(new Fingerprint).getQimei36(e,t)})).catch((function(e){}))}(e.appkey,(function(e){t.qimei36=e.q36})),t.network=new K(e),t.storage=new z(e.appkey),t.initCommonInfo(e),t.store=new A(e.appkey,t.storage),t.errorReport=new g(t.config,t.commonInfo,"web",t.network),t.strategy=new y(null==e.needQueryConfig||e.needQueryConfig,t.config,t.commonInfo,t.storage,t.network),t.logidStartTime=t.storage.getItem(V),t.logidStartTime||(t.logidStartTime=Date.now().toString(),t.storage.setItem(V,t.logidStartTime)),n=t.logidStartTime,o=Date.now()-Number.parseFloat(n),Math.floor(o/864e5)>=365&&t.storage.clear(),t.initSession(e),t.onDirectUserAction("rqd_js_init",{}),setTimeout((function(){return t.lifeCycle.emit("init")}),0),t.initDelayTime=e.delay?e.delay:1e3,t.cycleTask(t.initDelayTime)),t}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}(n,r),n.prototype.initSession=function(e){var t=18e5;e.sessionDuration&&e.sessionDuration>3e4&&(t=e.sessionDuration),this.beaconSession=new b(this.storage,t,this)},n.prototype.initCommonInfo=function(e){var t=Number(this.storage.getItem(a));try{var r=JSON.parse(this.storage.getItem(s));(Date.now()-t>3e4||!r)&&this.storage.setItem(s,JSON.stringify([]))}catch(e){}e.uploadUrl&&(this.uploadUrl=e.uploadUrl+"?appkey="+e.appkey);var n=[window.screen.width,window.screen.height];window.devicePixelRatio&&n.push(window.devicePixelRatio),this.commonInfo={deviceId:this.storage.createDeviceId(),language:navigator&&navigator.language||"zh_CN",query:window.location.search,userAgent:navigator.userAgent,pixel:n.join("*"),channelID:e.channelID?String(e.channelID):"",openid:e.openid?String(e.openid):"",unid:e.unionid?String(e.unionid):"",sdkVersion:X},this.config.appVersion=e.versionCode?String(e.versionCode):"",this.config.strictMode=e.strictMode},n.prototype.cycleTask=function(e){var t=this;this.intervalID=window.setInterval((function(){t.pauseSearching||t.store.getEvents().then((function(e){0==e.length&&(t.pauseSearching=!0);var r=[],n=JSON.parse(t.storage.getItem(s));n||(n=[]),e&&e.forEach((function(e){var t=f(e.mapValue);-1==n.indexOf(t)&&(r.push(e),n.push(t))})),0!=r.length&&(t.storage.setItem(s,JSON.stringify(n)),t._normalLogPipeline(t.assembleData(r)))})).catch((function(e){}))}),e)},n.prototype.onReport=function(e,t,r){var n=this;if(this.isUnderIE8)this.errorReport.reportError("601","UnderIE8");else{this.pauseSearching=!1;var o=this.generateData(e,t,r);if(r&&0==this.delayTime&&!this.underWeakNet)this._normalLogPipeline(this.assembleData(o));else{var i=o.shift();i&&this.store.insertEvent(i,(function(e){e&&n.errorReport.reportError("604","insertEvent fail!")})).catch((function(e){n._normalLogPipeline(n.assembleData(o))}))}}},n.prototype.onSendBeacon=function(e,t){if(this.isUnderIE)this.errorReport.reportError("605","UnderIE");else{this.pauseSearching=!1;var r=this.assembleData(this.generateData(e,t,!0));"function"==typeof navigator.sendBeacon&&navigator.sendBeacon(this.uploadUrl||this.strategy.getUploadUrl(),JSON.stringify(r))}},n.prototype.generateData=function(e,r,n){var o=[],i="4.5.14-web_"+(n?"direct_log_id":"normal_log_id"),a=Number(this.storage.getItem(i));return a=a||1,r=t(t({},r),{A99:n?"Y":"N",A100:a.toString(),A72:X,A88:this.logidStartTime}),a++,this.storage.setItem(i,a.toString()),o.push({eventCode:e,eventTime:Date.now().toString(),mapValue:d(r,this.config.strictMode)}),o},n.prototype.assembleData=function(e){var r=this.beaconSession.getSession();return{appVersion:this.config.appVersion?p(this.config.appVersion):"",sdkId:"js",sdkVersion:X,mainAppKey:this.config.appkey,platformId:3,common:d(t(t({},this.additionalParams),{A2:this.commonInfo.deviceId,A8:this.commonInfo.openid,A12:this.commonInfo.language,A17:this.commonInfo.pixel,A23:this.commonInfo.channelID,A50:this.commonInfo.unid,A76:r.sessionId,A101:this.commonInfo.userAgent,A102:window.location.href,A104:document.referrer,A119:this.commonInfo.query,A153:this.qimei36}),!1),events:e}},n.prototype.monitorUploadFailed=function(){this.uselessCycleTaskNum++,this.uselessCycleTaskNum>=5&&(window.clearInterval(this.intervalID),this.cycleTask(6e4),this.underWeakNet=!0)},n.prototype.doCustomCycleTask=function(){this.uselessCycleTaskNum>=5&&(window.clearInterval(this.intervalID),this.cycleTask(this.initDelayTime)),this.uselessCycleTaskNum=0,this.underWeakNet=!1},n}(m)},"object"==r(0)(t)&&void 0!==e?e.exports=i():void 0===(o="function"==typeof(n=i)?n.call(t,r,t,e):n)||(e.exports=o)},function(e,t,r){var n=r(0),o=function(e){switch(n(e)){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}},i=function(e,t,r){var n={};return t.getAllResponseHeaders().trim().split("\n").forEach((function(e){if(e){var t=e.indexOf(":"),r=e.substr(0,t).trim().toLowerCase(),o=e.substr(t+1).trim();n[r]=o}})),{error:e,statusCode:t.status,statusMessage:t.statusText,headers:n,body:r}},a=function(e,t){return t||"text"!==t?e.response:e.responseText};e.exports=function(e,t){var r,s,c,u=(e.method||"GET").toUpperCase(),l=e.url;if(e.qs){var d=(r=e.qs,s=s||"&",c=c||"=",null===r&&(r=void 0),"object"===n(r)?Object.keys(r).map((function(e){var t=encodeURIComponent(o(e))+c;return Array.isArray(r[e])?r[e].map((function(e){return t+encodeURIComponent(o(e))})).join(s):t+encodeURIComponent(o(r[e]))})).filter(Boolean).join(s):"");d&&(l+=(-1===l.indexOf("?")?"?":"&")+d)}var p=new XMLHttpRequest;if(p.open(u,l,!0),p.responseType=e.dataType||"text",e.xhrFields)for(var f in e.xhrFields)p[f]=e.xhrFields[f];var h=e.headers;if(h)for(var m in h)h.hasOwnProperty(m)&&"content-length"!==m.toLowerCase()&&"user-agent"!==m.toLowerCase()&&"origin"!==m.toLowerCase()&&"host"!==m.toLowerCase()&&p.setRequestHeader(m,h[m]);return e.onProgress&&p.upload&&(p.upload.onprogress=e.onProgress),e.onDownloadProgress&&(p.onprogress=e.onDownloadProgress),e.timeout&&(p.timeout=e.timeout),p.ontimeout=function(e){var r=new Error("timeout");t(i(r,p))},p.onload=function(){t(i(null,p,a(p,e.dataType)))},p.onerror=function(r){var n=a(p,e.dataType);if(n)t(i(null,p,n));else{var o=p.statusText;o||0!==p.status||(o=new Error("CORS blocked or network error")),t(i(o,p,n))}},p.send(e.body||""),p}},function(e,t){e.exports={eachLimit:function(e,t,r,n){if(n=n||function(){},!e.length||("function"==typeof t?t():t)<=0)return n();var o=0,i=0,a=0;!function s(){if(o>=e.length)return n();for(;a<("function"==typeof t?t():t)&&i<e.length;)a+=1,r(e[(i+=1)-1],(function(t){t?(n(t),n=function(){}):(a-=1,(o+=1)>=e.length?n():s())}))}()},retry:function(e,t,r){e<1?r():function n(o){t((function(t,i){t&&o<e?n(o+1):r(t,i)}))}(1)}}},function(e,t,r){"use strict";var n=r(9),o=r(10);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var a=r(1),s=(r(4),r(11),r(13),r(14),r(3),{}),c=function(){function e(t){n(this,e),this.options=a.extend(a.clone(s),t||{}),this.strategy="aggressive",this.loop=0,this.reachLimit=!1,this.curParallelLimit=6,this.chunkUploadDurationArray=[],this.lastLoopUploadDuration=[],this.margin=1.25}return o(e,[{key:"calAvarageDuration",value:function(){var e=[],t=0;if(this.chunkUploadDurationArray.length<this.curParallelLimit)return 0;for(var r=this.chunkUploadDurationArray.length-1;r>=this.chunkUploadDurationArray.length-1-this.curParallelLimit;r--)this.chunkUploadDurationArray[r]&&(t+=this.chunkUploadDurationArray[r].duration,e.push(this.chunkUploadDurationArray[r].duration));return this.chunkUploadDurationArray=[],t/this.curParallelLimit}},{key:"nextTick",value:function(){var e=this.calAvarageDuration();if(!e)return!1;0!==this.lastLoopUploadDuration.length&&e>=this.getAvarageData(this.lastLoopUploadDuration)*this.margin?this.curParallelLimit--:"progressive"===this.strategy||this.reachLimit?this.curParallelLimit++:(this.curParallelLimit=2*this.curParallelLimit,this.curParallelLimit>=18&&(this.curParallelLimit=18)),this.lastLoopUploadDuration.push(e)}},{key:"getAvarageData",value:function(e,t){var r,n=0,o=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(e,t):void 0}}(e))){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){c=!0,a=e},f:function(){try{s||null==r.return||r.return()}finally{if(c)throw a}}}}(e);try{for(o.s();!(r=o.n()).done;)n+=r.value}catch(e){o.e(e)}finally{o.f()}return n/e.length}},{key:"triggerFallback",value:function(){this.reachLimit&&(this.curParallelLimit=Math.floor(.75*this.curParallelLimit)),this.reachLimit=!0}},{key:"getScheduledParallelLimit",value:function(){return this.curParallelLimit>18&&(this.curParallelLimit=18),this.curParallelLimit<6&&(this.curParallelLimit=6),this.curParallelLimit}},{key:"markStart",value:function(e){window.performance&&window.performance.mark&&window.performance.mark("".concat(e,"_start"))}},{key:"markEnd",value:function(e){if(window.performance&&window.performance.mark&&window.performance.mark("".concat(e,"_end")),window.performance&&window.performance.measure){window.performance.measure(e,"".concat(e,"_start"),"".concat(e,"_end"));var t=window.performance.getEntriesByName(e)[0].duration;this.chunkUploadDurationArray.push({partNumber:e,duration:t}),this.nextTick()}}}]),e}();e.exports=c}])},module.exports=t()},593:e=>{"use strict";e.exports=JSON.parse('{"_from":"axios@^0.21.1","_id":"axios@0.21.4","_inBundle":false,"_integrity":"sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==","_location":"/axios","_phantomChildren":{},"_requested":{"type":"range","registry":true,"raw":"axios@^0.21.1","name":"axios","escapedName":"axios","rawSpec":"^0.21.1","saveSpec":null,"fetchSpec":"^0.21.1"},"_requiredBy":["/"],"_resolved":"https://mirrors.tencent.com/npm/axios/-/axios-0.21.4.tgz","_shasum":"c67b90dc0568e5c1cf2b0b858c43ba28e2eda575","_spec":"axios@^0.21.1","_where":"/Users/<USER>/工作/开发/点播/上传/OA_vod-js-sdk-v6","author":{"name":"Matt Zabriskie"},"browser":{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},"bugs":{"url":"https://github.com/axios/axios/issues"},"bundleDependencies":false,"bundlesize":[{"path":"./dist/axios.min.js","threshold":"5kB"}],"dependencies":{"follow-redirects":"^1.14.0"},"deprecated":false,"description":"Promise based HTTP client for the browser and node.js","devDependencies":{"coveralls":"^3.0.0","es6-promise":"^4.2.4","grunt":"^1.3.0","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.1.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^23.0.0","grunt-karma":"^4.0.0","grunt-mocha-test":"^0.13.3","grunt-ts":"^6.0.0-beta.19","grunt-webpack":"^4.0.2","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1","karma":"^6.3.2","karma-chrome-launcher":"^3.1.0","karma-firefox-launcher":"^2.1.0","karma-jasmine":"^1.1.1","karma-jasmine-ajax":"^0.1.13","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^4.3.6","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.8","karma-webpack":"^4.0.2","load-grunt-tasks":"^3.5.2","minimist":"^1.2.0","mocha":"^8.2.1","sinon":"^4.5.0","terser-webpack-plugin":"^4.2.3","typescript":"^4.0.5","url-search-params":"^0.10.0","webpack":"^4.44.2","webpack-dev-server":"^3.11.0"},"homepage":"https://axios-http.com","jsdelivr":"dist/axios.min.js","keywords":["xhr","http","ajax","promise","node"],"license":"MIT","main":"index.js","name":"axios","repository":{"type":"git","url":"git+https://github.com/axios/axios.git"},"scripts":{"build":"NODE_ENV=production grunt build","coveralls":"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js","examples":"node ./examples/server.js","fix":"eslint --fix lib/**/*.js","postversion":"git push && git push --tags","preversion":"npm test","start":"node ./sandbox/server.js","test":"grunt test","version":"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json"},"typings":"./index.d.ts","unpkg":"dist/axios.min.js","version":"0.21.4"}')},147:e=>{"use strict";e.exports=JSON.parse('{"name":"vod-js-sdk-v6","version":"1.7.0","description":"tencent cloud vod js sdk v6","main":"lib/src/tc_vod.js","unpkg":"dist/vod-js-sdk-v6.js","typings":"lib/src/tc_vod.d.ts","scripts":{"test":"cross-env NODE_ENV=test mocha -r espower-typescript/guess -r jsdom-global/register -r test/env test/**/*.test.ts","cover":"cross-env NODE_ENV=test nyc mocha -r espower-typescript/guess -r jsdom-global/register -r test/env test/**/*.test.ts","dev":"webpack --config webpack.dev.config.js --watch","dist":"webpack --config webpack.config.js","build":"npm run dist && npm run compile","compile":"tsc -p tsconfig.json","prepublish":"npm run build","lint":"tsc --noEmit && eslint \'src/**/*.{js,ts,tsx}\' --quiet --fix"},"repository":{"type":"git","url":"git+https://github.com/tencentyun/vod-js-sdk-v6.git"},"keywords":["tencentcloud","sdk","vod"],"author":"alsotang <<EMAIL>>","license":"MIT","bugs":{"url":"https://github.com/tencentyun/vod-js-sdk-v6/issues"},"homepage":"https://github.com/tencentyun/vod-js-sdk-v6#readme","dependencies":{"axios":"^0.21.1","cos-js-sdk-v5":"^1.3.7","eventemitter3":"^4.0.7","js-sha1":"^0.6.0","typescript":"^5.1.6","uuid":"^3.4.0"},"devDependencies":{"@types/mocha":"^5.2.5","@types/semver":"^6.0.0","@types/sha1":"^1.1.1","@types/uuid":"^3.4.4","@typescript-eslint/eslint-plugin":"^1.9.0","@typescript-eslint/parser":"^1.9.0","cross-env":"^6.0.3","eslint":"^5.16.0","eslint-config-prettier":"^4.3.0","eslint-plugin-prettier":"^3.1.0","espower-typescript":"^9.0.1","jsdom":"^13.1.0","jsdom-global":"^3.0.2","mm":"^2.4.1","mocha":"^5.2.0","nyc":"^13.1.0","power-assert":"^1.6.1","prettier":"^1.17.1","semver":"^6.1.1","ts-loader":"^9.4.4","webpack":"^5.88.1","webpack-cli":"^5.1.4"},"nyc":{"extension":[".ts",".tsx"],"include":["src"],"reporter":["html"],"all":true}}')}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var r=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(r.exports,r,r.exports,__webpack_require__),r.exports}__webpack_require__.amdO={},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var __webpack_exports__=__webpack_require__(754);return __webpack_exports__})()));
//# sourceMappingURL=vod-js-sdk-v6.js.map