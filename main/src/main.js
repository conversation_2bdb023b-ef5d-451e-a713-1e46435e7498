import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import config from 'common/config'
import './plugins/entry.js'
import 'animate.css'
import elementPuls from './plugins/element-puls'
import globalComponent from 'common/components'
import globalDirective from 'common/directive'
import TextSelectPlugin from 'common/js/text_select.js'
import { initFont, get_font_family, change_font_family } from 'common/js/utils'
import components from './components'
import './assets/scss/index.scss'
const $favicon = document.querySelector('link[rel="icon"]')
$favicon.href = `${config.API_URL}/pageImg/open/logo`
initFont()
change_font_family(get_font_family())
// 新建标签 script
const creactScriptElm = (url) => {
  const scriptElm = document.createElement('script')
  scriptElm.type = 'application/javascript'
  scriptElm.src = url
  scriptElm.referrerPolicy = 'origin'
  document.head ? document.head.appendChild(scriptElm) : document.body.appendChild(scriptElm)
}
creactScriptElm(`${config.API_URL}/static_config/tinymce.min.js`)
// import { prefetchMicroApps } from '@/qiankun'
// if (!window.qiankunStarted) {
//   window.qiankunStarted = true
//   prefetchMicroApps()
// }
// 阻止浏览器的返回
// window.addEventListener('popstate', function () {
//   console.log(document.URL)
//   history.pushState(null, null, document.URL)
// })
const app = createApp(App)
app
  .use(TextSelectPlugin)
  .use(store)
  .use(router)
  .use(globalDirective)
  .use(elementPuls)
  .use(globalComponent)
  .use(components)
  .mount('#app')
