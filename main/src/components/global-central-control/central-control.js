import api from '@/api'
import { ref } from 'vue'
import { qiankunActions } from '@/qiankun'
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip'
import docxtemplater from 'docxtemplater'
import ImageModule from 'docxtemplater-image-module-free'
import { saveAs } from 'file-saver'
import { WordXml } from 'common/Word/wordXml.js'
import { replaceNullWithEmptyString } from 'common/js/utils.js'
import { ElMessage } from 'element-plus'
const getImage = (dataURL) => {
  const base64Regex = /^data:image\/(png|jpg|svg|svg\+xml);base64,/
  if (!base64Regex.test(dataURL)) return false
  const stringBase64 = dataURL.replace(base64Regex, '')
  let binaryString
  if (typeof window !== 'undefined') {
    binaryString = window.atob(stringBase64)
  } else {
    binaryString = new Buffer(stringBase64, 'base64').toString('binary')
  }
  const len = binaryString.length
  const bytes = new Uint8Array(len)
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  return bytes.buffer
}
export const globalRefMethod = () => {
  const isShow = ref(false)
  const isHidden = ref(true)
  const dataList = ref([])

  const handleDuplicateNames = (data, key) => {
    let repeatObj = {}
    let newData = []
    for (let index = 0; index < data.length; index++) {
      let item = data[index]
      const name = item[key]
      if (repeatObj[name]) {
        item[key] = `${item[key]}(${repeatObj[name]})`
        repeatObj[name] += 1
      } else {
        repeatObj[name] = 1
      }
      newData.push(item)
    }
    return newData
  }
  const globalDownload = (res, id) => {
    const data = res
    const r = new FileReader()
    r.onload = (e) => {
      try {
        const resData = JSON.parse(e.target.result)
        dataList.value.forEach((item) => {
          if (item.id === id) {
            if (item.fileType === 'txt') {
              item.progress = 100
              item.text = '下载完成'
              item.isType = 'success'
              saveAs(res, item.fileName)
            } else {
              item.text = '下载失败'
              item.isType = 'exception'
              ElMessage.error(resData.message)
            }
          }
        })
      } catch (err) {
        dataList.value.forEach((item) => {
          if (item.id === id) {
            item.progress = 100
            item.text = '下载完成'
            item.isType = 'success'
            saveAs(res, item.fileName)
          }
        })
      }
    }
    r.readAsText(data)
  }
  const onDownloadProgress = (progressEvent, id) => {
    if (progressEvent?.event?.lengthComputable) {
      const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)
      dataList.value.forEach((item) => {
        if (item.id === id) {
          item.progress = parseInt(progress)
        }
      })
    }
  }
  const globalImportMethod = () => {
    const globalImport = (data) => {
      if (Object.prototype.toString.call(data) === '[object Array]') {
        if (data.length) {
          isShow.value = true
          isHidden.value = true
          data.forEach((file) => {
            globalImportProgress(file)
          })
        }
      } else {
        isShow.value = true
        isHidden.value = true
        globalImportProgress(data)
      }
      qiankunActions.setGlobalState({ importList: [] })
    }
    const globalImportProgress = (file) => {
      dataList.value.unshift({ ...file, progress: 0, className: 'import', text: '导入' })
      onImportProgress(file.id)
    }
    const onImportProgress = (id) => {
      var _setInterval = null
      dataList.value.forEach((item) => {
        if (item.id === id) {
          _setInterval = setInterval(() => {
            if (item.progress >= 90) {
              clearInterval(_setInterval)
              isImportResult(id)
            } else {
              item.progress = item.progress + 9
            }
          }, 200)
        }
      })
    }
    const isImportResult = async (code) => {
      const res = await api.downloadImportResult(code, {})
      const r = new FileReader()
      r.onload = (e) => {
        try {
          const resData = JSON.parse(e.target.result)
          console.log(resData)
          setTimeout(() => {
            isImportResult(code)
          }, 2000)
        } catch (err) {
          dataList.value.forEach((item) => {
            if (item.id === code) {
              item.progress = 100
              item.text = '下载结果'
              item.isType = 'success'
            }
          })
        }
      }
      r.readAsText(res)
    }

    const downloadImportResult = async (code, fileName) => {
      const res = await api.downloadImportResult(code, {})
      saveAs(res, fileName)
    }
    return { globalImport, downloadImportResult }
  }
  const globalDownloadFileMethod = () => {
    const globalDownloadFile = (data) => {
      if (Object.prototype.toString.call(data) === '[object Array]') {
        if (data.length) {
          isShow.value = true
          isHidden.value = true
          data.forEach((file) => {
            globalProgressDownload(file)
          })
        }
      } else {
        isShow.value = true
        isHidden.value = true
        globalProgressDownload(data)
      }
      qiankunActions.setGlobalState({ downloadFile: [] })
    }
    // 通用附件
    const globalProgressDownload = async (file) => {
      const id = guid()
      dataList.value.unshift({ ...file, id, progress: 0, className: 'download', text: '下载' })
      const res = await api.globalProgressDownload(id, file.fileId, {}, onDownloadProgress)
      globalDownload(res, id)
    }
    return { globalDownloadFile }
  }
  const globalBatchDownloadFileMethod = () => {
    const globalBatchDownloadFile = (data) => {
      if (Object.prototype.toString.call(data) === '[object Array]') {
        if (data.length) {
          isShow.value = true
          isHidden.value = true
          data.forEach((file) => {
            globalProgressDownloadZip(file)
          })
        }
      } else {
        isShow.value = true
        isHidden.value = true
        globalProgressDownloadZip(data)
      }
      qiankunActions.setGlobalState({ batchDownloadFile: [] })
    }
    // 通用附件
    const globalProgressDownloadZip = async (file) => {
      const id = guid()
      dataList.value.unshift({ ...file, id, progress: 0, className: 'download', text: '下载' })
      const res = await api.globalProgressDownloadZip(file.params, id, onDownloadProgress)
      globalDownload(res, id)
    }
    return { globalBatchDownloadFile }
  }
  const globalExtendDownloadFileMethod = () => {
    const globalExtendDownloadFile = (data) => {
      if (Object.prototype.toString.call(data) === '[object Array]') {
        if (data.length) {
          isShow.value = true
          isHidden.value = true
          data.forEach((file) => {
            globalOtherDownload(file)
          })
        }
      } else {
        isShow.value = true
        isHidden.value = true
        globalOtherDownload(data)
      }
      qiankunActions.setGlobalState({ extendDownloadFile: [] })
    }
    // 其他接口文件下载
    const globalOtherDownload = async (file) => {
      const id = guid()
      dataList.value.unshift({ ...file, id, progress: 0, className: 'download', text: '下载' })
      const res = await api.globalOtherDownload(file.url, file.params, id, onDownloadProgress, file.config)
      globalDownload(res, id)
    }
    return { globalExtendDownloadFile }
  }
  const globalExportWordMethod = () => {
    const exportWord = async ({ code, name, data = {}, imgSize = {} }) => {
      if (!code) return
      qiankunActions.setGlobalState({ exportWordObj: {} })
      const id = guid()
      isShow.value = true
      isHidden.value = true
      dataList.value.unshift({
        id,
        fileSize: 0,
        fileName: `${name}.docx`,
        fileType: 'docx',
        progress: 0,
        className: 'download',
        text: '导出'
      })
      const content = await api.globalDocumentTemplate(id, code, onDownloadProgress)
      const blob = new Blob([content])
      const r = new FileReader()
      r.onload = (e) => {
        try {
          const resData = JSON.parse(e.target.result)
          ElMessage.error(resData.message)
          dataList.value.forEach((item) => {
            if (item.id === id) {
              item.text = '导出失败'
              item.isType = 'exception'
            }
          })
        } catch (err) {
          const zip = new PizZip(content)
          // 图片处理
          const opts = {
            getImage: getImage,
            getSize: (img, tagValue, tagName) => imgSize[tagName] || [120, 120]
          }
          const word = new docxtemplater().loadZip(zip).attachModule(new ImageModule(opts))
          console.log(data)
          console.log(replaceNullWithEmptyString(data))
          word.setData(replaceNullWithEmptyString(data))
          try {
            word.render()
          } catch (error) {
            const e = { message: error.message, name: error.name, stack: error.stack, properties: error.properties }
            console.log(JSON.stringify({ error: e }))
            throw error
          }
          const docx = word.getZip().generate({ type: 'blob' })
          saveAs(docx, `${name}.docx`)
          dataList.value.forEach((item) => {
            if (item.id === id) {
              item.progress = 100
              item.text = '导出完成'
              item.isType = 'success'
            }
          })
        }
      }
      r.readAsText(blob)
    }
    return { exportWord }
  }
  const globalExportWordHtmlMethod = () => {
    const pretreatmentWordHtml = ({ code, name, data = {}, key, imgSize = {} }) => {
      if (!code) return
      qiankunActions.setGlobalState({ exportWordHtmlObj: {} })
      const id = guid()
      isShow.value = true
      isHidden.value = true
      dataList.value.unshift({
        id,
        fileSize: 0,
        fileName: `${name}.docx`,
        fileType: 'docx',
        progress: 0,
        className: 'download',
        text: '导出'
      })
      new WordXml().initData(data[key]).then((res) => {
        // eslint-disable-line
        exportWordHtml({ id, code, name, data: { ...data, rawXml: res.xml }, imgData: res.imgData, imgSize })
      })
    }
    const exportWordHtml = async ({ id, code, name, data = {}, imgData = [], imgSize = {} }) => {
      const content = await api.globalDocumentTemplate(id, code, onDownloadProgress)
      const blob = new Blob([content])
      const r = new FileReader()
      r.onload = (e) => {
        try {
          const resData = JSON.parse(e.target.result)
          ElMessage.error(resData.message)
          dataList.value.forEach((item) => {
            if (item.id === id) {
              item.text = '导出失败'
              item.isType = 'exception'
            }
          })
        } catch (err) {
          const zip = new PizZip(content)
          if (imgData.length) {
            var img = zip.folder('word/media')
            var imgFile = zip.folder('word/_rels/')
            var imgUrlText = imgFile.file('document.xml.rels').asText().replace('</Relationships>', '')
            imgData.forEach((item) => {
              img.file(item.name, item.file, { base64: true })
              imgUrlText += `<Relationship Id="rId${item.id}" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="media/${item.name}"/>`
            })
            imgUrlText += '</Relationships>'
            imgFile.file('document.xml.rels', imgUrlText)
          }
          // 图片处理
          const opts = {
            getImage: getImage,
            getSize: (img, tagValue, tagName) => imgSize[tagName] || [120, 120]
          }
          const word = new docxtemplater().loadZip(zip).attachModule(new ImageModule(opts))
          console.log(data)
          console.log(replaceNullWithEmptyString(data))
          word.setData(replaceNullWithEmptyString(data))
          try {
            word.render()
          } catch (error) {
            const e = { message: error.message, name: error.name, stack: error.stack, properties: error.properties }
            console.log(JSON.stringify({ error: e }))
            throw error
          }
          const docx = word.getZip().generate({ type: 'blob' })
          saveAs(docx, `${name}.docx`)
          dataList.value.forEach((item) => {
            if (item.id === id) {
              item.progress = 100
              item.text = '导出完成'
              item.isType = 'success'
            }
          })
        }
      }
      r.readAsText(blob)
    }
    return { pretreatmentWordHtml }
  }
  const globalExportWordListMethod = () => {
    const globalCreateWordList = async ({ create, code, name, data = [], wordNameKey, imgSize = {} }) => {
      if (!code) return
      const id = guid()
      isShow.value = true
      isHidden.value = true
      qiankunActions.setGlobalState({ exportWordList: {} })
      dataList.value.unshift({
        id,
        fileSize: 0,
        fileName: `${name}.zip`,
        fileType: 'zip',
        progress: 0,
        className: 'download',
        text: '导出'
      })
      const newData = handleDuplicateNames(data, wordNameKey)
      if (create) {
        const res = await api.globalDocumentZip(create.url, create.params)
        const blob = new Blob([res])
        if (blob.size) {
          const r = new FileReader()
          r.onload = (e) => {
            try {
              const resData = JSON.parse(e.target.result)
              ElMessage.error(resData.message)
              dataList.value.forEach((item) => {
                if (item.id === id) {
                  item.text = '导出失败'
                  item.isType = 'exception'
                }
              })
            } catch (err) {
              pretreatmentWordList(id, code, name, newData, wordNameKey, imgSize, res)
            }
          }
          r.readAsText(blob)
        } else {
          pretreatmentWordList(id, code, name, newData, wordNameKey, imgSize)
        }
      } else {
        pretreatmentWordList(id, code, name, newData, wordNameKey, imgSize)
      }
    }
    const pretreatmentWordList = async (id, code, name, data = [], wordNameKey, imgSize = {}, createZip) => {
      const content = await api.globalDocumentTemplate(id, code, onDownloadProgress)
      const blob = new Blob([content])
      const r = new FileReader()
      r.onload = (e) => {
        try {
          const resData = JSON.parse(e.target.result)
          ElMessage.error(resData.message)
          dataList.value.forEach((item) => {
            if (item.id === id) {
              item.text = '导出失败'
              item.isType = 'exception'
            }
          })
        } catch (err) {
          const zipList = createZip ? new PizZip(createZip) : new PizZip()
          for (let i = 0, len = data.length; i < len; i++) {
            const zip = new PizZip(content)
            // 图片处理
            const opts = {
              getImage: getImage,
              getSize: (img, tagValue, tagName) => imgSize[tagName] || [120, 120]
            }
            const word = new docxtemplater().loadZip(zip).attachModule(new ImageModule(opts))
            console.log(data[i])
            console.log(replaceNullWithEmptyString(data[i]))
            word.setData(replaceNullWithEmptyString(data[i]))
            try {
              word.render()
            } catch (error) {
              const e = { message: error.message, name: error.name, stack: error.stack, properties: error.properties }
              console.log(JSON.stringify({ error: e }))
              throw error
            }
            const docx = word.getZip().generate({ type: 'ArrayBuffer' })
            zipList.file(`${data[i][wordNameKey]}.docx`, docx, { binary: true })
          }
          const word = zipList.generate({ type: 'blob' })
          saveAs(word, `${name}.zip`)
          dataList.value.forEach((item) => {
            if (item.id === id) {
              item.progress = 100
              item.text = '导出完成'
              item.isType = 'success'
            }
          })
        }
      }
      r.readAsText(blob)
    }
    return { globalCreateWordList }
  }
  const globalExportWordHtmlListMethod = () => {
    const globalCreateWordHtmlList = async ({ create, code, name, data = [], wordNameKey, key, imgSize = {} }) => {
      if (!code) return
      const id = guid()
      isShow.value = true
      isHidden.value = true
      qiankunActions.setGlobalState({ exportWordHtmlList: {} })
      dataList.value.unshift({
        id,
        fileSize: 0,
        fileName: `${name}.zip`,
        fileType: 'zip',
        progress: 0,
        className: 'download',
        text: '导出'
      })
      const newData = handleDuplicateNames(data, wordNameKey)
      if (create) {
        const res = await api.globalDocumentZip(create.url, create.params)
        const blob = new Blob([res])
        if (blob.size) {
          const r = new FileReader()
          r.onload = (e) => {
            try {
              const resData = JSON.parse(e.target.result)
              ElMessage.error(resData.message)
              dataList.value.forEach((item) => {
                if (item.id === id) {
                  item.text = '导出失败'
                  item.isType = 'exception'
                }
              })
            } catch (err) {
              pretreatmentWordHtmlList(id, code, name, newData, wordNameKey, key, imgSize, res)
            }
          }
          r.readAsText(blob)
        } else {
          pretreatmentWordHtmlList(id, code, name, newData, wordNameKey, key, imgSize)
        }
      } else {
        pretreatmentWordHtmlList(id, code, name, newData, wordNameKey, key, imgSize)
      }
    }
    const pretreatmentWordHtmlList = async (id, code, name, data = [], wordNameKey, key, imgSize = {}, createZip) => {
      const content = await api.globalDocumentTemplate(id, code, onDownloadProgress)
      const blob = new Blob([content])
      const r = new FileReader()
      r.onload = (e) => {
        try {
          const resData = JSON.parse(e.target.result)
          ElMessage.error(resData.message)
          dataList.value.forEach((item) => {
            if (item.id === id) {
              item.text = '导出失败'
              item.isType = 'exception'
            }
          })
        } catch (err) {
          const zipList = createZip ? new PizZip(createZip) : new PizZip()
          var length = 0
          for (let i = 0, len = data.length; i < len; i++) {
            new WordXml().initData(data[i][key]).then((res) => {
              // eslint-disable-line
              const zip = new PizZip(content)
              if (res.imgData.length) {
                var img = zip.folder('word/media')
                var imgFile = zip.folder('word/_rels/')
                var imgUrlText = imgFile.file('document.xml.rels').asText().replace('</Relationships>', '')
                res.imgData.forEach((item) => {
                  img.file(item.name, item.file, { base64: true })
                  imgUrlText += `<Relationship Id="rId${item.id}" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="media/${item.name}"/>`
                })
                imgUrlText += '</Relationships>'
                imgFile.file('document.xml.rels', imgUrlText)
              }
              // 图片处理
              const opts = {
                getImage: getImage,
                getSize: (img, tagValue, tagName) => imgSize[tagName] || [120, 120]
              }
              const word = new docxtemplater().loadZip(zip).attachModule(new ImageModule(opts))
              console.log({ ...data[i], rawXml: res.xml })
              console.log(replaceNullWithEmptyString({ ...data[i], rawXml: res.xml }))
              word.setData(replaceNullWithEmptyString({ ...data[i], rawXml: res.xml }))
              try {
                word.render()
              } catch (error) {
                const e = { message: error.message, name: error.name, stack: error.stack, properties: error.properties }
                console.log(JSON.stringify({ error: e }))
                throw error
              }
              const docx = word.getZip().generate({ type: 'ArrayBuffer' })
              zipList.file(`${data[i][wordNameKey]}.docx`, docx, { binary: true })
              length++
              if (length === data.length) {
                const word = zipList.generate({ type: 'blob' })
                saveAs(word, `${name}.zip`)
                dataList.value.forEach((item) => {
                  if (item.id === id) {
                    item.progress = 100
                    item.text = '导出完成'
                    item.isType = 'success'
                  }
                })
              }
            })
          }
        }
      }
      r.readAsText(blob)
    }
    return { globalCreateWordHtmlList }
  }
  return {
    isShow,
    isHidden,
    dataList,
    globalImportMethod,
    globalDownloadFileMethod,
    globalBatchDownloadFileMethod,
    globalExtendDownloadFileMethod,
    globalExportWordMethod,
    globalExportWordHtmlMethod,
    globalExportWordListMethod,
    globalExportWordHtmlListMethod
  }
}

export const dragMethod = () => {
  const positionX = ref(0)
  const positionY = ref(0)
  const GlobalFileRef = ref()
  // 鼠标按下移动
  const onMovedown = (e) => {
    const pdiv = document.querySelector('body')
    const odiv = GlobalFileRef.value
    const disX = e.clientX - odiv.offsetLeft
    const disY = e.clientY - odiv.offsetTop
    const diffWidth = pdiv.offsetWidth - odiv.offsetWidth
    const diffHeight = pdiv.offsetHeight - odiv.offsetHeight
    if (diffWidth <= 0 || diffHeight <= 0) {
      document.onmousemove = null
      document.onmouseup = null
      return
    }
    document.onmousemove = (e) => {
      let left = e.clientX - disX
      let top = e.clientY - disY
      const minWidth = pdiv.offsetLeft
      const minHeight = pdiv.offsetTop
      const maxWidth = pdiv.offsetLeft + pdiv.offsetWidth - odiv.offsetWidth
      const maxHeight = pdiv.offsetTop + pdiv.offsetHeight - odiv.offsetHeight
      left = left < minWidth ? minWidth : left
      top = top < minHeight ? minHeight : top
      left = left > maxWidth ? maxWidth : left
      top = top > maxHeight ? maxHeight : top
      positionX.value = top
      positionY.value = left
      odiv.style.left = left + 'px'
      odiv.style.top = top + 'px'
    }
    document.onmouseup = () => {
      document.onmousemove = null
      document.onmouseup = null
    }
  }
  const onMoveup = () => {
    document.onmousemove = null
    document.onmouseup = null
  }
  return { positionX, positionY, GlobalFileRef, onMovedown, onMoveup }
}

export const fileIcon = (fileType) => {
  const IconClass = {
    docx: 'globalFileWord',
    doc: 'globalFileWord',
    wps: 'globalFileWPS',
    xlsx: 'globalFileExcel',
    xls: 'globalFileExcel',
    pdf: 'globalFilePDF',
    txt: 'globalFileTXT',
    jpg: 'globalFilePicture',
    png: 'globalFilePicture',
    gif: 'globalFilePicture',
    avi: 'globalFileVideo',
    mp4: 'globalFileVideo',
    zip: 'globalFileCompress',
    rar: 'globalFileCompress'
  }
  return IconClass[fileType] || 'globalFileUnknown'
}

export const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
