<template>
  <div class="global-central-control" :class="[isHidden ? 'hidden' : 'isHidden']" ref="GlobalFileRef" v-if="isShow">
    <div class="global-central-control-head" @mousedown="onMovedown" @mouseup="onMoveup">
      <div class="global-central-control-head-name">系统任务列表</div>
      <div class="global-central-control-head-text">
        <el-icon :class="[isHidden ? 'hidden' : 'isHidden']" @click="UnfoldAndFold">
          <DArrowRight />
        </el-icon>
        <el-icon @click="isShow = !isShow">
          <Close />
        </el-icon>
      </div>
    </div>
    <transition name="zy-el-zoom-in-top">
      <el-scrollbar class="global-central-control-body" v-show="isHidden">
        <el-empty :image-size="100" description="暂无系统任务" v-if="!dataList.length" />
        <div class="global-central-control-item" v-for="item in dataList" :key="item.id">
          <div class="global-central-control-info">
            <div class="globalFileIcon" :class="fileIcon(item.fileType)"></div>
            <div class="global-central-control-name ellipsis">{{ item.fileName }}</div>
            <div class="global-central-control-size">{{ item.fileSize ? size2Str(item.fileSize) : '' }}</div>
            <div class="global-central-control-text" v-if="!item.isType">{{ item.text }}{{ item.progress }}%</div>
            <div :class="`global-central-control-${item.className}`" v-if="item.isType === 'success'"
              @click="handleResult(item)">{{ item.text }}</div>
            <div class="global-central-control-exception" v-if="item.isType === 'exception'">{{ item.text }}</div>
          </div>
          <el-progress :percentage="item.progress" :show-text="false" :status="item.isType || 'success'"
            v-if="!item.fileURL" :stroke-width="4" />
        </div>
      </el-scrollbar>
    </transition>
  </div>
</template>
<script>
export default { name: 'GlobalCentralControl' }
</script>
<script setup>
import { watch } from 'vue'
import { useStore } from 'vuex'
import { size2Str } from 'common/js/utils.js'
import { qiankunActions } from '@/qiankun'
import { globalRefMethod, dragMethod, fileIcon } from './central-control'
const store = useStore()
const {
  isShow, isHidden, dataList,
  globalImportMethod,
  globalDownloadFileMethod,
  globalBatchDownloadFileMethod,
  globalExtendDownloadFileMethod,
  globalExportWordMethod,
  globalExportWordHtmlMethod,
  globalExportWordListMethod,
  globalExportWordHtmlListMethod
} = globalRefMethod()

const { GlobalFileRef, onMovedown, onMoveup } = dragMethod()
const { globalImport, downloadImportResult } = globalImportMethod()
const { globalDownloadFile } = globalDownloadFileMethod()
const { globalBatchDownloadFile } = globalBatchDownloadFileMethod()
const { globalExtendDownloadFile } = globalExtendDownloadFileMethod()
const { exportWord } = globalExportWordMethod()
const { pretreatmentWordHtml } = globalExportWordHtmlMethod()
const { globalCreateWordList } = globalExportWordListMethod()
const { globalCreateWordHtmlList } = globalExportWordHtmlListMethod()

const UnfoldAndFold = () => {
  isHidden.value = !isHidden.value
}
const GlobalCentralControlMethod = ({ show }) => {
  isShow.value = show
  isHidden.value = show
  qiankunActions.setGlobalState({ globalCentralControlObj: {} })
}
const handleResult = (row) => {
  if (row.className === 'import') {
    downloadImportResult(row.id, row.downloadName)
  }
}

watch(() => store.state.importList, (val) => { globalImport(val) })
watch(() => store.state.downloadFile, (val) => { globalDownloadFile(val) })
watch(() => store.state.batchDownloadFile, (val) => { globalBatchDownloadFile(val) })
watch(() => store.state.extendDownloadFile, (val) => { globalExtendDownloadFile(val) })
watch(() => store.state.exportWordObj, (val) => { exportWord(val) })
watch(() => store.state.exportWordHtmlObj, (val) => { pretreatmentWordHtml(val) })
watch(() => store.state.exportWordList, (val) => { globalCreateWordList(val) })
watch(() => store.state.exportWordHtmlList, (val) => { globalCreateWordHtmlList(val) })
watch(() => store.state.globalCentralControlObj, (val) => { if (JSON.stringify(val) !== '{}') { GlobalCentralControlMethod(val) } })
</script>
<style lang="scss">
.global-central-control {
  position: fixed;
  top: calc(100% - 360px);
  right: 70px;
  width: 450px;
  background: #ffffff;
  box-shadow: var(--zy-el-box-shadow);
  border-radius: var(--el-border-radius-base);
  z-index: 1000;

  .global-central-control-head {
    width: 410px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--zy-el-border-color-lighter);
    margin: auto;
    cursor: move;
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-four);

    .global-central-control-head-name {
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }

    .global-central-control-head-text {
      font-size: var(--zy-navigation-font-size);
      display: flex;
      align-items: center;

      .zy-el-icon {
        margin-left: 16px;
        cursor: pointer;
      }

      .hidden {
        transform: rotate(90deg);
      }

      .isHidden {
        transform: rotate(-90deg);
      }
    }
  }

  .global-central-control-body {
    width: 100%;
    height: 290px;

    .zy-el-scrollbar__view {
      padding: 0 var(--zy-distance-two);
      padding-top: var(--zy-distance-two);
    }

    .global-central-control-item {
      margin-bottom: var(--zy-distance-two);

      .global-central-control-info {
        display: flex;
        align-items: center;
        padding: var(--zy-font-name-distance-five) 0;
        padding-left: var(--zy-title-font-size);
        position: relative;

        .global-central-control-name {
          width: 55%;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
        }

        .global-central-control-size {
          width: 25%;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          text-align: right;
        }

        .global-central-control-text,
        .global-central-control-import,
        .global-central-control-download {
          width: 20%;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          text-align: right;
        }

        .global-central-control-text,
        .global-central-control-import,
        .global-central-control-download {
          width: 20%;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          text-align: right;
        }

        .global-central-control-text {
          color: var(--zy-el-color-info);
        }

        .global-central-control-import {
          cursor: pointer;
          color: var(--zy-el-color-success);
        }

        .global-central-control-exception {
          width: 20%;
          text-align: right;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          color: var(--zy-el-color-danger);
        }

        .globalFileIcon {
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          width: var(--zy-title-font-size);
          height: var(--zy-title-font-size);
          vertical-align: middle;
        }

        .globalFileUnknown {
          background: url("./img/unknown.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }

        .globalFilePDF {
          background: url("./img/PDF.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }

        .globalFileWord {
          background: url("./img/Word.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }

        .globalFileExcel {
          background: url("./img/Excel.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }

        .globalFilePicture {
          background: url("./img/picture.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }

        .globalFileVideo {
          background: url("./img/video.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }

        .globalFileTXT {
          background: url("./img/TXT.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }

        .globalFileCompress {
          background: url("./img/compress.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }

        .globalFileWPS {
          background: url("./img/WPS.png") no-repeat;
          background-size: 80% 80%;
          background-position: 0 center;
        }
      }
    }
  }
}
</style>
