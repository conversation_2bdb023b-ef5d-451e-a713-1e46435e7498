<template>
  <div class="global-file-preview">
    <div class="global-file-preview-head">
      <div class="global-file-preview-name ellipsis" :title="fileName">{{ fileName }}</div>
      <div class="global-file-preview-button">
        <div class="global-file-preview-icon" @click="handleDownload" v-if="isDownload">
          <el-icon>
            <Download />
          </el-icon>
        </div>
        <div class="global-file-preview-icon" @click="handleClose" v-if="isClose">
          <el-icon>
            <SwitchButton />
          </el-icon>
        </div>
      </div>
    </div>
    <div class="global-file-preview-body">
      <template v-if="['xls', 'xlsx'].includes(fileType)">
        <preview-excel :id="fileId" :type="fileType" :name="fileName"></preview-excel>
      </template>
      <template v-if="['pdf', 'doc', 'docx', 'wps', 'ofd'].includes(fileType)">
        <preview-pdf :id="fileId" :type="fileType" :name="fileName"></preview-pdf>
      </template>
      <template v-if="['pptx'].includes(fileType)">
        <preview-ppt :id="fileId" :type="fileType" :name="fileName"></preview-ppt>
      </template>
      <template v-if="['mp4', 'flv', 'wmv', 'mov', 'asf', 'avi', 'rm', 'ram'].includes(fileType)">
        <preview-video :id="fileId" :type="fileType" :name="fileName"></preview-video>
      </template>
      <template v-if="['png', 'jpg', 'jpeg', 'gif', 'tif'].includes(fileType)">
        <preview-pic :id="fileId" :type="fileType" :name="fileName"></preview-pic>
      </template>
      <template
        v-if="
          ['txt', 'json', 'js', 'css', 'java', 'py', 'html', 'jsx', 'ts', 'tsx', 'xml', 'md', 'log'].includes(fileType)
        ">
        <preview-text :id="fileId" :name="fileName"></preview-text>
      </template>
    </div>
  </div>
</template>
<script>
export default { name: 'GlobalFilePreview' }
</script>
<script setup>
import { computed, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'
import { Download, SwitchButton } from '@element-plus/icons-vue'
const PreviewExcel = defineAsyncComponent(() => import('./components/preview-excel'))
const PreviewPdf = defineAsyncComponent(() => import('./components/preview-pdf'))
const PreviewPpt = defineAsyncComponent(() => import('./components/preview-ppt'))
const PreviewVideo = defineAsyncComponent(() => import('./components/preview-video'))
const PreviewPic = defineAsyncComponent(() => import('./components/preview-pic'))
const PreviewText = defineAsyncComponent(() => import('./components/preview-text'))
const store = useStore()
const props = defineProps({
  query: { type: Object, default: () => ({}) },
  isClose: { type: Boolean, default: false }
})
const emit = defineEmits(['close'])
const fileId = computed(() => props.query.id)
const fileType = computed(() => props.query.fileType)
const fileName = computed(() => props.query.fileName)
const fileSize = computed(() => props.query.fileSize)
const isDownload = computed(() => props.query.isDownload)
const isClose = computed(() => props.isClose)

const handleDownload = () => {
  store.commit('setDownloadFile', [
    { fileId: fileId.value, fileType: fileType.value, fileName: fileName.value, fileSize: fileSize.value }
  ])
}
const handleClose = () => {
  emit('close')
}
</script>
<style lang="scss">
.global-file-preview {
  width: 100%;
  height: 100%;

  .global-file-preview-head {
    width: 100%;
    height: 52px;
    box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.05);
    padding: 0 32px 0 22px;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eeeeee;

    .global-file-preview-name {
      cursor: pointer;
      font-weight: bold;
      font-size: var(--zy-name-font-size);
    }

    .global-file-preview-button {
      display: flex;
      align-items: center;

      .global-file-preview-icon {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-left: 12px;

        .zy-el-icon {
          font-size: 22px;
        }
      }
    }
  }

  .global-file-preview-body {
    width: 100%;
    height: calc(100% - 52px);
    background: #eeeeee;
  }
}
</style>
