<template>
  <el-scrollbar class="preview-ppt" v-loading="loading">
    <vue-office-pptx :src="fileUrl" @rendered="renderedHandler" @error="errorHandler" />
  </el-scrollbar>
</template>
<script>
export default { name: 'PreviewPpt' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import VueOfficePptx from '@vue-office/pptx'
const props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })
const fileUrl = ref('')
const loading = ref(false)
onMounted(() => { globalDownload() })

const globalDownload = async () => {
  loading.value = true
  const res = await api.globalDownload(props.id)
  fileUrl.value = URL.createObjectURL(res)
}
const renderedHandler = () => {
  loading.value = false
  console.log("渲染完成")
}
const errorHandler = () => {
  loading.value = false
  console.log("渲染失败")
}
</script>
<style lang="scss">
.preview-ppt {
  width: 100%;
  height: 100%;

  .vue-office-pptx {
    width: 100%;
    max-width: 1200px;
    height: auto !important;
    margin: 0 auto;

    .vue-office-pptx-main {
      height: auto !important;

      .pptx-preview-wrapper {
        height: auto !important;
        overflow: hidden;
      }
    }
  }
}
</style>
