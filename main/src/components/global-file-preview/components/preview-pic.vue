<template>
  <div class="preview-pic">
    <el-image-viewer :url-list="fileUrl"></el-image-viewer>
  </div>
</template>
<script>
export default { name: 'PreviewPic' }
</script>
<script setup>
import api from '@/api'
import { computed } from 'vue'
const props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })
const fileUrl = computed(() => [`${api.fileURL(props.id + '.' + props.type)}`])
</script>
<style lang="scss">
.preview-pic {
  width: 100%;
  height: 100%;
  position: relative;

  .zy-el-image-viewer__wrapper {
    position: absolute;
    overflow: hidden;

    .zy-el-image-viewer__mask {
      background-color: transparent;
    }

    .zy-el-image-viewer__close {
      display: none;
    }
  }
}
</style>
