<template>
  <div class="preview-text" v-loading="loading">
    <el-scrollbar class="preview-text-box">
      <pre class="preview-text-text">{{ text }}</pre>
    </el-scrollbar>
  </div>
</template>
<script>
export default { name: 'PreviewText' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
const props = defineProps({ id: { type: String, default: '' } })
const text = ref('')
const loading = ref(false)
onMounted(() => { globalDownload() })

const globalDownload = async () => {
  loading.value = true
  const res = await api.globalDownload(props.id)
  const arrayBuffer = await readBuffer(res)
  text.value = await readText(arrayBuffer)
  loading.value = false
}
const readBuffer = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = loadEvent => resolve(loadEvent.target.result)
    reader.onerror = e => reject(e)
    reader.readAsArrayBuffer(file)
  })
}
const readText = async (buffer) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = loadEvent => resolve(loadEvent.target.result)
    reader.onerror = e => reject(e)
    reader.readAsText(new Blob([buffer]), 'utf-8')
  })
}
</script>
<style lang="scss">
.preview-text {
  width: 100%;
  height: 100%;

  .preview-text-box {
    width: 100%;
    height: 100%;

    .zy-el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .is-vertical {
      width: 9px;
      border-radius: var(--el-border-radius-base);

      .zy-el-scrollbar__thumb {
        border-radius: var(--el-border-radius-base);
      }
    }
  }

  .preview-text-text {
    width: 990px;
    padding: 22px;
    display: block;
    font-size: 16px;
    line-height: 24px;
    word-break: break-word;
    white-space: break-spaces;
    background-color: #fff;
    margin: 20px auto;
    box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.05);
  }
}
</style>
