<template>
  <div class="preview-excel" v-loading="loading">
    <vue-office-excel :src="fileUrl" @rendered="renderedHandler" @error="errorHandler" />
  </div>
</template>
<script>
export default { name: 'PreviewExcel' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'
const props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })
const loading = ref(false)
const fileUrl = ref('')
onMounted(() => { globalDownload() })

const xlsToxlsx = async (file) => {
  const param = new FormData()
  param.append('file', file)
  const res = await api.xlsToxlsx(param)
  fileUrl.value = URL.createObjectURL(res)
}
const globalDownload = async () => {
  loading.value = true
  const res = await api.globalDownload(props.id)
  if (props.type === 'xls') {
    xlsToxlsx(res)
  } else {
    fileUrl.value = URL.createObjectURL(res)
  }
}
const renderedHandler = () => {
  loading.value = false
  console.log("渲染完成")
}
const errorHandler = () => {
  loading.value = false
  console.log("渲染失败")
}
</script>
<style lang="scss">
.preview-excel {
  width: 100%;
  height: 100%;
}
</style>
