<template>
  <div class="preview-pdf" v-loading="loading">
    <vue-office-pdf :src="fileUrl" @rendered="renderedHandler" @error="errorHandler" />
  </div>
</template>
<script>
export default { name: 'PreviewPdf' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import VueOfficePdf from '@vue-office/pdf'
const props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })
const fileUrl = ref('')
const loading = ref(false)
onMounted(() => {
  globalDownload()
})

const wordTopdf = async (file) => {
  const param = new FormData()
  param.append('file', file)
  const res = await api.wordTopdf(param)
  fileUrl.value = URL.createObjectURL(res)
}
const ofdTopdf = async (file) => {
  const param = new FormData()
  param.append('file', file)
  const res = await api.ofdTopdf(param)
  fileUrl.value = URL.createObjectURL(res)
}
const globalDownload = async () => {
  loading.value = true
  const res = await api.globalDownload(props.id)
  if (['doc', 'docx', 'wps'].includes(props.type)) {
    wordTopdf(res)
  } else if (props.type === 'ofd') {
    ofdTopdf(res)
  } else {
    fileUrl.value = URL.createObjectURL(res)
  }
}
const renderedHandler = () => {
  loading.value = false
  console.log('渲染完成')
}
const errorHandler = () => {
  loading.value = false
  console.log('渲染失败')
}
</script>
<style lang="scss">
.preview-pdf {
  width: 100%;
  height: 100%;

  .vue-office-pdf {
    width: 100%;
    height: 100%;

    .vue-office-pdf-wrapper {
      background: transparent !important;
    }
  }
}
</style>
