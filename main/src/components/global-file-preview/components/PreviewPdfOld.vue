<template>
  <div class="PreviewPdf">
    <div id="viewerContainer" ref="containerRef">
      <div id="viewer" class="pdfViewer"></div>
    </div>
  </div>
</template>
<script>
export default { name: 'PreviewPdf' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import 'pdfjs-dist/web/pdf_viewer.css'
import { GlobalWorkerOptions, getDocument } from 'pdfjs-dist'
import { PDFLinkService, PDFViewer, PDFFindController, EventBus } from 'pdfjs-dist/web/pdf_viewer'
import * as pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry'
GlobalWorkerOptions.workerSrc = pdfjsWorker
const props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' }, name: { type: String, default: '' }, fileUrl: { type: String, default: '' } })
const fileUrl = ref('')
const currentPage = ref(1) // 当前页码
const totalPages = ref(0) // 总页码
const ScaleValue = ref('page-fit') // 缩放比例
var eventBus = '' // pdf定义监听事件
var newViewer = '' // pdf实例
const containerRef = ref() // pdf渲染元素
onMounted(() => { globalDownload() })

const wordTopdf = async (file) => {
  const param = new FormData()
  param.append('file', file)
  const res = await api.wordTopdf(param)
  fileUrl.value = URL.createObjectURL(new Blob([res]))
  loadPdf(fileUrl.value)
}
const globalDownload = async () => {
  const res = await api.globalDownload(props.id)
  if (['doc', 'docx', 'wps'].includes(props.type)) {
    wordTopdf(res)
  } else {
    fileUrl.value = URL.createObjectURL(new Blob([res]))
    loadPdf(fileUrl.value)
  }
}
/*加载PDF文件*/
const loadPdf = (url) => {
  eventBus = new EventBus()
  const linkService = new PDFLinkService({ eventBus })
  const findController = new PDFFindController({ eventBus, linkService })
  newViewer = new PDFViewer({ container: containerRef.value, eventBus, linkService, findController })
  linkService.setViewer(newViewer)
  eventBus.on('pagesinit', () => {
    totalPages.value = newViewer.pagesCount
    newViewer.currentScaleValue = ScaleValue.value
    // 监听页码变化
    eventBus._on('pagechanging', e => { currentPage.value = e.pageNumber })
    // 关键字搜索的数量和位置
    eventBus._on('updatefindmatchescount', e => { console.log(e) })
  })
  const loadingTask = getDocument({ url })
  loadingTask.promise.then(pdf => {
    if (pdf) {
      newViewer.setDocument(pdf)
      linkService.setDocument(pdf)
    }
  })
}
// const previousPageClick = () => { // 上一页
//   newViewer.currentPageNumber = currentPage.value === 1 ? totalPages.value : currentPage.value - 1
// }
// const nextPageClick = () => { // 下一页
//   newViewer.currentPageNumber = currentPage.value === totalPages.value ? 1 : currentPage.value + 1
// }
// const zoomInClick = () => {
//   newViewer.currentScaleValue = 1
// }
// const zoomOutClick = () => {
//   newViewer.currentScaleValue = 0.5
// }
// // 关键字搜索
// const searchClick = () => {
//   eventBus.dispatch('find', { phraseSearch: true, query: '环境', findPrevious: false, highlightAll: true })
// }
// // 上一个下一个
// const againClick = (type) => {
//   eventBus.dispatch('find', { type: 'again', phraseSearch: true, query: '环境', findPrevious: type, highlightAll: true })
// }
</script>
<style lang="scss">
.PreviewPdf {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;

  #viewerContainer {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: auto;

    #viewer {
      margin: auto;

      * {
        box-sizing: content-box;
      }
    }
  }
}
</style>
