<template>
  <div class="preview-video">
    <video class="preview-video-body" controlslist="nodownload" ref="videoRef" controls></video>
  </div>
</template>
<script>
export default { name: 'PreviewVideo' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import flvjs from 'flv.js'
const props = defineProps({ id: { type: String, default: '' } })
const videoRef = ref()
const flvPlayer = ref()
const fileUrl = ref('')
onMounted(() => { globalDownload() })

const globalDownload = async () => {
  const res = await api.globalDownload(props.id)
  fileUrl.value = URL.createObjectURL(res)
  createdPlay()
}
// 检测浏览器是否支持 flv.js
const createdPlay = () => {
  if (flvjs.isSupported()) {
    // 创建一个播放器实例
    flvPlayer.value = flvjs.createPlayer({
      type: 'mp4', // 媒体类型，默认是 flv
      isLive: true, // 是否是直播流
      hasAudio: true, // 是否有音频
      hanVideo: true, // 是否有视频
      url: fileUrl.value
    }, {
      autoCleanupMinBackwardDuration: true, // 清除缓存 对 SourceBuffer 进行自动清理
    })
    flvPlayer.value.attachMediaElement(videoRef.value)
    console.log(flvPlayer.value)
    flvPlayer.value.load()
  }
}
</script>
<style lang="scss">
.preview-video {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-video-body {
    width: 691px;
    height: 388px;
  }
}
</style>
