<template>
  <div class="xyl-region">
    <el-popover v-model:visible="show" trigger="click" placement="bottom-start" transition="zy-el-zoom-in-top"
      popper-class="xyl-region-popover" :disabled="disabled">
      <template #reference>
        <div class="xyl-region-view">
          <div class="xyl-region-icon" v-html="regionIcon"></div>
          <div class="xyl-region-name">{{ regionObj.name }}</div>
          <div :class="['xyl-region-icon', show ? 'xyl-region-is-icon' : '']" v-if="!disabled">
            <el-icon>
              <CaretBottom />
            </el-icon>
          </div>
        </div>
      </template>
      <div class="xyl-region-filter" v-if="user.accountId === '1'">
        <el-input v-model="filterText" placeholder="请输入地区名称" clearable />
      </div>
      <el-scrollbar class="xyl-region-tree">
        <el-tree ref="treeRef" highlight-current :node-key="props.nodeKey" :data="regionData" :props="props.props"
          :filter-node-method="filterNode" @node-click="handleNodeClick" />
      </el-scrollbar>
    </el-popover>
  </div>
</template>
<script>
export default { name: 'XylRegion' }
</script>
<script setup>
import { ref, watch, nextTick } from 'vue'
import { user } from 'common/js/system_var.js'
const props = defineProps({
  modelValue: [String, Number],
  data: { type: Array, default: () => [] },
  nodeKey: { type: String, default: 'id' },
  props: { type: Object, default: () => { return { children: 'children', label: 'label' } } }
})
const emit = defineEmits(['update:modelValue', 'select'])

const regionIcon = `<svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19596" width="24" height="24"><path d="M753.810286 731.428571c74.459429 10.678857 137.142857 26.185143 181.76 44.909715 58.002286 24.283429 88.429714 55.588571 88.429714 93.330285 0 29.988571-19.017143 56.027429-55.588571 77.385143-27.209143 15.872-64.731429 29.842286-111.542858 41.398857C764.196571 1011.419429 641.828571 1024 512 1024s-252.196571-12.580571-344.868571-35.547429c-46.811429-11.629714-84.406857-25.6-111.616-41.398857C19.163429 925.769143 0 899.510857 0 869.668571c0-37.814857 30.134857-69.046857 87.917714-93.330285 39.789714-16.676571 93.622857-30.793143 156.818286-41.106286l24.137143-3.657143 10.605714 72.411429c-67.876571 9.728-124.050286 23.478857-162.596571 39.716571-28.598857 12.068571-42.788571 24.137143-42.788572 25.965714 0 6.875429 38.546286 29.769143 111.030857 47.762286C271.872 939.008 387.949714 950.857143 512 950.857143c123.611429 0 239.835429-11.922286 326.875429-33.426286 72.557714-17.993143 111.030857-40.886857 111.030857-47.762286 0-1.828571-14.336-13.897143-43.154286-26.038857-34.523429-14.482286-82.944-26.916571-141.165714-36.352l-22.308572-3.437714 10.532572-72.411429z m15.652571-623.908571a352.841143 352.841143 0 0 1 102.180572 253.44 376.685714 376.685714 0 0 1-99.181715 252.342857l-13.019428 13.458286-213.357715 211.821714a54.198857 54.198857 0 0 1-69.266285 5.632l-6.656-5.632L256.804571 626.834286a376.685714 376.685714 0 0 1-110.445714-265.801143A352.841143 352.841143 0 0 1 248.466286 107.52a369.298286 369.298286 0 0 1 520.923428 0z m-475.721143 46.518857a288.402286 288.402286 0 0 0-83.382857 206.921143 312.100571 312.100571 0 0 0 79.725714 207.140571l12.214858 12.8 206.701714 204.361143 206.701714-204.361143a312.100571 312.100571 0 0 0 91.940572-219.940571 288.402286 288.402286 0 0 0-84.699429-206.994286 304.274286 304.274286 0 0 0-429.202286 0z m215.259429 38.765714a170.642286 170.642286 0 1 1 0 341.357715 170.642286 170.642286 0 0 1 0-341.284572z m0 64.073143a106.642286 106.642286 0 1 0 0 213.284572 106.642286 106.642286 0 0 0 0-213.284572z" fill="#ffffff" p-id="19597"></path></svg>`

const show = ref(false)
const disabled = ref(false)
const treeRef = ref()
const regionId = ref('')
const regionObj = ref({})
const regionData = ref([])
const filterText = ref('')

watch(filterText, (val) => {
  treeRef.value?.filter(val)
})
const filterNode = (value, data) => {
  if (!value) return true
  return data[props.props.label].includes(value)
}
const handleNodeClick = (data) => {
  show.value = false
  filterText.value = ''
  emit('update:modelValue', data[props.nodeKey])
}
// 首次进来默认选中
const selectedMethods = (data, id) => {
  data.forEach(item => {
    if (item[props.nodeKey] === id) {
      if (JSON.stringify(regionObj.value) !== JSON.stringify(item)) {
        regionObj.value = item
        emit('select', regionObj.value)
        nextTick(() => { treeRef.value.setCurrentKey(id) })
      } else {
        nextTick(() => { treeRef.value.setCurrentKey(id) })
      }
    }
    if (item[props.props.children] && item[props.props.children].length) {
      selectedMethods(item[props.props.children], id)
    }
  })
}

watch(() => props.modelValue, () => {
  regionId.value = props.modelValue
  if (regionId.value !== regionObj.value[props.nodeKey]) regionObj.value = {}
  nextTick(() => { treeRef.value?.setCurrentKey(null) })
  selectedMethods(regionData.value, regionId.value)
}, { immediate: true })
watch(() => props.data, () => {
  regionData.value = props.data
  if (props.data.length === 1) {
    const item = props.data[0]
    const length = item[props.props.children].length
    disabled.value = length ? false : true
  } else {
    disabled.value = false
  }
  selectedMethods(regionData.value, regionId.value)
}, { immediate: true })
</script>
<style lang="scss">
.xyl-region {
  display: inline-block;
  margin: auto;
  padding: 0 6px;
  // background-color: var(--zy-el-color-primary);

  .xyl-region-view {
    display: flex;
    align-items: center;
    height: var(--zy-height);

    .xyl-region-icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 22px;
        height: 22px;

        path {
          fill: #fff;
        }
      }
    }

    .xyl-region-name {
      color: #fff;
      padding: 0 6px;
      line-height: var(--zy-line-height);
      font-size: var(--zy-name-font-size);
    }

    .xyl-region-icon {
      width: 24px;
      color: #fff;
      display: flex;
      align-items: center;

      .zy-el-icon {
        transition: all 0.6s;
        transform: rotateZ(0deg);
      }
    }

    .xyl-region-is-icon {
      .zy-el-icon {
        transition: all 0.6s;
        transform: rotateZ(-180deg);
      }
    }
  }
}

.xyl-region-popover {
  width: 290px !important;
  padding: 0 !important;

  .xyl-region-filter {
    padding: var(--zy-distance-five) var(--zy-distance-five) 0 var(--zy-distance-five);

    .zy-el-input {
      --zy-el-input-height: var(--zy-height-routine);
    }
  }

  .zy-el-scrollbar__wrap {
    max-height: 220px;
  }

  .zy-el-scrollbar__view {
    padding: var(--zy-distance-five) 0;
  }

  .xyl-region-tree {
    width: 100%;

    .zy-el-tree-node.is-current {
      &>.zy-el-tree-node__content {
        .zy-el-tree-node__label {
          color: var(--zy-el-color-primary);
        }
      }
    }

    .zy-el-tree-node__content {
      height: var(--zy-height);
    }
  }
}
</style>
