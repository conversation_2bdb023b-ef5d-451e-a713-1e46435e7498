import XylMenu from './xyl-menu/xyl-menu.vue'
import XylTab from './xyl-tab/xyl-tab.vue'
import XylTabItem from './xyl-tab/xyl-tab-item.vue'
import XylRegion from './xyl-region/xyl-region.vue'
import TinyMceEditor from 'common/components/TinyMceEditor/TinyMceEditor.vue'

const components = [XylMenu, XylTab, XylTabItem, XylRegion, TinyMceEditor]
export default { install (app) { components.forEach(v => app.component(v.name, v)) } }
