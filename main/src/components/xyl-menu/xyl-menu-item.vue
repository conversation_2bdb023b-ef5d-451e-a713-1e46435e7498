<template>
  <template v-for="item in menuData">
    <el-sub-menu :index="item.id"
                 :key="item.id"
                 popper-class="xyl-menu-popper"
                 v-if="item.children.length">
      <template #title><span class="xyl-menu-text">{{ item.name }}</span></template>
      <xyl-menu-item :menuData="item.children"></xyl-menu-item>
    </el-sub-menu>
    <el-menu-item :index="item.id"
                  :key="item.id"
                  v-if="!item.children.length">
      <template #title><span class="xyl-menu-text">{{ item.name }}</span></template>
    </el-menu-item>
  </template>
</template>
<script>
export default { name: 'XylMenuItem' }
</script>
<script setup>
import { ref, watch } from 'vue'
const props = defineProps({
  menuData: { type: Array, default: () => [] }
})
const menuData = ref(props.menuData)
watch(() => props.menuData, () => { menuData.value = props.menuData })
</script>
