<template>
  <div class="xyl-menu">
    <el-scrollbar>
      <el-menu :class="['xyl-menu-body', isCollapse ? 'xyl-menu-collapse' : 'xyl-menu-unfold']"
               :collapse="isCollapse"
               :default-active="menuId"
               @select="handleSelect">
        <template v-for="item in menuData">
          <el-sub-menu :index="item.id"
                       :key="item.id"
                       popper-class="xyl-menu-popper"
                       v-if="item.children.length">
            <template #title>
              <el-image fit="cover"
                        :src="item.icon"
                        v-show="isCollapse" />
              <span class="xyl-menu-text">{{ item.name }}</span>
            </template>
            <xyl-menu-item :menuData="item.children"></xyl-menu-item>
          </el-sub-menu>
          <el-menu-item :index="item.id"
                        :key="item.id"
                        v-if="!item.children.length">
            <el-image fit="cover"
                      :src="item.icon"
                      v-show="isCollapse" />
            <template #title>
              <span class="xyl-menu-text">{{ item.name }}</span>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
    </el-scrollbar>
    <div class="xyl-menu-icon">
      <span @click="isCollapse = !isCollapse">
        <el-icon v-if="isCollapse">
          <ArrowRight />
        </el-icon>
        <el-icon v-if="!isCollapse">
          <ArrowLeft />
        </el-icon>
      </span>
    </div>
  </div>
</template>
<script>
export default { name: 'XylMenu' }
</script>
<script setup>
import { ref, watch } from 'vue'
import XylMenuItem from './xyl-menu-item.vue'
const props = defineProps({
  modelValue: [String, Number],
  menuData: { type: Array, default: () => [] }
})
const emit = defineEmits(['update:modelValue', 'select'])

watch(() => props.modelValue, () => { menuId.value = props.modelValue })
watch(() => props.menuData, () => { menuData.value = props.menuData })

const isCollapse = ref(false)
const menuId = ref(props.modelValue)
const menuData = ref(props.menuData)

const handleSelect = (key) => {
  emit('update:modelValue', key)
  selectData(props.menuData, key)
}

const selectData = (data, id) => {
  data.forEach(item => {
    if (item.children.length === 0) {
      if (item.id === id) { emit('select', item) }
    } else {
      selectData(item.children, id)
    }
  })
}
</script>
<style lang="scss">
.xyl-menu {
  width: 100%;
  height: 100%;
  display: flex;

  .zy-el-scrollbar {
    padding-top: 16px;
    background-color: #fff;
  }

  &:hover {
    .xyl-menu-icon {
      span {
        display: block;
      }
    }
  }

  .xyl-menu-body:not(.zy-el-menu--collapse) {
    width: 200px;
  }

  .zy-el-menu {
    border-right: 0;
    --zy-el-menu-bg-color: #fff;

    .zy-el-menu-item.is-active {
      font-weight: bold;
      position: relative;
      background: var(--zy-el-color-primary-light-9);

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 3px;
        height: 100%;
      }
    }

    .zy-el-image {
      width: 24px;
      display: flex;
      align-items: center;
    }
  }

  .xyl-menu-unfold {
    .zy-el-menu-item {
      min-width: 200px;
      height: auto;
      min-height: calc(var(--zy-el-menu-sub-item-height));
      line-height: 1;
    }

    .zy-el-sub-menu__title {
      min-width: 200px;
      height: auto;
      min-height: calc(var(--zy-el-menu-sub-item-height));
      line-height: 1;
      padding-right: 40px;
    }

    .xyl-menu-text {
      display: inline-block;
      width: 100%;
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      padding: var(--zy-distance-four) 0;
      text-overflow: clip;
      white-space: normal;
    }
  }

  .zy-el-menu--collapse {
    .zy-el-menu-item {
      min-width: auto !important;
      height: calc(var(--zy-el-menu-sub-item-height));
      min-height: auto;
      line-height: calc(var(--zy-el-menu-sub-item-height));
    }

    .zy-el-sub-menu__title {
      min-width: auto !important;
      height: calc(var(--zy-el-menu-sub-item-height));
      min-height: auto;
      line-height: calc(var(--zy-el-menu-sub-item-height));
      padding-right: calc(var(--zy-el-menu-base-level-padding));
    }

    .xyl-menu-text {
      text-overflow: initial;
      white-space: nowrap;
    }
  }

  .xyl-menu-icon {
    width: 17px;
    height: 100%;
    display: flex;
    align-items: center;

    span {
      display: none;
      width: 17px;
      height: 83px;
      line-height: 83px;
      background: url("../../assets/img/components/menu_icon.png") no-repeat;
      background-size: 100% 100%;
      background-position: center;
      cursor: pointer;

      .zy-el-icon {
        color: #fff;
      }
    }
  }
}

.xyl-menu-popper {
  .zy-el-menu {
    width: 200px;
  }

  .zy-el-menu-item.is-active {
    font-weight: bold;
    position: relative;
    background: var(--zy-el-color-primary-light-9);

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 3px;
      height: 100%;
    }
  }

  .zy-el-menu-item {
    min-width: 200px;
    height: auto;
    min-height: calc(var(--zy-el-menu-sub-item-height));
    line-height: 1;
  }

  .zy-el-sub-menu__title {
    min-width: 200px;
    height: auto;
    min-height: calc(var(--zy-el-menu-sub-item-height));
    line-height: 1;
    padding-right: 40px;
  }

  .xyl-menu-text {
    display: inline-block;
    width: 100%;
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-four) 0;
    text-overflow: clip;
    white-space: normal;
  }
}</style>
