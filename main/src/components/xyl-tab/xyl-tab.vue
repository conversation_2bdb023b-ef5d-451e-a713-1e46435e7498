<template>
  <div class="xyl-tab" ref="tabRef">
    <div class="xyl-tab-wrap" ref="wrapRef" :style="wrapWidth">
      <div :class="['xyl-tab-scroll', { 'is-scroll': prevShow || nextShow }]" :style="scrollStyle" ref="scrollRef">
        <slot></slot>
      </div>
      <div class="xyl-tab-prev" v-if="prevShow" @click="scrollClick('prev')">
        <el-icon>
          <DArrowLeft />
        </el-icon>
      </div>
      <div class="xyl-tab-next" v-if="nextShow" @click="scrollClick('next')">
        <el-icon>
          <DArrowRight />
        </el-icon>
      </div>
    </div>
    <div class="xyl-tab-feature" ref="featureRef">
      <div class="xyl-tab-item" v-if="feature && number > 1">
        <div class="xyl-tab-item-icon" @click="closeOther">
          <el-icon>
            <Close />
          </el-icon>
        </div>
        <span class="tabSlots" @click="closeOther">关闭其他</span>
      </div>
      <div class="xyl-tab-item" v-if="feature">
        <div class="xyl-tab-item-icon" @click="refresh">
          <el-icon>
            <Refresh />
          </el-icon>
        </div>
        <span class="tabSlots" @click="refresh">刷新</span>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'XylTab' }
</script>
<script setup>
import { ref, onMounted, computed, provide, nextTick, useSlots } from 'vue'
import elementResizeDetectorMaker from 'element-resize-detector'
const slots = useSlots()
const props = defineProps({
  modelValue: [String, Number],
  feature: { type: Boolean, default: false },
  distance: { type: Number, default: 168 }
})
const emit = defineEmits(['update:modelValue', 'tabClick', 'refresh', 'close', 'closeOther'])

const erd = elementResizeDetectorMaker()

const tabRef = ref()
const wrapRef = ref()
const scrollRef = ref()
const featureRef = ref()
const prevShow = ref(false)
const nextShow = ref(false)
const tabWidth = ref()
const wrapWidth = ref()
const scrollLeft = ref(0)
const feature = computed(() => props.feature)
const tabId = computed(() => props.modelValue)
const number = computed(() => slots.default()[0].children.length)
const scrollStyle = computed(() => ({ transform: `translateX(${scrollLeft.value}px)` }))

onMounted(() => {
  nextTick(() => {
    erd.listenTo(tabRef.value, (element) => {
      tabWidth.value = element.offsetWidth
    })
    erd.listenTo(featureRef.value, (element) => {
      wrapWidth.value = `width: calc(100% - ${element.offsetWidth}px);`
    })
  })
})

const scrollClick = (type) => {
  const left = wrapRef.value.offsetWidth - scrollRef.value.scrollWidth
  if (type === 'prev') {
    scrollLeft.value = scrollLeft.value + props.distance > 0 ? 0 : scrollLeft.value + props.distance
  } else if (type === 'next') {
    scrollLeft.value = scrollLeft.value - props.distance < left ? left : scrollLeft.value - props.distance
  }
  prevShow.value = scrollLeft.value !== 0
  nextShow.value = scrollLeft.value !== left
}
const obtainActive = (key, id) => { emit(key, id) }
const tabUpdateRef = (el) => {
  nextTick(() => {
    if (wrapRef.value.offsetWidth > scrollRef.value.offsetWidth) {
      scrollLeft.value = 0
      prevShow.value = false
      nextShow.value = false
      return
    }
    const elLeft = el.offsetLeft
    const elWidth = el.offsetWidth
    const wrapWidth = wrapRef.value.offsetWidth
    const maxLeft = scrollRef.value.offsetWidth - wrapRef.value.offsetWidth
    const elPosition = elLeft + elWidth
    if (maxLeft > (elPosition - wrapWidth)) {
      if (maxLeft > (elPosition - (wrapWidth / 2))) {
        scrollLeft.value = elPosition - (wrapWidth / 2) > 0 ? -(elPosition - (wrapWidth / 2)) : 0
      } else {
        scrollLeft.value = -(maxLeft)
      }
    } else {
      scrollLeft.value = elPosition - wrapWidth > 0 ? -(elPosition - wrapWidth) : 0
    }
    prevShow.value = scrollLeft.value !== 0
    nextShow.value = scrollLeft.value !== -(maxLeft)
  })
}
const refresh = () => {
  obtainActive('refresh', tabId.value)
}
const closeOther = () => {
  obtainActive('closeOther', tabId.value)
}
provide('tabId', tabId)
provide('number', number)
provide('feature', feature)
provide('tabWidth', tabWidth)
provide('obtainActive', obtainActive)
provide('tabUpdateRef', tabUpdateRef)
</script>
<style lang="scss">
.xyl-tab {
  width: 100%;
  margin: auto;
  background-color: #fff;
  border-bottom: 1px solid var(--zy-el-border-color-lighter);
  display: flex;
  align-items: center;

  .xyl-tab-wrap {
    width: 100%;
    overflow: hidden;
    position: relative;

    .xyl-tab-prev,
    .xyl-tab-next {
      position: absolute;
      top: 0;
      height: 100%;
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      z-index: 9;
    }

    .xyl-tab-prev {
      left: 0;
    }

    .xyl-tab-next {
      right: 0;
    }

    .xyl-tab-scroll {
      white-space: nowrap;
      position: relative;
      transition: transform 0.3s;
      float: left;
      display: block;
      z-index: 3;

      &.is-scroll {
        &> :last-child {
          border-right: 1px solid transparent;
        }
      }
    }
  }

  .xyl-tab-item {
    display: inline-flex;
    align-items: center;
    padding: var(--zy-distance-five) 0;
    padding-left: var(--zy-distance-two);
    border-right: 1px solid var(--zy-el-border-color-light);
    cursor: pointer;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: calc(50% - 5px);
      bottom: 0;
      transform: translateX(-50%);
      width: 38px;
      height: 2px;
      background-color: transparent;
    }

    .tabSlots {
      display: inline-block;
      line-height: var(--zy-line-height);
      font-size: var(--zy-name-font-size);
    }

    .xyl-tab-item-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      padding-left: var(--zy-distance-four);
    }

    .tab-popover-icon {
      display: inline-block;
      height: calc(var(--zy-name-font-size) * var(--zy-line-height));
      padding: 0 var(--zy-distance-three);
      line-height: var(--zy-line-height);
      font-size: var(--zy-name-font-size);
      background: url("../../assets/img/components/tab_icon.png") no-repeat;
      background-size: 9px 20px;
      background-position: center;
      vertical-align: middle;

      &:focus {
        outline: none;
      }

      &.is-active {
        background: url("../../assets/img/components/tab_icon_is.png") no-repeat;
        background-size: 9px 20px;
        background-position: center;
      }
    }

    &.is-active {
      font-weight: 700;

      &::after {
        background-color: var(--zy-el-color-primary);
      }
    }

    &.is-feature {
      padding: var(--zy-distance-five) var(--zy-distance-two);
    }
  }


  .xyl-tab-feature {
    height: 100%;
    display: inline-flex;

    .xyl-tab-item {
      border: 0;
      border-left: 1px solid var(--zy-el-border-color-light);
      padding: var(--zy-distance-five) var(--zy-distance-two);
      white-space: nowrap;

      .xyl-tab-item-icon {
        padding: 0
      }
    }
  }
}

.xyl-tab-popover {
  padding: 0 !important;

  .xyl-tab-extension {
    .xyl-tab-extension-item {
      width: 100%;
      padding: var(--zy-font-text-distance-five) var(--zy-distance-five);
      line-height: var(--zy-line-height);
      font-size: var(--zy-text-font-size);
      color: var(--zy-el-text-color-secondary);
      box-shadow: var(--zy-el-box-shadow-light);
      border: 1px solid var(--zy-el-border-color-light);
      cursor: pointer;
    }

    .xyl-tab-extension-item+.xyl-tab-extension-item {
      border-top: 0;
      border-bottom: 1px solid var(--zy-el-border-color-light);
    }
  }
}
</style>
