<template>
  <div :class="['xyl-tab-item forbidSelect', { 'is-feature': feature }, { 'is-active': tabId === value }]"
    @click="tabClick" ref="XylTabItem">
    <span class="tabSlots">
      <slot></slot>
    </span>
    <div class="xyl-tab-item-icon" @click.stop="close" v-if="feature && number > 1">
      <el-icon>
        <Close />
      </el-icon>
    </div>
    <template v-if="!feature">
      <el-popover v-model:visible="show" :offset="2" trigger="click" :show-arrow="false" placement="bottom-start"
        transition="zy-el-zoom-in-top" popper-class="xyl-tab-popover">
        <template #reference>
          <div :class="['tab-popover-icon', { 'is-active': show }]" @click.stop></div>
        </template>
        <div class="xyl-tab-extension">
          <div class="xyl-tab-extension-item" v-if="tabId === value" @click="refresh">刷新</div>
          <div class="xyl-tab-extension-item" v-if="number > 1" @click="close">关闭</div>
          <div class="xyl-tab-extension-item" v-if="number > 1" @click="closeOther">关闭其他</div>
        </div>
      </el-popover>
    </template>
  </div>
</template>
<script>
export default { name: 'XylTabItem' }
</script>
<script setup>
import { ref, computed, watch, inject, onMounted } from 'vue'
const props = defineProps({ value: [String, Number] })
const tabId = inject('tabId')
const number = inject('number')
const feature = inject('feature')
const tabWidth = inject('tabWidth')
const obtainActive = inject('obtainActive')
const tabUpdateRef = inject('tabUpdateRef')

const show = ref(false)
const XylTabItem = ref()
const value = computed(() => props.value)
watch(() => [tabId.value, number.value, tabWidth.value], () => { if (value.value === tabId.value) { tabUpdateRef(XylTabItem.value) } })
onMounted(() => { if (value.value === tabId.value) { tabUpdateRef(XylTabItem.value) } })
const tabClick = () => {
  obtainActive('update:modelValue', value.value)
  obtainActive('tabClick', value.value)
  tabUpdateRef(XylTabItem.value)
}
const refresh = () => {
  show.value = false
  obtainActive('refresh', value.value)
}
const close = () => {
  show.value = false
  obtainActive('close', value.value)
}
const closeOther = () => {
  show.value = false
  obtainActive('closeOther', value.value)
}
</script>
