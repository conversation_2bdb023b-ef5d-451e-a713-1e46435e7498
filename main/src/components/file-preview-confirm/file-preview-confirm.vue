<template>
  <el-config-provider :locale="locale" namespace="zy-el">
    <div class="file-preview-confirm-wrapper" @click="handleClose">
      <transition name="zy-el-zoom-in-bottom">
        <div class="file-preview-confirm" v-if="elIsShow" @click.stop>
          <global-file-preview :query="props.query" is-close @close="handleClose"></global-file-preview>
        </div>
      </transition>
    </div>
  </el-config-provider>
</template>

<script>
export default { name: 'FilePreviewConfirm' }
</script>
<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { qiankunActions } from '@/qiankun'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
const GlobalFilePreview = defineAsyncComponent(() => import('@/components/global-file-preview/global-file-preview.vue'))
const locale = zhCn
const props = defineProps({
  query: { type: Object, default: () => ({}) },
  closeCallback: { type: Function }
})
const elIsShow = ref(false)

onMounted(() => {
  setTimeout(() => {
    elIsShow.value = true
  }, 168)
})
const handleClose = () => {
  elIsShow.value = false
  qiankunActions.setGlobalState({ filePreview: {} })
  setTimeout(() => {
    props.closeCallback()
  }, 168)
}
</script>
<style lang="scss" scoped>
.file-preview-confirm-wrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 9999;

  .file-preview-confirm {
    width: 100%;
    height: 90%;
    background: #fff;
    border-radius: 10px 10px 0px 0px;
  }
}
</style>
