import { createVNode, render } from 'vue'
import FilePreviewConfirm from './file-preview-confirm.vue'

export default ({ query }) => {
  // 点击关闭按钮，销毁组件
  const closeCallback = () => {
    render(null, document.body)
  }
  // 创建组件实例
  const VNode = createVNode(FilePreviewConfirm, { query, closeCallback })
  // 获取主应用实例
  const mainApp = document.querySelector('#app')?.__vue_app__
  if (mainApp) {
    VNode.appContext = mainApp._context
  }
  // 渲染组件
  render(VNode, document.body)
}
// import { createApp } from 'vue'
// import store from '@/store'
// import FilePreviewConfirm from './file-preview-confirm.vue'

// export default ({ query }) => {
//   // 点击关闭按钮，销毁组件
//   const closeCallback = () => {
//     app.unmount()
//     document.body.removeChild(container)
//   }
//   // 创建容器
//   const container = document.createElement('div')
//   document.body.appendChild(container)
//   // 创建 Vue 应用实例
//   const app = createApp(FilePreviewConfirm, { query, closeCallback })
//   // 使用 store
//   app.use(store)
//   // 挂载应用
//   app.mount(container)
// }