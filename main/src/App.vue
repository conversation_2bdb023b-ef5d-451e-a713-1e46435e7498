<template>
  <el-config-provider :locale="locale" namespace="zy-el">
    <router-view></router-view>
    <global-central-control></global-central-control>
  </el-config-provider>
</template>
<script setup>
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import GlobalCentralControl from '@/components/global-central-control/global-central-control.vue'
const locale = zhCn
</script>
<style lang="scss">
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: Microsoft YaHei;
}
</style>
