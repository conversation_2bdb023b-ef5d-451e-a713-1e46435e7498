import router from '@/router'
import store from '@/store'
import { detectionVersion } from 'common/js/CheckVersion'
import { currentTheme, globalReadOpenConfig } from 'common/js/GlobalMethod'
import 'customize/config//entry.js'
import { firstTime } from 'customize/config/entry'
var sign = ''
currentTheme()
const globalEntry = (to, next) => {
  globalReadOpenConfig((data) => {
    detectionVersion()
    sign = data?.systemSign
    firstTime(to, next, () => {
      if (sessionStorage.getItem('token')) {
        store.dispatch('loginUser')
        if (to.meta.moduleName === 'verify' || to.name === 'LoginView') {
          next({ path: '/' })
        } else {
          next()
        }
      } else {
        if (to.meta.moduleName !== 'verify' && to.name !== 'LoginView' && to.name !== 'FilePreview') {
          next({ path: '/LoginView' })
        } else {
          next()
        }
      }
    })
  })
}
router.beforeEach((to, from, next) => {
  // if (process.env.NODE_ENV !== 'development') { throttleIsNewVersion() }
  if (sign) {
    if (sessionStorage.getItem('token')) {
      if (to.meta.moduleName === 'verify' || to.name === 'LoginView') {
        next({ path: '/' })
      } else {
        next()
      }
    } else {
      if (to.meta.moduleName !== 'verify' && to.name !== 'LoginView' && to.name !== 'FilePreview') {
        next({ path: '/LoginView' })
      } else {
        next()
      }
    }
  } else {
    globalEntry(to, next)
  }
})
