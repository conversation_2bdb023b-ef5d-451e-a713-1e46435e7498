import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import {
  ElConfigProvider,
  ElScrollbar,
  ElPopover,
  ElIcon,
  ElButton,
  ElTable,
  ElTableV2,
  ElPagination,
  ElForm,
  ElFormItem,
  ElCheckboxGroup,
  ElCheckbox,
  ElRadioGroup,
  ElRadioButton,
  ElRadio,
  ElInput,
  ElSelect,
  ElOption,
  ElSelectV2,
  ElTreeSelect,
  ElDatePicker,
  ElTree,
  ElTreeV2,
  ElTabs,
  ElTabPane,
  ElContainer,
  ElHeader,
  ElAside,
  ElMain,
  ElImage,
  ElMenu,
  ElSubMenu,
  ElMenuItem,
  ElProgress,
  ElUpload,
  ElTag,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElEmpty,
  ElCarousel,
  ElCarouselItem,
  ElImageViewer,
  ElInfiniteScroll,
  ElInputNumber,
  ElLink,
  ElCollapse,
  ElCollapseItem,
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElBadge,
  ElBacktop,
  ElAutocomplete
} from 'element-plus'
ElTable.props['scrollbar-always-on'] = {
  type: Boolean,
  default: true
}
const components = [
  ElConfigProvider,
  ElScrollbar,
  ElPopover,
  ElIcon,
  ElButton,
  ElTable,
  ElTableV2,
  ElPagination,
  ElForm,
  ElFormItem,
  ElCheckboxGroup,
  ElCheckbox,
  ElRadioGroup,
  ElRadioButton,
  ElRadio,
  ElInput,
  ElSelect,
  ElOption,
  ElSelectV2,
  ElTreeSelect,
  ElDatePicker,
  ElTree,
  ElTreeV2,
  ElTabs,
  ElTabPane,
  ElContainer,
  ElHeader,
  ElAside,
  ElMain,
  ElImage,
  ElMenu,
  ElSubMenu,
  ElMenuItem,
  ElProgress,
  ElUpload,
  ElTag,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElEmpty,
  ElCarousel,
  ElCarouselItem,
  ElImageViewer,
  ElInfiniteScroll,
  ElInputNumber,
  ElLink,
  ElCollapse,
  ElCollapseItem,
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElBadge,
  ElBacktop,
  ElAutocomplete
]
export default {
  install (app) {
    components.forEach(v => app.component(v.name, v))
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
  }
}