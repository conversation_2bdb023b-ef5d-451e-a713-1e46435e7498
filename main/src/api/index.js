// 导入封装的方法
import HTTP from 'common/http'
import GlobalApi from 'common/http/GlobalApi'
import customizeApi from 'customize/config/api'
const api = {
  ...GlobalApi,
  ...customizeApi,
  personalDoList (params) { // 个人待办列表
    return HTTP.json('/pendingMessage/list', params)
  },
  boxMessageList (params) { // 消息盒子列表
    return HTTP.json('/boxMessage/list', params)
  },
  boxMessageRead (params) { // 消息盒子全部标记已读
    return HTTP.json('/redDot/signBatch', params)
  }
}
export default api
