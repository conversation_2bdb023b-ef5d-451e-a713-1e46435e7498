import store from '@/store'
import router from '@/router'
import config from 'common/config'
import { initGlobalState } from 'qiankun'
import { AiChatClass } from 'common/js/GlobalClass.js'
import { loginOutClear } from 'common/js/GlobalMethod.js'
import { ElMessageBox } from 'element-plus'
// 定义全局下发的数据
export const initialState = {
  mainWindow: window,
  AiChatClass: AiChatClass,
  // 全局配置
  globalConfig: null,
  theme: {},
  // 用户信息
  user: {},
  menu: [],
  area: [],
  role: [],
  readConfig: {},
  readOpenConfig: {},
  versionUpdate: false,
  // 实现子应用直接跳转子应用
  openRoute: { name: '', path: '', query: {} },
  // 实现子应用关闭当前页面跳转子应用
  closeOpenRoute: { openId: '', closeId: '' },
  // 页面路由缓存
  keepAliveRoute: [],
  // 刷新页面
  refreshRoute: '',
  // 刷新页面
  code: 200,
  // 文件预览
  filePreview: {},
  // 导入文件
  importList: [],
  // 下载附件
  downloadFile: [],
  // 批量下载附件
  batchDownloadFile: [],
  // 扩展下载附件
  extendDownloadFile: [],
  // 导出word
  exportWordObj: {},
  // 导出word带html的
  exportWordHtmlObj: {},
  // 批量导出word
  exportWordList: {},
  // 批量导出word带html的
  exportWordHtmlList: {},
  AiChatCode: '',
  AiChatElShow: true,
  AiChatWidth: 400,
  AiChatConfig: {},
  AiChatWindow: '',
  AiChatFile: [],
  AiChatParams: {},
  AiChatContent: '',
  AiChatSetContent: '',
  AiChatAddContent: '',
  AiChatSendMessage: '',
  AiChatToolSendMessage: {},
  // 操作任务管理器
  globalCentralControlObj: {},
  // 消息盒子刷新
  boxMessageRefresh: false,
  // 待办刷新
  personalDoRefresh: false
}
// 初始化全局下发的数据
export const qiankunActions = initGlobalState(initialState)

// 检测全局下发数据的改变
qiankunActions.onGlobalStateChange((state) => {
  if (JSON.stringify(state.filePreview) !== '{}') store.dispatch('handleFilePreview', state.filePreview)
  // 修改全局下发的数据
  if (JSON.stringify(store.state.AiChatCode) !== JSON.stringify(state.AiChatCode)) {
    store.commit('setAiChatCode', state.AiChatCode)
  }
  if (JSON.stringify(store.state.AiChatElShow) !== JSON.stringify(state.AiChatElShow)) {
    store.commit('setAiChatElShow', state.AiChatElShow)
  }
  if (JSON.stringify(store.state.AiChatWidth) !== JSON.stringify(state.AiChatWidth)) {
    store.commit('setAiChatWidth', state.AiChatWidth)
  }
  if (JSON.stringify(store.state.AiChatConfig) !== JSON.stringify(state.AiChatConfig)) {
    store.commit('setAiChatConfig', state.AiChatConfig)
  }
  if (JSON.stringify(store.state.AiChatWindow) !== JSON.stringify(state.AiChatWindow)) {
    store.commit('setAiChatWindow', state.AiChatWindow)
  }
  if (JSON.stringify(store.state.AiChatFile) !== JSON.stringify(state.AiChatFile)) {
    store.commit('setAiChatFile', state.AiChatFile)
  }
  if (JSON.stringify(store.state.AiChatParams) !== JSON.stringify(state.AiChatParams)) {
    store.commit('setAiChatParams', state.AiChatParams)
  }
  if (JSON.stringify(store.state.AiChatContent) !== JSON.stringify(state.AiChatContent)) {
    store.commit('setAiChatContent', state.AiChatContent)
  }
  if (JSON.stringify(store.state.AiChatSetContent) !== JSON.stringify(state.AiChatSetContent)) {
    store.commit('setAiChatSetContent', state.AiChatSetContent)
  }
  if (JSON.stringify(store.state.AiChatAddContent) !== JSON.stringify(state.AiChatAddContent)) {
    store.commit('setAiChatAddContent', state.AiChatAddContent)
  }
  if (JSON.stringify(store.state.AiChatSendMessage) !== JSON.stringify(state.AiChatSendMessage)) {
    store.commit('setAiChatSendMessage', state.AiChatSendMessage)
  }
  if (JSON.stringify(store.state.AiChatToolSendMessage) !== JSON.stringify(state.AiChatToolSendMessage)) {
    store.commit('setAiChatToolSendMessage', state.AiChatToolSendMessage)
  }
  if (JSON.stringify(store.state.openRoute) !== JSON.stringify(state.openRoute)) {
    store.commit('setOpenRoute', state.openRoute)
  }
  if (JSON.stringify(store.state.closeOpenRoute) !== JSON.stringify(state.closeOpenRoute)) {
    store.commit('setCloseOpenRoute', state.closeOpenRoute)
  }
  if (JSON.stringify(store.state.versionUpdate) !== JSON.stringify(state.versionUpdate)) {
    store.commit('setVersionUpdate', state.versionUpdate)
    if (state.versionUpdate) {
      const VersionUpdatePrompt = sessionStorage.getItem('VersionUpdatePrompt') || ''
      if (VersionUpdatePrompt === 'true') {
        ElMessageBox.confirm('检测到新版本，建议立即更新以确保平台正常使用。', '更新提示', {
          confirmButtonText: '确认更新',
          cancelButtonText: '稍后更新',
          type: 'success'
        }).then(() => {
          window.location.href = `${config.mainPath}?v=${new Date().getTime()}`
          // window.location.reload(true)
        })
      } else {
        window.location.href = `${config.mainPath}?v=${new Date().getTime()}`
        // window.location.reload(true)
      }
    }
  }
  if (JSON.stringify(store.state.importList) !== JSON.stringify(state.importList)) {
    store.commit('setImportList', state.importList)
  }
  if (JSON.stringify(store.state.downloadFile) !== JSON.stringify(state.downloadFile)) {
    store.commit('setDownloadFile', state.downloadFile)
  }
  if (JSON.stringify(store.state.batchDownloadFile) !== JSON.stringify(state.batchDownloadFile)) {
    store.commit('setBatchDownloadFile', state.batchDownloadFile)
  }
  if (JSON.stringify(store.state.extendDownloadFile) !== JSON.stringify(state.extendDownloadFile)) {
    store.commit('setExtendDownloadFile', state.extendDownloadFile)
  }
  if (JSON.stringify(store.state.exportWordObj) !== JSON.stringify(state.exportWordObj)) {
    store.commit('setExportWordObj', state.exportWordObj)
  }
  if (JSON.stringify(store.state.exportWordHtmlObj) !== JSON.stringify(state.exportWordHtmlObj)) {
    store.commit('setExportWordHtmlObj', state.exportWordHtmlObj)
  }
  if (JSON.stringify(store.state.exportWordList) !== JSON.stringify(state.exportWordList)) {
    store.commit('setExportWordList', state.exportWordList)
  }
  if (JSON.stringify(store.state.exportWordHtmlList) !== JSON.stringify(state.exportWordHtmlList)) {
    store.commit('setExportWordHtmlList', state.exportWordHtmlList)
  }
  if (JSON.stringify(store.state.globalCentralControlObj) !== JSON.stringify(state.globalCentralControlObj)) {
    store.commit('setGlobalCentralControlObj', state.globalCentralControlObj)
  }
  if (JSON.stringify(store.state.boxMessageRefresh) !== JSON.stringify(state.boxMessageRefresh)) {
    store.commit('setBoxMessageRefresh', state.boxMessageRefresh)
  }
  if (JSON.stringify(store.state.personalDoRefresh) !== JSON.stringify(state.personalDoRefresh)) {
    store.commit('setPersonalDoRefresh', state.personalDoRefresh)
  }
  if (state.code === 302) {
    loginOutClear()
    qiankunActions.setGlobalState({ code: 200 })
  }
  if (state.code === 403) {
    router.push({ path: '/NoneAccessAuthority' })
    qiankunActions.setGlobalState({ code: 200 })
  }
})
