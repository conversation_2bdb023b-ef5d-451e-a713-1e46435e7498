import config from 'common/config'
import { initialState } from './globalState'
import { loadMicroApp, prefetchApps } from 'qiankun'

export { qiankunActions } from './globalState'

const { microApp, prefetchApp } = config
const microAppItem = (name, entry) => ({ name, entry, container: `#${name}`, props: { routerBase: config.catalog + name, globalState: initialState } })

/* 微应用预加载 */
export const prefetchMicroApps = () => prefetchApps(prefetchApp)
/* 微应用预加载 */
export const prefetchMicroMenu = (data) => prefetchApps(prefetchApp.filter(v => data.includes(v.name)))
/* 加载微应用 */
export const loadFilterApp = (key) => Object.hasOwnProperty.call(microApp, key) ? loadMicroApp(microAppItem(key, microApp[key])) : null
