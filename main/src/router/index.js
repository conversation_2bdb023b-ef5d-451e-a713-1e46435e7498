import { createRouter, createWebHistory } from 'vue-router'

import customizeRouter from 'customize/config/router'

const FilePreview = () => import('@/views/FilePreview/FilePreview.vue')
const MicroAppContainer = () => import('@/views/MicroAppContainer/MicroAppContainer.vue')

const routes = [
  ...customizeRouter,
  { path: '/FilePreview', name: 'FilePreview', component: FilePreview },
  { path: '/MicroAppContainer:pathMatch(.*)*', name: 'MicroAppContainer', component: MicroAppContainer }
]
const router = createRouter({
  // scrollBehavior: () => {
  //   console.log(document.URL)
  //   history.pushState(null, null, document.URL)
  // },
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router
