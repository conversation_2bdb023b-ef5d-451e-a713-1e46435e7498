<template>
  <SubAppViewport :name="name"></SubAppViewport>
</template>
<script>
export default { name: 'MicroAppContainer' }
</script>
<script setup>
import config from 'common/config'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { loadMicroApp } from 'qiankun'
import { qiankunActions } from '@/qiankun'
import { initialState } from '@/qiankun/globalState'
const { microApp } = config
const SubAppViewport = {
  name: 'SubAppViewport',
  props: ['name'],
  template: `<div :id="name" class="MicroAppContainer"></div>`
}
const route = useRoute()
const router = useRouter()
const store = useStore()
const name = ref(route.query.name)
const path = ref(route.query.path)
const microAppEl = ref()
onMounted(() => {
  loadFilterApp()
})
const microAppItem = (name, entry) => ({
  name,
  entry,
  container: `#${name}`,
  props: { routerBase: `${config.catalog}MicroAppContainer/${name}`, globalState: initialState }
})
const loadFilterApp = () => {
  if (!Object.hasOwnProperty.call(microApp, name.value)) return router.push({ path: '/NotFoundPage' })
  microAppEl.value = loadMicroApp(microAppItem(name.value, microApp[name.value]))
  microAppEl.value.mountPromise.then(() => {
    qiankunActions.setGlobalState({
      theme: store.getters.getThemeFn,
      user: store.getters.getUserFn,
      menu: store.getters.getMenuFn,
      area: store.getters.getAreaFn,
      role: store.getters.getRoleFn,
      readConfig: store.getters.getReadConfig,
      readOpenConfig: store.getters.getReadOpenConfig
    })
  })
  router.push({ path: `/MicroAppContainer/${name.value}/${path.value}`, query: route.query })
}
onBeforeUnmount(() => {
  microAppEl.value?.unmount()
  microAppEl.value = null
})
</script>
<style lang="scss">
.MicroAppContainer {
  width: 100%;
  height: 100%;

  > div {
    width: 100%;
    height: 100%;
  }
}
</style>
