<template>
  <div class="FilePreview">
    <global-file-preview :query="route.query"></global-file-preview>
  </div>
</template>
<script>
export default { name: 'FilePreview' }
</script>
<script setup>
import { useRoute } from 'vue-router'
import { defineAsyncComponent } from 'vue'
const GlobalFilePreview = defineAsyncComponent(() => import('@/components/global-file-preview/global-file-preview.vue'))
const route = useRoute()
</script>
<style lang="scss">
.FilePreview {
  width: 100%;
  height: 100%;
}
</style>
