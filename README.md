## 产品5.0架构

**1、框架:** VUE 3.0

**2、微前端框架:** qiankun

**3、UI库:** Element Plus

**4、ajax请求库:** axios



## 微前端 qiankun

**1、复杂度可控:** 每一个业务模块可以独立开发,避免代码巨无霸,保持开发时的高速编译,保持较低的复杂度,便于维护与开发效率。

![image-20220803155224811](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20220803155224811.png)



**2、独立部署:** 每一个模块可单独部署,不对其他模块有任何影响。

**3、技术选型灵活:** 也是最具吸引力的,在同一项目下可以使用如今市面上所有前端技术栈vue react angular, 也包括未来的前端技术栈。

**4、容错:** 单个模块发生错误,不影响全局，就跟后端微服务一样。

**5、扩展:** 每一个服务可以独立横向扩展以满足业务伸缩性，解决资源的不必要消耗。



## 结构




```
|———common     		公共代码块
 |———config    		 公共的配置
 |———http    		 封装的请求方法
|———main       		基座 = 主应用
|———demo       		子应用 // 例子
```

![image-20220803155815092](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20220803155815092.png)
