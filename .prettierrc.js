module.exports = {
  // ================ 基础配置 ================
  // 每行代码长度，设置为 Infinity 表示不限制行长度，不会自动换行
  // 可选值：数字，默认 80
  printWidth: 120,

  // 缩进空格数
  // 可选值：数字，默认 2
  tabWidth: 2,

  // 是否使用制表符缩进
  // 可选值：true | false，默认 false
  useTabs: false,

  // 是否在语句末尾添加分号
  // 可选值：true | false，默认 true
  semi: false,

  // 是否使用单引号
  // 可选值：true | false，默认 false
  singleQuote: true,

  // 对象属性引号处理
  // 可选值：
  // - "as-needed" - 仅在必要时添加引号
  // - "consistent" - 如果对象中至少有一个属性需要引号，则所有属性都添加引号
  // - "preserve" - 保持原始引号
  quoteProps: 'as-needed',

  // JSX 中是否使用单引号
  // 可选值：true | false，默认 false
  jsxSingleQuote: false,

  // 尾随逗号方式
  // 可选值：
  // - "none" - 不添加尾随逗号
  // - "es5" - 在 ES5 中有效的尾随逗号（对象、数组等）
  // - "all" - 尽可能添加尾随逗号
  trailingComma: 'none',

  // 对象字面量中的括号空格
  // 可选值：true | false，默认 true
  // true: { foo: bar }
  // false: {foo: bar}
  bracketSpacing: true,

  // 是否将 > 放在同一行
  // 可选值：true | false，默认 false
  // true: <div>
  //       </div>
  // false: <div></div>
  bracketSameLine: true,

  // 箭头函数参数括号
  // 可选值：
  // - "always" - 始终使用括号
  // - "avoid" - 当只有一个参数时省略括号
  arrowParens: 'always',

  // 函数名和括号之间的空格
  // 可选值：
  // - true - 在函数名和括号之间添加空格
  // - false - 在函数名和括号之间不添加空格
  spaceBeforeFunctionParen: true,

  // 函数参数括号内的空格
  // 可选值：
  // - true - 在函数参数括号内添加空格
  // - false - 在函数参数括号内不添加空格
  functionParens: true,

  // 行尾换行符
  // 可选值：
  // - "lf" - 仅换行（\n）
  // - "crlf" - 回车换行（\r\n）
  // - "cr" - 仅回车（\r）
  // - "auto" - 保持现有的行尾
  endOfLine: 'lf',

  // ================ Vue 相关配置 ================
  // Vue 文件中的 script 和 style 标签是否缩进
  // 可选值：true | false，默认 false
  vueIndentScriptAndStyle: false,

  // 是否强制每个属性单独一行
  // 可选值：true | false，默认 false
  // true: <div
  //   id="foo"
  //   class="bar"
  // >
  // false: <div id="foo" class="bar">
  singleAttributePerLine: false,

  // HTML 空白敏感度
  // 可选值：
  // - "css" - 遵循 CSS display 属性的默认值
  // - "strict" - 空格被认为是敏感的
  // - "ignore" - 空格被认为是不敏感的
  htmlWhitespaceSensitivity: 'ignore',

  // 是否格式化嵌入的代码
  // 可选值：
  // - "auto" - 如果 Prettier 可以识别，则格式化嵌入的代码
  // - "off" - 从不格式化嵌入的代码
  embeddedLanguageFormatting: 'auto',

  // ================ 其他配置 ================
  // Markdown 文本换行方式
  // 可选值：
  // - "always" - 如果超过 printWidth，则换行
  // - "never" - 从不换行
  // - "preserve" - 保持原样
  proseWrap: 'preserve',

  // 是否需要特殊注释才格式化
  // 可选值：true | false，默认 false
  requirePragma: false,

  // 是否在格式化后的文件顶部插入特殊注释
  // 可选值：true | false，默认 false
  insertPragma: false
}
