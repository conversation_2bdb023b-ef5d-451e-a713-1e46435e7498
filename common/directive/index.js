import { ElMessage } from 'element-plus'
import { hasPermission } from '../js/permissions'
import elSelectLoadMore from './elSelectLoadMore'
export default {
  install (app) {
    app.directive('has', {
      mounted (el, binding) {
        if (!binding.value) return
        if (!hasPermission(binding.value, binding.arg)) {
          el.parentNode.removeChild(el)
        }
      }
    })
    app.directive('copy', {
      mounted (el, { value }) {
        el.$value = value
        el.handler = () => {
          if (!el.$value) {
            ElMessage({ message: '无复制内容', type: 'warning' })
            return
          }
          const textarea = document.createElement('textarea')
          textarea.readOnly = 'readonly'
          textarea.style.position = 'absolute'
          textarea.style.left = '-9999px'
          textarea.value = el.$value
          document.body.appendChild(textarea)
          textarea.select()
          const result = document.execCommand('Copy')
          if (result) {
            ElMessage({ message: '复制成功', type: 'success' })
          }
          document.body.removeChild(textarea)
        }
        el.addEventListener('click', el.handler)
      },
      updated (el, { value }) {
        el.$value = value
      },
      unmounted (el) {
        el.removeEventListener('click', el.handler)
      }
    })
    app.directive('selectLoadmore', elSelectLoadMore)
  }
}
