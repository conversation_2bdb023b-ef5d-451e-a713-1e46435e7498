@page {
  size: auto;
  margin: 3.18cm 2.54cm;
  box-sizing: border-box;
}
// @font-face {
//   font-family: FZPHFW;
//   src: url('common/font/FZPHFW.TTF');
// }
// @font-face {
//   font-family: LCD2;
//   src: url('common/font/LCD2 Bold.ttf');
// }
// @font-face {
//   font-family: YouSheBiaoTiHei;
//   src: url('common/font/YouSheBiaoTiHei.ttf');
// }
@mixin change_font_family() {
  font-family: Microsoft YaHei;

  [data-family='cn'] & {
    font-family: Microsoft YaHei;
  }

  [data-family='tw'] & {
    font-family: FZPHFW;
  }

  [data-family='zangwenxinbai'] & {
    font-family: zangwenxinbai;
  }
}
@mixin grey() {
  [grey='1'] & {
    filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  @include change_font_family();
}
::selection {
  color: #fff;
  background-color: #008cff;
}
body {
  @include grey();
  color: var(--zy-el-text-color-primary);
}
html,
body,
#app {
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}
// 禁止选中
.forbidSelect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
// 单行超出省略号显示
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 所有浏览器隐藏滚动条样式
.scrollBar {
  /*IE下隐藏滚动条*/
  -ms-scroll-chaining: chained;
  -ms-overflow-style: none;
  -ms-content-zooming: zoom;
  -ms-scroll-rails: none;
  -ms-content-zoom-limit-min: 100%;
  -ms-content-zoom-limit-max: 500%;
  // -ms-scroll-snap-type: proximity;
  -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
  -ms-overflow-style: none;
  overflow: auto;
  /*火狐下隐藏滚动条*/
  scrollbar-width: none;

  /*谷歌下隐藏滚动条*/
  &::-webkit-scrollbar {
    display: none;
  }
}

.zy-el-select__popper {
  .zy-el-select-dropdown__item {
    height: var(--zy-height);
    line-height: var(--zy-height);
  }
  .zy-el-tree-node__content {
    height: var(--zy-height);
  }
}
.zy-el-scrollbar {
  .is-horizontal {
    height: 12px;

    .zy-el-scrollbar__thumb {
      opacity: 0.5;
      &:hover {
        opacity: 0.9;
      }
    }
  }
  .is-vertical {
    width: 10px;
    .zy-el-scrollbar__thumb {
      opacity: 0.5;
      &:hover {
        opacity: 0.9;
      }
    }
  }
}
.globalTable {
  .zy-el-table {
    height: 100%;
    --zy-el-table-text-color: var(--zy-el-text-color-primary);
    --zy-el-table-header-text-color: var(--zy-el-text-color-primary);
    --zy-el-table-header-bg-color: var(--zy-el-color-info-light-8);
    --zy-el-table-vertical-bg-color: #666;
    --zy-el-table-horizontal-bg-color: #ddd;
    thead {
      font-weight: bold;
      .zy-el-table__cell {
        background: var(--zy-el-color-info-light-8) !important;
      }
    }
    .zy-el-table__body {
      padding-bottom: 16px;
    }
    .zy-el-table__cell {
      border-right: none !important;
      padding: var(--zy-distance-five) 0;
    }
    .cell {
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      .globalTableCheck {
        font-size: 18px;
        color: var(--zy-el-color-success);
      }
      .globalTableClose {
        font-size: 18px;
        color: var(--zy-el-color-error);
      }
      .globalTableClock {
        font-size: 18px;
        color: var(--zy-el-color-warning);
      }
      .globalTableLock,
      .globalTableUnlock {
        font-size: 18px;
        color: var(--zy-el-text-color-primary);
      }
    }
    .is-horizontal {
      height: 12px;

      .zy-el-scrollbar__thumb {
        opacity: 0.5;
        &:hover {
          opacity: 0.9;
        }
      }
    }
    .is-vertical {
      width: 10px;
      .zy-el-scrollbar__thumb {
        opacity: 0.5;
        &:hover {
          opacity: 0.9;
        }
      }
    }
    .globalTableCustom {
      position: relative;
      .TableCustomIcon {
        position: absolute;
        right: 20px;
        top: 50%;
        width: 20px;
        height: 20px;
        transform: translateY(-50%);
        background: url('../../assets/img/components/table_custom_icon.png') no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
    tbody {
      .globalTableCustom {
        padding: 8px 0 !important;
        .zy-el-button {
          height: var(--zy-height-secondary);
          padding: 6px 12px;
          border-radius: var(--el-border-radius-small);
        }
        .zy-el-button + .zy-el-dropdown {
          margin-left: 12px;
        }
      }
      .globalTableImg {
        padding: 5px 0 !important;
        .cell {
          height: var(--zy-height-routine);
          .zy-el-image {
            height: var(--zy-height-routine);
            img {
              width: auto;
            }
            .zy-el-image__error {
              display: none;
            }
          }
        }
      }
      .globalTableIcon {
        padding: 5px 0 !important;
        .cell {
          height: var(--zy-height-routine);
          display: flex;
          align-items: center;
        }
      }
      .globalTableSwitch {
        .zy-el-switch {
          height: var(--zy-height-secondary);
        }
      }
      .globalTableRate {
        .zy-el-rate {
          display: flex;
          align-items: center;
          height: var(--zy-height-secondary);
        }
      }
      .globalTableForm {
        position: relative;
        cursor: pointer;
        .globalTableFormText {
          width: 100%;
          height: 100%;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          min-height: calc(var(--zy-text-font-size) * var(--zy-line-height));
        }
        .globalTableFormInput {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: calc(100% - 24px);
          height: 100%;
          z-index: 9;
          display: flex;
          align-items: center;
        }
      }
      .globalTableSort {
        position: relative;
        cursor: pointer;
        .globalTableSortText {
          width: 100%;
          height: 100%;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          min-height: calc(var(--zy-text-font-size) * var(--zy-line-height));
        }
        .globalTableSortInput {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: calc(100% - 24px);
          height: 100%;
          z-index: 9;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
.globalPagination {
  width: 100%;
  height: 42px;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .zy-el-pagination {
    --zy-height: var(--zy-height-routine);
    --zy-el-component-size: var(--zy-height);

    .zy-el-select {
      width: 128px;
    }
  }
}
.globalLayout {
  width: calc(100% - 260px);
  height: 100%;
}
.globalForm {
  padding: var(--zy-distance-one) var(--zy-distance-two) var(--zy-distance-two) var(--zy-distance-one);
  .zy-el-form-item-br {
    width: 100%;
  }
  .globalFormName {
    width: 100%;
    font-weight: bold;
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    margin-bottom: var(--zy-form-distance-bottom);
    position: relative;
    padding: var(--zy-distance-five);
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-left: 6px solid var(--zy-el-color-primary);
      transform: translateY(-50%);
    }
    &::before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: calc(100% - 20px);
      height: 1px;
      background-color: var(--zy-el-color-primary);
    }
  }
  .zy-el-form-item {
    width: 290px;
    margin-right: var(--zy-distance-two) !important;
    margin-bottom: 18px !important;
    .zy-el-form-item__label {
      position: relative;
      .zy-el-button {
        position: absolute;
        top: -2px;
        height: var(--zy-height-secondary);
        margin-left: 8px;
      }
    }
    .zy-el-form-item__content {
      & > .zy-el-input,
      .zy-el-select,
      .zy-el-select-v2,
      .zy-el-input-number {
        width: 290px;
      }
      & > .zy-el-date-editor {
        width: 290px;
        & > .zy-el-input__wrapper {
          width: 100%;
        }
      }
      .globalFormColor {
        width: 100%;
        .zy-el-input-group__prepend {
          padding: 0;
          background-color: var(--el-fill-color-blank);
        }
      }
    }
  }
  .globalFormTitle {
    width: 100%;
    .zy-el-form-item__content {
      & > .zy-el-input,
      .zy-el-select,
      .zy-el-select-v2 {
        width: 100%;
      }
    }
  }
  .globalFormVerifyCode {
    width: 100%;
    .zy-el-form-item__content {
      & > .zy-el-input {
        width: 290px;
        margin-right: 12px;
      }
    }
  }
  .globalFormVideo {
    width: 100%;
    margin-right: 0 !important;
    .zy-el-form-item__content {
      width: 100%;
    }
  }
  .globalFormTime {
    width: 100%;
    .zy-el-form-item__content {
      width: 420px;
      & > .zy-el-date-editor {
        width: 50%;
        & > .zy-el-input__wrapper {
          width: 100%;
        }
      }
    }
  }
  .globalFormDate {
    width: 420px;
    .zy-el-form-item__content {
      width: 420px;
      & > .zy-el-date-editor {
        width: 50%;
        & > .zy-el-input__wrapper {
          width: 100%;
        }
      }
    }
  }
  .globalFormButton {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: var(--zy-distance-two);
    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}

.anchor-location-body {
  .globalForm {
    padding: var(--zy-distance-one) var(--zy-distance-two) var(--zy-distance-two) var(--zy-distance-one);

    .zy-el-form-item-br {
      width: 100%;
    }

    .globalFormName {
      width: 100%;
      font-weight: bold;
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      margin-bottom: var(--zy-form-distance-bottom);
      position: relative;
      padding: var(--zy-distance-five);

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid var(--zy-el-color-primary);
        transform: translateY(-50%);
      }

      &::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: calc(100% - 20px);
        height: 1px;
        background-color: var(--zy-el-color-primary);
      }
    }

    .zy-el-form-item {
      width: 280px;
      margin-right: 20px !important;

      .zy-el-form-item__label {
        position: relative;

        .zy-el-button {
          position: absolute;
          top: -2px;
          height: var(--zy-height-secondary);
          margin-left: 8px;
        }
      }

      .zy-el-form-item__content {
        & > .zy-el-input,
        .zy-el-select,
        .zy-el-select-v2,
        .zy-el-input-number {
          width: 280px;
        }

        & > .zy-el-date-editor {
          width: 280px;

          & > .zy-el-input__wrapper {
            width: 100%;
          }
        }

        .globalFormColor {
          width: 100%;

          .zy-el-input-group__prepend {
            padding: 0;
            background-color: var(--el-fill-color-blank);
          }
        }
      }
    }

    .globalFormTitle {
      width: 100%;

      .zy-el-form-item__content {
        & > .zy-el-input,
        .zy-el-select,
        .zy-el-select-v2 {
          width: 100%;
        }
      }
    }

    .globalFormVerifyCode {
      width: 100%;

      .zy-el-form-item__content {
        & > .zy-el-input {
          width: 280px;
          margin-right: 12px;
        }
      }
    }

    .globalFormVideo {
      width: 100%;
      margin-right: 0 !important;

      .zy-el-form-item__content {
        width: 100%;
      }
    }

    .globalFormTime {
      width: 100%;

      .zy-el-form-item__content {
        width: 420px;

        & > .zy-el-date-editor {
          width: 50%;

          & > .zy-el-input__wrapper {
            width: 100%;
          }
        }
      }
    }

    .globalFormDate {
      width: 420px;

      .zy-el-form-item__content {
        width: 420px;

        & > .zy-el-date-editor {
          width: 50%;

          & > .zy-el-input__wrapper {
            width: 100%;
          }
        }
      }
    }

    .globalFormButton {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-right: var(--zy-distance-two);

      .zy-el-button + .zy-el-button {
        margin-left: var(--zy-distance-two);
      }
    }
  }
}
