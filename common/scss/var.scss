// html {
//   :root {
//     // --zy-el-color-primary:#bc1d1d;
//     // 高度
//     --zy-height: 36px;
//     --zy-height-routine: 32px;
//     --zy-height-secondary: 26px;
//     // 行高
//     --zy-line-height: 1.6;
//     // 字体大小
//     --zy-system-font-size: 28px;
//     --zy-title-font-size: 26px;
//     --zy-navigation-font-size: 18px;
//     --zy-name-font-size: 16px;
//     --zy-text-font-size: 14px;
//     // 文字颜色
//     --zy-el-text-color-primary: #303133;
//     --zy-el-text-color-regular: #606266;
//     --zy-el-text-color-secondary: #909399;
//     // 边框颜色
//     --zy-el-border-color: #dcdfe6;
//     --zy-el-border-color-light: #e4e7ed;
//     --zy-el-border-color-lighter: #ebeef5; // 常用
//     --zy-el-border-color-extra-light: #f2f6fc;
//     // 边距
//     --zy-distance-one: 40px;
//     --zy-distance-two: 20px;
//     --zy-distance-three: 16px;
//     --zy-distance-four: 12px;
//     --zy-distance-five: 10px;
//     --zy-font-name-distance-five: 6px;
//     --zy-font-text-distance-five: 3px;
//     // 圆角
//     --el-border-radius-base: 4px;
//     --el-border-radius-small: 2px;
//     // 盒子阴影
//     --zy-el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
//     --zy-el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
//     --zy-el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
//     // 表单宽度，边距
//     --zy-form-width-one: calc(var(--zy-distance-one) + (var(--zy-distance-two) * 4) + (var(--zy-form-width-item) * 3));
//     --zy-form-width-two: calc(var(--zy-distance-one) + (var(--zy-distance-two) * 3) + (var(--zy-form-width-item) * 2));
//     --zy-form-width-item: 290px;
//     --zy-form-distance-bottom: 22px;
//   }
// }
