import { saveAs } from 'file-saver'
import * as ExcelJS from 'exceljs'

/**
 * 支持多sheet 导出Excel
 * @param name Excel名称
 * @param data Excel数据集合
 * @param data.name sheet名称
 * @param data.tableHead 多行表头
 * @param data.tableData 数据，一个数组表示一个行的数据
 * @param data.merges 合并单元格
 */
export const export_json_to_excel_sheet = (data, name = 'XLSX工作表') => {
  // 创建一个工作簿
  const workbook = new ExcelJS.Workbook()
  const worksheet = []
  data.forEach(item => {
    const sheet = workbook.addWorksheet(item.name)
    const tableList = [...item.tableHead, ...item.tableData]
    sheet.addRows(tableList)
    const result = columnWidth(tableList)
    for (const i in tableList) {
      sheet.getRow(parseInt(i) + 1).height = 36
      sheet.findRow(parseInt(i) + 1).alignment = { wrapText: true, vertical: 'middle', horizontal: 'center' }
      if (i < item.tableHead.length) {
        sheet.findRow(parseInt(i) + 1).font = { size: 12, color: { rgb: '000000' }, bold: true }
      } else {
        sheet.findRow(parseInt(i) + 1).font = { size: 12, color: { rgb: '000000' } }
      }
    }
    if (item?.lineHeight?.length) {
      for (let index = 0; index < item.lineHeight.length; index++) {
        const itemLine = item.lineHeight
        sheet.getRow(parseInt(itemLine.line)).height = itemLine.height
      }
    }
    if (item?.cellStyle?.length) cellStyle(sheet, item.cellStyle)
    for (const i in result) {
      sheet.getColumn(parseInt(i) + 1).width = result[i].width
    }
    for (const i in item.merge) {
      sheet.mergeCells(item.merge[i])
    }
    worksheet.push(sheet)
  })
  // 保存设置
  workbook.xlsx.writeBuffer().then(buffer => {
    saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `${name}.xlsx`)
  })
}

const columnWidth = (tableData) => {
  const width = tableData.map(row => row.map(val => {
    if (val == null) { /* 先判断是否为null/undefined*/
      return { 'width': 10 }
    } else if (val.toString().charCodeAt(0) > 255) { /* 再判断是否为中文*/
      return { 'width': val.toString().length * 2 > 52 ? 52 : (val.toString().length * 2 < 16 ? 16 : val.toString().length * 2) }
    } else {
      return { 'width': val.toString().length > 28 ? 28 : (val.toString().length < 16 ? 16 : val.toString().length) }
    }
  }))
  const result = width[0]
  for (let i = 1; i < width.length; i++) {
    for (let j = 0; j < width[i].length; j++) {
      if (result[j]['width'] < width[i][j]['width']) {
        result[j]['width'] = width[i][j]['width']
      }
    }
  }
  return result
}
const cellStyle = (sheet, tableData) => {
  for (let i = 0; i < tableData.length; i++) {
    const item = tableData[i]
    for (let index = 0; index < item.length; index++) {
      const row = item[index]
      const itemRow = sheet.getRow(row.line)
      const itemCell = itemRow.getCell(row.column)
      if (row.border) {
        if (typeof row.border === 'object') {
          itemCell.border = {
            top: !row.border.top || row.border.top === 'none' ? { style: 'none' } : { style: 'thin', color: { argb: row.border.top } },
            left: !row.border.left || row.border.left === 'none' ? { style: 'none' } : { style: 'thin', color: { argb: row.border.left } },
            right: !row.border.right || row.border.right === 'none' ? { style: 'none' } : { style: 'thin', color: { argb: row.border.right } },
            bottom: !row.border.bottom || row.border.bottom === 'none' ? { style: 'none' } : { style: 'thin', color: { argb: row.border.bottom } }
          }
        } else {
          itemCell.border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' }, bottom: { style: 'thin' } }
        }
      }
      if (row.fontColor) itemCell.font = { color: { argb: row.fontColor } }
      if (row.bgColor) itemCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: row.bgColor } }
    }
  }
}
