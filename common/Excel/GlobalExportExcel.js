import { ref } from 'vue'
export const GlobalExportExcel = () => {

  const isIndex = ref(0)
  const hierarchy = ref(0)
  const tableHeadKey = ref([])
  const tableHeadList = ref([])
  const tableHeadMerge = ref([])
  /**
   * @description: 一级表头Excel导出
   * @param {Array} 表头数据数组对象格式，例如：[{key: 'name', value: '姓名'}]
   * @param {Array} 表格数据数组对象格式，例如：[{name: '姓名'}]
   * @return void
   */
  const exportExcel = (tableHead, tableData = [], name = 'XLSX工作表', cellStyle = []) => {
    const tableHeadArr = [tableHead.map(v => v.value)]
    const dataObj = tableDataMethods(1, tableHead.map(v => v.key), tableData)
    const tableDataArr = dataObj.data
    const tableMergeArr = dataObj.merge
    import('./exportExcel').then(excel => {
      excel.export_json_to_excel_sheet([
        {
          name: 'sheet1',
          merge: [...new Set(tableMergeArr)],
          tableHead: tableHeadArr,
          tableData: tableDataArr,
          cellStyle: cellStyle
        }
      ], name)
    })
  }
  const exportExcelSheet = (data, name = 'XLSX工作表') => {
    let newData = []
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      const tableHeadArr = [item.tableHead.map(v => v.value)]
      const dataObj = tableDataMethods(1, item.tableHead.map(v => v.key), item.tableData)
      const tableDataArr = dataObj.data
      const tableMergeArr = dataObj.merge
      newData.push({
        name: item.name,
        merge: [...new Set(tableMergeArr)],
        tableHead: tableHeadArr,
        tableData: tableDataArr,
        cellStyle: item.cellStyle
      })
    }
    import('./exportExcel').then(excel => {
      excel.export_json_to_excel_sheet(newData, name)
    })
  }
  /**
   * @description: 多级表头Excel导出
   * @param {Array} 树形结果层级表头数据，id唯一值也作为tableData的对象key，label为显示名称，children为子级
   * @param {Array} 表格数据数组对象格式，例如：[{name: '姓名'}] 对象key对应tableHead的id字段
   * @return void
   */
  const exportExcelTree = (tableHead, tableData = [], name = 'XLSX工作表', cellStyle = []) => {
    isIndex.value = 0
    hierarchy.value = 0
    tableHeadKey.value = []
    tableHeadList.value = []
    tableHeadMerge.value = []
    isHierarchy(tableHead)
    tableHeadMethods(tableHead)
    const length = tableHeadList.value.length
    const dataObj = tableDataMethods(length, tableHeadKey.value, tableData)
    const tableDataArr = dataObj.data
    const tableMergeArr = [...tableHeadMerge.value, ...dataObj.merge]
    import('./exportExcel').then(excel => {
      excel.export_json_to_excel_sheet([
        {
          name: 'sheet1',
          merge: [...new Set(tableMergeArr)],
          tableHead: tableHeadList.value,
          tableData: tableDataArr,
          cellStyle: cellStyle
        }
      ], name)
    })
  }
  const isHierarchy = (data, index = 1) => {
    hierarchy.value = hierarchy.value >= index ? hierarchy.value : index
    data.forEach(item => { if (item.children.length) { isHierarchy(item.children, index + 1) } })
  }

  const tableHeadMethods = (data, index = 1) => {
    var _index = index + 1
    var isNum = 0
    var headArr = []
    data.forEach(item => {
      headArr.push(item.label)
      if (item.children.length) {
        const isLength = tableHeadMethods(item.children, _index)
        isNum += isLength
        tableHeadMerge.value.push(`${mergeNum(isIndex.value - isLength)}${index}:${mergeNum(isIndex.value - 1)}${index}`)
        for (let i = 0; i < isLength - 1; i++) {
          headArr.push('')
        }
      } else {
        tableHeadKey.value.push(item.key || item.id)
        isIndex.value += 1
        isNum += 1
        if (hierarchy.value > index) {
          for (let i = _index; i <= hierarchy.value; i++) {
            tableHeadList.value[i - 1] = [...tableHeadList.value[i - 1] || [], '']
          }
        }
        const isLength = tableHeadList.value[index - 1]?.length || 0 > isNum ? tableHeadList.value[index - 1]?.length + isNum || 0 : isNum
        if (`${mergeNum(isLength - 1)}${index}` !== `${mergeNum(isLength - 1)}${hierarchy.value}`) {
          tableHeadMerge.value.push(`${mergeNum(isLength - 1)}${index}:${mergeNum(isLength - 1)}${hierarchy.value}`)
        }
      }
    })
    tableHeadList.value[index - 1] = [...tableHeadList.value[index - 1] || [], ...headArr]
    return isNum
  }
  const mergeNum = (num) => {
    const MergeArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    var number = ''
    if (num / 26 < 1) {
      number = MergeArr[num]
    } else {
      number = MergeArr[Math.floor(num / 26) - 1] + MergeArr[num - ((Math.floor(num / 26)) * 26)]
    }
    return number
  }
  const tableDataMethods = (length, key, data) => {
    let newTableData = []
    let tableDataMerge = []
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      const rowIndex = (length || 0) + index + 1
      const rowMerge = tableDataRowMerge(rowIndex, key, item.rowMerge || [])
      const columnMerge = tableDataColumnMerge(rowIndex, key, item.columnMerge || [])
      tableDataMerge = [...tableDataMerge, ...rowMerge.merge, ...columnMerge.merge]
      newTableData.push(key.map(v => item[v]))
    }
    return { data: newTableData, merge: tableDataMerge }
  }
  const tableDataRowMerge = (rowIndex, key, data) => {
    let newRowData = []
    let newRowMerge = []
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      const columnIndex = key.indexOf(item.key)
      const start = mergeNum(columnIndex) + rowIndex
      const end = mergeNum(columnIndex + item.value) + rowIndex
      newRowData.push({ ...item, rowIndex, columnIndex, merge: `${start}:${end}` })
      newRowMerge.push(`${start}:${end}`)
    }
    return { data: newRowData, merge: newRowMerge }
  }
  const tableDataColumnMerge = (rowIndex, key, data) => {
    let newColumnData = []
    let newColumnMerge = []
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      const columnIndex = key.indexOf(item.key)
      const columnIndexNum = mergeNum(columnIndex)
      const start = columnIndexNum + rowIndex
      const end = columnIndexNum + (rowIndex + item.value)
      newColumnData.push({ ...item, rowIndex, columnIndex, merge: `${start}:${end}` })
      newColumnMerge.push(`${start}:${end}`)
    }
    return { data: newColumnData, merge: newColumnMerge }
  }

  return { exportExcel, exportExcelSheet, exportExcelTree }
}
