import { createVNode, render } from 'vue'
import unauthorized from './unauthorized.vue'

// 准备div容器
const divNode = createVNode('div', { class: 'zy-confirm-container' })
render(divNode, document.body)
// 获取 DOM 节点, 用于挂载组件或卸载组件
const container = divNode.el

export default ({ name, callback }) => {
  // 2. 点击取消按钮，触发callback同时销毁组件
  const cancelCallback = () => {
    render(null, container)
    if (callback) { callback() }
  }
  // 1. 创建 unauthorized 组件
  const VNode = createVNode(unauthorized, { name, cancelCallback })
  render(VNode, container)
}
