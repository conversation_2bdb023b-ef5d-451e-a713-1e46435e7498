<template>
  <div class="unauthorized-wrapper">
    <transition name="unauthorized-fade">
      <div class="unauthorized" v-show="visible">
        <div class="unauthorized-icon"></div>
        <div class="unauthorized-title">抱歉，“{{ props.name }}”地区暂未获得软件正版授权</div>
        <div class="unauthorized-text">
          唯一指定官方授权：
          <div class="official-logo"></div>
        </div>
        <div class="unauthorized-close" @click="handleCancel">
          <svg viewBox="0 0 1024 1024">
            <path
              fill="currentColor"
              d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"></path>
          </svg>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default { name: 'Unauthorized' }
</script>
<script setup>
import { ref, onMounted } from 'vue'
const props = defineProps({
  name: { type: String, default: '' },
  cancelCallback: { type: Function }
})

const visible = ref(false)

onMounted(() => {
  visible.value = true
})

const handleCancel = () => {
  visible.value = false
  setTimeout(() => {
    props.cancelCallback()
  }, 300)
}
</script>
<style lang="scss" scoped>
.unauthorized-wrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  backdrop-filter: blur(2px);
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .unauthorized {
    min-width: 520px;
    padding: var(--zy-distance-one);
    vertical-align: middle;
    background-color: #fff;
    border-radius: var(--el-border-radius-base);
    box-shadow: var(--zy-el-box-shadow);
    backface-visibility: hidden;
    position: relative;

    .unauthorized-icon {
      width: 120px;
      height: 120px;
      background: url('./img/unauthorized_icon.png') no-repeat;
      background-size: 100% 100%;
      margin: auto;
    }

    .unauthorized-title {
      text-align: center;
      font-weight: bold;
      padding: var(--zy-distance-three) 0;
      font-size: 18px;
      line-height: var(--zy-line-height);
    }

    .unauthorized-text {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      line-height: var(--zy-line-height);

      .official-logo {
        width: 103px;
        height: 28px;
        background: url('./img/official_logo.png') no-repeat;
        background-size: 103px 25px;
        background-position: center bottom;
      }
    }

    .unauthorized-close {
      position: absolute;
      top: 0;
      right: 0;
      width: 52px;
      height: 52px;
      cursor: pointer;
      text-align: center;
      line-height: 52px;
      font-size: var(--zy-navigation-font-size);

      svg {
        width: 1em;
        height: 1em;
      }
    }
  }

  .unauthorized-fade-enter-active {
    -webkit-animation: unauthorized-fade-in 0.3s;
    animation: unauthorized-fade-in 0.3s;
  }

  .unauthorized-fade-leave-active {
    -webkit-animation: unauthorized-fade-out 0.3s;
    animation: unauthorized-fade-out 0.3s;
  }

  @keyframes unauthorized-fade-in {
    0% {
      transform: translate3d(0, -20px, 0);
      opacity: 0;
    }

    100% {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }
  }

  @keyframes unauthorized-fade-out {
    0% {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }

    100% {
      transform: translate3d(0, -20px, 0);
      opacity: 0;
    }
  }
}
</style>
