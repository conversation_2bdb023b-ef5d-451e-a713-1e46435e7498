<template>
  <el-config-provider :locale="locale" namespace="zy-el">
    <div class="intelligent-assistant-confirm-wrapper" ref="confirmWrapper" @click="handleContains">
      <transition name="intelligent-assistant-confirm-fade">
        <div class="intelligent-assistant-confirm" v-show="visible">
          <div class="intelligent-assistant-confirm-img">
            <el-image :src="IntelligentAssistant" fit="cover"></el-image>
          </div>
          <div class="intelligent-assistant-confirm-title">{{ props.title }}</div>
          <div class="intelligent-assistant-confirm-text">{{ props.message }}</div>
          <div class="intelligent-assistant-confirm-button">
            <el-button @click="handleCancel" v-if="cancelText">{{ props.cancelText }}</el-button>
            <el-button type="primary" @click="handleConfirm">{{ props.confirmText }}</el-button>
          </div>
        </div>
      </transition>
    </div>
  </el-config-provider>
</template>

<script>
export default { name: 'IntelligentAssistantConfirm' }
</script>
<script setup>
import { ref, onMounted } from 'vue'
import { IntelligentAssistant } from 'common/js/system_var.js'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
const props = defineProps({
  title: { type: String, default: '' },
  message: { type: String, default: '' },
  confirmText: { type: String, default: '' },
  cancelText: { type: String, default: '' },
  confirmCallback: { type: Function },
  cancelCallback: { type: Function }
})

const locale = zhCn
const confirmWrapper = ref()
const visible = ref(false)

onMounted(() => {
  visible.value = true
})

const handleConfirm = () => {
  visible.value = false
  setTimeout(() => {
    props.confirmCallback()
  }, 300)
}
const handleCancel = () => {
  visible.value = false
  setTimeout(() => {
    props.cancelCallback()
  }, 300)
}
const handleContains = (e) => {
  if (e.target.contains(confirmWrapper.value)) {
    handleCancel()
  }
}
</script>
<style lang="scss" scoped>
.intelligent-assistant-confirm-wrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .intelligent-assistant-confirm {
    width: 390px;
    padding-bottom: 10px;
    vertical-align: middle;
    background-color: #fff;
    border-radius: var(--el-border-radius-base);
    box-shadow: var(--zy-el-box-shadow);
    backface-visibility: hidden;
    position: relative;

    .intelligent-assistant-confirm-img {
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(50%, -50%);
      width: 66px;
      height: 66px;

      .zy-el-image {
        width: 100%;
        height: 100%;
      }
    }

    .intelligent-assistant-confirm-title {
      font-weight: bold;
      padding: var(--zy-distance-three) var(--zy-distance-two);
      padding-bottom: var(--zy-font-name-distance-five);
      font-size: var(--zy-navigation-font-size);
      line-height: var(--zy-line-height);
    }

    .intelligent-assistant-confirm-text {
      padding: 0 var(--zy-distance-two);
      color: var(--zy-el-text-color-regular);
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      padding-bottom: var(--zy-distance-three);
    }

    .intelligent-assistant-confirm-button {
      display: flex;
      justify-content: flex-end;
      padding: 0 var(--zy-distance-two);

      // .zy-el-button {
      //   height: var(--zy-height-routine);
      // }
    }
  }

  .intelligent-assistant-confirm-fade-enter-active {
    -webkit-animation: intelligent-assistant-confirm-fade-in 0.3s;
    animation: intelligent-assistant-confirm-fade-in 0.3s;
  }

  .intelligent-assistant-confirm-fade-leave-active {
    -webkit-animation: intelligent-assistant-confirm-fade-out 0.3s;
    animation: intelligent-assistant-confirm-fade-out 0.3s;
  }

  @keyframes intelligent-assistant-confirm-fade-in {
    0% {
      transform: translate3d(0, -20px, 0);
      opacity: 0;
    }

    100% {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }
  }

  @keyframes intelligent-assistant-confirm-fade-out {
    0% {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }

    100% {
      transform: translate3d(0, -20px, 0);
      opacity: 0;
    }
  }
}
</style>
