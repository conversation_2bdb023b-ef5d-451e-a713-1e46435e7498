import { createVNode, render } from 'vue'
import IntelligentAssistantConfirm from './intelligent-assistant-confirm.vue'

// 准备div容器
const divNode = createVNode('div', { class: 'zy-confirm-container' })
render(divNode, document.body)
// 获取 DOM 节点, 用于挂载组件或卸载组件
const container = divNode.el

export default ({ title, message, confirmText, cancelText }) => {
  return new Promise((resolve, reject) => {
    // 2. 点击确认按钮，触发resolve同时销毁组件
    const confirmCallback = () => {
      render(null, container)
      resolve('点击确认')
    }
    // 3. 点击取消按钮，触发reject同时销毁组件
    const cancelCallback = () => {
      render(null, container)
      reject(new Error('点击取消'))
    }
    // 1. 创建 IntelligentAssistantConfirm 组件
    const VNode = createVNode(IntelligentAssistantConfirm, { title, message, confirmText, cancelText, confirmCallback, cancelCallback })
    render(VNode, container)
  })
}
