<template>
  <div class="select-role">
    <div class="select-role-body" v-for="(item, index) in roleList" :key="index">
      <div class="select-role-name">{{ item.moduleName }}</div>
      <div class="select-role-list">
        <el-checkbox-group v-model="roleId" @change="roleChange">
          <el-checkbox v-for="row in item.roles" :key="row[props.rowKey]" :label="row[props.rowKey]">
            {{ row.name }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div class="select-role-button">
      <el-button type="primary" @click="submitForm">确定</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'SelectRole' }
</script>
<script setup>
import api from '@/api'
import { ref, watch } from 'vue'
const props = defineProps({
  rowKey: { type: String, default: 'key' },
  params: { type: Object, default: () => ({}) },
  roleData: { type: Array, default: () => [] }
})
const emit = defineEmits(['callback'])
const roleId = ref([])
const roleList = ref([])
const roleSelectList = ref([])

const roleData = async () => {
  const res = await api.roleData(props.params)
  var { data } = res
  roleList.value = data
  roleId.value = [...roleId.value, ...props.roleData.map((v) => v[props.rowKey])]
  if (roleId.value.length) {
    roleChange(roleId.value)
  }
}
const roleChange = (val) => {
  var arrData = []
  roleList.value.forEach((item) => {
    item.roles.forEach((row) => {
      if (val.includes(row[props.rowKey])) {
        arrData.push(row)
      }
    })
  })
  roleSelectList.value = arrData
}
const submitForm = async () => {
  emit('callback', roleSelectList.value)
}
const resetForm = () => {
  emit('callback')
}
watch(
  () => props.params,
  () => {
    roleData()
  },
  { immediate: true }
)
</script>
<style lang="scss">
.select-role {
  width: 990px;

  .select-role-body {
    padding: var(--zy-distance-one);
    display: flex;
    border-bottom: 1px solid var(--zy-el-border-color-lighter);

    .select-role-name {
      width: 128px;
      line-height: var(--zy-height);
      font-weight: bold;
    }

    .select-role-list {
      width: calc(100% - 128px);
    }
  }

  .select-role-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--zy-distance-one) 0 var(--zy-distance-two) 0;

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
