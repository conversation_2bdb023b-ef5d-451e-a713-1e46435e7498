<template>
  <div class="global-info-box">
    <div class="global-info-item">
      <div class="global-info-label">
        <span>
          {{ label }}
          <slot name="label"></slot>
        </span>
      </div>
      <div class="global-info-content">
        <span>
          <slot></slot>
        </span>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'GlobalInfoItem' }
</script>
<script setup>
import { computed } from 'vue'
const props = defineProps({ label: { type: String, default: '' } })
const label = computed(() => props.label)
</script>
