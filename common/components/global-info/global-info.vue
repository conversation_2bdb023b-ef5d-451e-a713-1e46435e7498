<template>
  <div class="global-info">
    <slot></slot>
  </div>
</template>
<script>
export default { name: 'GlobalInfo' }
</script>
<style lang="scss">
.global-info {
  width: 100%;
  margin: auto;
  border-top: 1px solid var(--zy-el-border-color-lighter);

  .global-info-line {
    display: flex;

    .global-info-box {
      width: 50%;

      .global-info-item {
        width: 100%;
      }
    }

    .global-info-box + .global-info-box {
      .global-info-item {
        .global-info-label {
          border-left-color: var(--zy-el-color-info-light-9);
        }
      }
    }
  }

  .global-info-box {
    display: flex;
  }

  .global-info-item {
    width: 100%;
    display: flex;
    align-items: center;

    .global-info-label {
      width: 120px;
      height: 100%;
      box-sizing: border-box;
      background: var(--zy-el-color-info-light-9);
      padding: var(--zy-distance-five) 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid var(--zy-el-border-color-lighter);
      border-left-color: var(--zy-el-color-info-light-9);

      & > span {
        display: inline-block;
        min-height: calc(var(--zy-text-font-size) * var(--zy-line-height));
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
      }
    }

    .global-info-content {
      width: calc(100% - 120px);
      height: 100%;
      box-sizing: border-box;
      background: #fff;
      padding: var(--zy-distance-five) var(--zy-distance-four);
      display: flex;
      align-items: center;
      border: 1px solid var(--zy-el-border-color-lighter);
      border-top-color: #fff;
      border-left: 0;

      & > span {
        display: inline-block;
        word-wrap: break-word;
        word-break: break-all;
        min-height: calc(var(--zy-text-font-size) * var(--zy-line-height));
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);

        pre {
          white-space: pre-wrap;
        }

        .xyl-global-file {
          padding: 0;

          .globalFile {
            padding: 0;
          }

          .globalFile + .globalFile {
            padding-top: var(--zy-distance-four);
          }
        }

        .zy-el-rate {
          display: flex;
          align-items: center;
          height: var(--zy-height-secondary);
        }
      }
    }
  }
}
</style>
