<template>
  <div class="zzt-global-file">
    <div class="globalFile" v-for="item in fileData" :key="item.id">
      <div class="globalFileIcon" :class="fileIcon(item.extName)"></div>
      <div class="globalFileText ellipsis">{{ item.originalFileName }}</div>
      <div class="globalFileButton">
        <span @click="handlePreview(item)"></span>
        <span @click="handleDownload(item)"></span>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'ZztGlobalFile' }
</script>
<script setup>
import { computed } from 'vue'
import { globalFileLocation, inSystemFileDownload } from 'common/js/public'
const props = defineProps({ fileData: { type: Array, default: () => [] } })
const fileData = computed(() => props.fileData)

const fileIcon = (fileType) => {
  const IconClass = {
    docx: 'globalFileWord',
    doc: 'globalFileWord',
    wps: 'globalFileWPS',
    xlsx: 'globalFileExcel',
    xls: 'globalFileExcel',
    pdf: 'globalFilePDF',
    pptx: 'globalFilePPT',
    ppt: 'globalFilePPT',
    txt: 'globalFileTXT',
    jpg: 'globalFilePicture',
    png: 'globalFilePicture',
    gif: 'globalFilePicture',
    avi: 'globalFileVideo',
    mp4: 'globalFileVideo',
    zip: 'globalFileCompress',
    rar: 'globalFileCompress'
  }
  return IconClass[fileType] || 'globalFileUnknown'
}
const handlePreview = (row) => {
  globalFileLocation({
    name: process.env.VUE_APP_NAME,
    fileId: row.id,
    fileType: row.extName,
    fileName: row.originalFileName,
    fileSize: row.fileSize
  })
}
const handleDownload = (row) => {
  inSystemFileDownload(row.id, row.originalFileName)
}
</script>
<style lang="scss">
.zzt-global-file {
  padding-bottom: var(--zy-distance-four);

  .globalFile {
    width: 100%;
    display: flex;
    align-items: center;
    padding-bottom: var(--zy-distance-five);

    .globalFileIcon {
      width: var(--zy-title-font-size);
      height: var(--zy-title-font-size);
      min-width: var(--zy-title-font-size);
      vertical-align: middle;
    }

    .globalFileText {
      max-width: 100%;
      padding-left: 6px;
      padding-right: 20px;
      position: relative;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      vertical-align: middle;
    }

    .globalFileButton {
      display: flex;
      align-items: center;

      span {
        display: inline-block;
        width: var(--zy-title-font-size);
        height: var(--zy-title-font-size);
        background: url('./img/preview.png') no-repeat;
        background-size: 80% 80%;
        background-position: center;
        cursor: pointer;
        vertical-align: middle;
      }

      span + span {
        margin-left: var(--zy-distance-two);
        background: url('./img/download.png') no-repeat;
        background-size: 80% 80%;
        background-position: center;
      }
    }

    .globalFileUnknown {
      background: url('./img/unknown.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFilePDF {
      background: url('./img/PDF.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFileWord {
      background: url('./img/Word.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFileExcel {
      background: url('./img/Excel.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFilePicture {
      background: url('./img/picture.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFileVideo {
      background: url('./img/video.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFileTXT {
      background: url('./img/TXT.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFileCompress {
      background: url('./img/compress.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFileWPS {
      background: url('./img/WPS.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }

    .globalFilePPT {
      background: url('./img/PPT.png') no-repeat;
      background-size: 80% 80%;
      background-position: center;
    }
  }
}
</style>
