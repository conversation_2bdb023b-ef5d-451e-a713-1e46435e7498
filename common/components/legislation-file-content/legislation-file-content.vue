<template>
  <div class="legislationFileContent" ref="leftDetail">
    <div class="legislationFileContentTitle" ref="leftTitle">{{ props.detail.title }}</div>
    <div class="legislationFileContentLabel" ref="leftLabel">
      <el-descriptions column="2" class="descriptions-row2">
        <el-descriptions-item label="制定机关：">
          {{ props.detail.office }}
        </el-descriptions-item>
        <el-descriptions-item label="发文字号：">
          {{ props.detail.reportOfficeName }}
        </el-descriptions-item>
        <el-descriptions-item label="发布日期：">
          {{ format(props.detail.submitDate, 'YYYY-MM-DD') }}
        </el-descriptions-item>
        <el-descriptions-item label="施行日期：">
          {{ format(props.detail.executeDate, 'YYYY-MM-DD') }}
        </el-descriptions-item>
        <el-descriptions-item label="效力级别：">
          {{ props.detail.biglevel }}
        </el-descriptions-item>
        <el-descriptions-item label="时效性：">
          {{ props.detail.status }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template v-if="props.detail.textFileInfos">
      <regulation-filePreviews
        :fileId="props.detail.textFileInfos[0].id"
        :fileType="props.detail.textFileInfos[0].extName"
        :fileName="props.detail.textFileInfos[0].newFileName"
        ref="textFileRef"
        @matchesCountCallback="matchesCountCallback"
        :style="contentStyle" />
    </template>
    <template v-if="props.detail.text_attachment_ids">
      <regulation-filePreviews
        :fileId="props.detail.text_attachment_ids"
        :fileType="props.detail.wordType"
        @matchesCountCallback="matchesCountCallback"
        ref="wordFileRef"
        :style="contentStyle" />
    </template>

    <template v-if="!(props.detail.textFileInfos || props.detail.text_attachment_ids)">
      <el-scrollbar :style="contentStyle">
        <div v-html="props.detail.content" class="legislationFileContent-word" />
      </el-scrollbar>
    </template>
  </div>
</template>

<script>
export default { name: 'legislationFileContent' }
</script>

<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { format } from 'common/js/time.js'
const RegulationFilePreviews = defineAsyncComponent(() => import('../regulation-filePreview/RegulationFilePreview.vue'))
const emit = defineEmits(['matchesCountCallback'])
const props = defineProps({
  detail: { type: Array, default: () => {} }
})

const leftTitle = ref()
const leftLabel = ref()
const leftDetail = ref()
const contentStyle = ref({ height: '0' })

const textFileRef = ref()
const wordFileRef = ref()
const currents = ref(0) //
const total = ref(0) //

onMounted(() => {
  setTimeout(() => {
    var leftTitleHeight = leftTitle.value.offsetHeight
    var leftLabelHeight = leftLabel.value.offsetHeight
    var leftDetailHeight = leftDetail.value.offsetHeight
    contentStyle.value = { height: leftDetailHeight - leftLabelHeight - leftTitleHeight - 20 + 'px' }
  })
})

const search = (keyword) => {
  if (props.detail.textFileInfos) {
    textFileRef.value.seach(keyword)
  }
  if (props.detail.word) {
    wordFileRef.value.seach(keyword)
  }
}

const searchUp = (keyword) => {
  if (props.detail.textFileInfos) {
    textFileRef.value.againClick(true, keyword)
  }
  if (props.detail.word) {
    wordFileRef.value.againClick(true, keyword)
  }
}

const searchNext = (keyword) => {
  if (props.detail.textFileInfos) {
    textFileRef.value.againClick(false, keyword)
  }
  if (props.detail.word) {
    wordFileRef.value.againClick(false, keyword)
  }
}

const print = () => {
  if (props.detail.textFileInfos) {
    textFileRef.value.print()
  }
  if (props.detail.text_attachment_ids) {
    wordFileRef.value.print()
  }
}
const matchesCountCallback = (currentNum, totals) => {
  currents.value = currentNum || 0
  total.value = totals || 0
  emit('matchesCountCallback', currents.value, total.value)
}

defineExpose({
  search,
  searchUp,
  searchNext,
  total,
  print,
  currents
})
</script>

<style lang="scss">
.legislationFileContent {
  height: 1200px;

  .legislationFileContentTitle {
    color: #333;
    font-weight: bold;
    font-size: var(--zy-system-font-size);
    text-align: center;
    padding-bottom: var(--zy-distance-two);
  }

  .legislationFileContentLabel {
    background-color: #f8f8f8;
    padding: var(--zy-distance-five) var(--zy-distance-four);
    border-radius: var(--el-border-radius-base);
    margin-bottom: var(--zy-distance-five);

    .descriptions-row2 {
      .zy-el-descriptions__cell {
        background-color: #f8f8f8;
        padding-bottom: var(--zy-font-name-distance-five) !important;
      }

      td {
        width: 50%;
      }
    }
  }

  .legislationFileContentContent {
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border-radius: var(--el-border-radius-base);
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
  }

  .legislationFileContent-word {
    line-height: 2;
    padding: var(--zy-distance-five);
  }
}
</style>
