<template>
  <el-popover
    placement="top-start"
    v-model:visible="ElWhetherVisible"
    popper-class="xyl-slide-verify-popper"
    trigger="contextmenu"
    width="auto"
    :disabled="disabled">
    <div
      class="xyl-slide-verify"
      @mouseenter="canvasMouseenter"
      @mouseleave="canvasMouseleave"
      :style="{ width: props.w + 'px' }"
      onselectstart="return false;">
      <canvas ref="canvas" :width="props.w" :height="props.h"></canvas>
      <div v-if="props.show" class="xyl-slide-verify-refresh-icon" @click="refresh">
        <el-icon>
          <RefreshRight />
        </el-icon>
      </div>
      <canvas ref="block" :width="props.w" :height="props.h" class="xyl-slide-verify-block"></canvas>
    </div>
    <template #reference>
      <div
        class="xyl-slide-verify-slider"
        :style="{ width: props.w + 'px' }"
        :class="{
          'container-active': containerCls.containerActive,
          'container-success': containerCls.containerSuccess,
          'container-fail': containerCls.containerFail
        }"
        @mouseenter="ElMouseenter"
        @mouseleave="ElMouseleave"
        @touchstart="startEl"
        @touchend="endEl">
        <div class="xyl-slide-verify-slider-mask" :style="{ width: sliderBox.width }">
          <div
            class="xyl-slide-verify-slider-mask-item"
            :style="{ left: sliderBox.left }"
            @mousedown="start"
            @touchstart="start"
            @touchmove="touchMoveEvent"
            @touchend="touchEndEvent">
            <el-icon>
              <Right v-if="sliderBox.iconCls === 'arrow-right'" />
              <Check v-if="sliderBox.iconCls === 'success'" />
              <Close v-if="sliderBox.iconCls === 'fail'" />
            </el-icon>
          </div>
        </div>
        <div class="xyl-slide-verify-slider-text">{{ props.sliderText }}</div>
      </div>
    </template>
  </el-popover>
</template>
<script>
export default { name: 'XylSlideVerify' }
</script>
<script setup>
import { reactive, ref, computed, onMounted, onUnmounted } from 'vue'
import { useSlideAction } from './hooks'
import { createImg, draw, getRandomImg, getRandomNumberByRange, throttle } from './util'
import img0 from './img/img.jpg'
import img1 from './img/img1.jpg'
import img2 from './img/img2.jpg'
import img3 from './img/img3.jpg'
import img4 from './img/img4.jpg'
import img5 from './img/img5.jpg'
const props = defineProps({
  l: { type: Number, default: 42 }, // block length
  r: { type: Number, default: 10 }, // block radius
  w: { type: Number, default: 320 }, // canvas width
  h: { type: Number, default: 160 }, // canvas height
  sliderText: { type: String, default: '请拖动滑块验证' },
  accuracy: { type: Number, default: 5 }, // 若为 -1 则不进行机器判断
  show: { type: Boolean, default: true },
  disabled: { type: Boolean, default: false },
  imgs: { type: Array, default: () => [img0, img1, img2, img3, img4, img5] },
  interval: { type: Number, default: 50 } // 节流时长间隔
})
const emit = defineEmits(['success', 'again', 'fail', 'refresh'])
const disabled = computed(() => props.disabled)
const ElWhetherVisible = ref(false)
// 图片加载完关闭遮蔽罩
const blockX = ref(0)
const blockY = ref(0)
// class
const containerCls = reactive({ containerActive: false, containerSuccess: false, containerFail: false })
// sliderMaskWidth sliderLeft
const sliderBox = reactive({ iconCls: 'arrow-right', width: '0', left: '0' })
const block = ref()
const blockCtx = ref()
const canvas = ref()
const canvasCtx = ref()
let img
const { success, isMouseDown, start, move, end, verify } = useSlideAction()
const reset = () => {
  success.value = false
  containerCls.containerActive = false
  containerCls.containerSuccess = false
  containerCls.containerFail = false
  sliderBox.iconCls = 'arrow-right'
  sliderBox.left = '0'
  sliderBox.width = '0'
  block.value.style.left = '0'
  canvasCtx.value?.clearRect(0, 0, props.w, props.h)
  blockCtx.value?.clearRect(0, 0, props.w, props.h)
  block.value.width = props.w
  img.src = getRandomImg(props.imgs)
}
const refresh = () => {
  reset()
  emit('refresh')
}
let isElWhetherVisible = false
const startEl = () => {
  ElWhetherVisible.value = true
}
const endEl = () => {
  ElWhetherVisible.value = false
}
const ElMouseenter = () => {
  if (ElWhetherVisible.value) {
    isElWhetherVisible = true
  }
  ElWhetherVisible.value = true
}
const ElMouseleave = () => {
  if (!isMouseDown.value) {
    setTimeout(() => {
      ElWhetherVisible.value = isElWhetherVisible
      isElWhetherVisible = false
    }, 200)
  }
}
const canvasMouseenter = () => {
  if (ElWhetherVisible.value) {
    isElWhetherVisible = true
  }
  ElWhetherVisible.value = true
}
const canvasMouseleave = () => {
  if (!isMouseDown.value) {
    setTimeout(() => {
      ElWhetherVisible.value = isElWhetherVisible
      isElWhetherVisible = false
    }, 200)
  }
}
function moveCb(moveX) {
  // ElWhetherVisible.value = true
  sliderBox.left = moveX + 'px'
  let blockLeft = ((props.w - 40 - 20) / (props.w - 40)) * moveX
  block.value.style.left = blockLeft + 'px'
  containerCls.containerActive = true
  sliderBox.width = moveX + 'px'
}
function endCb(timestamp) {
  const { spliced, TuringTest } = verify(block.value.style.left, blockX.value, props.accuracy)
  ElWhetherVisible.value = false
  if (spliced) {
    if (props.accuracy === -1) {
      containerCls.containerSuccess = true
      sliderBox.iconCls = 'success'
      success.value = true
      emit('success', timestamp)
      return
    }
    if (TuringTest) {
      // success
      containerCls.containerSuccess = true
      sliderBox.iconCls = 'success'
      success.value = true
      emit('success', timestamp)
    } else {
      containerCls.containerFail = true
      sliderBox.iconCls = 'fail'
      emit('again')
    }
  } else {
    containerCls.containerFail = true
    sliderBox.iconCls = 'fail'
    emit('fail')
    setTimeout(() => {
      reset()
    }, 1000)
  }
}
const touchMoveEvent = throttle((e) => {
  move(props.w, e, moveCb)
}, props.interval)
const touchEndEvent = (e) => end(e, endCb)
onMounted(() => {
  const _canvasCtx = canvas.value?.getContext('2d')
  const _blockCtx = block.value?.getContext('2d')
  canvasCtx.value = _canvasCtx
  blockCtx.value = _blockCtx
  img = createImg(props.imgs, () => {
    const L = props.l + props.r * 2 + 3
    blockX.value = getRandomNumberByRange(L + 10, props.w - (L + 10))
    blockY.value = getRandomNumberByRange(10 + props.r * 2, props.h - (L + 10))
    if (_canvasCtx && _blockCtx) {
      draw(_canvasCtx, blockX.value, blockY.value, props.l, props.r, 'fill')
      draw(_blockCtx, blockX.value, blockY.value, props.l, props.r, 'clip')
      _canvasCtx.drawImage(img, 0, 0, props.w, props.h)
      _blockCtx.drawImage(img, 0, 0, props.w, props.h)
      const _y = blockY.value - props.r * 2 - 1
      const imgData = _blockCtx.getImageData(blockX.value, _y, L, L)
      block.value.width = L
      _blockCtx.putImageData(imgData, 0, _y)
    }
  })
  document.addEventListener('mousemove', touchMoveEvent)
  document.addEventListener('mouseup', touchEndEvent)
})
onUnmounted(() => {
  document.removeEventListener('mousemove', touchMoveEvent)
  document.removeEventListener('mouseup', touchEndEvent)
})
defineExpose({ refresh })
</script>
<style lang="scss">
.xyl-slide-verify {
  position: relative;

  .xyl-slide-verify-block {
    position: absolute;
    left: 0;
    top: 0;
  }

  .xyl-slide-verify-refresh-icon {
    position: absolute;
    right: 0;
    top: 0;
    width: 34px;
    height: 34px;
    cursor: pointer;

    .zy-el-icon {
      font-size: 34px;
      color: #fff;
    }
  }
}

.xyl-slide-verify-slider {
  position: relative;
  text-align: center;
  width: 100%;
  height: var(--zy-height);
  line-height: var(--zy-height);
  border: 1px solid var(--zy-el-border-color);
  border-radius: var(--el-border-radius-base);

  .xyl-slide-verify-slider-mask {
    position: absolute;
    left: 0;
    top: -1px;
    height: var(--zy-height);
    border: 1px solid transparent;
    background: var(--zy-el-color-primary-light-9);
    border-top-left-radius: var(--el-border-radius-base);
    border-bottom-left-radius: var(--el-border-radius-base);

    .xyl-slide-verify-slider-mask-item {
      position: absolute;
      left: 0;
      top: -1px;
      width: 52px;
      height: var(--zy-height);
      background: #fff;
      box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
      cursor: pointer;
      transition: background 0.2s linear;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: var(--zy-el-color-primary);

        .zy-el-icon {
          color: #fff;
        }
      }

      .zy-el-icon {
        line-height: 1;
        font-size: 30px;
        color: var(--zy-el-text-color-primary);
      }
    }
  }

  .xyl-slide-verify-slider-text {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-height);
  }
}

.container-active .xyl-slide-verify-slider-mask {
  height: var(--zy-height);
  border: 1px solid var(--zy-el-color-primary);

  .xyl-slide-verify-slider-mask-item {
    height: var(--zy-height);
    top: -1px;
    border: 1px solid var(--zy-el-color-primary);
  }
}

.container-success .xyl-slide-verify-slider-mask {
  height: var(--zy-height);
  border: 1px solid var(--zy-el-color-success);
  background-color: var(--zy-el-color-success-light-9);

  .xyl-slide-verify-slider-mask-item {
    height: var(--zy-height);
    top: -1px;
    border: 1px solid var(--zy-el-color-success);
    background-color: var(--zy-el-color-success) !important;

    .zy-el-icon {
      color: #fff;
    }
  }
}

.container-fail .xyl-slide-verify-slider-mask {
  height: var(--zy-height);
  border: 1px solid var(--zy-el-color-error);
  background-color: var(--zy-el-color-error-light-9);

  .xyl-slide-verify-slider-mask-item {
    height: var(--zy-height);
    top: -1px;
    border: 1px solid var(--zy-el-color-error);
    background-color: var(--zy-el-color-error) !important;
  }

  .zy-el-icon {
    color: #fff;
  }
}

.container-active .xyl-slide-verify-slider-text,
.container-success .xyl-slide-verify-slider-text,
.container-fail .xyl-slide-verify-slider-text {
  display: none;
}

@keyframes loading {
  0% {
    opacity: 0.7;
  }

  100% {
    opacity: 9;
  }
}

.xyl-slide-verify-popper {
  --zy-el-popover-padding: 0;
  line-height: 0 !important;
}
</style>
