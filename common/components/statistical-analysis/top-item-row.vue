<template>
  <div class="item-box" :style="{ background: props.color }">
    <div class="info">
      <div class="info-num">{{ props.num || 0 }}</div>
      <div class="info-name">{{ props.name }}</div>
    </div>
    <div class="img-box">
      <img :src="props.url" alt="" />
    </div>
  </div>
</template>

<script>
export default { name: 'top-item-row' }
</script>
<script setup>
const props = defineProps({
  color: { type: String, default: '' },
  num: { type: [Number, String], default: 0 },
  name: { type: String, default: '' },
  url: { type: String, default: '' }
})
</script>
<style lang="scss" scoped>
.item-box {
  display: flex;
  background-color: #6ca8fd;
  height: 150px;
  border-radius: 4px 4px 4px 4px;
  padding: 0px 50px;
  justify-content: space-between;

  .img-box {
    display: flex;
    align-items: center;

    img {
      width: 60px;
      height: 60px;
    }
  }

  .info {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;

    .info-num {
      font-size: 30px;
      font-weight: bold;
      color: #ffffff;
    }

    .info-name {
      font-size: 26px;
      font-weight: 400;
      color: #ffffff;
      position: relative;

      &:after {
        content: '';
        width: 200px;
        height: 10px;
        background: #ffffff;
        border-radius: 0px 0px 0px 0px;
        opacity: 0.2;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
}
</style>
