<template>
  <div class="box">
    <div class="title" v-if="props.title">
      <div class="line"></div>
      <div class="text">{{ props.title }}</div>
    </div>
    <slot></slot>
  </div>
</template>
<script>
export default { name: 'card-item' }
</script>
<script setup>
const props = defineProps({
  title: { type: String, default: '' }
})
</script>
<style lang="scss" scoped>
.box {
  padding: 22px 30px;
  box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.15);
  border-radius: 1px 1px 1px 1px;

  .title {
    display: flex;
    align-items: center;

    .line {
      height: 18px;
      width: 4px;
      margin-right: 6px;
      background-color: var(--zy-el-color-primary);
    }

    .text {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
    }
  }
}
</style>
