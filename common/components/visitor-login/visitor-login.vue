<template>
  <div class="visitor-login" v-if="elShow">
    <div class="visitor-login-body">
      <div class="visitor-login-close" @click="handleClose">
        <el-icon>
          <CircleClose />
        </el-icon>
      </div>
      <div class="visitor-login-logo">
        <el-image :src="systemLogo" fit="cover" />
      </div>
      <div class="visitor-login-name" v-html="loginSystemName"></div>
      <el-form :model="form" :rules="rules" ref="formRef" label-position="top" class="visitor-login-form">
        <el-form-item prop="userName">
          <el-input v-model="form.userName" placeholder="请输入您的姓名"></el-input>
        </el-form-item>
        <el-form-item prop="mobile">
          <el-input v-model.number="form.mobile" placeholder="请输入您的电话号码"></el-input>
        </el-form-item>
        <el-form-item prop="verifyCode" class="visitor-login-form-verify-code">
          <el-input v-model="form.verifyCode" placeholder="请输入验证码"></el-input>
          <el-button @click="handleGetVerifyCode" :disabled="countDownText != '获取验证码'" type="primary" plain>
            {{ countDownText }}
          </el-button>
        </el-form-item>
        <el-button type="primary" class="visitor-login-form-button" :loading="loading" @click="submitForm(formRef)">
          {{ loading ? '登录中' : '登录' }}
        </el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
export default { name: 'VisitorLogin' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, computed } from 'vue'
import { systemLogo, systemName, platformAreaName, loginNameLineFeedPosition } from 'common/js/system_var.js'
import { ElMessage } from 'element-plus'
const props = defineProps({ modelValue: { type: Boolean, default: false } })
const emit = defineEmits(['update:modelValue', 'callback'])

const elShow = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const loading = ref(false)
const loginSystemName = computed(() => {
  const name = (platformAreaName.value || '') + systemName.value
  const num = Number(loginNameLineFeedPosition.value || '0') || 0
  return num ? name.substring(0, num) + '\n' + name.substring(num) : name
})

const form = reactive({
  userName: '',
  mobile: '',
  verifyCode: '',
  verifyCodeId: ''
})
const formRef = ref()
const rules = reactive({
  userName: [{ required: true, message: '请输入您的姓名', trigger: ['blur', 'change'] }],
  mobile: [{ required: true, message: '请输入您的电话号码', trigger: ['blur', 'change'] }],
  verifyCode: [{ required: true, message: '请输入验证码', trigger: ['blur', 'change'] }]
})
const countDownText = ref('获取验证码')
const countDown = ref(0)

const handleCountDown = () => {
  if (countDown.value === 0) {
    countDownText.value = '获取验证码'
    countDown.value = 60
    return
  } else {
    countDownText.value = countDown.value + 's'
    countDown.value--
  }
  setTimeout(() => {
    handleCountDown()
  }, 1000)
}

const handleClose = () => {
  elShow.value = false
}

const handleGetVerifyCode = async () => {
  const myreg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
  if (!form.mobile) return ElMessage({ type: 'warning', message: '请先输入手机号！' })
  if (!myreg.test(form.mobile)) return ElMessage({ type: 'warning', message: '手机号码有误，请重填' })
  var { data, code } = await api.openVerifyCodeSend({ sendType: 'no_login', mobile: form.mobile })
  if (code === 200) {
    countDown.value = 60
    form.verifyCodeId = data
    handleCountDown()
    ElMessage({ type: 'success', message: '短信验证码已发送！' })
  }
}

const submitForm = async (formEl) => {
  loading.value = true
  await formEl.validate((valid, fields) => {
    if (valid) {
      const microAppToken = sessionStorage.getItem('microAppToken') || ''
      if (microAppToken) {
        emit('callback')
      } else {
        oauthToken()
      }
    } else {
      loading.value = false
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}

const oauthToken = async () => {
  try {
    var { data, code } = await api.oauthToken({
      grant_type: 'anonymous',
      verifyCode: form.verifyCode,
      mobile: form.mobile,
      userName: form.userName,
      verifyCodeId: form.verifyCodeId
    })
    if (code === 200) {
      const oauth_user = { mobile: form.mobile, userName: form.userName }
      const verify_code = { verifyCode: form.verifyCode, verifyCodeId: form.verifyCodeId }
      sessionStorage.setItem('microAppToken', data.token)
      sessionStorage.setItem('oauth_user', JSON.stringify(oauth_user))
      sessionStorage.setItem('verify_code', JSON.stringify(verify_code))
      ElMessage({ type: 'success', message: '登陆成功' })
      emit('callback')
      elShow.value = false
    }
  } catch {
    loading.value = false
  }
}
</script>
<style lang="scss">
.visitor-login {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 99;

  .visitor-login-body {
    position: relative;
    z-index: 2;
    box-shadow: 0px 2px 25px 1px rgba(0, 0, 0, 0.1);
    padding: var(--zy-distance-one);
    padding-bottom: var(--zy-distance-two);
    background-color: #fff;
    border-radius: 10px;

    .visitor-login-close {
      position: absolute;
      top: -16px;
      right: -16px;
      color: #fff;
      font-size: 22px;
      width: 22px;
      height: 22px;
      cursor: pointer;
    }

    .visitor-login-logo {
      width: 60px;
      margin: auto;
      margin-bottom: var(--zy-distance-two);

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .visitor-login-name {
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: var(--zy-system-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      letter-spacing: 2px;
      padding-bottom: var(--zy-distance-one);
      white-space: pre-wrap;
      margin: auto;
    }

    .visitor-login-form {
      width: 320px;
      margin: auto;
      padding-bottom: var(--zy-distance-one);

      input:-webkit-autofill {
        transition: background-color 5000s ease-in-out 0s;
      }

      .zy-el-form-item {
        margin-bottom: var(--zy-form-distance-bottom);
      }

      .visitor-login-form-verify-code {
        .zy-el-form-item__content {
          display: flex;
          justify-content: space-between;
        }

        .zy-el-input {
          width: 56%;
        }
      }

      .visitor-login-form-button {
        width: 100%;
      }
    }
  }
}
</style>
