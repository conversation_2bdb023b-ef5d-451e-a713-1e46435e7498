<template>
  <div class="xyl-statistics-label">
    <div class="xyl-statistics-label-head">
      <div class="xyl-statistics-label-title">{{ props.label }}</div>
    </div>
    <el-scrollbar class="xyl-statistics-label-scroll">
      <div class="xyl-statistics-label-body">
        <slot></slot>
      </div>
    </el-scrollbar>
  </div>
</template>
<script>
export default { name: 'XylStatisticsLabel' }
</script>
<script setup>
import { computed, provide } from 'vue'
const props = defineProps({ modelValue: [String, Number], label: { type: String, default: '统计维度' } })
const emit = defineEmits(['update:modelValue', 'change'])

const labelId = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const handleChange = (value) => {
  labelId.value = value
  emit('change', value)
}
provide('labelId', labelId)
provide('handleChange', handleChange)
</script>
<style lang="scss">
.xyl-statistics-label {
  width: 180px;
  height: 100%;
  background-color: #fff;

  .xyl-statistics-label-head {
    width: 100%;
    padding: var(--zy-distance-two) 0;
    display: flex;
    align-items: center;
    justify-content: center;

    .xyl-statistics-label-title {
      text-align: center;
      line-height: var(--zy-line-height);
      font-size: var(--zy-name-font-size);
      font-weight: bold;
      padding: 0 var(--zy-distance-five);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        transform: translate(-100%, -50%);
        width: 16px;
        height: 2px;
        background: #dadada;
      }

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(100%, -50%);
        width: 16px;
        height: 2px;
        background: #dadada;
      }
    }
  }

  .xyl-statistics-label-scroll {
    width: 100%;
    height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-two) * 2)));

    .xyl-statistics-label-body {
      padding: 0 var(--zy-distance-two);

      .xyl-statistics-label-item {
        padding-bottom: var(--zy-distance-two);

        .xyl-statistics-label-name {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 var(--zy-distance-two);
          height: var(--zy-height);
          font-size: var(--zy-text-font-size);
          cursor: pointer;
          border: 1px solid var(--zy-el-color-primary);
          border-radius: var(--el-border-radius-small);
          color: var(--zy-el-color-primary);
          -moz-user-select: none;
          /*火狐*/
          -webkit-user-select: none;
          /*webkit浏览器*/
          -ms-user-select: none;
          /*IE10*/
          -khtml-user-select: none;
          /*早期浏览器*/
          user-select: none;
        }
      }

      .xyl-statistics-label-item.is-active {
        .xyl-statistics-label-name {
          font-size: var(--zy-text-font-size);
          color: #fff;
          font-weight: bold;
          background: var(--zy-el-color-primary);
          position: relative;

          &::after {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            top: 50%;
            left: 100%;
            transform: translateY(-50%);
            border-left: 10px solid var(--zy-el-color-primary);
            border-top: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid transparent;
          }
        }
      }
    }
  }
}
</style>
