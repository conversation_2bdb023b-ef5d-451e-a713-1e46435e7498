<template>
  <div :class="['xyl-statistics-label-item', { 'is-active': labelId === value }]" @click="handleClick">
    <div class="xyl-statistics-label-name">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default { name: 'XylStatisticsLabelItem' }
</script>
<script setup>
import { computed, inject } from 'vue'

const props = defineProps({ value: [String, Number] })

const value = computed(() => props.value)
const labelId = inject('labelId')
const handleChange = inject('handleChange')
const handleClick = () => {
  if (value.value === labelId.value) return
  handleChange(value.value)
}
</script>
