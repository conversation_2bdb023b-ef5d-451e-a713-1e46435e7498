/*!
 * @form-create/element-ui v3.2.3
 * (c) 2018-2024 xaboy
 * Github https://github.com/xaboy/form-create
 * Released under the MIT License.
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).formCreate={},e.Vue)}(this,(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function l(e,t){return l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},l(e,t)}function c(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function s(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=u(e);if(t){var i=u(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return c(this,n)}}function f(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function p(e,t){return Object.keys(e).reduce((function(n,r){return t&&-1!==t.indexOf(r)||n.push(e[r]),n}),[])}function h(e){return Array.isArray(e)?e:[null,void 0,""].indexOf(e)>-1?[]:[e]}var m=t.defineComponent({name:"fcCheckbox",inheritAttrs:!1,props:{formCreateInject:Object,modelValue:{type:Array,default:function(){return[]}},type:String,input:Boolean,inputValue:String},emits:["update:modelValue","fc.el"],setup:function(e,n){var r=t.toRef(e.formCreateInject,"options",[]),i=t.toRef(e,"modelValue"),o=t.toRef(e,"inputValue",""),a=t.ref(o.value),u=t.toRef(e,"input",!1),l=function(e){var t=f(h(i.value)),n=t.indexOf(a.value);a.value=e,n>-1&&(t.splice(n,1),t.push(e),c(t))};t.watch(o,(function(e){u.value?l(e):a.value=e}));var c=function(e){n.emit("update:modelValue",e)};return{options:function(){return Array.isArray(r.value)?r.value:[]},value:i,onInput:c,updateCustomValue:l,makeInput:function(e){if(u.value)return t.createVNode(e,{value:a.value,label:a.value},{default:function(){return[t.createVNode(t.resolveComponent("ElInput"),{modelValue:a.value,"onUpdate:modelValue":l},null)]}})}}},render:function(){var e,n,i=this,o="button"===this.type?"ElCheckboxButton":"ElCheckbox",a=t.resolveComponent(o);return t.createVNode(t.resolveComponent("ElCheckboxGroup"),t.mergeProps(this.$attrs,{modelValue:this.value,"onUpdate:modelValue":this.onInput,ref:"el"}),r({default:function(){return[i.options().map((function(e,n){var i=r({},e),u=i.value,l=i.label;return delete i.value,delete i.label,t.createVNode(a,t.mergeProps(i,{label:u,value:u,key:o+n+"-"+u}),{default:function(){return[l||u||""]}})})),null===(e=(n=i.$slots).default)||void 0===e?void 0:e.call(n),i.makeInput(a)]}},p(this.$slots,["default"])))},mounted:function(){this.$emit("fc.el",this.$refs.el)}});function v(e){e=e||new Map;var t={$on:function(t,n){var r=e.get(t);r&&r.push(n)||e.set(t,[n])},$once:function(e,n){n._once=!0,t.$on(e,n)},$off:function(t,n){var r=e.get(t);r&&r.splice(r.indexOf(n)>>>0,1)},$emit:function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];(e.get(n)||[]).slice().map((function(e){e._once&&(t.$off(n,e),delete e._once),e.apply(void 0,i)})),(e.get("*")||[]).slice().map((function(e){e(n,i)}))}};return t}function g(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&"undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===n&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=e:i.appendChild(document.createTextNode(e))}}g("._fc-frame ._fc-files img{display:inline-block;height:100%;vertical-align:top;width:100%}._fc-frame ._fc-upload-btn{border:1px dashed #c0ccda;cursor:pointer}._fc-frame._fc-disabled ._fc-upload-btn,._fc-frame._fc-disabled .zy-el-button{color:#999;cursor:not-allowed!important}._fc-frame ._fc-upload-cover{background:rgba(0,0,0,.6);bottom:0;left:0;opacity:0;position:absolute;right:0;top:0;-webkit-transition:opacity .3s;-o-transition:opacity .3s;transition:opacity .3s}._fc-frame ._fc-upload-cover i{color:#fff;cursor:pointer;font-size:20px;margin:0 2px}._fc-frame ._fc-files:hover ._fc-upload-cover{opacity:1}._fc-frame .zy-el-upload{display:block}._fc-frame ._fc-upload-icon{cursor:pointer}._fc-files,._fc-frame ._fc-upload-btn{background:#fff;border:1px solid #c0ccda;border-radius:4px;-webkit-box-shadow:2px 2px 5px rgba(0,0,0,.1);box-shadow:2px 2px 5px rgba(0,0,0,.1);-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;height:58px;line-height:58px;margin-right:4px;overflow:hidden;position:relative;text-align:center;width:58px}");var y={name:"IconCircleClose"},b={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},w=[t.createElementVNode("path",{fill:"currentColor",d:"M466.752 512l-90.496-90.496a32 32 0 0145.248-45.248L512 466.752l90.496-90.496a32 32 0 1145.248 45.248L557.248 512l90.496 90.496a32 32 0 11-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 01-45.248-45.248L466.752 512z"},null,-1),t.createElementVNode("path",{fill:"currentColor",d:"M512 896a384 384 0 100-768 384 384 0 000 768zm0 64a448 448 0 110-896 448 448 0 010 896z"},null,-1)];y.render=function(e,n,r,i,o,a){return t.openBlock(),t.createElementBlock("svg",b,w)};var x={name:"IconDocument"},_={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},$=[t.createElementVNode("path",{fill:"currentColor",d:"M832 384H576V128H192v768h640V384zm-26.496-64L640 154.496V320h165.504zM160 64h480l256 256v608a32 32 0 01-32 32H160a32 32 0 01-32-32V96a32 32 0 0132-32zm160 448h384v64H320v-64zm0-192h160v64H320v-64zm0 384h384v64H320v-64z"},null,-1)];x.render=function(e,n,r,i,o,a){return t.openBlock(),t.createElementBlock("svg",_,$)};var k={name:"IconDelete"},C={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},V=[t.createElementVNode("path",{fill:"currentColor",d:"M160 256H96a32 32 0 010-64h256V95.936a32 32 0 0132-32h256a32 32 0 0132 32V192h256a32 32 0 110 64h-64v672a32 32 0 01-32 32H192a32 32 0 01-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 01-32-32V416a32 32 0 0164 0v320a32 32 0 01-32 32zm192 0a32 32 0 01-32-32V416a32 32 0 0164 0v320a32 32 0 01-32 32z"},null,-1)];k.render=function(e,n,r,i,o,a){return t.openBlock(),t.createElementBlock("svg",C,V)};var O={name:"IconView"},S={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},E=[t.createElementVNode("path",{fill:"currentColor",d:"M512 160c320 0 512 352 512 352S832 864 512 864 0 512 0 512s192-352 512-352zm0 64c-225.28 0-384.128 208.064-436.8 288 52.608 79.872 211.456 288 436.8 288 225.28 0 384.128-208.064 436.8-288-52.608-79.872-211.456-288-436.8-288zm0 64a224 224 0 110 448 224 224 0 010-448zm0 64a160.192 160.192 0 00-160 160c0 88.192 71.744 160 160 160s160-71.808 160-160-71.744-160-160-160z"},null,-1)];O.render=function(e,n,r,i,o,a){return t.openBlock(),t.createElementBlock("svg",S,E)};var R={name:"IconFolderOpened"},j={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},F=[t.createElementVNode("path",{fill:"currentColor",d:"M878.08 448H241.92l-96 384h636.16l96-384zM832 384v-64H485.76L357.504 192H128v448l57.92-231.744A32 32 0 01216.96 384H832zm-24.96 512H96a32 32 0 01-32-32V160a32 32 0 0132-32h287.872l128.384 128H864a32 32 0 0132 32v96h23.04a32 32 0 0131.04 39.744l-112 448A32 32 0 01807.04 896z"},null,-1)];function D(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!t.isVNode(e)}R.render=function(e,n,r,i,o,a){return t.openBlock(),t.createElementBlock("svg",j,F)};var A=t.defineComponent({name:"fcFrame",props:{type:{type:String,default:"input"},field:String,helper:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},src:{type:String,required:!0},icon:{type:String,default:"IconFolderOpened"},width:{type:String,default:"500px"},height:{type:String,default:"370px"},maxLength:{type:Number,default:0},okBtnText:{type:String,default:"确定"},closeBtnText:{type:String,default:"关闭"},modalTitle:String,handleIcon:{type:[String,Boolean],default:void 0},title:String,allowRemove:{type:Boolean,default:!0},onOpen:{type:Function,default:function(){}},onOk:{type:Function,default:function(){}},onCancel:{type:Function,default:function(){}},onLoad:{type:Function,default:function(){}},onBeforeRemove:{type:Function,default:function(){}},onRemove:{type:Function,default:function(){}},onHandle:Function,modal:{type:Object,default:function(){return{}}},srcKey:[String,Number],modelValue:[Array,String,Number,Object],previewMask:void 0,footer:{type:Boolean,default:!0},reload:{type:Boolean,default:!0},closeBtn:{type:Boolean,default:!0},okBtn:{type:Boolean,default:!0},formCreateInject:Object},emits:["update:modelValue","change"],components:{IconFolderOpened:R,IconView:O},data:function(){return{fileList:h(this.modelValue),previewVisible:!1,frameVisible:!1,previewImage:"",bus:new v}},watch:{modelValue:function(e){this.fileList=h(e)}},methods:{close:function(){this.closeModel(!0)},closeModel:function(e){this.bus.$emit(e?"$close":"$ok"),this.reload&&(this.bus.$off("$ok"),this.bus.$off("$close")),this.frameVisible=!1},handleCancel:function(){this.previewVisible=!1},showModel:function(){this.disabled||!1===this.onOpen()||(this.frameVisible=!0)},input:function(){var e=this.fileList,t=1===this.maxLength?e[0]||"":e;this.$emit("update:modelValue",t),this.$emit("change",t)},makeInput:function(){var e=this;return t.createVNode(t.resolveComponent("ElInput"),t.mergeProps({type:"text",modelValue:this.fileList.map((function(t){return e.getSrc(t)})).toString(),readonly:!0},{key:1}),{append:function(){return t.createVNode(t.resolveComponent("ElButton"),{icon:t.resolveComponent(e.icon),onClick:function(){return e.showModel()}},null)},suffix:function(){return e.fileList.length&&!e.disabled?t.createVNode(t.resolveComponent("ElIcon"),{class:"el-input__icon _fc-upload-icon",onClick:function(){e.fileList=[],e.input()}},{default:function(){return[t.createVNode(y,null,null)]}}):null}})},makeGroup:function(e){return(!this.maxLength||this.fileList.length<this.maxLength)&&e.push(this.makeBtn()),t.createVNode("div",{key:2},[e])},makeItem:function(e,n){return t.createVNode("div",{class:"_fc-files",key:"3"+e},[n])},valid:function(e){var t=this.formCreateInject.field||this.field;if(t&&e!==t)throw new Error("[frame]无效的字段值")},makeIcons:function(e,n){if(!1!==this.handleIcon||!0===this.allowRemove){var r=[];return("file"!==this.type&&!1!==this.handleIcon||"file"===this.type&&this.handleIcon)&&r.push(this.makeHandleIcon(e,n)),this.allowRemove&&r.push(this.makeRemoveIcon(e,n)),t.createVNode("div",{class:"_fc-upload-cover",key:4},[r])}},makeHandleIcon:function(e,n){var r=this,i=t.resolveComponent(!0===this.handleIcon||void 0===this.handleIcon?"icon-view":this.handleIcon);return t.createVNode(t.resolveComponent("ElIcon"),{onClick:function(){return r.handleClick(e)},key:"5"+n},{default:function(){return[t.createVNode(i,null,null)]}})},makeRemoveIcon:function(e,n){var r=this;return t.createVNode(t.resolveComponent("ElIcon"),{onClick:function(){return r.handleRemove(e)},key:"6"+n},{default:function(){return[t.createVNode(k,null,null)]}})},makeFiles:function(){var e=this;return this.makeGroup(this.fileList.map((function(n,r){return e.makeItem(r,[t.createVNode(t.resolveComponent("ElIcon"),{onClick:function(){return e.handleClick(n)}},{default:function(){return[t.createVNode(x,null,null)]}}),e.makeIcons(n,r)])})))},makeImages:function(){var e=this;return this.makeGroup(this.fileList.map((function(n,r){return e.makeItem(r,[t.createVNode("img",{src:e.getSrc(n)},null),e.makeIcons(n,r)])})))},makeBtn:function(){var e=this,n=t.resolveComponent(this.icon);return t.createVNode("div",{class:"_fc-upload-btn",onClick:function(){return e.showModel()},key:7},[t.createVNode(t.resolveComponent("ElIcon"),null,{default:function(){return[t.createVNode(n,null,null)]}})])},handleClick:function(e){if(this.onHandle)return this.onHandle(e);this.previewImage=this.getSrc(e),this.previewVisible=!0},handleRemove:function(e){this.disabled||!1!==this.onBeforeRemove(e)&&(this.fileList.splice(this.fileList.indexOf(e),1),this.input(),this.onRemove(e))},getSrc:function(e){return this.srcKey?e[this.srcKey]:e},frameLoad:function(e){var t=this;this.onLoad(e);try{!0===this.helper&&(e.form_create_helper={api:this.formCreateInject.api,close:function(e){t.valid(e),t.closeModel()},set:function(e,n){t.valid(e),!t.disabled&&t.$emit("update:modelValue",n)},get:function(e){return t.valid(e),t.modelValue},onOk:function(e){return t.bus.$on("$ok",e)},onClose:function(e){return t.bus.$on("$close",e)}})}catch(e){console.error(e)}},makeFooter:function(){var e=this,n=this.$props,r=n.okBtnText,i=n.closeBtnText,o=n.closeBtn,a=n.okBtn;if(n.footer)return t.createVNode("div",null,[o?t.createVNode(t.resolveComponent("ElButton"),{onClick:function(){return!1!==e.onCancel()&&(e.frameVisible=!1)}},D(i)?i:{default:function(){return[i]}}):null,a?t.createVNode(t.resolveComponent("ElButton"),{type:"primary",onClick:function(){return!1!==e.onOk()&&e.closeModel()}},D(r)?r:{default:function(){return[r]}}):null])}},render:function(){var e,n=this,i=this.type;e="input"===i?this.makeInput():"image"===i?this.makeImages():this.makeFiles();var o=this.$props,a=o.width,u=void 0===a?"30%":a,l=o.height,c=o.src,s=o.title,f=o.modalTitle;return t.nextTick((function(){n.$refs.frame&&n.frameLoad(n.$refs.frame.contentWindow||{})})),t.createVNode("div",{class:{"_fc-frame":!0,"_fc-disabled":this.disabled}},[e,t.createVNode(t.resolveComponent("ElDialog"),{appendToBody:!0,modal:this.previewMask,title:f,modelValue:this.previewVisible,onClose:this.handleCancel},{default:function(){return[t.createVNode("img",{style:"width: 100%",src:n.previewImage},null)]}}),t.createVNode(t.resolveComponent("ElDialog"),t.mergeProps({appendToBody:!0},r({width:u,title:s},this.modal),{modelValue:this.frameVisible,onClose:function(){return n.closeModel(!0)}}),{default:function(){return[n.frameVisible||!n.reload?t.createVNode("iframe",{ref:"frame",src:c,frameBorder:"0",style:{height:l,border:"0 none",width:"100%"}},null):null]},footer:function(){return n.makeFooter()}})])},beforeMount:function(){var e=this.formCreateInject,t=e.name,n=e.field,r=e.api;t&&r.on("fc:closeModal:"+t,this.close),n&&r.on("fc:closeModal:"+n,this.close)},beforeUnmount:function(){var e=this.formCreateInject,t=e.name,n=e.field,r=e.api;t&&r.off("fc:closeModal:"+t,this.close),n&&r.off("fc:closeModal:"+n,this.close)}}),P=t.defineComponent({name:"fcRadio",inheritAttrs:!1,props:{formCreateInject:Object,modelValue:{type:[String,Number,Boolean],default:""},type:String,input:Boolean,inputValue:String},emits:["update:modelValue","fc.el"],setup:function(e,n){var r=t.toRef(e.formCreateInject,"options",[]),i=t.toRef(e,"modelValue"),o=t.toRef(e,"inputValue",""),a=t.ref(o.value),u=t.toRef(e,"input",!1);t.watch(o,(function(e){u.value?c(e):a.value=e}));var l=function(e){n.emit("update:modelValue",e)},c=function(e){var t=a.value;a.value=e,i.value===t&&l(e)};return{options:function(){return Array.isArray(r.value)?r.value:[]},value:i,onInput:l,updateCustomValue:c,customValue:a,makeInput:function(e){if(u.value)return t.createVNode(e,{value:a.value,label:a.value},{default:function(){return[t.createVNode(t.resolveComponent("ElInput"),{modelValue:a.value,"onUpdate:modelValue":c},null)]}})}}},render:function(){var e,n,i=this,o="button"===this.type?"ElRadioButton":"ElRadio",a=t.resolveComponent(o);return t.createVNode(t.resolveComponent("ElRadioGroup"),t.mergeProps(this.$attrs,{modelValue:this.value,"onUpdate:modelValue":this.onInput,ref:"el"}),r({default:function(){return[i.options().map((function(e,n){var i=r({},e),u=i.value,l=i.label;return delete i.value,delete i.label,t.createVNode(a,t.mergeProps(i,{label:u,value:u,key:o+n+"-"+u}),{default:function(){return[l||u||""]}})})),null===(e=(n=i.$slots).default)||void 0===e?void 0:e.call(n),i.makeInput(a)]}},p(this.$slots,["default"])))},mounted:function(){this.$emit("fc.el",this.$refs.el)}}),N={type:function(e,t){return Object.prototype.toString.call(e)==="[object "+t+"]"},Undef:function(e){return null==e},Element:function(e){return"object"===i(e)&&null!==e&&1===e.nodeType&&!N.Object(e)},trueArray:function(e){return Array.isArray(e)&&e.length>0},Function:function(e){var t=this.getType(e);return"Function"===t||"AsyncFunction"===t},getType:function(e){var t=Object.prototype.toString.call(e);return/^\[object (.*)\]$/.exec(t)[1]},empty:function(e){return null==e||(!(!Array.isArray(e)||!Array.isArray(e)||e.length)||"string"==typeof e&&!e)}};function I(e,t){return{}.hasOwnProperty.call(e,t)}["Date","Object","String","Boolean","Array","Number"].forEach((function(e){N[e]=function(t){return N.type(t,e)}}));var B=t.defineComponent({name:"fcSelect",inheritAttrs:!1,props:{formCreateInject:Object,modelValue:{type:[Array,String,Number,Boolean,Object],default:void 0},type:String},emits:["update:modelValue","fc.el"],setup:function(e){var n=t.toRef(e.formCreateInject,"options",[]);return{options:function(){return Array.isArray(n.value)?n.value:[]},value:t.toRef(e,"modelValue")}},render:function(){var e,n,i=this,o=function(e,n){return t.createVNode(t.resolveComponent("ElOption"),t.mergeProps(e,{key:n+"-"+e.value}),null)},a=this.options();return t.createVNode(t.resolveComponent("ElSelect"),t.mergeProps(this.$attrs,{modelValue:this.value,"onUpdate:modelValue":function(e){return i.$emit("update:modelValue",e)},ref:"el"}),r({default:function(){return[a.map((function(e,n){return I(e||"","options")?function(e,n){return t.createVNode(t.resolveComponent("ElOptionGroup"),{label:e.label,key:n+"-"+e.label},{default:function(){return[N.trueArray(e.options)&&e.options.map((function(e,t){return o(e,t)}))]}})}(e,n):o(e,n)})),null===(e=(n=i.$slots).default)||void 0===e?void 0:e.call(n)]}},p(this.$slots,["default"])))},mounted:function(){this.$emit("fc.el",this.$refs.el)}}),T=t.defineComponent({name:"fcTree",inheritAttrs:!1,formCreateParser:{mergeProp:function(e){var t=e.prop.props;t.nodeKey||(t.nodeKey="id"),t.props||(t.props={label:"title"})}},props:{type:String,modelValue:{type:[Array,String,Number],default:function(){return[]}}},emits:["update:modelValue","fc.el"],watch:{modelValue:function(){this.setValue()}},methods:{updateValue:function(){var e;this.$refs.tree&&(e="selected"===this.type?this.$refs.tree.getCurrentKey():this.$refs.tree.getCheckedKeys(),this.$emit("update:modelValue",e))},setValue:function(){this.$refs.tree&&("selected"===this.type?this.$refs.tree.setCurrentKey(this.modelValue):this.$refs.tree.setCheckedKeys(h(this.modelValue)))}},render:function(){return t.createVNode(t.resolveComponent("ElTree"),t.mergeProps(this.$attrs,{ref:"tree",onCheck:this.updateValue,"onNode-click":this.updateValue}),this.$slots)},mounted:function(){this.setValue(),this.$emit("fc.el",this.$refs.tree)}});g("._fc-exceed .zy-el-upload{display:none}.zy-el-upload-list.is-disabled .zy-el-upload{cursor:not-allowed!important}");var M={name:"IconUpload"},L={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},U=[t.createElementVNode("path",{fill:"currentColor",d:"M160 832h704a32 32 0 110 64H160a32 32 0 110-64zm384-578.304V704h-64V247.296L237.248 490.048 192 444.8 508.8 128l316.8 316.8-45.312 45.248L544 253.696z"},null,-1)];function H(e,t){return{url:e,name:z(e),uid:t}}function z(e){return(""+e).split("/").pop()}M.render=function(e,n,r,i,o,a){return t.openBlock(),t.createElementBlock("svg",L,U)};var Y=t.defineComponent({name:"fcUpload",inheritAttrs:!1,formCreateParser:{toFormValue:function(e){return h(e)},toValue:function(e,t){return 1===t.prop.props.limit?e[0]||"":e}},props:{previewMask:void 0,onPreview:Function,modalTitle:String,modelValue:[Array,String]},emits:["update:modelValue","change","remove","fc.el"],data:function(){return{previewVisible:!1,previewImage:"",fileList:[]}},created:function(){this.fileList=h(this.modelValue).map(H)},watch:{modelValue:function(e){this.fileList=h(e).map(H)}},methods:{handlePreview:function(e){this.onPreview?this.onPreview.apply(this,arguments):(this.previewImage=e.url,this.previewVisible=!0)},update:function(e){var t=e.map((function(e){return e.url})).filter((function(e){return void 0!==e}));this.$emit("update:modelValue",t)},handleCancel:function(){this.previewVisible=!1},handleChange:function(e,t){this.$emit.apply(this,["change"].concat(Array.prototype.slice.call(arguments))),"success"===e.status&&this.update(t)},handleRemove:function(e,t){this.$emit.apply(this,["remove"].concat(Array.prototype.slice.call(arguments))),this.update(t)}},render:function(){var e,n,i=this,o=h(this.modelValue).length;return t.createVNode(t.Fragment,null,[t.createVNode(t.resolveComponent("ElUpload"),t.mergeProps({key:o,"list-type":"picture-card"},this.$attrs,{class:{"_fc-exceed":!!this.$attrs.limit&&this.$attrs.limit<=o},onPreview:this.handlePreview,onChange:this.handleChange,onRemove:this.handleRemove,fileList:this.fileList,ref:"upload"}),r({default:function(){return[(null===(e=(n=i.$slots).default)||void 0===e?void 0:e.call(n))||t.createVNode(t.resolveComponent("ElIcon"),null,{default:function(){return[t.createVNode(M,null,null)]}})]}},p(this.$slots,["default"]))),t.createVNode(t.resolveComponent("ElDialog"),{appendToBody:!0,modal:this.previewMask,title:this.modalTitle,modelValue:this.previewVisible,onClose:this.handleCancel},{default:function(){return[t.createVNode("img",{style:"width: 100%",src:i.previewImage},null)]}})])},mounted:function(){this.$emit("fc.el",this.$refs.upload)}});function q(e,t,n){e[t]=n}function J(e,t){delete e[t]}function G(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=!1;for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var o=t[i];if((r=Array.isArray(o))||N.Object(o)){var a=void 0===e[i];if(r)r=!1,a&&q(e,i,[]);else if(o._clone&&void 0!==n){if(!n){q(e,i,o._clone());continue}o=o.getRule(),a&&q(e,i,{})}else a&&q(e,i,{});e[i]=G(e[i],o,n)}else q(e,i,o),N.Undef(o)||(N.Undef(o.__json)||(e[i].__json=o.__json),N.Undef(o.__origin)||(e[i].__origin=o.__origin))}return void 0!==n&&Array.isArray(e)?e.filter((function(e){return!e||!e.__ctrl})):e}function W(e){return G({},{value:e}).value}var K=Object.assign||function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&q(e,r,t[r]);return e};function X(){return K.apply(this,arguments)}function Q(e){return"object"!==i(e)||null===e?e:e instanceof Array?f(e):r({},e)}g('._fc-group{display:flex;flex-direction:column;justify-content:center;min-height:38px;width:100%}._fc-group-disabled ._fc-group-add,._fc-group-disabled ._fc-group-btn{cursor:not-allowed}._fc-group-handle{background-color:#fff;border:1px dashed #d9d9d9;border-radius:15px;bottom:-15px;display:flex;flex-direction:row;padding:3px 8px;position:absolute;right:30px}._fc-group-btn{cursor:pointer}._fc-group-idx{align-items:center;background:#eee;border-radius:15px;bottom:-15px;display:flex;font-weight:700;height:30px;justify-content:center;left:10px;position:absolute;width:30px}._fc-group-handle ._fc-group-btn+._fc-group-btn{margin-left:7px}._fc-group-container{border:1px dashed #d9d9d9;border-radius:5px;display:flex;flex-direction:column;margin:5px 5px 25px;padding:20px 20px 25px;position:relative}._fc-group-arrow{height:20px;position:relative;width:20px}._fc-group-arrow:before{border-left:2px solid #999;border-top:2px solid #999;content:"";height:9px;left:5px;position:absolute;top:8px;transform:rotate(45deg);width:9px}._fc-group-arrow._fc-group-down{transform:rotate(180deg)}._fc-group-plus-minus{cursor:pointer;height:20px;position:relative;width:20px}._fc-group-plus-minus:after,._fc-group-plus-minus:before{background-color:#409eff;content:"";height:2px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:60%}._fc-group-plus-minus:before{transform:translate(-50%,-50%) rotate(90deg)}._fc-group-plus-minus._fc-group-minus:before{display:none}._fc-group-plus-minus._fc-group-minus:after{background-color:#f56c6c}._fc-group-add{border:1px solid rgba(64,158,255,.5);border-radius:15px;cursor:pointer;height:25px;width:25px}._fc-group-add._fc-group-plus-minus:after,._fc-group-add._fc-group-plus-minus:before{width:50%}');var Z=t.defineComponent({name:"fcGroup",props:{field:String,rule:Array,expand:Number,options:Object,button:{type:Boolean,default:!0},max:{type:Number,default:0},min:{type:Number,default:0},modelValue:{type:Array,default:function(){return[]}},defaultValue:Object,sortBtn:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},syncDisabled:{type:Boolean,default:!0},onBeforeRemove:{type:Function,default:function(){}},onBeforeAdd:{type:Function,default:function(){}},formCreateInject:Object,parse:Function},data:function(){return{len:0,cacheRule:{},cacheValue:{},sort:[],form:t.markRaw(this.formCreateInject.form.$form())}},emits:["update:modelValue","change","itemMounted","remove","add"],watch:{rule:{handler:function(e,t){var n=this;Object.keys(this.cacheRule).forEach((function(r){var i=n.cacheRule[r];if(i.$f){var o=i.$f.formData();if(e===t)i.$f.deferSyncValue((function(){G(i.rule,e),i.$f.setValue(o)}),!0);else{var a=i.$f.formData();i.$f.once("reloading",(function(){i.$f.setValue(a)})),i.rule=W(e)}}}))},deep:!0},expand:function(e){var t=e-this.modelValue.length;t>0&&this.expandRule(t)},modelValue:{handler:function(e){var t=this;e=e||[];var n=Object.keys(this.sort),r=n.length,i=r-e.length;if(i<0){for(var o=i;o<0;o++)this.addRule(e.length+o,!0);for(var a=0;a<r;a++)this.setValue(n[a],e[a])}else{if(i>0)for(var u=0;u<i;u++)this.removeRule(n[r-u-1]);e.forEach((function(r,i){t.setValue(n[i],e[i])}))}},deep:!0}},methods:{_value:function(e){return e&&I(e,this.field)?e[this.field]:e},cache:function(e,t){this.cacheValue[e]=JSON.stringify(t)},input:function(e){this.$emit("update:modelValue",e),this.$emit("change",e)},formData:function(e,t){var n=this,i=this.cacheRule,o=this.sort;if(o.filter((function(e){return i[e].$f})).length===o.length){var a=o.map((function(i){var o=e===i?t:r({},n.cacheRule[i].$f.form),a=n.field?o[n.field]||null:o;return n.cache(i,a),a}));this.input(a)}},setValue:function(e,t){var n=this.field;n&&(t=a({},n,this._value(t))),this.cacheValue[e]!==JSON.stringify(n?t[n]:t)&&this.cache(e,t)},addRule:function(e,n){var i=this,o=this.formCreateInject.form.copyRules(this.rule||[]),u=this.options?r({},this.options):{submitBtn:!1,resetBtn:!1};if(this.defaultValue){u.formData||(u.formData={});var l=W(this.defaultValue);X(u.formData,this.field?a({},this.field,l):l)}this.parse&&this.parse({rule:o,options:u,index:this.sort.length}),this.cacheRule[++this.len]={rule:o,options:u},n&&t.nextTick((function(){return i.$emit("add",o,Object.keys(i.cacheRule).length-1)}))},add$f:function(e,n,r){var i=this;this.cacheRule[n].$f=r,t.nextTick((function(){i.$emit("itemMounted",r,Object.keys(i.cacheRule).indexOf(n))}))},removeRule:function(e,n){var r=this,i=Object.keys(this.cacheRule).indexOf(e);delete this.cacheRule[e],delete this.cacheValue[e],n&&t.nextTick((function(){return r.$emit("remove",i)}))},add:function(e){if(!this.disabled&&!1!==this.onBeforeAdd(this.modelValue)){var t=f(this.modelValue);t.push(this.defaultValue?W(this.defaultValue):this.field?null:{}),this.input(t)}},del:function(e,t){if(!this.disabled&&!1!==this.onBeforeRemove(this.modelValue,e)){this.removeRule(t,!0);var n=f(this.modelValue);n.splice(e,1),this.input(n)}},addIcon:function(e){return t.createVNode("div",{class:"_fc-group-btn _fc-group-plus-minus",onClick:this.add},null)},delIcon:function(e,n){var r=this;return t.createVNode("div",{class:"_fc-group-btn _fc-group-plus-minus _fc-group-minus",onClick:function(){return r.del(e,n)}},null)},sortUpIcon:function(e){var n=this;return t.createVNode("div",{class:"_fc-group-btn _fc-group-arrow _fc-group-up",onClick:function(){return n.changeSort(e,-1)}},null)},sortDownIcon:function(e){var n=this;return t.createVNode("div",{class:"_fc-group-btn _fc-group-arrow _fc-group-down",onClick:function(){return n.changeSort(e,1)}},null)},changeSort:function(e,t){var n=this.sort[e];this.sort[e]=this.sort[e+t],this.sort[e+t]=n,this.formData(0)},makeIcon:function(e,t,n){var r=this;if(this.$slots.button)return this.$slots.button({total:e,index:t,vm:this,key:n,del:function(){return r.del(t,n)},add:this.add});var i=[];return(!this.max||e<this.max)&&e===t+1&&i.push(this.addIcon(n)),e>this.min&&i.push(this.delIcon(t,n)),this.sortBtn&&t&&i.push(this.sortUpIcon(t)),this.sortBtn&&t!==e-1&&i.push(this.sortDownIcon(t)),i},emitEvent:function(e,t,n,r){this.$emit.apply(this,[e].concat(f(t),[this.cacheRule[r].$f,n]))},expandRule:function(e){for(var t=0;t<e;t++)this.addRule(t)}},created:function(){var e=this;t.watch((function(){return r({},e.cacheRule)}),(function(t){e.sort=Object.keys(t)}),{immediate:!0});for(var n=(this.expand||0)-this.modelValue.length,i=0;i<this.modelValue.length;i++)this.addRule(i);n>0&&this.expandRule(n)},render:function(){var e=this,n=this.sort,r=this.button,i=this.form,o=this.disabled,u=0===n.length?this.$slots.default?this.$slots.default({vm:this,add:this.add}):t.createVNode("div",{key:"a_def",class:"_fc-group-plus-minus _fc-group-add fc-clock",onClick:this.add},null):n.map((function(u,l){var c=e.cacheRule[u],s=c.rule,f=c.options,d=r&&!o?e.makeIcon(n.length,l,u):[];return t.createVNode("div",{class:"_fc-group-container",key:u},[t.createVNode(i,t.mergeProps({key:u},{disabled:o,"onUpdate:modelValue":function(t){return e.formData(u,t)},"onEmit-event":function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return e.emitEvent(t,r,l,u)},"onUpdate:api":function(t){return e.add$f(l,u,t)},inFor:!0,modelValue:e.field?a({},e.field,e._value(e.modelValue[l])):e.modelValue[l],rule:s,option:f,extendOption:!0}),null),t.createVNode("div",{class:"_fc-group-idx"},[l+1]),d.length?t.createVNode("div",{class:"_fc-group-handle fc-clock"},[d]):null])}));return t.createVNode("div",{key:"con",class:"_fc-group "+(o?"_fc-group-disabled":"")},[u])}}),ee=t.defineComponent({name:"fcSubForm",props:{rule:Array,options:{type:Object,default:function(){return t.reactive({submitBtn:!1,resetBtn:!1})}},modelValue:{type:Object,default:function(){return{}}},disabled:{type:Boolean,default:!1},syncDisabled:{type:Boolean,default:!0},formCreateInject:Object},data:function(){return{cacheValue:{},subApi:{},form:t.markRaw(this.formCreateInject.form.$form())}},emits:["fc:subform","update:modelValue","change","itemMounted"],watch:{modelValue:function(e){this.setValue(e)}},methods:{formData:function(e){this.cacheValue=JSON.stringify(e),this.$emit("update:modelValue",e),this.$emit("change",e)},setValue:function(e){var t=JSON.stringify(e);this.cacheValue!==t&&(this.cacheValue=t,this.subApi.coverValue(e||{}))},add$f:function(e){var n=this;this.subApi=e,t.nextTick((function(){n.$emit("itemMounted",e)}))}},render:function(){var e=this.form;return t.createVNode(e,{disabled:this.disabled,"onUpdate:modelValue":this.formData,modelValue:this.modelValue,"onEmit-event":this.$emit,"onUpdate:api":this.add$f,rule:this.rule,option:this.options,extendOption:!0},null)}}),te={name:"IconWarning"},ne={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},re=[t.createElementVNode("path",{fill:"currentColor",d:"M512 64a448 448 0 110 896 448 448 0 010-896zm0 832a384 384 0 000-768 384 384 0 000 768zm48-176a48 48 0 11-96 0 48 48 0 0196 0zm-48-464a32 32 0 0132 32v288a32 32 0 01-64 0V288a32 32 0 0132-32z"},null,-1)];te.render=function(e,n,r,i,o,a){return t.openBlock(),t.createElementBlock("svg",ne,re)};var ie=[m,A,P,B,T,Y,Z,ee,te];function oe(e,t){var n=null;return function(){for(var r=this,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];null!==n&&clearTimeout(n),n=setTimeout((function(){return e.call.apply(e,[r].concat(o))}),t)}}function ae(e){var t=e.replace(/([A-Z])/g,"-$1").toLocaleLowerCase();return 0===t.indexOf("-")&&(t=t.substr(1)),t}var ue=function e(t,n){if(t&&t!==n)return t.props.formCreateInject?t.props.formCreateInject:t.parent?e(t.parent,n):void 0};function le(e,n,i){return t.defineComponent({name:"FormCreate"+(e.isMobile?"Mobile":""),components:n,directives:i,props:{rule:{type:Array,required:!0,default:function(){return[]}},option:{type:Object,default:function(){return{}}},extendOption:Boolean,driver:String,modelValue:Object,disabled:{type:Boolean,default:void 0},preview:{type:Boolean,default:void 0},api:Object,name:String,subForm:{type:Boolean,default:!0},inFor:Boolean},emits:["update:api","update:modelValue","mounted","submit","change","emit-event","control","remove-rule","remove-field","sync","reload","repeat-field","update","validate-field-fail","validate-fail","created"],render:function(){return this.fc.render()},setup:function(n){var i=t.getCurrentInstance();t.provide("parentFC",i);var o=t.inject("parentFC",null),a=t.toRefs(n),u=a.rule,l=a.modelValue,c=a.subForm,s=a.inFor,d=t.reactive({ctxInject:{},destroyed:!1,isShow:!0,unique:1,renderRule:f(u.value||[]),updateValue:JSON.stringify(l.value||{})}),p=new e(i),m=p.api(),v=s.value,g=function(){var e=ue(i,o);if(e)if(v){var t=h(e.getSubForm()),n=t.indexOf(m);n>-1&&t.splice(n,1)}else e.subForm()},y=null;t.onBeforeMount((function(){var e="",t=n.option&&n.option.globalClass||{};Object.keys(t).forEach((function(n){var r="";t[n].style&&Object.keys(t[n].style).forEach((function(e){r+=ae(e)+":"+t[n].style[e]+";"})),t[n].content&&(r+=t[n].content+";"),r&&(e+=".".concat(n,"{").concat(r,"}"))})),n.option&&n.option.style&&(e+=n.option.style),e&&((y=document.createElement("style")).type="text/css",y.innerHTML=e,document.head.appendChild(y))}));var b=oe((function(){p.bus.$emit("$loadData.$topForm")}),100),w=oe((function(){p.bus.$emit("$loadData.$form")}),100),x=function(e){p.bus.$emit("change-$form."+e)};return t.onMounted((function(){o&&(m.top.bus.$on("$loadData.$form",b),m.top.bus.$on("change",x)),p.mounted()})),t.onBeforeUnmount((function(){o&&(m.top.bus.$off("$loadData.$form",b),m.top.bus.$off("change",x)),y&&document.head.removeChild(y),g(),d.destroyed=!0,p.unmount()})),t.onUpdated((function(){p.updated()})),t.watch(c,(function(e){e?function(){if(o){var e,t=ue(i,o);t&&(v?(e=h(t.getSubForm())).push(m):e=m,t.subForm(e))}}():g()}),{immediate:!0}),t.watch((function(){return f(u.value)}),(function(e){p.$handle.isBreakWatch()||e.length===d.renderRule.length&&e.every((function(e){return d.renderRule.indexOf(e)>-1}))||(p.$handle.reloadRule(u.value),i.setupState.renderRule())})),t.watch((function(){return n.option}),(function(){p.initOptions(),m.refresh()}),{deep:!0}),t.watch((function(){return[n.disabled,n.preview]}),(function(){m.refresh()})),t.watch(l,(function(e){JSON.stringify(e||{})!==d.updateValue&&(m.config.forceCoverValue?m.coverValue(e||{}):m.setValue(e||{}))}),{deep:!0}),r(r({fc:t.markRaw(p),parent:o?t.markRaw(o):o,fapi:t.markRaw(m)},t.toRefs(d)),{},{refresh:function(){++d.unique},renderRule:function(){d.renderRule=f(u.value||[])},updateValue:function(e){if(!d.destroyed){var n=JSON.stringify(e);d.updateValue!==n&&(d.updateValue=n,i.emit("update:modelValue",e),t.nextTick((function(){w(),o||b()})))}}})},created:function(){var e=t.getCurrentInstance();e.emit("update:api",e.setupState.fapi),e.setupState.fc.init()}})}var ce=["props"],se=["class","style","directives"],fe=["on"],de=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=[].concat(ce,f(o.normal||[])),u=[].concat(se,f(o.array||[])),l=[].concat(fe,f(o.functional||[])),c=o.props||[];return t.reduce((function(t,n){for(var o in n)if(t[o])if(c.indexOf(o)>-1)t[o]=e([n[o]],t[o]);else if(a.indexOf(o)>-1)t[o]=r(r({},t[o]),n[o]);else if(u.indexOf(o)>-1){var s=t[o]instanceof Array?t[o]:[t[o]],d=n[o]instanceof Array?n[o]:[n[o]];t[o]=[].concat(f(s),f(d))}else if(l.indexOf(o)>-1)for(var p in n[o])if(t[o][p]){var h=t[o][p]instanceof Array?t[o][p]:[t[o][p]],m=n[o][p]instanceof Array?n[o][p]:[n[o][p]];t[o][p]=[].concat(f(h),f(m))}else t[o][p]=n[o][p];else if("hook"===o)for(var v in n[o])t[o][v]?t[o][v]=pe(t[o][v],n[o][v]):t[o][v]=n[o][v];else t[o]=n[o];else a.indexOf(o)>-1||l.indexOf(o)>-1||c.indexOf(o)>-1?t[o]=r({},n[o]):u.indexOf(o)>-1?t[o]=n[o]instanceof Array?f(n[o]):"object"===i(n[o])?r({},n[o]):n[o]:t[o]=n[o];return t}),n)},pe=function(e,t){return function(){e&&e.apply(this,arguments),t&&t.apply(this,arguments)}},he=["type","slot","emitPrefix","value","name","native","hidden","display","inject","options","emit","link","prefix","suffix","update","sync","optionsTo","key","slotUpdate","computed","preview","component","cache","modelEmit"],me=["validate","children","control"],ve=["effect"];function ge(){return[].concat(he,f(ce),f(se),f(fe),me,ve)}function ye(e,t,n){return"[form-create ".concat(e,"]: ").concat(t)+(n?"\n\nrule: "+JSON.stringify(n.getRule?n.getRule():n):"")}function be(e,t){console.error(ye("err",e,t))}var we="[[FORM-CREATE-PREFIX-",xe="-FORM-CREATE-SUFFIX]]",_e="$FN:",$e="$FNX:",ke="$GLOBAL:",Ce="function";function Ve(e,t){return JSON.stringify(G(Array.isArray(e)?[]:{},e,!0),(function(e,t){if(!t||!0!==t._isVue){if(i(t)!==Ce)return t;if(t.__json)return t.__json;if(t.__origin&&(t=t.__origin),!t.__emit)return we+t+xe}}),t)}function Oe(e){return new Function("return "+e)()}function Se(e,t){if(e&&N.String(e)&&e.length>4){var n=e.trim(),r=!1;try{if(n.indexOf(xe)>0&&0===n.indexOf(we))n=n.replace(xe,"").replace(we,""),r=!0;else if(0===n.indexOf(_e))n=n.replace(_e,""),r=!0;else{if(0===n.indexOf(ke)){var i=n.replace(ke,"");return n=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t[0].api.getGlobalEvent(i);if(r)return r.call.apply(r,[this].concat(t))},n.__json=e,n.__inject=!0,n}if(0===n.indexOf($e))return(n=Oe("function($inject){"+n.replace($e,"")+"}")).__json=e,n.__inject=!0,n;t||0!==n.indexOf(Ce)||n===Ce||(r=!0)}if(!r)return e;var o=Oe(-1===n.indexOf(Ce)&&0!==n.indexOf("(")?"function "+n:n);return o.__json=e,o}catch(e){return void be("解析失败:".concat(n,"\n\nerr: ").concat(e))}}return e}function Ee(e,t){return JSON.parse(e,(function(e,n){return N.Undef(n)||!n.indexOf?n:Se(n,t)}))}function Re(e,t){return{value:e,enumerable:!1,configurable:!1,writable:!!t}}function je(e,t){return Fe([e],t||!1)[0]}function Fe(e,t){return G([],f(e),t||!1)}function De(e,t){return de(Array.isArray(t)?t:[t],e,{array:me,normal:ve}),e}function Ae(e){var t=N.Function(e.getRule)?e.getRule():e;return t.type||(t.type="input"),t}function Pe(e,t){Object.defineProperties(e,Object.keys(t).reduce((function(e,n){return e[n]={get:function(){return t[n]()}},e}),{}))}function Ne(e){return e.__fc__||(e.__origin__?e.__origin__.__fc__:null)}function Ie(e,t){try{t=e()}catch(e){!function(e){be(e.toString()),console.error(e)}(e)}return t}function Be(){var e={},n=function(e){return e||"default"};return{setSlot:function(t,r){t=n(t),!r||Array.isArray(r)&&r.length||(e[t]||(e[t]=[]),e[t].push(r))},getSlot:function(t,r){t=n(t);var i=[];return(e[t]||[]).forEach((function(e){if(Array.isArray(e))i.push.apply(i,f(e));else if(N.Function(e)){var t=e.apply(void 0,f(r||[]));Array.isArray(t)?i.push.apply(i,f(t)):i.push(t)}else N.Undef(e)||i.push(e)})),i},getSlots:function(){var t=this,n={};return Object.keys(e).forEach((function(e){n[e]=function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return t.getSlot(e,r)}})),n},slotLen:function(t){return t=n(t),e[t]?e[t].length:0},mergeBag:function(e){var n=this;if(!e)return this;var r=N.Function(e.getSlots)?e.getSlots():e;return Array.isArray(e)||t.isVNode(e)?this.setSlot(void 0,(function(){return e})):Object.keys(r).forEach((function(e){n.setSlot(e,r[e])})),this}}}function Te(e){var t=r({},e.props||{});return Object.keys(e.on||{}).forEach((function(n){var r,i="on".concat((r=n).replace(r[0],r[0].toLocaleUpperCase()));Array.isArray(t[i])?t[i]=[].concat(f(t[i]),[e.on[n]]):t[i]?t[i]=[t[i],e.on[n]]:t[i]=e.on[n]})),t.key=e.key,t.ref=e.ref,t.class=e.class,t.style=e.style,t.slot&&delete t.slot,t}function Me(e,t){return Object.setPrototypeOf(e,t),e}var Le=function(e,t){return"string"==typeof e?String(t):"number"==typeof e?Number(t):t},Ue={"==":function(e,t){return JSON.stringify(e)===JSON.stringify(Le(e,t))},"!=":function(e,t){return!Ue["=="](e,t)},">":function(e,t){return e>t},">=":function(e,t){return e>=t},"<":function(e,t){return e<t},"<=":function(e,t){return e<=t},on:function(e,t){return e&&e.indexOf&&e.indexOf(Le(e[0],t))>-1},notOn:function(e,t){return!Ue.on(e,t)},in:function(e,t){return t&&t.indexOf&&t.indexOf(e)>-1},notIn:function(e,t){return!Ue.in(e,t)},between:function(e,t){return e>t[0]&&e<t[1]},notBetween:function(e,t){return e<t[0]||e>t[1]},empty:function(e){return N.empty(e)},notEmpty:function(e){return!N.empty(e)},pattern:function(e,t){return new RegExp(t,"g").test(e)}};function He(e,t){return(Array.isArray(t)?t:(t||"").split(".")).forEach((function(t){null!=e&&(e=e[t])})),e}function ze(e,t){return function(n,r,i){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=new Ye(e,n,r,i,o);return t&&(N.Function(t)?t(a):a.props(t)),a}}function Ye(e,t,n,r,i){this._data=X({props:{},on:{},options:[],children:[],hidden:!1,display:!0,value:void 0},{type:e,title:t,field:n,value:r,props:i||{}}),this.event=this.on}function qe(e){e.forEach((function(e){Ye.prototype[e]=function(t){return De(this._data,a({},e,arguments.length<2?t:a({},t,arguments[1]))),this}}))}X(Ye.prototype,{getRule:function(){return this._data},setProp:function(e,t){return q(this._data,e,t),this},modelField:function(e){return this._data.modelField=e,this},_clone:function(){var e=new this.constructor;return e._data=je(this._data),e}}),qe(ge());var Je=ze("");function Ge(e,t,n){var r=Je("",t);return r._data.type=e,r._data.title=n,r}function We(){return{create:Ge,factory:ze}}function Ke(e){var t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch(e){return t}}function Xe(e){if("undefined"!=typeof XMLHttpRequest){var t,n=new XMLHttpRequest,r=e.action;n.onerror=function(t){e.onError(t)},n.onload=function(){if(n.status<200||n.status>=300)return e.onError(function(e,t,n){var r="fail to ".concat(e," ").concat(n.status,"'"),i=new Error(r);return i.status=n.status,i.url=e,i}(r,0,n),Ke(n));e.onSuccess(Ke(n))},n.open(e.method||"get",r,!0),e.data&&("json"!==(e.dataType||"").toLowerCase()?(t=new FormData,Object.keys(e.data).map((function(n){t.append(n,e.data[n])}))):(t=JSON.stringify(e.data),n.setRequestHeader("content-type","application/json"))),e.withCredentials&&"withCredentials"in n&&(n.withCredentials=!0);var i=e.headers||{};Object.keys(i).forEach((function(e){null!=i[e]&&n.setRequestHeader(e,i[e])})),n.send(t)}}function Qe(e,t){return new Promise((function(n,i){(t||Xe)(r(r({},e),{},{onSuccess:function(t){var r=function(e){return e},i=Se(e.parse);N.Function(i)?r=i:i&&N.String(i)&&(r=function(e){return He(e,i)}),n(r(t))},onError:function(e){i(e)}}))}))}function Ze(e){return W(e)}function et(e){return tt(e.replace(/(-[a-z])/g,(function(e){return e.replace("-","").toLocaleUpperCase()})))}function tt(e){return e.replace(e[0],e[0].toLowerCase())}function nt(e){return null==e?"":"object"===i(e)?JSON.stringify(e,null,2):String(e)}var rt=0;function it(){var e=370+ ++rt;return"F"+Math.random().toString(36).substr(3,3)+Number("".concat(Date.now())).toString(36)+e.toString(36)+"c"}function ot(e,t,n){var r,o=e;return(t||"").split(".").forEach((function(e){r&&(o[r]&&"object"==i(o[r])||(o[r]={}),o=o[r]),r=e})),o[r]=n,o}var at=1;function ut(e){X(this,{$handle:e,fc:e.fc,vm:e.vm,$manager:e.$manager,vNode:new e.fc.CreateNode(e.vm),id:at++}),Pe(this,{options:function(){return e.options},sort:function(){return e.sort}}),this.initCache(),this.initRender()}!function(e){X(e.prototype,{initCache:function(){this.clearCacheAll()},clearCache:function(e){e.rule.cache||(this.cache[e.id]?((!0===this.cache[e.id].use||this.cache[e.id].parent)&&this.$handle.refresh(),this.cache[e.id].parent&&this.clearCache(this.cache[e.id].parent),this.cache[e.id]=null):e.parent&&this.clearCache(e.parent))},clearCacheAll:function(){this.cache={}},setCache:function(e,t,n){this.cache[e.id]={vnode:t,use:!1,parent:n,slot:e.rule.slot}},getCache:function(e){var t=this.cache[e.id];if(t)return t.use=!0,t.vnode}})}(ut),function(e){X(e.prototype,{initRender:function(){this.cacheConfig={}},getTypeSlot:function(e){return function t(n){if(n){var r=void 0;return e.rule.field&&(r=n.slots["field-"+ae(e.rule.field)]||n.slots["field-"+e.rule.field]),r||(r=n.slots["type-"+ae(e.type)]||n.slots["type-"+e.type]),r||t(n.setupState.parent)}}(this.vm)},render:function(){var e=this;if(this.vm.setupState.isShow){this.$manager.beforeRender();var t=Be();return this.sort.forEach((function(n){e.renderSlot(t,e.$handle.ctxs[n])})),this.$manager.render(t)}},renderSlot:function(e,t,n){if(this.isFragment(t)){t.initProp(),this.mergeGlobal(t),t.initNone();var r=this.renderChildren(t.loadChildrenPending(),t),i=r.default;i&&e.setSlot(t.rule.slot,(function(){return i()})),delete r.default,e.mergeBag(r)}else e.setSlot(t.rule.slot,this.renderCtx(t,n))},mergeGlobal:function(e){var n=this;this.$handle.options.global&&(this.cacheConfig[e.trueType]||(this.cacheConfig[e.trueType]=t.computed((function(){var t=n.$handle.options.global;return De({},[t["*"],t[e.originType]||t[e.type]||t[e.type]||{}])}))),e.prop=De({},[this.cacheConfig[e.trueType].value,e.prop]))},setOptions:function(e){var t=e.loadPending({key:"options",origin:e.prop.options,def:[]});e.prop.options=t,e.prop.optionsTo&&t&&ot(e.prop,e.prop.optionsTo,t)},deepSet:function(e){var t=e.rule.deep;t&&Object.keys(t).sort((function(e,t){return e.length<t.length?-1:1})).forEach((function(n){ot(e.prop,n,t[n])}))},parseSide:function(e,t){return N.Object(e)?De({props:{formCreateInject:t.prop.props.formCreateInject}},e):e},renderSides:function(e,t,n){var r=t[n?"rule":"prop"];return[this.renderRule(this.parseSide(r.prefix,t)),e,this.renderRule(this.parseSide(r.suffix,t))]},renderId:function(e,t){var n=this,r=this.$handle["field"===t?"fieldCtx":"nameCtx"][e];return r?r.map((function(e){return n.renderCtx(e,e.parent)})):void 0},renderCtx:function(e,t){var n=this;try{if("hidden"===e.type)return;var r=e.rule;if(!this.cache[e.id]||this.cache[e.id].slot!==r.slot){var i;e.initProp(),this.mergeGlobal(e),e.initNone(),this.$manager.tidyRule(e),this.deepSet(e),this.setOptions(e),this.ctxProp(e);var o=e.prop;o.preview=!!(null!=o.preview?o.preview:void 0!==this.vm.props.preview?this.vm.props.preview:this.options.preview),o.props.formCreateInject=this.injectProp(e);var a=!1!==o.cache,u=o.preview;if(o.hidden)return void this.setCache(e,void 0,t);i=function(){for(var i=arguments.length,l=new Array(i),c=0;c<i;c++)l[c]=arguments[c];var s={rule:r,prop:o,preview:u,api:n.$handle.api,model:o.model||{},slotValue:l};l.length&&r.slotUpdate&&Ie((function(){return r.slotUpdate(s)}));var f={},d=e.loadChildrenPending();e.parser.renderChildren?f=e.parser.renderChildren(d,e):!1!==e.parser.loadChildren&&(f=n.renderChildren(d,e));var p,h=n.getTypeSlot(e);return h?(s.children=f,p=h(s)):p=u?e.parser.preview(Q(f),e):e.parser.render(Q(f),e),p=n.renderSides(p,e),!e.input&&N.Undef(o.native)||!0===o.native||(p=n.$manager.makeWrap(e,p)),e.none&&(p=Array.isArray(p)?p.map((function(e){return e&&e.__v_isVNode?n.none(e):e})):n.none(p)),a&&n.setCache(e,(function(){return n.stable(p)}),t),p},this.setCache(e,i,t)}return function(){var t=n.getCache(e);if(t)return t.apply(void 0,arguments);if(!n.cache[e.id]){var r=n.renderCtx(e,e.parent);return r?r():void 0}}}catch(e){return void console.error(e)}},none:function(e){if(e)return Array.isArray(e.props.class)?e.props.class.push("fc-none"):e.props.class=e.props.class?[e.props.class,"fc-none"]:"fc-none",e},stable:function(e){var t=this;return(Array.isArray(e)?e:[e]).forEach((function(e){e&&e.__v_isVNode&&e.children&&"object"===i(e.children)&&(e.children.$stable=!0,t.stable(e.children))})),e},getModelField:function(e){return e.prop.modelField||e.parser.modelField||this.fc.modelFields[this.vNode.aliasMap[e.type]]||this.fc.modelFields[e.type]||this.fc.modelFields[e.originType]||"modelValue"},isFragment:function(e){return"fragment"===e.type||"template"===e.type},injectProp:function(e){var t=this,n=this.vm.setupState;n.ctxInject[e.id]||(n.ctxInject[e.id]={api:this.$handle.api,form:this.fc.create,subForm:function(n){t.$handle.addSubForm(e,n)},getSubForm:function(){return t.$handle.subForm[e.id]},options:[],children:[],preview:!1,id:e.id,field:e.field,rule:e.rule,input:e.input});var r=n.ctxInject[e.id];return X(r,{preview:e.prop.preview,options:e.prop.options,children:e.loadChildrenPending()}),r},ctxProp:function(e){var t=this,n=e.ref,i=e.key,o=e.rule;this.$manager.mergeProp(e),e.parser.mergeProp(e);var u=[{ref:n,key:o.key||"".concat(i,"fc"),slot:void 0,on:{vnodeMounted:function(n){n.el.__rule__=e.rule,t.onMounted(e,n.el)},"fc.updateValue":function(n){t.$handle.onUpdateValue(e,n)},"fc.el":function(t){e.exportEl=t,t&&((t.$el||t).__rule__=e.rule)}}}];if(e.input){!0===this.vm.props.disabled&&(e.prop.props.disabled=!0);var l=this.getModelField(e),c={callback:function(n){t.onInput(e,n)},value:this.$handle.getFormData(e)};u.push({on:r(a({},"update:".concat(l),c.callback),e.prop.modelEmit?a({},e.prop.modelEmit,(function(){return t.onEmitInput(e)})):{}),props:a({},l,c.value)}),e.prop.model=c}return de(u,e.prop),e.prop},onMounted:function(e,t){e.el=this.vm.refs[e.ref]||t,e.parser.mounted(e),this.$handle.effect(e,"mounted")},onInput:function(e,t){e.prop.modelEmit?this.$handle.onBaseInput(e,t):this.$handle.onInput(e,t)},onEmitInput:function(e){this.$handle.setValue(e,e.parser.toValue(e.modelValue,e),e.modelValue)},renderChildren:function(e,n){var r=this;if(!N.trueArray(e))return{};var i=Be();return e.map((function(o){if(o)return N.String(o)?i.setSlot(null,o):o.__fc__?r.renderSlot(i,o.__fc__,n):void(o.type&&t.nextTick((function(){r.$handle.loadChildren(e,n),r.$handle.refresh()})))})),i.getSlots()},defaultRender:function(e,t){var n=e.prop;return n.component?"string"==typeof n.component?this.vNode.make(n.component,n,t):this.vNode.makeComponent(n.component,n,t):this.vNode[e.type]?this.vNode[e.type](n,t):this.vNode[e.originType]?this.vNode[e.originType](n,t):this.vNode.make(tt(n.type),n,t)},renderRule:function(e,t,n){var i=this;if(e){if(N.String(e))return e;var o;if(n)o=e.type;else if(o=e.is,e.type){o=et(e.type);var a=this.vNode.aliasMap[o];a&&(o=et(a))}if(o){var u=Be();N.trueArray(e.children)&&e.children.forEach((function(e){e&&u.setSlot(null==e?void 0:e.slot,(function(){return i.renderRule(e)}))}));var l=r({},e);return delete l.type,delete l.is,this.vNode.make(o,l,u.mergeBag(t).getSlots())}}}})}(ut);var lt=["hook:updated","hook:mounted"];function ct(e,n,i){var o,a=it(),u=!!n.field;X(this,{id:a,ref:a,wrapRef:a+"fi",rule:n,origin:n.__origin__||n,name:n.name,pending:{},none:!1,watch:[],linkOn:[],root:[],ctrlRule:[],children:[],parent:null,group:n.subRule?this:null,cacheConfig:null,prop:r({},n),computed:{},payload:{},refRule:{},input:u,el:void 0,exportEl:void 0,defaultValue:u?W(i):void 0,field:n.field||void 0}),this.updateKey(),o=this,Object.defineProperties(o.origin,{__fc__:Re(t.markRaw(o),!0)}),this.update(e,!0)}function st(e,t){for(var n=0;n<e.ctrlRule.length;n++){var r=e.ctrlRule[n];if(r.children===t)return r}}function ft(e){return!!e.rule.__ctrl}function dt(e,t){return"function"==typeof t?""+t:t}X(ct.prototype,{getParentGroup:function(){for(var e=this.parent;e;){if(e.group)return e;e=e.parent}},loadChildrenPending:function(){var e=this,t=this.rule.children||[];return Array.isArray(t)?t:this.loadPending({key:"children",origin:t,def:[],onLoad:function(t){e.$handle&&e.$handle.loadChildren(t,e)},onUpdate:function(t,n){e.$handle&&(t===n?e.$handle.loadChildren(t,e):e.$handle.updateChildren(e,t,n))},onReload:function(t){e.$handle?e.$handle.updateChildren(e,[],t):delete e.pending.children}})},loadPending:function(e){var t=this,n=e.key,r=e.origin,i=e.def,o=e.onLoad,a=e.onReload,u=e.onUpdate;if(this.pending[n]&&this.pending[n].origin===r)return this.getPending(n,i);delete this.pending[n];var l=r;if(N.Function(r)){var c=Ie((function(){return r({rule:t.rule,api:t.$api,update:function(e){var o=e||i,a=t.getPending(n,i);t.setPending(n,r,o),u&&u(o,a)},reload:function(){var e=t.getPending(n,i);delete t.pending[n],a&&a(e),t.$api&&t.$api.sync(t.rule)}})}));c&&N.Function(c.then)?(c.then((function(e){var a=e||i;t.setPending(n,r,a),o&&o(a),t.$api&&t.$api.sync(t.rule)})).catch((function(e){console.error(e)})),l=i,this.setPending(n,r,l)):(l=c||i,this.setPending(n,r,l),o&&o(l))}return l},getPending:function(e,t){return this.pending[e]&&this.pending[e].value||t},setPending:function(e,n,r){this.pending[e]={origin:n,value:t.reactive(r)}},effectData:function(e){return this.payload[e]||(this.payload[e]={}),this.payload[e]},clearEffectData:function(e){void 0===e?this.payload={}:delete this.payload[e]},updateKey:function(e){this.key=it(),e&&this.parent&&this.parent.updateKey(e)},updateType:function(){this.originType=this.rule.type,this.type=et(this.rule.type),this.trueType=this.$handle.getType(this.originType)},setParser:function(e){this.parser=e,e.init(this)},initProp:function(){var e=this,t=r({},this.rule);delete t.children,this.prop=De({},[t].concat(f(Object.keys(this.payload).map((function(t){return e.payload[t]}))),[this.computed]))},initNone:function(){this.none=!(N.Undef(this.prop.display)||this.prop.display)},injectValidate:function(){var e=this;return h(this.prop.validate).map((function(t){if(N.Function(t.validator)){var n=r({},t),i=e;return n.validator=function(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=t.validator).call.apply(e,[{that:this,id:i.id,field:i.field,rule:i.rule,api:i.$handle.api}].concat(r))},n}return t}))},check:function(e){return this.vm===e.vm},unwatch:function(){this.watch.forEach((function(e){return e()})),this.watch=[],this.refRule={}},unlink:function(){this.linkOn.forEach((function(e){return e()})),this.linkOn=[]},link:function(){this.unlink(),this.$handle.appendLink(this)},watchTo:function(){this.$handle.watchCtx(this)},delete:function(){var e=void 0;this.unwatch(),this.unlink(),this.rmCtrl(),this.parent&&this.parent.children.splice(this.parent.children.indexOf(this)>>>0,1),X(this,{deleted:!0,prop:r({},this.rule),computed:{},el:e,$handle:e,$api:e,vm:e,vNode:e,parent:null,children:[],cacheConfig:null,none:!1})},rmCtrl:function(){this.ctrlRule.forEach((function(e){return e.__fc__&&e.__fc__.rm()})),this.ctrlRule=[]},rm:function(){var e=this,t=function(){var t=e.root.indexOf(e.origin);t>-1&&(e.root.splice(t,1),e.$handle&&e.$handle.refresh())};this.deleted?t():this.$handle.noWatch((function(){e.$handle.deferSyncValue((function(){e.rmCtrl(),t(),e.$handle.rmCtx(e),X(e,{root:[]})}),e.input)}))},update:function(e,t){X(this,{deleted:!1,$handle:e,$render:e.$render,$api:e.api,vm:e.vm,vNode:e.$render.vNode,updated:!1,cacheValue:this.rule.value}),!t&&this.unwatch(),this.watchTo(),this.link(),this.updateType()}});var pt={init:function(e){},toFormValue:function(e,t){return e},toValue:function(e,t){return e},mounted:function(e){},render:function(e,t){return t.$handle.fc.renderDriver&&t.$handle.fc.renderDriver.defaultRender?t.$handle.fc.renderDriver.defaultRender(t,e):t.$render.defaultRender(t,e)},preview:function(e,t){return t.$handle.fc.renderDriver&&t.$handle.fc.renderDriver.defaultPreview?t.$handle.fc.renderDriver.defaultPreview(t,e):this.render(e,t)},mergeProp:function(e){}},ht=["field","value","vm","template","name","config","control","inject","sync","payload","optionsTo","update","slotUpdate","computed","component","cache"];function mt(e){var n=this;Pe(this,{options:function(){return e.options.value||{}},bus:function(){return e.bus}}),X(this,{fc:e,vm:e.vm,watching:!1,loading:!1,reloading:!1,noWatchFn:null,deferSyncFn:null,isMounted:!1,formData:t.reactive({}),subRuleData:t.reactive({}),subForm:{},form:t.reactive({}),appendData:{},providers:{},cycleLoad:null,loadedId:1,nextTick:null,changeStatus:!1,pageEnd:!0,nextReload:function(){n.lifecycle("reload")}}),this.initData(e.rules),this.$manager=new e.manager(this),this.$render=new ut(this),this.api=e.extendApiFn.reduce((function(e,t){return X(e,Ie((function(){return t(e,n)}),{})),e}),function(e){function t(t){return N.Undef(t)?t=e.fields():Array.isArray(t)||(t=[t]),t}function n(n,r,i){t(n).forEach((function(t){e.getCtxs(t).forEach((function(t){q(t.rule,r,i),e.$render.clearCache(t)}))}))}function o(){var t=e.subForm;return Object.keys(t).reduce((function(e,n){var r=t[n];return r?(Array.isArray(r)?e.push.apply(e,f(r)):e.push(r),e):e}),[])}var u={get config(){return e.options},set config(t){e.fc.options.value=t},get options(){return e.options},set options(t){e.fc.options.value=t},get form(){return e.form},get rule(){return e.rules},get parent(){return e.vm.setupState.parent&&e.vm.setupState.parent.setupState.fapi},get top(){return u.parent?u.parent.top:u},get children(){return o()},formData:function(n){return t(n).reduce((function(t,n){var r=e.getFieldCtx(n);return r?(t[r.field]=Ze(r.rule.value),t):t}),!1!==e.options.appendValue?Ze(e.appendData):{})},getValue:function(t){var n=e.getFieldCtx(t);if(n)return Ze(n.rule.value)},coverValue:function(t){var n=r({},t||{});e.deferSyncValue((function(){u.fields().forEach((function(r){var i=e.fieldCtx[r];if(i){var o=I(t,r);i.forEach((function(e){e.rule.value=o?t[r]:void 0})),delete n[r]}})),X(e.appendData,n)}))},setValue:function(t){var n=t;arguments.length>=2&&(n=a({},t,arguments[1])),e.deferSyncValue((function(){Object.keys(n).forEach((function(t){var r=e.fieldCtx[t];if(!r)return e.appendData[t]=n[t];r.forEach((function(e){e.rule.value=n[t]}))}))}))},removeField:function(t){var n=e.getCtx(t);return e.deferSyncValue((function(){e.getCtxs(t).forEach((function(e){e.rm()}))}),!0),n?n.origin:void 0},removeRule:function(e){var t=e&&Ne(e);if(t)return t.rm(),t.origin},fields:function(){return e.fields()},append:function(t,n,r){var i,o=e.sort.length-1,a=e.getCtx(n);if(a)if(r){if(i=a.getPending("children",a.rule.children),!Array.isArray(i))return;o=a.rule.children.length-1}else o=a.root.indexOf(a.origin),i=a.root;else i=e.rules;i.splice(o+1,0,t)},prepend:function(t,n,r){var i,o=0,a=e.getCtx(n);if(a)if(r){if(i=a.getPending("children",a.rule.children),!Array.isArray(i))return}else o=a.root.indexOf(a.origin),i=a.root;else i=e.rules;i.splice(o,0,t)},hidden:function(t,r){n(r,"hidden",!!t),e.refresh()},hiddenStatus:function(t){var n=e.getCtx(t);if(n)return!!n.rule.hidden},display:function(t,r){n(r,"display",!!t),e.refresh()},displayStatus:function(t){var n=e.getCtx(t);if(n)return!!n.rule.display},disabled:function(n,r){t(r).forEach((function(t){e.getCtxs(t).forEach((function(e){q(e.rule.props,"disabled",!!n)}))})),e.refresh()},all:function(t){return Object.keys(e.ctxs).map((function(n){var r=e.ctxs[n];return t?r.origin:r.rule}))},model:function(t){return e.fields().reduce((function(n,r){var i=e.fieldCtx[r][0];return n[r]=t?i.origin:i.rule,n}),{})},component:function(t){return Object.keys(e.nameCtx).reduce((function(n,r){var i=e.nameCtx[r].map((function(e){return t?e.origin:e.rule}));return n[r]=1===i.length?i[0]:i,n}),{})},bind:function(){return u.form},reload:function(t){e.reloadRule(t)},updateOptions:function(t){e.fc.updateOptions(t),u.refresh()},onSubmit:function(e){u.updateOptions({onSubmit:e})},sync:function(t){if(Array.isArray(t))t.forEach((function(e){return u.sync(e)}));else{var n=N.Object(t)?Ne(t):e.getCtxs(t);n&&((n=Array.isArray(n)?n:[n]).forEach((function(t){if(!t.deleted){var n=e.subForm[t.id];n&&(Array.isArray(n)?n.forEach((function(e){e.refresh()})):n&&n.refresh()),e.$render.clearCache(t)}})),e.refresh())}},refresh:function(){o().forEach((function(e){e.refresh()})),e.$render.clearCacheAll(),e.refresh()},refreshOptions:function(){e.$manager.updateOptions(e.options),u.refresh()},hideForm:function(t){e.vm.setupState.isShow=!t},changeStatus:function(){return e.changeStatus},clearChangeStatus:function(){e.changeStatus=!1},updateRule:function(t,n){e.getCtxs(t).forEach((function(e){X(e.rule,n)}))},updateRules:function(e){Object.keys(e).forEach((function(t){u.updateRule(t,e[t])}))},mergeRule:function(t,n){e.getCtxs(t).forEach((function(e){De(e.rule,n)}))},mergeRules:function(e){Object.keys(e).forEach((function(t){u.mergeRule(t,e[t])}))},getRule:function(t,n){var r=e.getCtx(t);if(r)return n?r.origin:r.rule},getRenderRule:function(t){var n=e.getCtx(t);if(n)return n.prop},getRefRule:function(t){var n=e.getCtxs(t);if(n){var r=n.map((function(e){return e.rule}));return 1===r.length?r[0]:r}},setEffect:function(t,n,r){var i=e.getCtx(t);i&&n&&("$"===n[0]&&(n=n.substr(1)),I(i.rule,"$"+n)&&q(i.rule,"$"+n,r),I(i.rule,"effect")||(i.rule.effect={}),q(i.rule.effect,n,r))},clearEffectData:function(t,n){var r=e.getCtx(t);r&&(n&&"$"===n[0]&&(n=n.substr(1)),r.clearEffectData(n),u.sync(t))},updateValidate:function(e,t,r){r?u.mergeRule(e,{validate:t}):n(e,"validate",t)},updateValidates:function(e,t){Object.keys(e).forEach((function(n){u.updateValidate(n,e[n],t)}))},refreshValidate:function(){u.refresh()},resetFields:function(n){t(n).forEach((function(t){e.getCtxs(t).forEach((function(t){e.$render.clearCache(t),t.rule.value=Ze(t.defaultValue)}))}))},method:function(e,t){var n=u.el(e);if(!n||!n[t])throw new Error(ye("err","".concat(t,"方法不存在")));return function(){return n[t].apply(n,arguments)}},exec:function(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];return Ie((function(){return u.method(e,t).apply(void 0,r)}))},toJson:function(e){return Ve(u.rule,e)},trigger:function(e,t){for(var n=u.el(e),r=arguments.length,i=new Array(r>2?r-2:0),o=2;o<r;o++)i[o-2]=arguments[o];n&&n.$emit.apply(n,[t].concat(i))},el:function(t){var n=e.getCtx(t);if(n)return n.el||e.vm.refs[n.ref]},closeModal:function(t){e.bus.$emit("fc:closeModal:"+t)},getSubForm:function(t){var n=e.getCtx(t);return n?e.subForm[n.id]:void 0},getChildrenRuleList:function(t){var n="object"===i(t),r=n?Ne(t):e.getCtx(t),o=r?r.rule:n?t:u.getRule(t);if(!o)return[];var a,l=[];return(a=r?r.loadChildrenPending():o.children)&&a.forEach((function(e){"object"===i(e)&&(e.field&&l.push(e),l.push.apply(l,f(u.getChildrenRuleList(e))))})),l},getParentSubRule:function(t){var n="object"===i(t)?Ne(t):e.getCtx(t);if(n){var r=n.getParentGroup();if(r)return r.rule}},getChildrenFormData:function(e){return u.getChildrenRuleList(e).reduce((function(e,t){return e[t.field]=Ze(t.value),e}),{})},setChildrenFormData:function(t,n,r){var i=u.getChildrenRuleList(t);e.deferSyncValue((function(){i.forEach((function(e){I(n,e.field)?e.value=n[e.field]:r&&(e.value=void 0)}))}))},getGlobalEvent:function(e){var t=u.options.globalEvent[e];if(t)return"object"===i(t)&&(t=t.handle),Se(t)},getGlobalData:function(t){return new Promise((function(n,r){var i=u.options.globalData[t];i||n(e.fc.loadData[t]),"fetch"===i.type?u.fetch(i).then((function(e){n(e)})).catch(r):n(i.data)}))},nextTick:function(t){e.bus.$once("next-tick",t),e.refresh()},nextRefresh:function(t){e.nextRefresh(),t&&Ie(t)},deferSyncValue:function(t,n){e.deferSyncValue(t,n)},emit:function(t){for(var n,r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];(n=e.vm).emit.apply(n,[t].concat(i))},bus:e.bus,fetch:function(t){return new Promise((function(n,r){t=W(t),t=e.loadFetchVar(t),e.beforeFetch(t).then((function(){return Qe(t,e.fc.create.fetch).then(n).catch(r)}))}))},watchFetch:function(t,n,r){return e.fc.watchLoadData((function(i,o){var a=W(t);a=e.loadFetchVar(a,i),e.beforeFetch(a).then((function(){return Qe(a,e.fc.create.fetch).then((function(e){n&&n(e,o)})).catch((function(e){r&&r(e)}))}))}))},getData:function(t,n){return e.fc.getLoadData(t,n)},setData:function(t,n){return e.fc.setData(t,n)},refreshData:function(t){return e.fc.refreshData(t)},helper:{tidyFields:t,props:n}};return["on","once","off"].forEach((function(t){u[t]=function(){var n;(n=e.bus)["$".concat(t)].apply(n,arguments)}})),u.changeValue=u.changeField=u.setValue,u}(this))}X(mt.prototype,{initData:function(e){X(this,{ctxs:{},fieldCtx:{},nameCtx:{},sort:[],rules:e})},init:function(){this.appendData=r(r(r({},this.options.formData||{}),this.fc.vm.props.modelValue||{}),this.appendData),this.useProvider(),this.usePage(),this.loadRule(),this.$manager.__init(),this.lifecycle("created")},isBreakWatch:function(){return this.loading||this.noWatchFn||this.reloading},beforeFetch:function(e){var t=this;return new Promise((function(n){var r=t.options.beforeFetch&&Ie((function(){return t.options.beforeFetch(e,{api:t.api})}));r&&N.Function(r.then)?r.then(n):n()}))}}),function(e){X(e.prototype,{parseInjectEvent:function(e,t){var n=e.inject||this.options.injectEvent;return this.parseEventLst(e,t,n)},parseEventLst:function(e,t,n,r){var i=this;return Object.keys(t).forEach((function(o){var a=i.parseEvent(e,t[o],n,r);a&&(t[o]=a)})),t},parseEvent:function(e,t,n,r){if(N.Function(t)&&(!1!==n&&!N.Undef(n)||t.__inject))return this.inject(e,t,n);if(!r&&Array.isArray(t)&&t[0]&&(N.String(t[0])||N.Function(t[0])))return this.parseEventLst(e,t,n,!0);if(N.String(t)){var i=Se(t);if(i&&t!==i)return i.__inject?this.parseEvent(e,i,n,!0):i}},parseEmit:function(e){var t=this,n={},r=e.rule,i=r.emitPrefix,o=r.field,a=r.name,u=r.inject,l=r.emit||[];return N.trueArray(l)&&l.forEach((function(e){if(e){var l,c=i||o||a;if(N.Object(e)&&(l=e.inject,c=(e=e.name).prefix||c),c){var s=ae("".concat(c,"-").concat(e)),f=function(){var e,n,r;t.vm.emitsOptions&&(t.vm.emitsOptions[s]=null);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];(e=t.vm).emit.apply(e,[s].concat(o)),(n=t.vm).emit.apply(n,["emit-event",s].concat(o)),(r=t.bus).$emit.apply(r,[s].concat(o))};if(f.__emit=!0,l||!1!==u){var d=l||u||t.options.injectEvent;n[e]=N.Undef(d)?f:t.inject(r,f,d)}else n[e]=f}}})),e.computed.on=n,n},getInjectData:function(e,t){var n=this.vm.props,r=n.option,i=n.rule;return{$f:this.api,api:this.api,rule:i,self:e.__origin__,option:r,inject:t}},inject:function(e,t,n){if(t.__origin){if(this.watching&&!this.loading)return t;t=t.__origin}var r=this,i=function(){for(var i=r.getInjectData(e,n),o=arguments.length,a=new Array(o),u=0;u<o;u++)a[u]=arguments[u];return i.args=[].concat(a),a.unshift(i),t.apply(this,a)};return i.__origin=t,i.__json=t.__json,i},loadStrVar:function(e,t){var n=this;if(e&&"string"==typeof e&&e.indexOf("{{")>-1&&e.indexOf("}}")>-1){var r,i=e,o=function(e){for(var t,n=/{{\s*(.*?)\s*}}/g,r={};null!==(t=n.exec(e));)t[1]&&(r[t[1]]=!0);return Object.keys(r)}(e);if(o.forEach((function(i){var o=i.split("||"),a=o[0].trim();if(a){var u=(o[1]||"").trim(),l=t?t(a,u):n.fc.getLoadData(a,u);r=l,e=e.replaceAll("{{".concat(i,"}}"),null==l?"":l)}})),1===o.length&&i==="{{".concat(o[0],"}}"))return r}return e},loadFetchVar:function(e,t){var n=this,r=function(e){return n.loadStrVar(e,t)};if(e.action=r(e.action),e.headers){var i={};Object.keys(e.headers).forEach((function(t){i[r(t)]=r(e.headers[t])})),e.headers=i}if(e.data){var o={};Object.keys(e.data).forEach((function(t){o[r(t)]=r(e.data[t])})),e.data=o}return e}})}(mt),function(e){X(e.prototype,{usePage:function(){var e=this,t=this.options.page;if(t){var n,r=25,i=(n=this.rules).length<31?31:Math.ceil(n.length/3);N.Object(t)&&(t.first&&(r=parseInt(t.first,10)||r),t.limit&&(i=parseInt(t.limit,10)||i)),X(this,{first:r,limit:i,pageEnd:this.rules.length<=r}),this.bus.$on("page-end",(function(){return e.vm.emit("page-end",e.api)})),this.pageLoad()}},pageLoad:function(){var e=this;this.bus.$on(lt,(function t(){e.pageEnd?(e.bus.$off(lt,t),e.bus.$emit("page-end")):(e.first+=e.limit,e.pageEnd=e.rules.length<=e.first,e.loadRule(),e.refresh())}))}})}(mt),function(e){X(e.prototype,{clearNextTick:function(){this.nextTick&&clearTimeout(this.nextTick),this.nextTick=null},bindNextTick:function(e){var t=this;this.clearNextTick(),this.nextTick=setTimeout((function(){e(),t.nextTick=null}),10)},render:function(){return++this.loadedId,this.vm.setupState.unique>0?this.$render.render():(this.vm.setupState.unique=1,[])}})}(mt),function(e){X(e.prototype,{nextRefresh:function(e){var n=this,r=this.loadedId;t.nextTick((function(){r===n.loadedId&&(e?e():n.refresh())}))},parseRule:function(e){var t=this,n=Ae(e);return Object.defineProperties(n,{__origin__:Re(e,!0)}),function(e){var t={props:{},on:{},options:[],children:[],hidden:!1,display:!0,value:void 0};Object.keys(t).forEach((function(n){I(e,n)||(e[n]=t[n])}))}(n),this.appendValue(n),[n,n.prefix,n.suffix].forEach((function(e){e&&t.loadFn(e,n)})),this.loadCtrl(n),n.update&&(n.update=Se(n.update)),n},loadFn:function(e,t){var n=this;["on","props","deep"].forEach((function(r){e[r]&&n.parseInjectEvent(t,e[r])}))},loadCtrl:function(e){e.control&&e.control.forEach((function(e){e.handle&&(e.handle=Se(e.handle))}))},syncProp:function(e){var t=this,n=e.rule;N.trueArray(n.sync)&&de([{on:n.sync.reduce((function(e,r){return e["update:".concat(r)]=function(e){n.props[r]=e,t.vm.emit("sync",r,e,n,t.fapi)},e}),{})}],e.computed)},loadRule:function(){var e=this;this.cycleLoad=!1,this.loading=!0,this.pageEnd&&this.bus.$emit("load-start"),this.deferSyncValue((function(){if(e._loadRule(e.rules),e.loading=!1,e.cycleLoad&&e.pageEnd)return e.loadRule();e.syncForm(),e.pageEnd&&e.bus.$emit("load-end"),e.vm.setupState.renderRule()}))},loadChildren:function(e,t){if(this.cycleLoad=!1,this.loading=!0,this.bus.$emit("load-start"),this._loadRule(e,t),this.loading=!1,this.cycleLoad)return this.loadRule();this.syncForm(),this.bus.$emit("load-end"),this.$render.clearCache(t)},_loadRule:function(e,t){var n=this,r=function t(r){var i=e[r-1];if(!i||!i.__fc__)return r>0?t(r-1):-1;var o=n.sort.indexOf(i.__fc__.id);return o>-1?o:t(r-1)},i=function(e,t){N.trueArray(e)&&n._loadRule(e,t)},o=e.map((function(o,a){if((!t||N.Object(o))&&(n.pageEnd||t||!(a>=n.first))){if(o.__fc__&&o.__fc__.root===e&&n.ctxs[o.__fc__.id])return i(o.__fc__.loadChildrenPending(),o.__fc__),o.__fc__;var u,l=Ae(o),c=function(){return!(!l.field||!n.fieldCtx[l.field]||n.fieldCtx[l.field][0]===o.__fc__)};n.ruleEffect(l,"init",{repeat:c()}),c()&&n.vm.emit("repeat-field",o,n.api);var s=!1,f=!!o.__fc__,d=l.value;if(f){d=(u=o.__fc__).defaultValue;var p=!u.check(n);if(u.deleted){if(p){if(ft(u))return;u.update(n)}}else if(p){if(ft(u))return;e[a]=o=o._clone?o._clone():je(o),u=null,s=!0}}if(u)u.originType!==u.rule.type&&(u.updateType(),n.bindParser(u)),n.appendValue(u.rule),u.parent&&u.parent!==t&&n.rmSubRuleData(u);else{var h=n.parseRule(o);u=new ct(n,h,d),n.bindParser(u)}n.parseEmit(u),n.syncProp(u),u.parent=t||null,u.root=e,n.setCtx(u),!s&&!f&&n.effect(u,"load"),n.effect(u,"created");var m=u.loadChildrenPending();if(!1===u.parser.loadChildren||i(m,u),!t){var v=r(a);v>-1||!a?n.sort.splice(v+1,0,u.id):n.sort.push(u.id)}var g=u.rule;return u.updated||(u.updated=!0,N.Function(g.update)&&n.bus.$once("load-end",(function(){n.refreshUpdate(u,g.value,"init")})),n.effect(u,"loaded")),n.refreshControl(u)&&(n.cycleLoad=!0),u}})).filter((function(e){return!!e}));t&&(t.children=o)},refreshControl:function(e){return e.input&&e.rule.control&&this.useCtrl(e)},useCtrl:function(e){var n=this,i=function(e){var t=e.rule.control||[];return N.Object(t)?[t]:t}(e),o=[],a=this.api;if(!i.length)return!1;for(var u=function(t){var n=i[t],u=n.handle||function(e){return(Ue[n.condition||"=="]||Ue["=="])(e,n.value)};if(!N.trueArray(n.rule))return"continue";var l=r(r({},n),{},{valid:Ie((function(){return u(e.rule.value,a)})),ctrl:st(e,n.rule),isHidden:N.String(n.rule[0])});if(l.valid&&l.ctrl||!l.valid&&!l.ctrl&&!l.isHidden)return"continue";o.push(l)},l=0;l<i.length;l++)u(l);if(!o.length)return!1;var c=[],s=!1;return this.deferSyncValue((function(){o.reverse().forEach((function(t){var r=t.isHidden,i=t.valid,o=t.rule,u=t.prepend,l=t.append,f=t.child,d=t.ctrl,p=t.method;if(r)return i?e.ctrlRule.push({__ctrl:!0,children:o,valid:i}):e.ctrlRule.splice(e.ctrlRule.indexOf(d),1),void c[i?"push":"unshift"]((function(){"disabled"===p?n.api.disabled(!i,o):"display"===p?n.api.display(i,o):"required"===p?(o.forEach((function(e){n.api.setEffect(e,"required",i)})),i||n.api.clearValidateState(o)):n.api.hidden(!i,o)}));if(i){s=!0;var h={type:"fragment",native:!0,__ctrl:!0,children:o};e.ctrlRule.push(h),n.bus.$once("load-start",(function(){u?a.prepend(h,u,f):l||f?a.append(h,l||e.id,f):e.root.splice(e.root.indexOf(e.origin)+1,0,h)}))}else{e.ctrlRule.splice(e.ctrlRule.indexOf(d),1);var m=Ne(d);m&&m.rm()}}))})),c.length&&t.nextTick((function(){c.forEach((function(e){return e()}))})),this.vm.emit("control",e.origin,this.api),this.effect(e,"control"),s},reloadRule:function(e){return this._reloadRule(e)},_reloadRule:function(e){var t=this;e||(e=this.rules);var n=r({},this.ctxs);this.clearNextTick(),this.initData(e),this.fc.rules=e,this.deferSyncValue((function(){t.bus.$once("load-end",(function(){Object.keys(n).filter((function(e){return void 0===t.ctxs[e]})).forEach((function(e){return t.rmCtx(n[e])})),t.$render.clearCacheAll()})),t.reloading=!0,t.loadRule(),t.reloading=!1,t.refresh(),t.bus.$emit("reloading",t.api)})),this.bus.$off("next-tick",this.nextReload),this.bus.$once("next-tick",this.nextReload),this.bus.$emit("update",this.api)},refresh:function(){this.vm.setupState.refresh()}})}(mt),function(e){X(e.prototype,{setValue:function(e,t,n,r){e.deleted||(e.rule.value=t,this.changeStatus=!0,this.nextRefresh(),this.$render.clearCache(e),this.setFormData(e,n),this.syncValue(),this.valueChange(e,t),this.vm.emit("change",e.field,t,e.origin,this.api,r||!1),this.effect(e,"value"),this.emitEvent("change",e.field,t,{rule:e.origin,api:this.api,setFlag:r||!1}))},onInput:function(e,t){var n;e.input&&(this.isQuote(e,n=e.parser.toValue(t,e))||this.isChange(e,t))&&this.setValue(e,n,t)},onUpdateValue:function(e,t){var n=this;this.deferSyncValue((function(){var r=e.getParentGroup(),i=r?n.subRuleData[r.id]:null,o={};Object.keys(t||{}).forEach((function(e){i&&I(i,e)?o[e]=t[e]:I(n.api.form,e)?n.api.form[e]=t[e]:n.api.top!==n.api&&I(n.api.top.form,e)&&(n.api.top.form[e]=t[e])})),Object.keys(o).length&&n.api.setChildrenFormData(r.rule,o)}))},onBaseInput:function(e,t){this.setFormData(e,t),e.modelValue=t,this.nextRefresh(),this.$render.clearCache(e)},setFormData:function(e,t){e.modelValue=t;var n=e.getParentGroup();n&&(this.subRuleData[n.id]||(this.subRuleData[n.id]={}),this.subRuleData[n.id][e.field]=e.rule.value),q(this.formData,e.id,t)},rmSubRuleData:function(e){var t=e.getParentGroup();t&&this.subRuleData[t.id]&&delete this.subRuleData[t.id][e.field]},getFormData:function(e){return this.formData[e.id]},syncForm:function(){var e=this,n=t.reactive({}),r=this.fields();!1!==this.options.appendValue&&Object.keys(this.appendData).reduce((function(n,i){return-1===r.indexOf(i)&&(n[i]=t.toRef(e.appendData,i)),n}),n),r.reduce((function(n,r){var i=e.getCtx(r);return n[r]=t.toRef(i.rule,"value"),n}),n),this.form=n,this.syncValue()},appendValue:function(e){e.field&&I(this.appendData,e.field)&&(e.value=this.appendData[e.field],delete this.appendData[e.field])},addSubForm:function(e,t){this.subForm[e.id]=t},deferSyncValue:function(e,t){this.deferSyncFn||(this.deferSyncFn=e),this.deferSyncFn.sync||(this.deferSyncFn.sync=t),Ie(e),this.deferSyncFn===e&&(this.deferSyncFn=null,e.sync&&this.syncValue())},syncValue:function(){if(this.deferSyncFn)return this.deferSyncFn.sync=!0;this.vm.setupState.updateValue(r({},this.form))},isChange:function(e,t){return JSON.stringify(this.getFormData(e),dt)!==JSON.stringify(t,dt)},isQuote:function(e,t){return(N.Object(t)||Array.isArray(t))&&t===e.rule.value},refreshUpdate:function(e,t,n,r){var i=this;if(N.Function(e.rule.update)){var o=Ie((function(){return e.rule.update(t,e.origin,i.api,{origin:n||"change",linkField:r})}));if(void 0===o)return;e.rule.hidden=!0===o}},valueChange:function(e,t){this.refreshRule(e,t),this.bus.$emit("change-"+e.field,t)},refreshRule:function(e,t,n,r){this.refreshControl(e)&&(this.$render.clearCacheAll(),this.loadRule(),this.bus.$emit("update",this.api),this.refresh()),this.refreshUpdate(e,t,n,r)},appendLink:function(e){var t=this,n=e.rule.link;N.trueArray(n)&&n.forEach((function(n){var r=function(){return t.refreshRule(e,e.rule.value,"link",n)};t.bus.$on("change-"+n,r),e.linkOn.push((function(){return t.bus.$off("change-"+n,r)}))}))},fields:function(){return Object.keys(this.fieldCtx)}})}(mt),function(e){X(e.prototype,{getCtx:function(e){return this.getFieldCtx(e)||this.getNameCtx(e)[0]||this.ctxs[e]},getCtxs:function(e){return this.fieldCtx[e]||this.nameCtx[e]||(this.ctxs[e]?[this.ctxs[e]]:[])},setIdCtx:function(e,t,n){var r="".concat(n,"Ctx");this[r][t]?this[r][t].push(e):this[r][t]=[e]},rmIdCtx:function(e,t,n){var r="".concat(n,"Ctx"),i=this[r][t];if(!i)return!1;var o=i.splice(i.indexOf(e)>>>0,1).length>0;return i.length||delete this[r][t],o},getFieldCtx:function(e){return(this.fieldCtx[e]||[])[0]},getNameCtx:function(e){return this.nameCtx[e]||[]},setCtx:function(e){var t=e.id,n=e.field,r=e.name,i=e.rule;this.ctxs[t]=e,r&&this.setIdCtx(e,r,"name"),e.input&&(this.setIdCtx(e,n,"field"),this.setFormData(e,e.parser.toFormValue(i.value,e)),this.isMounted&&!this.reloading&&this.vm.emit("change",e.field,i.value,e.origin,this.api))},getParser:function(e){var t=this.fc.parsers,n=this.fc.renderDriver;if(n){var r=n.parsers||{},i=r[e.originType]||r[et(e.type)]||r[e.trueType];if(i)return i}return t[e.originType]||t[et(e.type)]||t[e.trueType]||pt},bindParser:function(e){e.setParser(this.getParser(e))},getType:function(e){var t=this.fc.CreateNode.aliasMap,n=t[e]||t[et(e)]||e;return et(n)},noWatch:function(e){this.noWatchFn||(this.noWatchFn=e),Ie(e),this.noWatchFn===e&&(this.noWatchFn=null)},watchCtx:function(e){var n=this;if(ge().filter((function(e){return"_"!==e[0]&&"$"!==e[0]&&-1===ht.indexOf(e)})).forEach((function(r){var i=t.toRef(e.rule,r),o="children"===r;e.refRule[r]=i,e.watch.push(t.watch(o?function(){return N.Function(i.value)?i.value:f(i.value||[])}:function(){return i.value},(function(t,a){var u=i.value;if(!n.isBreakWatch()){if(o&&!1===e.parser.loadChildren)return n.$render.clearCache(e),void n.nextRefresh();n.watching=!0,"link"!==r?(["props","on","deep"].indexOf(r)>-1?(n.parseInjectEvent(e.rule,u||{}),"props"===r&&e.input&&n.setFormData(e,e.parser.toFormValue(e.rule.value,e))):"emit"===r?n.parseEmit(e):"hidden"===r&&Boolean(u)!==Boolean(a)?n.$render.clearCacheAll():["prefix","suffix"].indexOf(r)>-1?u&&n.loadFn(u,e.rule):"type"===r?(e.updateType(),n.bindParser(e)):o&&(N.Function(a)&&(a=e.getPending("children",[])),N.Function(u)&&(u=e.loadChildrenPending()),n.updateChildren(e,u,a)),n.$render.clearCache(e),n.refresh(),n.watching=!1):e.link()}}),{deep:!o,sync:o}))})),e.input){var r=t.toRef(e.rule,"value");e.watch.push(t.watch((function(){return r.value}),(function(){var t=e.parser.toFormValue(r.value,e);n.isChange(e,t)&&n.setValue(e,r.value,t,!0)})))}this.bus.$once("load-end",(function(){var r=e.rule.computed;r&&("object"!==i(r)&&(r={value:r}),Object.keys(r).forEach((function(o){e.watch.push(t.watch((function(){var t=r[o];if(t){var a;if("object"===i(t)){var u=e.getParentGroup();return function e(t){if(t=Array.isArray(t)?{mode:"AND",group:t}:t,!N.trueArray(t.group))return!0;for(var r="OR"===t.mode,i=!0,o=0;o<t.group.length;o++){var a=t.group[o],l=void 0;if(l=a.mode?e(a):!!Ue[a.condition]&&new Function("_$","_$val","top","group","with(top){with(this){with(group){ return _$['".concat(a.condition,"'](").concat(a.field,", ").concat(a.compare?a.compare:"_$val","); }}}")).call(n.api.form,Ue,a.value,n.api.top.form,u&&n.subRuleData[u.id]||{}),r&&l)return!0;r||(i=i&&l)}return!r&&i}(t)}if(N.Function(t))a=function(){return t(n.api.form,n.api)};else{var l=e.getParentGroup();a=function(){return new Function("formulas","top","group","$rule","$api","with(top){with(this){with(group){with(formulas){ return ".concat(t," }}}}")).call(n.api.form,n.fc.formulas,n.api.top.form,l&&n.subRuleData[l.id]||{},e.rule,n.api)}}return Ie(a,void 0)}}),(function(t){setTimeout((function(){"value"===o?n.onInput(e,t):"$"===o[0]?n.api.setEffect(e.id,o,t):ot(e.rule,o,t)}))}),{immediate:!0}))})))})),this.watchEffect(e)},updateChildren:function(e,t,n){var r=this;this.deferSyncValue((function(){n&&n.forEach((function(n){-1===(t||[]).indexOf(n)&&n&&!N.String(n)&&n.__fc__&&n.__fc__.parent===e&&r.rmCtx(n.__fc__)})),N.trueArray(t)&&(r.loadChildren(t,e),r.bus.$emit("update",r.api))}))},rmSub:function(e){var t=this;N.trueArray(e)&&e.forEach((function(e){e&&e.__fc__&&t.rmCtx(e.__fc__)}))},rmCtx:function(e){var t=this;if(!e.deleted){var n=e.id,r=e.field,i=e.input,o=e.name;J(this.ctxs,n),J(this.formData,n),J(this.subForm,n),J(this.vm.setupState.ctxInject,n);var a=e.getParentGroup();a&&this.subRuleData[a.id]&&J(this.subRuleData[a.id],r),e.group&&J(this.subRuleData,n),i&&this.rmIdCtx(e,r,"field"),o&&this.rmIdCtx(e,o,"name"),i&&!I(this.fieldCtx,r)&&J(this.form,r),this.deferSyncValue((function(){if(!t.reloading){if(!1!==e.parser.loadChildren){var n=e.getPending("children",e.rule.children);N.trueArray(n)&&n.forEach((function(e){return e.__fc__&&t.rmCtx(e.__fc__)}))}e.root===t.rules&&t.vm.setupState.renderRule()}}),i);var u=this.sort.indexOf(n);return u>-1&&this.sort.splice(u,1),this.$render.clearCache(e),e.delete(),this.effect(e,"deleted"),i&&!this.fieldCtx[r]&&this.vm.emit("remove-field",r,e.rule,this.api),e.rule.__ctrl||this.vm.emit("remove-rule",e.rule,this.api),e}}})}(mt),function(e){X(e.prototype,{mounted:function(){var e=this,t=function(){e.isMounted=!0,e.lifecycle("mounted")};this.pageEnd?t():this.bus.$once("page-end",t)},lifecycle:function(e){this.vm.emit(e,this.api),this.emitEvent(e,this.api)},emitEvent:function(e){for(var t,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=this.options[e]||this.options[et("on-"+e)];if(o){var a=Se(o);N.Function(a)&&Ie((function(){return a.apply(void 0,r)}))}(t=this.bus).$emit.apply(t,[e].concat(r))}})}(mt),function(e){X(e.prototype,{useProvider:function(){var e=this,t=this.fc.providers;Object.keys(t).forEach((function(n){var r=t[n];N.Function(r)&&(r=r(e.fc)),r._c=function(e){var t=e.components;if(Array.isArray(t)){var n=function(e){return e.filter((function(e,t,n){return n.indexOf(e,0)===t}))}(t.filter((function(e){return"*"!==e})));return!!n.length&&n}return!!N.String(t)&&[t]}(r),e.onEffect(r),e.providers[n]=r}))},onEffect:function(e){var t=this,n=[];(e._c||["*"]).forEach((function(r){var i="*"===r?"*":t.getType(r);n.indexOf(i)>-1||(n.push(i),t.bus.$on("p:".concat(e.name,":").concat(i,":").concat(e.input?1:0),(function(t,n){e[t]&&e[t].apply(e,f(n))})))})),e._used=n},watchEffect:function(e){var n=this,r={required:function(){var t,n;return(I(e.rule,"$required")?e.rule.$required:null===(t=e.rule)||void 0===t||null===(n=t.effect)||void 0===n?void 0:n.required)||!1}};Object.keys(e.rule.effect||{}).forEach((function(t){r[t]=function(){return e.rule.effect[t]}})),Object.keys(e.rule).forEach((function(t){"$"===t[0]&&(r[t.substr(1)]=function(){return e.rule[t]})})),Object.keys(r).forEach((function(i){e.watch.push(t.watch(r[i],(function(t){n.effect(e,"watch",a({},i,t))}),{deep:!0}))}))},ruleEffect:function(e,t,n){this.emitEffect({rule:e,input:!!e.field,type:this.getType(e.type)},t,n)},effect:function(e,t,n){this.emitEffect({rule:e.rule,input:e.input,type:e.trueType,ctx:e,custom:n},t)},getEffect:function(e,t){return I(e,"$"+t)?e["$"+t]:I(e,"effect")&&I(e.effect,t)?e.effect[t]:void 0},emitEffect:function(e,t,n){var i=this,o=e.ctx,a=e.rule,u=e.input,l=e.type,c=e.custom;if(l&&!(["fcFragment","fragment"].indexOf(l)>-1)){var s=c||Object.keys(a).reduce((function(e,t){return"$"===t[0]&&(e[t.substr(1)]=a[t]),e}),r({},a.effect||{}));Object.keys(s).forEach((function(e){var c=i.providers[e];if(c&&(!c.input||u)){var f;if(c._c){if(!(c._used.indexOf(l)>-1))return;f=l}else f="*";var d=r({value:s[e],getValue:function(){return i.getEffect(a,e)}},n||{});o&&(d.getProp=function(){return o.effectData(e)},d.clearProp=function(){return o.clearEffectData(e)},d.mergeProp=function(e){return De(d.getProp(),[e])},d.id=o.id),i.bus.$emit("p:".concat(e,":").concat(f,":").concat(c.input?1:0),t,[d,a,i.api])}}))}}})}(mt);var vt=t.defineComponent({name:"fcFragment",inheritAttrs:!1,props:["vnode"],render:function(){return this.vnode}});function gt(e,n){var r=e.directives;return r?(Array.isArray(r)||(r=[r]),t.withDirectives(n,r.reduce((function(e,n){return e.concat(function(e){return Object.keys(e).map((function(n){var r=e[n],i=t.resolveDirective(n);if(i)return[i,r.value,r.arg,r.modifiers]})).filter((function(e){return!!e}))}(n))}),[]))):n}function yt(e){var t=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&l(e,t)}(n,e);var t=s(n);function n(){return o(this,n),t.apply(this,arguments)}return n}(bt);return Object.assign(t.prototype,e),t}function bt(e){X(this,{$handle:e,vm:e.vm,options:{},ref:"fcForm",mergeOptionsRule:{normal:["form","row","info","submitBtn","resetBtn"]}}),this.updateKey(),this.init()}X(bt.prototype,{__init:function(){var e=this;this.$render=this.$handle.$render,this.$r=function(){var t;return(t=e.$render).renderRule.apply(t,arguments)}},updateKey:function(){this.key=it()},init:function(){},update:function(){},beforeRender:function(){},form:function(){return this.vm.refs[this.ref]},getSlot:function(e){return function t(n){if(n){var r=n.slots[e];return r||t(n.setupState.parent)}}(this.vm)},mergeOptions:function(e,t){var n=this;return de(e.map((function(e){return n.tidyOptions(e)})),t,this.mergeOptionsRule)},updateOptions:function(e){var t=this;this.$handle.fc.renderDriver&&this.$handle.fc.renderDriver.updateOptions&&Ie((function(){t.$handle.fc.renderDriver.updateOptions(e,{handle:t.$handle,api:t.$handle.api})})),this.options=this.mergeOptions([e],this.getDefaultOptions()),this.update()},tidyOptions:function(e){return e},tidyRule:function(e){},mergeProp:function(e){},getDefaultOptions:function(){return{}},render:function(e){}});var wt={name:"componentValidate",load:function(e,t,n){var r=e.getValue();r?e.getProp().validate=[{validator:function(){var i=Ne(t);if(i){for(var o=arguments.length,a=new Array(o),u=0;u<o;u++)a[u]=arguments[u];return n.exec.apply(n,[i.id,!0===r?"formCreateValidate":r].concat(a,[{attr:e,rule:t,api:n}]))}}}]:(e.clearProp(),n.clearValidateState([t.field]))},watch:function(){wt.load.apply(wt,arguments)}},xt={fetch:function(e){function t(t,i,o){var a=t.value;n.deleted(t),N.Function(a)&&(a=a(i,o)),a=function(e){return N.String(e)&&(e={action:e,to:"options"}),e}(a);var u=function(e){void 0===e?t.clearProp():ot(t.getProp(),a.to||"options",e),o.sync(i)};if(a&&(a.action||a.key)){if((a=W(a)).to||(a.to="options"),a.key){var l=e.$handle.options.globalData[a.key];if(!l)return void u(void 0);if("static"===l.type)return void u(l.data);a=r(r({},a),l)}var c=a.onError,s=function(){if(!t.getValue())return t.clearProp(),o.sync(i),!0};n._fn[t.id]=e.watchLoadData(oe((function(l,f){if(f&&!1===a.watch)return n._fn[t.id]();var d=e.$handle.loadFetchVar(W(a),l),p=r(r({headers:{}},d),{},{onSuccess:function(e,t){if(!s()){var n=function(e){return t?e:I(e,"data")?e.data:e},r=Se(d.parse);N.Function(r)?n=r:r&&N.String(r)&&(n=function(e){return He(e,r)}),u(n(e,i,o)),o.sync(i)}},onError:function(e){u(void 0),s()||(c||function(e){return be(e.message||"fetch fail "+d.action)})(e,i,o)}});e.$handle.beforeFetch(p,{rule:i,api:o}).then((function(){N.Function(d.action)?d.action(i,o).then((function(e){p.onSuccess(e,!0)})).catch((function(e){p.onError(e)})):Ie((function(){return e.create.fetch(p,{inject:t,rule:i,api:o})}))}))}),a.wait||600))}else u(void 0)}var n={name:"fetch",_fn:[],mounted:function(){t.apply(void 0,arguments)},watch:function(){t.apply(void 0,arguments)},deleted:function(e){this._fn[e.id]&&this._fn[e.id](),e.clearProp()}};return n},loadData:function(e){var t={name:"loadData",_fn:[],mounted:function(t,n,r){this.deleted(t);var i=h(t.getValue()),o=[];i.forEach((function(i){if(i&&(i.attr||i.template)){var a=e.watchLoadData(oe((function(o){var a;a=i.template?e.$handle.loadStrVar(i.template,o):o(i.attr,i.default),!1!==i.copy&&(a=W(a));var u=i.modify?n:t.getProp();"child"===i.to?u.children?u.children[0]=a:u.children=[a]:ot(u,i.to||"options",a),r.sync(n)}),i.wait||300));!1!==i.watch?o.push(a):a()}})),this._fn[t.id]=o},deleted:function(e){this._fn[e.id]&&this._fn[e.id].forEach((function(e){e()})),e.clearProp()}};return t.watch=t.created,t},componentValidate:wt},_t={name:"html",loadChildren:!1,render:function(e,t){return t.prop.props.innerHTML=e.default(),t.vNode.make(t.prop.props.tag||"div",t.prop)},renderChildren:function(e){return{default:function(){return e.filter((function(e){return N.String(e)})).join("")}}}};function $t(e){e+="=";for(var t=decodeURIComponent(document.cookie).split(";"),n=0;n<t.length;n++){for(var r=t[n];" "===r.charAt(0);)r=r.substring(1);if(0===r.indexOf(e)){r=r.substring(e.length,r.length);try{return JSON.parse(r)}catch(e){return r}}}return null}function kt(e){var t=localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch(e){return t}return null}function Ct(e,t){if(!t)return null;var n=t.split("."),r=e(n.shift());return n.length?null==r?null:He(r,n):r}function Vt(e){return Ct($t,e)}function Ot(e){return Ct(kt,e)}function St(e,t){var n;return 2===arguments.length?t=(n=arguments[1])[e]:n=arguments[2],{id:t,prop:n}}function Et(){return St.apply(void 0,["name"].concat(Array.prototype.slice.call(arguments)))}var Rt=1,jt={};function Ft(e){var n,i=a({},vt.name,vt),o={},u={},l={},c={},s=[],d=[],p=[e.extendApi],h=r({},xt),m=We(),g={global:{}},y=t.reactive({}),b=function(){var e={};function n(){}return X(n.prototype,{make:function(e,t,n){return gt(t,this.h(e,Te(t),n))},makeComponent:function(e,n,r){try{return gt(n,t.createVNode(e,Te(n),r))}catch(e){return console.error(e),t.createVNode("")}},h:function(e,n,r){var i=t.getCurrentInstance().appContext.config.isNativeTag(e);i&&delete n.formCreateInject;try{return t.createVNode(i?e:t.resolveComponent(e),n,r)}catch(e){return console.error(e),t.createVNode("")}},aliasMap:e}),X(n,{aliasMap:e,alias:function(t,n){e[t]=n},use:function(e){Object.keys(e).forEach((function(t){var r=ae(t),i=nt(t).toLocaleLowerCase(),o=e[t];[t,r,i].forEach((function(e){n.alias(t,o),n.prototype[e]=function(e,t){return this.make(o,e,t)}}))}))}}),n}(),w={},x=!0===e.isMobile;function _(e){var t=jt[e];return Array.isArray(t)?t.map((function(e){return e.api()})):t?t.api():void 0}function $(e){s.push(e)}function k(){var e=Et.apply(void 0,arguments);e.id&&e.prop&&(u[e.id]=e.prop)}function C(){var e=Et.apply(void 0,arguments);e.id&&e.prop&&(h[e.id]=r(r({},e.prop),{},{name:e.id}))}function V(e){b.use(e)}function O(){var e=Et.apply(void 0,arguments);if(!e.id||!e.prop)return pt;var t=et(e.id),n=e.prop,r=!0===n.merge?o[t]:void 0;o[t]=Me(n,r||pt),m[t]=ze(t),n.maker&&X(m,n.maker)}function S(e,t){var n;if(N.String(e)){if(n=e,void 0===t)return i[n]}else n=e.displayName||e.name,t=e;if(n&&t){var r=et(n);i[n]=t,i[r]=t,delete b.aliasMap[n],delete b.aliasMap[r],delete o[n],delete o[r],t.formCreateParser&&O(n,t.formCreateParser)}}function E(){return le(q,i,u)}function R(){return vt}function j(e,t){return N.Function(e.install)?e.install(F,t):N.Function(e)&&e(F,t),this}function F(e,n){var i=function(e,n){var i=E();return t.createApp({data:function(){return t.reactive({rule:e,option:n})},render:function(){return t.h(i,r({ref:"fc"},this.$data))}})}(e,n||{});s.forEach((function(e){Ie((function(){return e(F,i)}))}));var o=document.createElement("div");return((null==n?void 0:n.el)||document.body).appendChild(o),i.mount(o).$refs.fc.fapi}function D(t){var n=r({},e);return t?n.inherit={components:i,parsers:o,directives:u,modelFields:l,providers:h,useApps:s,maker:m,formulas:w,loadData:y}:delete n.inherit,Ft(n)}function A(e,t){l[e]=t}function P(e,t){w[e]=t}function B(e,t){var n=c[e]||{},i=n.parsers||{};t.parsers&&Object.keys(t.parsers).forEach((function(e){i[e]=Me(t.parsers[e],pt)})),t.name=e,c[e]=r(r(r({},n),t),{},{parsers:i})}function T(e){e&&Object.keys(jt).forEach((function(t){(Array.isArray(jt[t])?jt[t]:[jt[t]]).forEach((function(t){t.bus.$emit("$loadData."+e)}))}))}function M(e,t){y[e]=t,T(e)}function L(e,t){var n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return Ie((function(){return t.apply(void 0,n)}))};n._driver=!0,M(e,n)}function U(e,t){var n=(e||"").split(".");e=n.shift();var r=n.join(".");if(I(y,e)){var i=y[e];return i&&i._driver?i=i(r):n.length&&(i=He(i,n)),null==i||""===i?t:i}return t}function H(e){p.push(e)}function z(e){delete y[e],T(e)}function Y(e,t){d.push({name:e,callback:t})}function q(n){var r=this;X(this,{id:Rt++,create:F,vm:n,manager:yt(e.manager),parsers:o,providers:h,modelFields:l,formulas:w,isMobile:x,rules:n.props.rule,name:n.props.name||it(),inFor:n.props.inFor,prop:{components:i,directives:u},drivers:c,renderDriver:null,setData:M,getData:U,refreshData:T,loadData:y,CreateNode:b,bus:new v,unwatch:[],options:t.ref({}),extendApiFn:p,fetchCache:new WeakMap}),d.forEach((function(e){r.bus.$on(e.name,e.callback)})),t.nextTick((function(){t.watch(r.options,(function(){r.$handle.$manager.updateOptions(r.options.value),r.api().refresh()}),{deep:!0})})),X(n.appContext.components,i),X(n.appContext.directives,u),this.$handle=new mt(this),this.name&&(this.inFor?(jt[this.name]||(jt[this.name]=[]),jt[this.name].push(this)):jt[this.name]=this)}function J(t){X(t,{version:e.version,ui:e.ui,extendApi:H,getData:U,setDataDriver:L,setData:M,removeData:z,refreshData:T,maker:m,component:S,directive:k,setModelField:A,setFormula:P,setDriver:B,register:C,$vnode:R,parser:O,use:j,factory:D,componentAlias:V,copyRule:je,copyRules:Fe,fetch:Xe,$form:E,parseFn:Se,parseJson:Ee,toJson:Ve,useApp:$,getApi:_,on:Y})}if(function(e){var t=e.key||[],n=e.array||[],r=e.normal||[];he.push.apply(he,f(t)),me.push.apply(me,f(n)),ve.push.apply(ve,f(r)),qe([].concat(f(t),f(n),f(r)))}(e.attrs||{}),q.isMobile=x,X(q.prototype,{init:function(){var e=this;this.isSub()&&this.unwatch.push(t.watch((function(){return e.vm.setupState.parent.setupState.fc.options.value}),(function(){e.initOptions(),e.$handle.api.refresh()}),{deep:!0}));var n=this.vm.setupState.parent?this.vm.setupState.parent.setupState.fc.renderDriver:this.vm.props.driver;this.renderDriver=this.drivers[n],this.initOptions(),this.$handle.init()},globalDataDriver:function(e){var t=this,n=e.split("."),r=n.shift(),i=this.options.value.globalData&&this.options.value.globalData[r];if(i){if("static"===i.type)return He(i.data,n);var o,a=this.fetchCache.get(i);if(a){if(a.status&&(o=He(a.data,n)),!a.loading)return o;a.loading=!1,this.fetchCache.set(i,a)}else this.fetchCache.set(i,{status:!1});var u=oe((function(){c();var e=t.fetchCache.get(i);t.options.value.globalData&&-1!==Object.values(t.options.value.globalData).indexOf(i)?(e&&(e.loading=!0,t.fetchCache.set(i,e)),t.bus.$emit("$loadData.$globalData."+r)):t.fetchCache.delete(i)}),i.wait||600),l=function(e){t.fetchCache.set(i,{status:!0,data:e}),t.bus.$emit("$loadData.$globalData."+r)},c=this.watchLoadData((function(e,n){if(n&&!1===i.watch)return c();if(n)u();else{var r=t.$handle.loadFetchVar(Q(i),e);t.$handle.api.fetch(r).then((function(e){l(e)})).catch((function(e){l(null)}))}}));return this.unwatch.push(c),o}},globalVarDriver:function(e){var t=this,n=e.split(".").shift(),r=this.options.value.globalVariable&&this.options.value.globalVariable[n];if(r){var i=N.Function(r)?r:r.handle;if(i){var o,a=this.watchLoadData((function(e,r){r&&(a(),t.bus.$emit("$loadData.$var."+n)),o=Ie((function(){return i(e,t.$handle.api)}))}));return this.unwatch.push(a),o}}},getLoadData:function(e,t){var n=null;if(null!=e){var r=e.split("."),i=r.shift();"$form"===i?n=this.$handle.api.top.formData():"$subForm"===i?n=this.$handle.api.formData():"$options"===i?n=this.options.value:"$globalData"===i?(n=this.globalDataDriver(r.join(".")),r=[]):"$var"===i?(n=this.globalVarDriver(r.join(".")),r=[]):(n=U(e,t),r=[]),r.length&&(n=He(n,r))}return null==n||""===n?t:n},watchLoadData:function(e){var t=this,n={},r=function(t){Ie((function(){e(i,t)}))},i=function(e,i){if(n[e])return n[e].val;var o=t.getLoadData(e,i),a=e.split("."),u=a.shift(),l=a.shift()||"",c=oe((function(){if(u!==e){var o=t.getLoadData(e,i);JSON.stringify(o)!==JSON.stringify(n[e].val)&&(n[e].val=o,r(!0))}else r(!0)}),0);return t.bus.$on("$loadData."+u,c),l&&t.bus.$on("$loadData."+u+"."+l,c),n[e]={fn:function(){t.bus.$off("$loadData."+u,c),l&&t.bus.$off("$loadData."+u+"."+l,c)},val:o},o};r(!1);var o=function(){Object.keys(n).forEach((function(e){return n[e].fn()})),n={}};return this.unwatch.push(o),o},isSub:function(){return this.vm.setupState.parent&&this.vm.props.extendOption},initOptions:function(){this.options.value={};var e=r({formData:{},submitBtn:{},resetBtn:{},globalEvent:{},globalData:{}},W(g));this.isSub()&&(e=this.mergeOptions(e,this.vm.setupState.parent.setupState.fc.options.value||{},!0)),e=this.mergeOptions(e,this.vm.props.option),this.updateOptions(e)},mergeOptions:function(e,t,n){return t=W(t),n&&["page","onSubmit","mounted","reload","formData","el","globalClass","style"].forEach((function(e){delete t[e]})),t.global&&(e.global=function(e,t){return e?(Object.keys(t||{}).forEach((function(n){t[n]&&(e[n]=De(e[n]||{},t[n]))})),e):t}(e.global,t.global),delete t.global),this.$handle.$manager.mergeOptions([t],e),e},updateOptions:function(e){this.options.value=this.mergeOptions(this.options.value,e),this.$handle.$manager.updateOptions(this.options.value),this.bus.$emit("$loadData.$options")},api:function(){return this.$handle.api},render:function(){return this.$handle.render()},mounted:function(){this.$handle.mounted()},unmount:function(){var e=this;if(this.name)if(this.inFor){var t=jt[this.name].indexOf(this);jt[this.name].splice(t,1)}else delete jt[this.name];d.forEach((function(t){e.bus.$off(t.name,t.callback)})),this.unwatch.forEach((function(e){return e()})),this.unwatch=[],this.$handle.reloadRule([])},updated:function(){var e=this;this.$handle.bindNextTick((function(){return e.bus.$emit("next-tick",e.$handle.api)}))}}),J(F),X(n=F,{create:F,isMobile:x,install:function(t,i){g=r(r({},g),i||{});var o="_installedFormCreate_"+e.ui;if(!0!==t[o]){t[o]=!0;var a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return F(e,t)};J(a),t.config.globalProperties.$formCreate=a;var u=E();t.component(u.name,u),s.forEach((function(e){Ie((function(){return e(n,t)}))}))}}}),L("$cookie",Vt),L("$localStorage",Ot),b.use({fragment:"fcFragment"}),e.install&&F.use(e),$((function(e,t){t.mixin({props:["formCreateInject"]})})),O(_t),e.inherit){var G=e.inherit;G.components&&X(i,G.components),G.parsers&&X(o,G.parsers),G.directives&&X(u,G.directives),G.modelFields&&X(l,G.modelFields),G.providers&&X(h,G.providers),G.useApps&&X(s,G.useApps),G.maker&&X(m,G.maker),G.loadData&&X(y,G.loadData),G.formulas&&X(w,G.formulas)}var K=E();return Me(K,F),Object.defineProperties(K,{fetch:{get:function(){return F.fetch},set:function(e){F.fetch=e}}}),K.util=F,K}var Dt={date:"YYYY-MM-DD",month:"YYYY-MM",datetime:"YYYY-MM-DD HH:mm:ss",timerange:"HH:mm:ss",daterange:"YYYY-MM-DD",monthrange:"YYYY-MM",datetimerange:"YYYY-MM-DD HH:mm:ss",year:"YYYY"},At="datePicker",Pt={name:At,maker:["year","month","date","dates","week","datetime","datetimeRange","dateRange","monthRange"].reduce((function(e,t){return e[t]=ze(At,{type:t.toLowerCase()}),e}),{}),mergeProp:function(e){var t=e.prop.props;t.valueFormat||(t.valueFormat=Dt[t.type]||Dt.date)}},Nt="hidden",It={name:Nt,maker:a({},Nt,(function(e,t){return ze(Nt)("",e,t)})),render:function(){return[]}},Bt="input",Tt={name:Bt,maker:function(){var e=["password","url","email","text","textarea"].reduce((function(e,t){return e[t]=ze(Bt,{type:t}),e}),{});return e.idate=ze(Bt,{type:"date"}),e}(),mergeProp:function(e){var t=e.prop.props;t&&t.autosize&&t.autosize.minRows&&(t.rows=t.autosize.minRows||2)}},Mt="slider",Lt="timePicker",Ut=[Pt,It,Tt,{name:Mt,maker:{sliderRange:ze(Mt,{range:!0})},toFormValue:function(e,t){var n=Array.isArray(e),r=t.prop.props,i=r.min||0;return!0===r.range?n?e:[i,parseFloat(e)||i]:n?parseFloat(e[0])||i:parseFloat(e)}},{name:Lt,maker:{time:ze(Lt,(function(e){return e.props.isRange=!1})),timeRange:ze(Lt,(function(e){return e.props.isRange=!0}))},mergeProp:function(e){var t=e.prop.props;t.valueFormat||(t.valueFormat="HH:mm:ss")}},{name:"FcRow",render:function(e,t){return t.vNode.col({props:{span:24}},{default:function(){return[t.vNode.row(t.prop,e)]}})}},{name:"select",toFormValue:function(e,t){return t.prop.props.multiple&&!Array.isArray(e)?h(e):e}}],Ht={button:"el-button",icon:"el-icon",slider:"el-slider",rate:"el-rate",upload:"fc-upload",cascader:"el-cascader",popover:"el-popover",tooltip:"el-tooltip",colorPicker:"el-colorPicker",timePicker:"el-time-picker",timeSelect:"el-time-select",datePicker:"el-date-picker",switch:"el-switch",select:"fc-select",checkbox:"fc-checkbox",radio:"fc-radio",inputNumber:"el-input-number",number:"el-input-number",input:"el-input",formItem:"el-form-item",form:"el-form",frame:"fc-frame",col:"el-col",row:"el-row",tree:"fc-tree",autoComplete:"el-autocomplete",auto:"el-autocomplete",group:"fc-group",object:"fc-sub-form",subForm:"fc-sub-form"};function zt(e,t){var n;I(e,t)&&(N.String(e[t])&&(e[t]=(a(n={},t,e[t]),a(n,"show",!0),n)))}function Yt(e){return!1===e}function qt(e){var t=r({},e);return delete t.children,t}var Jt={validate:function(){var e=this.form();return e?e.validate():new Promise((function(e){return e()}))},validateField:function(e){var t=this;return new Promise((function(n,r){var i=t.form();i?i.validateField(e,(function(e,t){t?r(t):n(e)})):n()}))},clearValidateState:function(e){var t=this.vm.refs[e.wrapRef];t&&t.clearValidate()},tidyOptions:function(e){return["submitBtn","resetBtn","row","info","wrap","col","title"].forEach((function(t){!function(e,t){I(e,t)&&!N.Object(e[t])&&(e[t]={show:!!e[t]})}(e,t)})),e},tidyRule:function(e){var t=e.prop;return zt(t,"title"),zt(t,"info"),t},mergeProp:function(e){e.prop=de([{info:this.options.info||{},wrap:this.options.wrap||{},col:this.options.col||{},title:this.options.title||{}},e.prop],{info:{trigger:"hover",placement:"top-start",icon:!0},title:{},col:{span:24},wrap:{}},{normal:["title","info","col","wrap"]})},getDefaultOptions:function(){return{form:{inline:!1,labelPosition:"right",labelWidth:"125px",disabled:!1,size:void 0},row:{show:!0,gutter:0},submitBtn:{type:"primary",loading:!1,disabled:!1,innerText:"提交",show:!0,col:void 0,click:void 0},resetBtn:{type:"default",loading:!1,disabled:!1,innerText:"重置",show:!1,col:void 0,click:void 0}}},update:function(){var e=this.options.form;this.rule={props:r({},e),on:{submit:function(e){e.preventDefault()}},class:[e.className,e.class,"form-create",this.options.preview?"is-preview":""],style:e.style,type:"form"}},beforeRender:function(){var e=this.key,t=this.ref,n=this.$handle;X(this.rule,{key:e,ref:t}),X(this.rule.props,{model:n.formData})},render:function(e){var t=this;return e.slotLen()&&!this.options.preview&&e.setSlot(void 0,(function(){return t.makeFormBtn()})),this.$r(this.rule,Yt(this.options.row.show)?e.getSlots():[this.makeRow(e)])},makeWrap:function(e,t){var n=this,i=e.prop,o="".concat(this.key).concat(e.key),a=i.col,u=this.isTitle(i),l=a.labelWidth||u?a.labelWidth:0,c=this.rule.props,s=c.inline,f=c.col,d=Yt(i.wrap.show)?t:this.$r(de([i.wrap,{props:r(r({labelWidth:void 0===l?l:nt(l),label:u?i.title.title:void 0},qt(i.wrap||{})),{},{prop:e.id,rules:e.injectValidate()}),class:i.className,key:"".concat(o,"fi"),ref:e.wrapRef,type:"formItem"}]),r({default:function(){return t}},u?{label:function(){return n.makeInfo(i,o,e)}}:{}));return!0===s||Yt(f)||Yt(a.show)?d:this.makeCol(i,o,[d])},isTitle:function(e){if(!1===this.options.form.title)return!1;var t=e.title;return!(!t.title&&!t.native||Yt(t.show))},makeInfo:function(e,t,n){var i=this,o=r({},e.title),u=r({},e.info),l="tooltip"===u.type,c=this.options.form,s=this.getSlot("title"),f=[s?s({title:o.title||"",rule:n.rule,options:this.options}):(o.title||"")+(c.labelSuffix||c["label-suffix"]||"")];if(!Yt(u.show)&&(u.info||u.native)&&!Yt(u.icon)){var d={type:u.type||"popover",props:qt(u),key:"".concat(t,"pop")};delete d.props.icon,delete d.props.show,delete d.props.info,delete d.props.align,delete d.props.native;var p="content";u.info&&!I(d.props,p)&&(d.props[p]=u.info),f["left"!==u.align?"unshift":"push"](this.$r(de([u,d]),a({},o.slot||(l?"default":"reference"),(function(){return i.$r({type:"ElIcon",style:"top:2px",key:"".concat(t,"i")},{default:function(){return i.$r({type:!0===u.icon?"icon-warning":u.icon})}},!0)}))))}var h=de([o,{props:qt(o),key:"".concat(t,"tit"),type:o.type||"span"}]);return delete h.props.show,delete h.props.title,delete h.props.native,this.$r(h,f)},makeCol:function(e,t,n){var r=e.col;return this.$r({class:r.class,type:"col",props:r||{span:24},key:"".concat(t,"col")},n)},makeRow:function(e){var t=this.options.row||{};return this.$r({type:"row",props:t,class:t.class,key:"".concat(this.key,"row")},e)},makeFormBtn:function(){var e=[];if(Yt(this.options.submitBtn.show)||e.push(this.makeSubmitBtn()),Yt(this.options.resetBtn.show)||e.push(this.makeResetBtn()),e.length){var t=this.$r({type:"formItem",key:"".concat(this.key,"fb")},e);return!0===this.rule.props.inline?t:this.$r({type:"col",props:{span:24},key:"".concat(this.key,"fc")},[t])}},makeResetBtn:function(){var e=this,t=r({},this.options.resetBtn),n=t.innerText;return delete t.innerText,delete t.click,delete t.col,delete t.show,this.$r({type:"button",props:t,class:"_fc-reset-btn",style:{width:t.width},on:{click:function(){var t=e.$handle.api;e.options.resetBtn.click?e.options.resetBtn.click(t):t.resetFields()}},key:"".concat(this.key,"b2")},[n])},makeSubmitBtn:function(){var e=this,t=r({},this.options.submitBtn),n=t.innerText;return delete t.innerText,delete t.click,delete t.col,delete t.show,this.$r({type:"button",props:t,class:"_fc-submit-btn",style:{width:t.width},on:{click:function(){var t=e.$handle.api;e.options.submitBtn.click?e.options.submitBtn.click(t):t.submit().catch((function(){}))}},key:"".concat(this.key,"b1")},[n])}},Gt={};!function(e){["group","tree","switch","upload","autoComplete","checkbox","cascader","colorPicker","datePicker","frame","inputNumber","radio","rate"].forEach((function(t){e[t]=ze(t)})),e.auto=e.autoComplete,e.number=e.inputNumber,e.color=e.colorPicker}(Gt),function(e){var t="select",n="multiple";e.selectMultiple=ze(t,a({},n,!0)),e.selectOne=ze(t,a({},n,!1))}(Gt),function(e){var t={treeSelected:"selected",treeChecked:"checked"};Object.keys(t).reduce((function(e,n){return e[n]=ze("tree",{type:t[n]}),e}),e)}(Gt),function(e){var t={image:["image",0],file:["file",0],uploadFileOne:["file",1],uploadImageOne:["image",1]};Object.keys(t).reduce((function(e,n){return e[n]=ze("upload",(function(e){return e.props({uploadType:t[n][0],maxLength:t[n][1]})})),e}),e),e.uploadImage=e.image,e.uploadFile=e.file}(Gt),function(e){var t={frameInputs:["input",0],frameFiles:["file",0],frameImages:["image",0],frameInputOne:["input",1],frameFileOne:["file",1],frameImageOne:["image",1]};Object.keys(t).reduce((function(e,n){return e[n]=ze("frame",(function(e){return e.props({type:t[n][0],maxLength:t[n][1]})})),e}),e),e.frameInput=e.frameInputs,e.frameFile=e.frameFiles,e.frameImage=e.frameImages}(Gt);function Wt(e,t){return N.Boolean(e)?e={show:e}:N.Undef(e)||N.Object(e)||(e={show:t}),e}function Kt(e,t){return{formEl:function(){return t.$manager.form()},wrapEl:function(e){var n=t.getFieldCtx(e);if(n)return t.vm.refs[n.wrapRef]},validate:function(n){return new Promise((function(r,i){var o=e.children,a=[t.$manager.validate()];o.forEach((function(e){a.push(e.validate())})),Promise.all(a).then((function(){r(!0),n&&n(!0)})).catch((function(r){i(r),n&&n(r),t.vm.emit("validate-fail",r,{api:e})}))}))},validateField:function(n,r){return new Promise((function(i,o){var a=t.getFieldCtx(n);if(a){var u=t.subForm[a.id],l=[t.$manager.validateField(a.id)];h(u).forEach((function(e){l.push(e.validate())})),Promise.all(l).then((function(){i(null),r&&r(null)})).catch((function(i){o(i),r&&r(i),t.vm.emit("validate-field-fail",i,{field:n,api:e})}))}}))},clearValidateState:function(n){var r=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];e.helper.tidyFields(n).forEach((function(e){i&&r.clearSubValidateState(e),t.getCtxs(e).forEach((function(e){t.$manager.clearValidateState(e)}))}))},clearSubValidateState:function(n){e.helper.tidyFields(n).forEach((function(e){t.getCtxs(e).forEach((function(e){var n=t.subForm[e.id];n&&(Array.isArray(n)?n.forEach((function(e){e.clearValidateState()})):n&&n.clearValidateState())}))}))},btn:{loading:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e.submitBtnProps({loading:!!t})},disabled:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e.submitBtnProps({disabled:!!t})},show:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e.submitBtnProps({show:!!t})}},resetBtn:{loading:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e.resetBtnProps({loading:!!t})},disabled:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e.resetBtnProps({disabled:!!t})},show:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e.resetBtnProps({show:!!t})}},submitBtnProps:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=Wt(t.options.submitBtn,!0);X(r,n),t.options.submitBtn=r,e.refreshOptions()},resetBtnProps:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=Wt(t.options.resetBtn,!1);X(r,n),t.options.resetBtn=r,e.refreshOptions()},submit:function(n,r){return new Promise((function(i,o){e.validate().then((function(){var r=e.formData();N.Function(n)&&Ie((function(){return n(r,e)})),N.Function(t.options.onSubmit)&&Ie((function(){return t.options.onSubmit(r,e)})),t.vm.emit("submit",r,e),i(r)})).catch((function(){for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];N.Function(r)&&Ie((function(){return r.apply(void 0,[e].concat(n))})),o.apply(void 0,n)}))}))}}}g(".form-create .form-create .zy-el-form-item{margin-bottom:22px}.form-create{width:100%}.form-create .fc-none,.form-create.is-preview .zy-el-form-item.is-required>.zy-el-form-item__label-wrap>.zy-el-form-item__label:before,.form-create.is-preview .zy-el-form-item.is-required>.zy-el-form-item__label:before,.form-create.is-preview .fc-clock{display:none!important}.fc-wrap-left .zy-el-form-item__label{justify-content:flex-start;justify-content:flex-end}.fc-wrap-top.zy-el-form-item{display:block}.fc-wrap-top.zy-el-form-item .zy-el-form-item__label{display:block;height:auto;line-height:22px;margin-bottom:8px;text-align:left}.zy-el-form--large .fc-wrap-top.zy-el-form-item .zy-el-form-item__label{line-height:22px;margin-bottom:12px}.zy-el-form--default .fc-wrap-top.zy-el-form-item .zy-el-form-item__label{line-height:22px;margin-bottom:8px}.zy-el-form--small .fc-wrap-top.zy-el-form-item .zy-el-form-item__label{line-height:20px;margin-bottom:4px}");var Xt={name:"required",load:function(e,t,n){var o=function(e){return N.Boolean(e)?{required:e}:N.String(e)?{message:e}:N.Undef(e)?{required:!1}:N.Function(e)?{validator:e}:N.Object(e)?e:{}}(e.getValue());if(!1===o.required)e.clearProp(),n.clearValidateState([t.field]);else{var a=r({required:!0,validator:function(e,t,n){N.empty(t)?n(a.message):n()}},o);if(!a.message){var u=t.title||"";a.message=(("object"===i(u)?u.title:u)||"")+"不能为空"}e.getProp().validate=[a]}n.sync(t)},watch:function(){Xt.load.apply(Xt,arguments)}};function Qt(e){e.componentAlias(Ht),ie.forEach((function(t){e.component(t.name,t)})),e.register(Xt),Ut.forEach((function(t){e.parser(t)})),Object.keys(Gt).forEach((function(t){e.maker[t]=Gt[t]})),"undefined"!=typeof window&&window.ElementPlus&&e.useApp((function(e,t){t.use(window.ElementPlus)}))}var Zt=Ft({ui:"element-ui",version:"3.2.3",manager:Jt,extendApi:Kt,install:Qt,attrs:{normal:["col","wrap"],array:["className"],key:["title","info"]}});"undefined"!=typeof window&&(window.formCreate=Zt);var en=Zt.maker;e.default=Zt,e.maker=en,Object.defineProperty(e,"__esModule",{value:!0})}));
