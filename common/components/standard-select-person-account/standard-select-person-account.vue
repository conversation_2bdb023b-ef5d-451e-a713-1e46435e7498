<template>
  <div class="standard-select-person-account">
    <div class="standard-select-person-account-body">
      <div class="standard-select-person-account-left">
        <div class="standard-select-person-account-input">
          <el-input v-model="keyword" placeholder="根据用户名称搜索" @keyup.enter="handleSearch" clearable />
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </div>
        <div class="standard-select-person-account-user-list">
          <div class="standard-select-person-account-user-list-head">
            <div class="standard-select-person-account-user-list-name">人员列表（{{ userDataList.length }}）</div>
            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAll"></el-checkbox>
            <!-- <div class="standard-select-person-account-user-list-checkbox">
              <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAll">全选</el-checkbox>
              <el-checkbox v-model="isNoSelect">仅显示未选人员</el-checkbox>
            </div> -->
            <el-radio-group v-model="isNoSelect">
              <el-radio :value="0">全部</el-radio>
              <el-radio :value="1">未选人员</el-radio>
            </el-radio-group>
          </div>
          <virtual-user v-model="userId" :data="userDataList" @handleChange="handleChange"></virtual-user>
        </div>
      </div>
      <div class="standard-select-person-account-right">
        <div class="standard-select-person-account-right-name">
          已选人员（{{ userData.length }}）
          <div @click="deleteAll">清空</div>
        </div>
        <virtual-select-user :data="userData" @handleDel="deleteclick"></virtual-select-user>
      </div>
    </div>
    <div class="standard-select-person-account-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'StandardSelectPersonAccount' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted, defineAsyncComponent } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const VirtualUser = defineAsyncComponent(() => import('../virtualElement/virtual-user.vue'))
const VirtualSelectUser = defineAsyncComponent(() => import('../virtualElement/virtual-select-user.vue'))
const props = defineProps({
  userId: { type: Array, default: () => [] },
  userData: { type: Array, default: () => [] }
})
const emit = defineEmits(['callback'])
const isNoSelect = ref(0)
const userDataList = computed(() => {
  return isNoSelect.value ? userDataArr.value.filter((item) => !userId.value.includes(item.id)) : userDataArr.value
})

onMounted(() => {
  if (props.userData.length || props.userId.length) {
    if (props.userData.length) {
      props.userData.forEach((item) => {
        userData.value.push(item)
        userIndexData.value[item.id] = item.id
      })
      getUserData()
    }
    if (props.userId.length) {
      getSelectUser(props.userId)
    }
  } else {
    getUserData()
  }
})
const getSelectUser = async (userIds) => {
  const res = await api.businessUserAccountList({ ids: userIds })
  var { data } = res
  data.forEach((item) => {
    userIndexData.value[item.id] = item.id
  })
  userData.value = data
  getUserData()
}

const keyword = ref('')
const handleSearch = () => {
  getUserData()
}

const getUserData = async () => {
  const res = await api.businessUserAccountList({ keyword: keyword.value, query: { isUsing: 1 } })
  var { data } = res
  userDataArr.value = data
  memoryChecked()
}

const checkAll = ref(false)
const isIndeterminate = ref(false)
const userId = ref([])
const userData = ref([])
const userDataArr = ref([])
const userIndexData = ref([])

const handleChange = (value) => {
  const checkedCount = value.length
  checkAll.value = checkedCount > userDataArr.value.length || checkedCount === userDataArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userDataArr.value.length
  var values = []
  for (let i = 0, len = value.length; i < len; i++) {
    values[value[i]] = value[i]
  }
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (!Object.prototype.hasOwnProperty.call(values, userDataArr.value[i].id)) {
      deleteData(userDataArr.value[i])
      delete userIndexData.value[userDataArr.value[i].id]
    }
  }
  for (let i = 0, len = value.length; i < len; i++) {
    if (!Object.prototype.hasOwnProperty.call(userIndexData.value, value[i])) {
      pushData(value[i])
      userIndexData.value[value[i]] = value[i]
    }
  }
}

const handleCheckAllData = (val) => {
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (Object.prototype.hasOwnProperty.call(userIndexData.value, userDataArr.value[i].id)) {
      if (!val) {
        deleteData(userDataArr.value[i])
      }
      val ? null : delete userIndexData.value[userDataArr.value[i].id] // eslint-disable-line
    } else {
      pushData(userDataArr.value[i].id)
      userIndexData.value[userDataArr.value[i].id] = userDataArr.value[i].id
    }
  }
}
const handleCheckAll = (val) => {
  userId.value = val ? [...new Set([...userId.value, ...userDataArr.value.map((v) => v.id)])] : []
  isIndeterminate.value = false
  handleCheckAllData(val)
}

const deleteData = (data) => {
  userData.value = userData.value.filter((item) => item.id !== data.id)
}
const pushData = (id) => {
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (userDataArr.value[i].id === id) {
      userData.value.push(userDataArr.value[i])
    }
  }
}

const deleteAll = () => {
  ElMessageBox.confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      userData.value = []
      userIndexData.value = []
      memoryChecked()
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消删除' })
    })
}
const deleteclick = (data) => {
  deleteData(data)
  delete userIndexData.value[data.id]
  memoryChecked()
}
const memoryChecked = () => {
  var userIdArr = []
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (Object.prototype.hasOwnProperty.call(userIndexData.value, userDataArr.value[i].id)) {
      userIdArr.push(userDataArr.value[i].id)
    }
  }
  userId.value = userIdArr
  const checkedCount = userIdArr.length
  checkAll.value = checkedCount > userDataArr.value.length || checkedCount === userDataArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userDataArr.value.length
  if (checkedCount === 0 && userDataArr.value.length === 0) {
    checkAll.value = false
  }
}
const submitForm = () => {
  emit('callback', userData.value)
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.standard-select-person-account {
  width: 990px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-four) var(--zy-distance-two) var(--zy-distance-two) var(--zy-distance-one);

  .standard-select-person-account-body {
    height: calc(100% - (var(--zy-height) + var(--zy-distance-two)));
    display: flex;
    justify-content: space-between;

    .standard-select-person-account-left {
      width: 600px;
      height: 100%;
      display: flex;
      flex-wrap: wrap;

      .standard-select-person-account-input {
        width: 100%;
        display: flex;
        padding-bottom: var(--zy-distance-three);

        .zy-el-input {
          margin-right: var(--zy-distance-two);
        }
      }

      .standard-select-person-account-user-list {
        width: 100%;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);

        .virtual-user {
          height: calc(
            100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + var(--zy-height) + var(--zy-distance-four))
          );
        }

        .standard-select-person-account-user-list-head {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          padding: 0 var(--zy-distance-three);
          padding-top: var(--zy-distance-four);

          .standard-select-person-account-user-list-name {
            font-weight: bold;
            font-size: var(--zy-name-font-size);
            line-height: var(--zy-line-height);
          }

          .zy-el-checkbox {
            height: auto;
          }

          // .standard-select-person-account-user-list-checkbox {
          //   height: 100%;
          //   display: flex;
          //   align-items: flex-end;
          //   flex-direction: column;
          //   justify-content: space-around;
          //   padding: 3px 0;

          //   .zy-el-checkbox {
          //     height: auto;
          //     position: relative;
          //     padding-right: 22px;
          //     margin: 0;

          //     .zy-el-checkbox__input {
          //       position: absolute;
          //       top: 50%;
          //       right: 0;
          //       transform: translateY(-50%);
          //     }
          //   }
          // }
          .zy-el-radio-group {
            width: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .standard-select-person-account-right {
      width: 310px;
      height: 100%;

      .standard-select-person-account-right-name {
        line-height: var(--zy-height);
        padding: var(--zy-distance-three);
        font-size: var(--zy-name-font-size);
        padding-top: 0;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;

        div {
          font-size: var(--zy-text-font-size);
          color: var(--zy-el-text-color-regular);
          font-weight: normal;
          cursor: pointer;
        }
      }
    }
  }

  .standard-select-person-account-button {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);
    padding-right: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
