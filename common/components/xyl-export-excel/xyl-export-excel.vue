<template>
  <div class="xyl-export-excel">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="导出字段" prop="excelHead" class="globalFormTitle">
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
          全选
        </el-checkbox>
        <el-checkbox-group v-model="form.excelHead" @change="handleCheckedCitiesChange">
          <el-checkbox v-for="item in excelHead" :label="item.key" :key="item.key">{{ item.value }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
    <div class="xyl-export-excel-progress" v-if="isShow" @click.stop>
      <div class="progress">
        <el-progress :percentage="percentage"></el-progress>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'XylExportExcel' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { GlobalExportExcel } from 'common/Excel/GlobalExportExcel'
import { ElMessage } from 'element-plus'
const { exportExcel } = GlobalExportExcel()
const props = defineProps({
  name: { type: String, default: '' },
  module: { type: String, default: '' },
  tableId: { type: String, default: '' },
  excelUrl: { type: String, default: '' },
  excelHead: { type: Array, default: () => [] },
  exportId: { type: Array, default: () => [] },
  params: { type: Object, default: () => ({}) },
  handleExcelHead: Function,
  handleExcelData: Function
})
const emit = defineEmits(['excelCallback'])

const formRef = ref()
const form = reactive({
  excelHead: []
})
const rules = reactive({
  excelHead: [{ required: true, message: '请选择导出字段', trigger: ['blur', 'change'] }]
})
const show = ref(false)
const isShow = ref(false)
const percentage = ref(0)
const _setInterval = ref()
const excelHead = ref([])
const excelData = ref([])
const checkAll = ref(false)
const isIndeterminate = ref(true)
onMounted(() => {
  if (props.excelHead.length) {
    form.excelHead = props.excelHead.filter((v) => v.check).map((v) => v.key)
    excelHead.value = props.excelHead.map((v) => ({ key: v.key, value: v.value }))
    props.excelUrl ? globalExportExcelUrlData() : globalExportExcelData()
  } else {
    if (props.tableId || props.module) {
      props.tableId && props.module ? tableHeadData() : globalExportExcelHead()
      props.excelUrl ? globalExportExcelUrlData() : globalExportExcelData()
    }
  }
})
const handleCheckAllChange = (val) => {
  form.excelHead = val ? excelHead.value.map((v) => v.key) : []
  isIndeterminate.value = false
}
const handleCheckedCitiesChange = (value) => {
  const checkedCount = value.length
  checkAll.value = checkedCount === excelHead.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < excelHead.value.length
}
const tableHeadData = async () => {
  const { data } = await api.tableHead(props.tableId)
  let headData = data
  if (typeof props.handleExcelHead === 'function') {
    headData = props.handleExcelHead(data) || data
  }
  form.excelHead = headData.filter((v) => v.isExcel && v.isExcelsel).map((v) => v.javaName)
  excelHead.value = headData.filter((v) => v.isExcel).map((v) => ({ key: v.javaName, value: v.columnComment }))
  handleCheckedCitiesChange(form.excelHead)
}
const globalExportExcelHead = async () => {
  const { data } = await api.globalExportExcelHead(props.module)
  form.excelHead = data.filter((v) => v.checked).map((v) => v.javaName)
  excelHead.value = data.map((v) => ({ key: v.javaName, value: v.chinaName }))
  handleCheckedCitiesChange(form.excelHead)
}
const globalExportExcelData = async () => {
  const { data } = await api.globalExportExcelData(
    props.module,
    props.exportId.length ? { ids: props.exportId } : props.params
  )
  if (typeof props.handleExcelData === 'function') {
    excelData.value = props.handleExcelData(data) || data
  } else {
    excelData.value = data
  }
  show.value = true
}
const globalExportExcelUrlData = async () => {
  const { data } = await api.globalJson(props.excelUrl, props.exportId.length ? { ids: props.exportId } : props.params)
  if (typeof props.handleExcelData === 'function') {
    excelData.value = props.handleExcelData(data) || data
  } else {
    excelData.value = data
  }
  show.value = true
}

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      isShow.value = true
      _setInterval.value = setInterval(() => {
        if (percentage.value >= 90) {
          if (show.value) {
            handleExportExcel()
          }
        } else {
          percentage.value = percentage.value + 9
        }
      }, 200)
    } else {
      ElMessage({ type: 'warning', message: '请选择需要导出的字段！' })
    }
  })
}
const handleExportExcel = () => {
  percentage.value = 100
  exportExcel(
    excelHead.value.filter((v) => form.excelHead.includes(v.key)),
    excelData.value,
    props.name
  )
  clearInterval(_setInterval.value)
  setTimeout(() => {
    show.value = false
    isShow.value = false
    percentage.value = 0
    setTimeout(() => {
      emit('excelCallback')
    }, 222)
  }, 1000)
}
const resetForm = () => {
  emit('excelCallback')
}
</script>
<style lang="scss">
.xyl-export-excel {
  width: 990px;

  .zy-el-checkbox-group {
    width: 100%;

    .zy-el-checkbox {
      width: calc(33.3% - 30px);
    }
  }

  .xyl-export-excel-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;

    .progress {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 280px;
      transform: translate(-50%, -50%);
      z-index: 1000;

      .zy-el-progress__text {
        color: #fff;
      }
    }
  }
}
</style>
