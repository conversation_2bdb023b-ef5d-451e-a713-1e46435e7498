<template>
  <div class="GlobalCommentManageEdit">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="内容" prop="commentContent" class="globalFormTitle">
        <el-input v-model="form.commentContent" type="textarea" placeholder="请输入内容" rows="6" />
      </el-form-item>
      <el-form-item label="附件" class="globalFormTitle">
        <xyl-upload-file :fileData="fileData" @fileUpload="fileUpload" />
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'GlobalCommentManageEdit' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  businessId: '',
  commentContent: '', // 内容
  terminalName: '', // 来源
  commentContent: '' // 内容
})
const rules = reactive({ commentContent: [{ required: true, message: '请输入内容', trigger: ['blur', 'change'] }] })
const fileData = ref([])
onMounted(() => {
  if (props.id) {
    commentInfo()
  }
})

const commentInfo = async () => {
  const res = await api.commentInfo({ detailId: props.id })
  var { data } = res
  form.businessId = data.businessId
  form.commentContent = data.commentContent // 内容
  form.terminalName = data.terminalName // 来源
  fileData.value = data.attachments
}
const fileUpload = (file) => {
  fileData.value = file
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      commentEdit()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const commentEdit = async () => {
  const { code } = await api.commentEdit({
    form: {
      id: props.id,
      businessId: form.businessId,
      commentContent: form.commentContent, // 内容
      terminalName: form.terminalName || 'PC', // 来源
      attachmentIds: fileData.value.map((v) => v.id).join(',')
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.GlobalCommentManageEdit {
  width: 680px;
}
</style>
