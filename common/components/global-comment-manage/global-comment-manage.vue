<template>
  <div class="global-comment-manage">
    <div class="global-comment-manage-left">
      <slot></slot>
    </div>
    <div class="global-comment-manage-right">
      <xyl-search-button
        @queryClick="handleQuery"
        @resetClick="handleReset"
        @handleButton="handleButton"
        :buttonList="buttonList"
        :buttonNumber="props.buttonNumber"
        searchPopover>
        <template #search>
          <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
        </template>
        <template #searchPopover>
          <el-select v-model="checkedStatus" @change="queryChange" placeholder="请选择审核状态" clearable>
            <el-option v-for="item in checkedStatusData" :key="item.key" :label="item.name" :value="item.key" />
          </el-select>
          <el-select v-model="terminalName" @change="queryChange" placeholder="请选择来源" clearable>
            <el-option v-for="item in terminalNameData" :key="item.key" :label="item.name" :value="item.key" />
          </el-select>
        </template>
      </xyl-search-button>
      <div class="globalTable">
        <el-table
          ref="tableRef"
          row-key="id"
          :data="tableData"
          @select="handleTableSelect"
          @select-all="handleTableSelect">
          <el-table-column type="selection" reserve-selection width="60" fixed />
          <el-table-column :label="`${props.text}人`" min-width="180" prop="commentUserName" show-overflow-tooltip />
          <el-table-column
            label=" 手机号码"
            min-width="120"
            prop="commentUserMobile"
            show-overflow-tooltip
            v-if="props.isShowMobile" />
          <el-table-column :label="`${props.text}内容`" min-width="320" prop="commentContent" show-overflow-tooltip />
          <el-table-column label="审核状态" width="120" class-name="globalTableIcon">
            <template #default="scope">
              <el-icon
                :class="[
                  !scope.row.checkedStatus
                    ? 'globalTableClock'
                    : scope.row.checkedStatus === 1
                    ? 'globalTableCheck'
                    : 'globalTableClose'
                ]">
                <Clock v-if="!scope.row.checkedStatus" />
                <CircleCheck v-if="scope.row.checkedStatus === 1" />
                <CircleClose v-if="scope.row.checkedStatus === 2" />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column :label="`${props.text}时间`" width="180">
            <template #default="scope">{{ format(scope.row.createDate) }}</template>
          </el-table-column>
          <el-table-column :label="`${props.text}人身份`" min-width="120">
            <template #default="scope">
              <div v-if="scope.row?.roles && scope.row?.roles.length">{{ scope.row?.roles[0]?.roleName }}</div>
            </template>
          </el-table-column>
          <el-table-column label="来源" width="120">
            <template #default="scope">
              {{ terminalNameObj[scope.row?.terminalName] || scope.row?.terminalName }}
            </template>
          </el-table-column>
          <xyl-global-table-button :data="tableButtonList" @buttonClick="handleCommand"></xyl-global-table-button>
        </el-table>
      </div>
      <div class="globalPagination">
        <el-pagination
          v-model:currentPage="pageNo"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleQuery"
          @current-change="handleQuery"
          :total="totals"
          background />
      </div>
    </div>
    <xyl-popup-window v-model="show" :name="id ? '编辑' : '新增'">
      <global-comment-manage-edit :id="id" @callback="callback"></global-comment-manage-edit>
    </xyl-popup-window>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel
        :name="`${text}管理`"
        :exportId="exportId"
        :params="exportParams"
        :module="props.excelModule"
        @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'GlobalCommentManage' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, watch, defineAsyncComponent } from 'vue'
import { format } from 'common/js/time.js'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { exportWordObj } from 'common/config/MicroGlobal'
import { ElMessage, ElMessageBox } from 'element-plus'
const GlobalCommentManageEdit = defineAsyncComponent(() => import('./global-comment-manage-edit.vue'))
const props = defineProps({
  id: { type: String, default: '' },
  type: { type: String, default: '' },
  text: { type: String, default: '评论' },
  excelModule: { type: String, default: '' },
  exportWord: { type: Object, default: () => ({ wordName: '', titleKey: '', title: '' }) },
  isShowMobile: { type: Boolean, default: false },
  buttonNumber: { type: Number, default: 1 }
})
const buttonList = ref([
  { id: 'exportWord', name: '导出word', type: 'primary', has: 'comment_export_word' },
  { id: 'del', name: '删除', type: 'primary', has: 'comment_del' },
  { id: 'using', name: '审核通过', type: 'primary', has: 'comment_audit_pass' },
  { id: 'stop', name: '审核不通过', type: 'primary', has: 'comment_audit_no_pass' }
])
const tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: 'comment_edit' }]
const id = ref('')
const show = ref(false)
const checkedStatus = ref('')
const checkedStatusData = ref([])
const terminalName = ref('')
const terminalNameData = ref([])
const terminalNameObj = ref({})
const {
  keyword,
  tableRef,
  totals,
  pageNo,
  pageSize,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleTableSelect,
  handleDel,
  tableRefReset,
  tableQuery,
  handleExportExcel
} = GlobalTable({
  tableApi: 'twoLevelTreeManage',
  delApi: 'commentDel',
  tableDataObj: { businessCode: props.type, businessId: props.id, isSelectForManage: '1' }
})

onMounted(() => {
  handleQuery()
  dictionaryData()
})

const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['audit_statute', 'terminal_name'] })
  var { data } = res
  checkedStatusData.value = data.audit_statute
  terminalNameData.value = data.terminal_name
  var terminal_name_obj = {}
  for (let index = 0; index < data.terminal_name.length; index++) {
    const item = data.terminal_name[index]
    terminal_name_obj[item.key] = item.name
  }
  terminalNameObj.value = terminal_name_obj
}
const handleButton = (isType) => {
  switch (isType) {
    case 'exportWord':
      handleExportWord()
      break
    case 'export':
      handleExportExcel()
      break
    case 'using':
      if (tableDataArray.value.length) {
        ElMessageBox.confirm(`此操作将当前选中的${props.text}审核通过, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            commentCheckPass()
          })
          .catch(() => {
            ElMessage({ type: 'info', message: '已取消操作' })
          })
      } else {
        ElMessage({ type: 'warning', message: '请至少选择一条数据' })
      }
      break
    case 'stop':
      if (tableDataArray.value.length) {
        ElMessageBox.confirm(`此操作将当前选中的${props.text}审核不通过, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            commentCheckNoPass()
          })
          .catch(() => {
            ElMessage({ type: 'info', message: '已取消操作' })
          })
      } else {
        ElMessage({ type: 'warning', message: '请至少选择一条数据' })
      }
      break
    case 'del':
      handleDel(props.text)
      break
    default:
      break
  }
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    default:
      break
  }
}
const queryChange = () => {
  tableQuery.value = {
    businessCode: props.type,
    businessId: props.id,
    checkedStatus: checkedStatus.value || null,
    terminalName: terminalName.value || null
  }
}
const handleReset = () => {
  keyword.value = ''
  checkedStatus.value = ''
  terminalName.value = ''
  tableQuery.value = {
    businessCode: props.type,
    businessId: props.id,
    checkedStatus: checkedStatus.value || null,
    terminalName: terminalName.value || null
  }
  handleQuery()
}
// const handleNew = () => {
//   id.value = ''
//   show.value = true
// }
const handleEdit = (item) => {
  id.value = item.id
  show.value = true
}
const callback = () => {
  tableRefReset()
  handleQuery()
  show.value = false
  exportShow.value = false
}
const handleExportWord = async () => {
  const { data } = await api.twoLevelTreeManage({
    pageNo: 1,
    pageSize: 99999,
    businessCode: props.type,
    businessId: props.id,
    isSelectForManage: '1'
  })
  let newTableData = []
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    newTableData.push({
      userName: item.commentUserName,
      content: item.commentContent,
      children: item.children.map((v) => ({
        chilName: v.commentUserName + (v.parentId !== item.id ? `回复${v.toCommenter}` : ''),
        chilContent: v.commentContent
      }))
    })
  }
  const wordObj = {
    titleShow: props?.exportWord?.titleKey && props?.exportWord?.title ? true : false,
    titleKey: props?.exportWord?.titleKey || '',
    title: props?.exportWord?.title || '',
    data: newTableData
  }
  exportWordObj({ code: 'comment', name: props?.exportWord?.wordName || `${props.text}导出`, data: wordObj })
}
const commentCheckPass = async () => {
  const { code } = await api.commentCheckPass({ ids: tableDataArray.value.map((v) => v.id) })
  if (code === 200) {
    ElMessage({ type: 'success', message: '审核通过成功' })
    tableRefReset()
    handleQuery()
  }
}
const commentCheckNoPass = async () => {
  const { code } = await api.commentCheckNoPass({ ids: tableDataArray.value.map((v) => v.id) })
  if (code === 200) {
    ElMessage({ type: 'success', message: '审核不通过成功' })
    tableRefReset()
    handleQuery()
  }
}
watch(
  () => props.excelModule,
  () => {
    buttonList.value = props.excelModule
      ? [
          { id: 'exportWord', name: '导出word', type: 'primary', has: 'comment_export_word' },
          { id: 'export', name: '导出Excel', type: 'primary', has: 'comment_export' },
          { id: 'del', name: '删除', type: 'primary', has: 'comment_del' },
          { id: 'using', name: '审核通过', type: 'primary', has: 'comment_audit_pass' },
          { id: 'stop', name: '审核不通过', type: 'primary', has: 'comment_audit_no_pass' }
        ]
      : [
          { id: 'exportWord', name: '导出word', type: 'primary', has: 'comment_export_word' },
          { id: 'del', name: '删除', type: 'primary', has: 'comment_del' },
          { id: 'using', name: '审核通过', type: 'primary', has: 'comment_audit_pass' },
          { id: 'stop', name: '审核不通过', type: 'primary', has: 'comment_audit_no_pass' }
        ]
  },
  { immediate: true }
)
</script>
<style lang="scss">
.global-comment-manage {
  width: 100%;
  height: 100%;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;

  .global-comment-manage-left {
    width: 42%;
    height: 100%;
    padding: var(--zy-distance-four) 0;
  }

  .global-comment-manage-right {
    width: calc(58% - 20px);
    height: 100%;

    .xyl-search-button {
      .xyl-button {
        width: calc(100% - 380px);
      }

      .xyl-search {
        width: 380px;

        .zy-el-select {
          margin-left: var(--zy-distance-two);
        }
      }
    }

    .globalTable {
      width: 100%;
      height: calc(100% - 116px);
    }
  }
}
</style>
