<template>
  <div class="trans-regional-set">
    <div class="trans-regional-set-title">跨地区设置</div>
    <el-radio-group v-model="type" @change="typeChange">
      <el-radio v-for="item in typeData" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
    </el-radio-group>
    <template v-if="type === '2'">
      <business-select-region
        v-model="regionData"
        :levelType="props.levelType"
        @callback="regionCallback"></business-select-region>
    </template>
    <template v-if="type === '3'">
      <business-region-select-person
        v-model="userData"
        :levelType="props.levelType"
        @callback="userCallback"></business-region-select-person>
    </template>
  </div>
</template>
<script>
export default { name: 'TransRegionalSet' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted } from 'vue'
const props = defineProps({
  modelValue: [String, Number],
  userData: { type: Array, default: () => [] },
  regionData: { type: Array, default: () => [] },
  levelType: { type: String, default: 'all' }
})
const emit = defineEmits([
  'update:modelValue',
  'update:userData',
  'change',
  'regionCallback',
  'userCallback',
  'isPropCallback'
])

const type = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const typeData = ref([])
const userData = computed({
  get() {
    return props.userData
  },
  set(value) {
    emit('update:userData', value)
  }
})
const regionData = computed({
  get() {
    return props.regionData
  },
  set(value) {
    emit('update:regionData', value)
  }
})
const regionListData = ref([])
const userListData = ref([])

onMounted(() => {
  dictionaryData()
})

const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['cross_type_person'] })
  var { data } = res
  type.value = type.value ? type.value : data.cross_type_person[0].key
  typeData.value = data.cross_type_person
}
const typeChange = () => {
  emit('change')
  isProp()
}
const userCallback = (data) => {
  if (data) {
    userListData.value = data
  }
  emit('userCallback', data)
  isProp()
}
const regionCallback = (data) => {
  if (data) {
    regionListData.value = data
  }
  emit('regionCallback', data)
  isProp()
}
const isProp = () => {
  if (type.value === '2') {
    emit('isPropCallback', regionListData.value.length)
  } else if (type.value === '3') {
    emit('isPropCallback', userListData.value.length)
  } else {
    emit('isPropCallback', 1)
  }
}
</script>
<style lang="scss">
.trans-regional-set {
  width: 990px;
  padding: var(--zy-distance-two) var(--zy-distance-one);
  margin: auto;
  border: 1px solid var(--zy-el-border-color-lighter);
  position: relative;

  .trans-regional-set-title {
    position: absolute;
    top: 0;
    left: 24px;
    transform: translateY(-50%);
    padding: 0 var(--zy-distance-three);
    height: var(--zy-height);
    line-height: var(--zy-line-height);
    display: flex;
    align-items: center;
    background-color: #fff;
  }

  .zy-el-radio-group {
    margin-bottom: var(--zy-distance-four);

    .zy-el-radio {
      height: var(--zy-height);
    }
  }
}
</style>
