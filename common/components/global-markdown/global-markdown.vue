<template>
  <div class="global-markdown" ref="elRef" @click="handleClick"></div>
</template>
<script>
export default { name: 'GlobalMarkdown' }
</script>
<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import markdownIt from './markdown-it'
import { deepCloneAndUpdate, buildCodeBlock } from './code-block.js'

const props = defineProps({
  modelValue: { type: String, default: '' },
  content: { type: String, default: '' },
  onLinkClick: { type: Function, default: null }
})

const emit = defineEmits(['update:modelValue', 'update'])
const elRef = ref()
const htmlValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const htmlData = ref('')
let isRendering = false
const renderQueue = ref([])
let renderTimeout = null

/** 检查元素是否有效 */
const isValidElement = () => {
  return elRef.value && elRef.value.isConnected
}

/** Step 4. 渲染markdown的 HTML Element. */
const renderMarkdown = (data) => {
  try {
    if (!isValidElement()) return

    const tmpDiv = document.createElement('div')
    tmpDiv.innerHTML = markdownIt.render(data)
    buildCodeBlock(tmpDiv)
    deepCloneAndUpdate(elRef.value, tmpDiv)
  } catch (error) {
    console.error('Error rendering markdown:', error)
  }
}

/** Step 3. 处理异步渲染 */
const processRenderQueue = () => {
  if (!isValidElement() || renderQueue.value.length === 0) {
    isRendering = false
    htmlValue.value = htmlData.value
    return
  }

  const data = renderQueue.value.shift()
  renderMarkdown(data)

  // 使用 requestAnimationFrame 替代 setTimeout 以获得更好的性能
  renderTimeout = requestAnimationFrame(() => {
    processRenderQueue()
  })
}

/** Step 2. 异步队列控制渲染 */
const enqueueRender = (data) => {
  if (!isValidElement()) return

  htmlData.value += data
  renderQueue.value.push(htmlData.value)

  if (!isRendering) {
    isRendering = true
    processRenderQueue()
  }
}

/** Step 1. 清空内容 */
const clearContent = () => {
  htmlData.value = ''
  htmlValue.value = ''
  elRef.value.innerHTML = ''
}

// 监听内容变化
watch(
  () => props.content,
  () => {
    nextTick(() => {
      if (!isValidElement()) return

      elRef.value.innerHTML = ''
      htmlData.value = props.content
      htmlValue.value = props.content

      if (props.content) {
        emit('update')
        renderMarkdown(props.content)
      }
    })
  },
  { immediate: true }
)

const handleClick = (e) => {
  const target = e.target
  if (target.tagName.toLowerCase() === 'a') {
    if (props.onLinkClick) {
      e.preventDefault()
      e.stopPropagation()
      props.onLinkClick({
        href: target.href,
        text: target.textContent,
        event: e
      })
    }
  }
}

// 组件挂载时的初始化
onMounted(() => {
  if (props.content) {
    emit('update')
    renderMarkdown(props.content)
  }
})

// 组件卸载时的清理
onUnmounted(() => {
  if (renderTimeout) {
    cancelAnimationFrame(renderTimeout)
  }
  renderQueue.value = []
  isRendering = false
})

defineExpose({ elRef, enqueueRender, clearContent })
</script>
<style lang="scss">
@import './index.scss';

.global-markdown {
  width: 100%;
  background: #fff;
  line-height: var(--zy-line-height);
  font-size: var(--zy-text-font-size);

  img {
    display: block;
    margin: 15px auto 15px;
    border-radius: 6px;
    width: 100%;
    cursor: pointer;
    cursor: zoom-in;
    box-shadow: 0 1px 15px rgba(27, 31, 35, 0.15), 0 0 1px rgba(106, 115, 125, 0.35);
  }

  h1 code,
  h2 code,
  h3 code,
  h4 code,
  h5 code,
  h6 code,
  p > code,
  li > code,
  table code {
    color: #c7254e;
    font-family: consolas !important;
    vertical-align: middle;
    margin: 0 3px;
    background-color: #f9f2f4 !important;
    line-height: var(--zy-line-height);
    font-size: var(--zy-text-font-size);
    padding: 0.2em 0.3em !important;
    border-radius: 3px !important;
    border: 1px solid #f9f2f4 !important;
  }

  p {
    color: var(--text-color);
    line-height: var(--zy-line-height);
    font-size: var(--zy-text-font-size);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    overflow: hidden;
    -webkit-line-clamp: 4;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    color: #1f2d3d;
    transition: all 0.2s ease-out;
  }

  h4,
  h5,
  h6 {
    font-size: 16px;
  }

  h1 {
    font-size: 26px;
    margin: 10px 0;
  }

  h2 {
    font-size: 22px;
  }

  h3 {
    font-size: 18px;
  }

  /* 代码样式 */
  pre {
    padding: 12px;
    white-space: pre;
    position: relative;
    border-radius: 7px;
    color: #bababa;
    background-color: #282c34;
    font-size: 14px;
    margin: 3px 0;

    code {
      border: none;
      line-height: 21px;
      border-radius: 7px;
      font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace !important;
      white-space: pre-wrap;
    }
  }

  kbd {
    background: #f7f7f7;
    color: #222325;
    border-radius: 0.25rem;
    border: 1px solid #cbcccd;
    box-shadow: 0 2px 0 1px #cbcccd;
    cursor: default;
    font-family: Arial, sans-serif;
    font-size: 0.75em;
    line-height: 1;
    min-width: 0.75rem;
    padding: 2px 5px;
    position: relative;
    top: -1px;

    &:hover {
      box-shadow: 0 1px 0 0.5px #cbcccd;
      top: 1px;
    }
  }

  a {
    color: #2d8cf0;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;

    &::after {
      content: '';
      display: block;
      width: 0;
      height: 1px;
      position: absolute;
      left: 0;
      bottom: -2px;
      background: #2d8cf0;
      transition: all 0.3s ease-in-out;
    }

    &:hover::after {
      width: 100%;
    }
  }

  hr {
    position: relative;
    margin: 20px 0;
    border: 2px dashed #bfe4fb;
    width: 100%;
    box-sizing: content-box;
    height: 0;
    overflow: visible;
    box-sizing: border-box;
  }

  hr::before {
    position: absolute;
    top: -11px;
    left: 2%;
    z-index: 1;
    color: #bfe4fb;
    content: '✂';
    font-size: 21px;
    line-height: 1;
    -webkit-transition: all 1s ease-in-out;
    -moz-transition: all 1s ease-in-out;
    -o-transition: all 1s ease-in-out;
    -ms-transition: all 1s ease-in-out;
    transition: all 1s ease-in-out;
  }

  hr:hover::before {
    left: calc(98% - 20px);
  }

  table {
    font-size: 15px;
    width: 100%;
    margin: 15px 0px;
    display: block;
    overflow-x: auto;
    border: none;
    border-collapse: collapse;
    border-spacing: 0;

    &::-webkit-scrollbar {
      height: 4px !important;
    }

    th {
      background: #bfe4fb;
      border: 1px solid #a6d6f5;
      white-space: nowrap;
      font-weight: 400;
      padding: 6px 15px;
      min-width: 100px;
    }

    td {
      border: 1px solid #a6d6f5;
      padding: 6px 15px;
      min-width: 100px;
    }
  }

  ul,
  ol {
    padding-left: 2em;

    li {
      margin: 4px 0px;
    }
  }

  ul li {
    list-style: circle;

    &::marker {
      transition: all 0.4s;
      /* color: #49b1f5; */
      color: var(--theme-color);
      font-weight: 600;
      font-size: 1.05em;
    }

    &:hover::marker {
      color: #ff7242;
    }
  }

  blockquote {
    border: none;
    margin: 15px 0px;
    color: inherit;
    border-radius: 4px;
    padding: 1px 15px;
    border-left: 4px solid var(--theme-color);
    background: #f8f8f8;
  }
}
</style>
