import XylDatePicker from './xyl-date-picker/xyl-date-picker.vue'

import XylTag from './xyl-tag/xyl-tag.vue'
import XylTagItem from './xyl-tag/xyl-tag-item.vue'
import XylLabel from './xyl-label/xyl-label.vue'
import XylLabelItem from './xyl-label/xyl-label-item.vue'
import GlobalInfo from './global-info/global-info.vue' // 格子组件
import GlobalInfoLine from './global-info/global-info-line.vue'
import GlobalInfoItem from './global-info/global-info-item.vue'
import AnchorLocation from './anchor-location/anchor-location'
import AnchorLocationItem from './anchor-location/anchor-location-item'
import IntelligentAssistant from './intelligent-assistant/intelligent-assistant.vue'
import WronglyWrittenCharacters from './wrongly-written-characters/wrongly-written-characters.vue'
import GlobalCountdown from './global-countdown/global-countdown.vue'
import XylUploadImg from './xyl-upload-img/xyl-upload-img.vue' // 图片上传
import XylUploadFile from './xyl-upload-file/xyl-upload-file.vue' // 文件上传
import XylGlobalFile from './xyl-global-file/xyl-global-file.vue' // 附件集合展示
import XylPopupWindow from './xyl-popup-window/xyl-popup-window.vue' // 弹窗
import XylSlideVerify from './xyl-slide-verify/xyl-slide-verify.vue' // 滑动验证
import XylGlobalTable from './xyl-global-table/xyl-global-table.vue' // 自定义表格
import XylGlobalTableButton from './xyl-global-table/xyl-global-table-button.vue' // 自定义表格按钮
import XylSearchButton from './xyl-search-button/xyl-search-button.vue' // 筛选按钮区域
import SlidingContainer from './sliding-container/sliding-container.vue' // 滑动容器
import XylSearchHighlight from './xyl-search-highlight/xyl-search-highlight.vue' // 关键字搜索高亮
import TextExpansionCollapse from './text-expansion-collapse/text-expansion-collapse.vue' // 文字过多展开收起
import XylExportExcel from './xyl-export-excel/xyl-export-excel.vue' // Excel导出
import XylImportExcel from './xyl-import-excel/xyl-import-excel.vue' // Excel导入
import UserVerify from './user-verify/user-verify.vue' // 用户查询
import EditRecord from './edit-record/edit-record.vue' // 编辑记录
import ShortMessageBox from './short-message-box/short-message-box.vue' // 表单短信模板组件
import ShortMessageForm from './short-message-form/short-message-form.vue' // 弹窗短信模板组件
import XylGlobalComment from './xyl-global-comment/xyl-global-comment.vue' // 评论组件
import GlobalCommentManage from './global-comment-manage/global-comment-manage.vue' // 评论管理组件
import GlobalDynamicTitle from './global-dynamic-title/global-dynamic-title.vue' // 标题文本组件
import TransferCloudDisk from './transfer-cloud-disk/transfer-cloud-disk.vue' // 转存云盘
import GlobalCollection from './global-collection/global-collection.vue' // 收藏
import XylStatisticsLabel from './xyl-statistics-label/xyl-statistics-label.vue' // 统计维度
import XylStatisticsLabelItem from './xyl-statistics-label/xyl-statistics-label-item.vue' // 统计维度

import XylTypeTree from './xyl-type-tree/xyl-type-tree.vue'
import XylGlobalTree from './xyl-global-tree/xyl-global-tree.vue'
import PublicLogin from './public-login/public-login.vue'
import VisitorLogin from './visitor-login/visitor-login.vue'

import SelectUnit from './select-unit/select-unit.vue'
import RegionSelectUnit from './region-select-unit/region-select-unit.vue'
import SelectPerson from './select-person/select-person.vue'
import InputSelectPerson from './input-select-person/input-select-person.vue'
import BusinessSelectPerson from './business-select-person/business-select-person.vue'
import RegionSelectPerson from './region-select-person/region-select-person.vue'
import BusinessRegionSelectPerson from './business-region-select-person/business-region-select-person.vue'
import SelectPersonAccount from './select-person-account/select-person-account.vue'
import RegionSelectPersonAccount from './region-select-person-account/region-select-person-account.vue'
import StandardSelectPersonAccount from './standard-select-person-account/standard-select-person-account.vue'
import InputSelectPersonAccount from './input-select-person-account/input-select-person-account.vue'
import BusinessSelectPersonAccount from './business-select-person-account/business-select-person-account.vue'

import SelectRegion from './select-region/select-region.vue'
import BusinessSelectRegion from './business-select-region/business-select-region.vue'

import SelectRole from './select-role/select-role.vue'
import BusinessSelectRole from './business-select-role/business-select-role.vue'

import VisibleRange from './visible-range/visible-range.vue'
import TransRegionalSet from './trans-regional-set/trans-regional-set.vue'
import GlobalTag from './global-tag/global-tag.vue'
import SimpleSelectPerson from './simple-select-person/simple-select-person.vue'
import AuroralPush from './auroral-push/auroral-push.vue'
const components = [
  XylDatePicker,
  XylTag,
  XylTagItem,
  XylLabel,
  XylLabelItem,
  SelectPerson,
  SelectUnit,
  RegionSelectUnit,
  GlobalCountdown,
  XylUploadImg,
  XylUploadFile,
  XylPopupWindow,
  XylGlobalTable,
  XylGlobalTableButton,
  XylSearchButton,
  XylSearchHighlight,
  XylTypeTree,
  XylGlobalTree,
  InputSelectPerson,
  BusinessSelectPerson,
  XylSlideVerify,
  XylGlobalFile,
  XylExportExcel,
  XylImportExcel,
  SelectRole,
  BusinessSelectRole,
  RegionSelectPerson,
  BusinessRegionSelectPerson,
  SelectPersonAccount,
  RegionSelectPersonAccount,
  StandardSelectPersonAccount,
  InputSelectPersonAccount,
  BusinessSelectPersonAccount,
  SelectRegion,
  BusinessSelectRegion,
  TransRegionalSet,
  VisibleRange,
  EditRecord,
  TransferCloudDisk,
  ShortMessageBox,
  ShortMessageForm,
  TextExpansionCollapse,
  XylGlobalComment,
  GlobalCommentManage,
  PublicLogin,
  VisitorLogin,
  UserVerify,
  GlobalInfo,
  GlobalInfoLine,
  GlobalInfoItem,
  AnchorLocation,
  AnchorLocationItem,
  IntelligentAssistant,
  WronglyWrittenCharacters,
  GlobalDynamicTitle,
  SlidingContainer,
  GlobalCollection,
  GlobalTag,
  SimpleSelectPerson,
  XylStatisticsLabel,
  XylStatisticsLabelItem,
  AuroralPush
]
export default {
  install (app) {
    components.forEach(v => app.component(v.name, v))
  }
}
