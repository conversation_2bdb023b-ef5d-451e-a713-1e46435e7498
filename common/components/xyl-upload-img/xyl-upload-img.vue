<template>
  <div class="xyl-upload-img">
    <template v-if="props.max !== 1">
      <div
        class="xyl-upload-img-item"
        :style="{ width: props.width, height: props.height }"
        v-for="(item, index) in fileSucceed"
        :key="item.id">
        <el-image :src="item.fileURL" :preview-src-list="imgList" :initial-index="index" fit="cover" />
        <span class="xyl-upload-img-actions" v-if="!disabled">
          <span class="xyl-upload-img-delete" @click="handleDel(item)">
            <el-icon>
              <CircleClose />
            </el-icon>
          </span>
        </span>
      </div>
      <div
        class="xyl-upload-img-item"
        :style="{ width: props.width, height: props.height }"
        v-for="item in fileArrAy"
        :key="item.uid">
        <el-progress type="circle" :width="progressWidth" :percentage="item.progress" />
      </div>
    </template>
    <template v-if="props.max === 1 && fileObj.uid">
      <div class="xyl-upload-img-item" :style="{ width: props.width, height: props.height }">
        <el-image :src="fileObj.fileURL" v-if="fileObj.fileURL" fit="cover" />
        <el-progress type="circle" :width="progressWidth" v-if="!fileObj.fileURL" :percentage="fileObj.progress" />
        <span class="xyl-upload-img-actions" v-if="fileObj.fileURL && !disabled">
          <span class="xyl-upload-img-delete" @click="handleDel">
            <el-icon>
              <CircleClose />
            </el-icon>
          </span>
        </span>
      </div>
    </template>
    <el-upload
      action="/"
      class="xyl-upload"
      :class="{ 'zy-is-upload-disabled': disabled }"
      :before-upload="handleImg"
      :http-request="imgUpload"
      :show-file-list="false"
      :multiple="props.max !== 1"
      :disabled="disabled"
      v-if="(props.max !== 1 || !fileObj.uid) && (!disabled || !fileObj.id || !fileSucceed.length)"
      :style="{ width: props.width, height: props.height }">
      <el-icon>
        <Plus />
      </el-icon>
    </el-upload>
  </div>
</template>
<script>
export default { name: 'XylUploadImg' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({
  apiName: { type: String, default: 'globalUpload' }, // 上传的api
  compress: { type: Object, default: () => ({}) },
  max: { type: Number, default: 0 },
  fileId: { type: String, default: '' },
  fileData: { type: Array, default: () => [] },
  fileType: { type: Array, default: () => ['jpg', 'png', 'gif', 'jpeg'] },
  width: { type: String, default: '160px' },
  height: { type: String, default: '160px' },
  progressWidth: { type: Number, default: 120 },
  disabled: { type: Boolean, default: false },
  beforeUpload: Function
})
const emit = defineEmits(['fileUpload', 'isAllSucceed'])
const disabled = computed(() => props.disabled)
const fileObj = ref({})
const fileArrAy = ref([])
const fileSucceed = ref([])
const imgList = ref([])

const defaultFile = () => {
  if (props.max === 1) {
    if (props.fileId) {
      fileArrAy.value.push({ uid: guid(), id: props.fileId, progress: 100 })
      fileObj.value = {
        uid: guid(),
        id: props.fileId,
        progress: 100,
        fileURL: api.fileURL(props.fileId),
        newFileName: props.fileId
      }
      imgList.value = [fileObj.value.fileURL]
    }
  } else {
    fileSucceed.value = props.fileData.map((item) => ({
      uid: guid(),
      id: item,
      progress: 100,
      fileURL: api.fileURL(item),
      newFileName: item
    }))
    imgList.value = fileSucceed.value.map((v) => v.fileURL)
  }
}
const handleImg = (file) => {
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
  const isShow = props.fileType.includes(fileType)
  if (!isShow) {
    ElMessage({ type: 'warning', message: `仅支持${props.fileType.join('、')}格式!` })
  }
  return isShow
}
const imgUpload = (file) => {
  if (props.max && fileSucceed.value.length + fileArrAy.value.length >= props.max) {
    ElMessage({ type: 'warning', message: `最多只能上传${props.max}个文件` })
    return false
  }
  if (typeof props.beforeUpload === 'function') {
    props.beforeUpload(file.file, (fileItem) => {
      var fileSize = parseFloat(parseInt(fileItem['size']) / 1024 / 1024).toFixed(2)
      var fileScale = compressSize(fileSize)
      if (fileScale === 1) {
        const param = new FormData()
        param.append('file', fileItem)
        globalUpload(param, guid())
      } else {
        compressImg(fileItem, fileScale)
      }
    })
  } else {
    var fileSize = parseFloat(parseInt(file.file['size']) / 1024 / 1024).toFixed(2)
    var fileScale = compressSize(fileSize)
    if (fileScale === 1) {
      const param = new FormData()
      param.append('file', file.file)
      globalUpload(param, guid())
    } else {
      compressImg(file.file, fileScale)
    }
  }
}
const compressSize = (size) => {
  var scale = 1
  var maxSize = 0
  for (const key in props.compress) {
    if (Object.hasOwnProperty.call(props.compress, key)) {
      if (Number(key) < size && maxSize < Number(key)) {
        scale = props.compress[key]
        maxSize = Number(key)
      }
    }
  }
  return scale
}
function compressImg(file, fileScale) {
  var read = new FileReader()
  read.readAsDataURL(file)
  read.onload = function (e) {
    var img = new Image()
    img.src = e.target.result
    img.onload = () => {
      const width = (img.width * fileScale).toFixed()
      const height = (img.height * fileScale).toFixed()
      const param = new FormData()
      param.append('file', file)
      param.append('imgCompressionPxs', `${width}x${height}`)
      globalUpload(param, guid(), `${width}x${height}`)
    }
  }
}

const globalUpload = async (params, uid, size) => {
  if (props.max === 1) {
    fileObj.value = { uid, progress: 0 }
    emit('isAllSucceed', false)
  } else {
    fileArrAy.value.push({ uid, progress: 0 })
    emit('isAllSucceed', !fileArrAy.value.length)
  }
  const res = await api[props.apiName](params, onUploadProgress, uid)
  var { data } = res
  const newFileName = size ? `${size}-compress-${data.newFileName}` : data.newFileName
  if (props.max === 1) {
    fileObj.value = { ...fileObj.value, ...data, newFileName: newFileName, fileURL: api.fileURL(newFileName) }
    emit('fileUpload', fileObj.value)
    emit('isAllSucceed', true)
  } else {
    fileArrAy.value = fileArrAy.value.filter((item) => item.uid !== uid)
    emit('fileUpload', [
      ...fileSucceed.value,
      { ...data, uid: uid, newFileName: newFileName, fileURL: api.fileURL(newFileName), progress: 100 }
    ])
    emit('isAllSucceed', !fileArrAy.value.length)
  }
}
const onUploadProgress = (progressEvent, uid) => {
  if (progressEvent?.event?.lengthComputable) {
    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)
    if (props.max === 1) {
      fileObj.value.progress = parseInt(progress)
    } else {
      fileArrAy.value.forEach((item) => {
        if (item.uid === uid) {
          item.progress = parseInt(progress)
        }
      })
    }
  }
}
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const handleDel = (file) => {
  if (props.max === 1) {
    emit('fileUpload', {})
  } else {
    emit(
      'fileUpload',
      fileSucceed.value.filter((item) => item.uid !== file.uid)
    )
  }
}
watch(
  () => props.fileId,
  (val) => {
    if (val.length) {
      defaultFile()
    } else {
      fileObj.value = {}
      fileArrAy.value = []
      imgList.value = []
    }
  },
  { immediate: true }
)
watch(
  () => props.fileData,
  (val) => {
    if (val.length) {
      defaultFile()
    } else {
      fileSucceed.value = []
      imgList.value = []
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.xyl-upload-img {
  display: flex;
  flex-wrap: wrap;

  .xyl-upload-img-item {
    width: 160px;
    height: 160px;
    margin: calc(var(--zy-distance-two) / 2) 0;
    margin-right: var(--zy-distance-two);
    position: relative;
    box-shadow: 0 0 0 1px var(--zy-el-border-color-darker) inset;
    overflow: hidden;

    .zy-el-image {
      width: 100%;
      height: 100%;
    }

    .zy-el-progress {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .xyl-upload-img-actions {
      position: absolute;
      top: 0;
      right: 0;
      display: inline-flex;
      right: -23px;
      top: -8px;
      width: 62px;
      height: 36px;
      justify-content: center;
      align-items: center;
      background: var(--zy-el-color-success);
      transform: rotate(45deg);
      color: #fff;

      span {
        font-size: 18px;
        margin-top: 11px;
        transform: rotate(-45deg);
        cursor: pointer;
      }
    }
  }

  .xyl-upload {
    width: 160px;
    height: 160px;
    margin: calc(var(--zy-distance-two) / 2) 0;
    margin-right: var(--zy-distance-two);

    .zy-el-upload {
      width: 100%;
      height: 100%;
      border: 1px dashed var(--zy-el-border-color-darker);
      position: relative;

      &:hover {
        border-color: var(--zy-el-color-primary);
      }

      & > i {
        font-size: 28px;
        color: var(--zy-el-text-color-secondary);
      }
    }
  }

  .zy-is-upload-disabled {
    cursor: not-allowed;
    background-color: var(--el-disabled-bg-color);

    .zy-el-upload {
      cursor: not-allowed;

      &:hover {
        border: 1px dashed var(--zy-el-border-color-darker);
      }
    }
  }
}
</style>
