<template>
  <div class="global-cropper forbidSelect">
    <div class="global-cropper-body">
      <div class="global-cropper-left">
        <div class="global-element-cropper" :style="getCropperStyle">
          <vueCropper
            ref="cropperRef"
            :img="cropperImgUrl"
            auto-crop
            fixed-box
            :info="false"
            outputType="jpeg"
            :enlarge="props.enlarge"
            :autoCropWidth="props.width"
            :autoCropHeight="props.height"
            :outputSize="2"
            @real-time="previewHandle" />
        </div>
        <div class="global-cropper-controls">
          <el-icon class="rotate_right" @click="rotateRight">
            <RefreshRight />
          </el-icon>
          <el-icon class="rotate_right" @click="changeScale(1)">
            <CirclePlus />
          </el-icon>
          <el-icon class="rotate_right" @click="changeScale(-1)">
            <Remove />
          </el-icon>
        </div>
      </div>
      <div class="global-cropper-right">
        <div class="global-cropper-preview" :style="getStyle">
          <img :style="previews.img" :src="previews.url" />
        </div>
      </div>
    </div>
    <div class="global-cropper-button">
      <el-button type="primary" @click="handleConfirm">确认</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
const props = defineProps({
  file: { type: Object, default: () => ({}) },
  enlarge: { type: Number, default: 1 },
  width: { type: Number, default: 144 },
  height: { type: Number, default: 192 },
  cropperStyle: { type: Object, default: () => ({ width: '460px', height: '360px' }) }
})
const emit = defineEmits(['callback'])
const getCropperStyle = computed(() => props.cropperStyle)
const getStyle = computed(() => ({ width: `${props.width}px`, height: `${props.height}px` }))
const cropperRef = ref()
const outputType = ref('')
const cropperImgUrl = ref('')
const previews = ref({})

onMounted(() => {
  const fileType = props.file.name.substring(props.file.name.lastIndexOf('.') + 1).toLowerCase()
  if (['jpg', 'jpeg'].includes(fileType)) {
    outputType.value = 'jpeg'
  } else {
    outputType.value = fileType
  }
  const URL = window.URL || window.webkitURL
  cropperImgUrl.value = URL.createObjectURL(props.file)
})
onUnmounted(() => {
  const URL = window.URL || window.webkitURL
  URL.revokeObjectURL(cropperImgUrl.value)
})
/* 放大缩小图片比例 */
const changeScale = (num) => {
  const scale = num || 1
  cropperRef.value.changeScale(scale)
}
/* 右旋转图片 */
const rotateRight = () => {
  cropperRef.value.rotateRight()
}
const previewHandle = (data) => {
  previews.value = data
}
// base64转图片文件
const dataURLtoFile = (dataUrl, filename) => {
  const arr = dataUrl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let len = bstr.length
  const u8arr = new Uint8Array(len)
  while (len--) {
    u8arr[len] = bstr.charCodeAt(len)
  }
  return new File([u8arr], filename, { type: mime })
}
const handleConfirm = () => {
  cropperRef.value.getCropData(async (data) => {
    const dataFile = dataURLtoFile(data, props.file.name)
    emit('callback', dataFile)
  })
}
const handleCancel = () => {
  emit('callback')
}
</script>
<style lang="scss">
.global-cropper {
  height: 460px;
  padding: 20px;

  .global-cropper-body {
    width: 100%;
    height: 380px;
    display: flex;
    justify-content: space-between;

    .global-cropper-left {
      height: 360px;
      display: flex;

      .global-element-cropper {
        .vue-cropper {
          width: 100%;
          height: 100%;
        }
      }

      .global-cropper-controls {
        width: 38px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        padding-left: 6px;

        .zy-el-icon {
          font-size: 22px;
          margin-top: 12px;
          cursor: pointer;
        }
      }
    }

    .global-cropper-right {
      .global-cropper-preview {
        overflow: hidden;
        border-radius: 2px;
        border: 1px solid #e8e8e8;
      }
    }
  }

  .global-cropper-button {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: flex-end;
    justify-content: center;

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
