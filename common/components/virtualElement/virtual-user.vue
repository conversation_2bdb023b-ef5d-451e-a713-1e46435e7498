<template>
  <div class="virtual-user" ref="virtualList">
    <div class="virtual-placeholder" ref="virtualPlaceholder">
      <div class="virtual-user-item">
        <div class="virtual-user-name ellipsis">占位</div>
      </div>
    </div>
    <el-scrollbar @scroll="handleScroll" ref="virtualScrollbar">
      <!-- 虚拟高度 -->
      <div class="virtualBody" :style="{ height: virtualRecord.virtualHeight + 'px' }"></div>
      <!-- 真实列表 -->
      <el-checkbox-group
        v-model="userId"
        :class="['realBody']"
        :style="{ transform: `translateY(${virtualRecord.offset}px)` }"
        @change="handleChange">
        <div
          class="virtual-user-item"
          v-for="(item, index) in virtualRecord.visibleData"
          :key="index + '_virtual-user'"
          @click="handleClick(item)">
          <div class="virtual-user-name ellipsis" :title="`${item.userName} - ${handleMobile(item.mobile)}`">
            {{ item.userName }} - {{ handleMobile(item.mobile) }}
          </div>
          <el-checkbox
            @click.stop
            :label="item[props.nodeKey]"
            :disabled="disabledUser(item[props.nodeKey])"></el-checkbox>
        </div>
      </el-checkbox-group>
    </el-scrollbar>
  </div>
</template>
<script>
export default { name: 'VirtualUser' }
</script>
<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { encryptPhone } from 'common/js/utils.js'
import { systemMobileEncrypt } from 'common/js/system_var.js'
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
const props = defineProps({
  modelValue: { type: Array, default: () => [] },
  data: { type: Array, default: () => [] },
  disabledUser: Function,
  nodeKey: { type: String, default: 'id' }
})
const emit = defineEmits(['update:modelValue', 'handleChange'])
const virtualList = ref()
const virtualScrollbar = ref()
const virtualPlaceholder = ref()
const userId = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
// 组件记录(默认)
const virtualRecord = reactive({
  height: 400,
  // 展示几个
  visibleCount: 66,
  // 刷新频率
  timeout: 4,
  // 行高
  itemHeight: 50,
  // translateY偏移量
  offset: 0,
  // 虚拟占位高度
  virtualHeight: 300,
  // 记录滚动高度
  recordScrollTop: 0,
  dataList: [],
  // 可展示的数据
  visibleData: []
})
// 合并配置
const mergeFn = () => {
  virtualRecord.dataList = JSON.parse(JSON.stringify(props.data))
  // 虚拟高度
  virtualRecord.virtualHeight = props.data.length * virtualRecord.itemHeight
  // 展示数量
  virtualRecord.visibleCount = Math.ceil(virtualRecord.height / virtualRecord.itemHeight)
}
let lastTime = 0
const handleScroll = (scroll) => {
  const currentTime = +new Date()
  if (currentTime - lastTime > virtualRecord.timeout) {
    virtualRecord.recordScrollTop = scroll.scrollTop
    updateVisibleData(scroll.scrollTop)
    lastTime = currentTime
  }
}
const updateVisibleData = (scrollTop) => {
  let start = Math.floor(scrollTop / virtualRecord.itemHeight) - Math.floor(virtualRecord.visibleCount / 2)
  start = start < 0 ? 0 : start
  const end = start + virtualRecord.visibleCount * 2
  virtualRecord.visibleData = virtualRecord.dataList.slice(start, end)
  virtualRecord.offset = start * virtualRecord.itemHeight
  nextTick(() => {
    virtualScrollbar.value.update()
  })
}
const handleClick = (item) => {
  if (disabledUser(item[props.nodeKey])) return
  let newUserId = []
  if (userId.value.includes(item[props.nodeKey])) {
    for (let index = 0; index < userId.value.length; index++) {
      if (userId.value[index] !== item[props.nodeKey]) {
        newUserId.push(userId.value[index])
      }
    }
  } else {
    newUserId = [...userId.value, item[props.nodeKey]]
  }
  userId.value = newUserId
  emit('handleChange', newUserId)
}
const handleChange = (value) => {
  emit('handleChange', value)
}
const disabledUser = (id) => {
  if (typeof props.disabledUser === 'function') {
    return props.disabledUser(id)
  } else {
    return false
  }
}
const handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)
watch(
  () => props.data,
  () => {
    // 合并数据
    mergeFn()
    // 更新视图
    updateVisibleData(virtualRecord.recordScrollTop)
  },
  { immediate: true, deep: true }
)
onMounted(() => {
  nextTick(() => {
    erd.listenTo(virtualList.value, (e) => {
      virtualRecord.height = e.offsetHeight
      // 合并数据
      mergeFn()
      // 更新视图
      updateVisibleData(virtualRecord.recordScrollTop)
    })
    erd.listenTo(virtualPlaceholder.value, (e) => {
      virtualRecord.itemHeight = e.offsetHeight
      // 合并数据
      mergeFn()
      // 更新视图
      updateVisibleData(virtualRecord.recordScrollTop)
    })
  })
})
</script>
<style lang="scss">
.virtual-user {
  width: 100%;
  height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-four) * 2)));

  .virtual-placeholder {
    position: fixed;
    top: -200%;
    left: -200%;
  }

  .zy-el-scrollbar {
    height: 100%;

    .zy-el-scrollbar__view {
      position: relative;
    }

    .virtualBody {
      width: 100%;
      position: absolute;
      z-index: -10;
    }

    .realBody {
      width: 100%;
      position: absolute;
    }
  }

  .virtual-user-item {
    display: flex;
    align-items: center;
    cursor: pointer;

    .virtual-user-name {
      width: calc(100% - 40px);
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      position: relative;
      padding: var(--zy-distance-four) 0;
      padding-left: var(--zy-distance-one);

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: var(--zy-distance-two);
        width: var(--zy-text-font-size);
        height: var(--zy-text-font-size);
        transform: translate(-50%, -50%);
        background: url('./img/select_person_user_icon.png') no-repeat;
        background-size: 100% 100%;
      }
    }

    .zy-el-checkbox {
      width: 40px;
      display: flex;
      justify-content: flex-end;
      padding-right: var(--zy-distance-three);

      .zy-el-checkbox__label {
        display: none;
      }
    }
  }
}
</style>
