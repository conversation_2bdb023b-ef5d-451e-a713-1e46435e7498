<template>
  <div class="virtual-select-user" ref="virtualList">
    <div class="virtual-placeholder" ref="virtualPlaceholder">
      <div class="virtual-select-user-item">
        <div class="virtual-select-user-name ellipsis"></div>
        <div class="virtual-select-user-text ellipsis"></div>
        <div class="virtual-select-user-del">
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
    </div>
    <el-scrollbar @scroll="handleScroll" ref="virtualScrollbar">
      <!-- 虚拟高度 -->
      <div class="virtualBody" :style="{ height: virtualRecord.virtualHeight + 'px' }"></div>
      <!-- 真实列表 -->
      <div :class="['realBody']" :style="{ transform: `translateY(${virtualRecord.offset}px)` }">
        <div
          class="virtual-select-user-item"
          v-for="(item, index) in virtualRecord.visibleData"
          :key="index + 'virtual-select-user_'">
          <div class="virtual-select-user-name ellipsis" :title="`${item.userName} - ${handleMobile(item.mobile)}`">
            {{ item.userName }} - {{ handleMobile(item.mobile) }}
          </div>
          <div class="virtual-select-user-text ellipsis">{{ item.position }}</div>
          <div class="virtual-select-user-del" @click="deleteclick(item)">
            <el-icon>
              <Close />
            </el-icon>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script>
export default { name: 'VirtualSelectUser' }
</script>
<script setup>
import { ref, reactive, watch, nextTick, onMounted } from 'vue'
import { encryptPhone } from 'common/js/utils.js'
import { systemMobileEncrypt } from 'common/js/system_var.js'
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
const props = defineProps({ data: { type: Array, default: () => [] } })
const emit = defineEmits(['handleDel'])
const virtualList = ref()
const virtualScrollbar = ref()
const virtualPlaceholder = ref()
// 组件记录(默认)
const virtualRecord = reactive({
  height: 400,
  // 展示几个
  visibleCount: 16,
  // 刷新频率
  timeout: 4,
  // 行高
  itemHeight: 90,
  // translateY偏移量
  offset: 0,
  // 虚拟占位高度
  virtualHeight: 300,
  // 记录滚动高度
  recordScrollTop: 0,
  dataList: [],
  // 可展示的数据
  visibleData: []
})
// 合并配置
const mergeFn = () => {
  virtualRecord.dataList = JSON.parse(JSON.stringify(props.data))
  // 虚拟高度
  virtualRecord.virtualHeight = props.data.length * virtualRecord.itemHeight
  // 展示数量
  virtualRecord.visibleCount = Math.ceil(virtualRecord.height / virtualRecord.itemHeight)
}
let lastTime = 0
const handleScroll = (scroll) => {
  const currentTime = +new Date()
  if (currentTime - lastTime > virtualRecord.timeout) {
    virtualRecord.recordScrollTop = scroll.scrollTop
    updateVisibleData(scroll.scrollTop)
    lastTime = currentTime
  }
}
const updateVisibleData = (scrollTop) => {
  let start = Math.floor(scrollTop / virtualRecord.itemHeight) - Math.floor(virtualRecord.visibleCount / 2)
  start = start < 0 ? 0 : start
  const end = start + virtualRecord.visibleCount * 2
  virtualRecord.visibleData = virtualRecord.dataList.slice(start, end)
  virtualRecord.offset = start * virtualRecord.itemHeight
  nextTick(() => {
    virtualScrollbar.value.update()
  })
}
const deleteclick = (data) => {
  emit('handleDel', data)
}
const handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)
watch(
  () => props.data,
  () => {
    // 合并数据
    mergeFn()
    // 更新视图
    updateVisibleData(virtualRecord.recordScrollTop)
  },
  { immediate: true, deep: true }
)
onMounted(() => {
  nextTick(() => {
    erd.listenTo(virtualList.value, (e) => {
      virtualRecord.height = e.offsetHeight
      // 合并数据
      mergeFn()
      // 更新视图
      updateVisibleData(virtualRecord.recordScrollTop)
    })
    erd.listenTo(virtualPlaceholder.value, (e) => {
      virtualRecord.itemHeight = e.offsetHeight
      // 合并数据
      mergeFn()
      // 更新视图
      updateVisibleData(virtualRecord.recordScrollTop)
    })
  })
})
</script>
<style lang="scss">
.virtual-select-user {
  width: 100%;
  height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));

  .virtual-placeholder {
    position: fixed;
    top: -200%;
    left: -200%;
  }

  .zy-el-scrollbar {
    height: 100%;

    .zy-el-scrollbar__view {
      position: relative;
    }

    .virtualBody {
      width: 100%;
      position: absolute;
      z-index: -10;
    }

    .realBody {
      width: 100%;
      position: absolute;
    }
  }

  .virtual-select-user-item + .virtual-select-user-item {
    margin-top: 12px;
  }

  .virtual-select-user-item {
    width: 290px;
    background: var(--el-color-info-light-9);
    padding: var(--zy-distance-five) var(--zy-distance-one);
    cursor: pointer;
    position: relative;
    border-radius: var(--el-border-radius-small);

    &::after {
      content: '';
      position: absolute;
      top: calc(var(--zy-distance-five) + var(--zy-font-text-distance-five));
      left: var(--zy-distance-two);
      transform: translateX(-50%);
      width: var(--zy-text-font-size);
      height: calc(var(--zy-text-font-size) * var(--zy-line-height));
      background: url('./img/select_person_user_icon.png') no-repeat;
      background-size: var(--zy-text-font-size) var(--zy-text-font-size);
      background-position: center;
    }

    .virtual-select-user-del {
      position: absolute;
      top: 0;
      right: 0;
      width: var(--zy-distance-one);
      height: 100%;
      padding-top: var(--zy-distance-five);
      text-align: center;
      font-size: var(--zy-navigation-font-size);
      line-height: var(--zy-line-height);
    }

    .virtual-select-user-name {
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      padding: var(--zy-font-text-distance-five) 0;
      height: calc(var(--zy-text-font-size) * var(--zy-line-height));
      box-sizing: content-box;
    }

    .virtual-select-user-text {
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      padding: var(--zy-font-text-distance-five) 0;
      height: calc(var(--zy-text-font-size) * var(--zy-line-height));
      color: var(--zy-el-text-color-secondary);
      box-sizing: content-box;
    }
  }
}
</style>
