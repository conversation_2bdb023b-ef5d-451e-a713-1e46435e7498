<template>
  <el-select
    v-model="selectedValue"
    :loading="loading"
    remote
    remote-show-suffix
    :remote-method="handleQuery"
    v-select-loadmore="handleScroll"
    filterable
    :disabled="disabled"
    :clearable="clearable"
    :placeholder="placeholder"
    @change="handleChange">
    <el-option v-for="item in tableData" :key="item[key]" :label="item[label]" :value="item[key]"></el-option>
  </el-select>
</template>
<script>
export default { name: 'XylSelect' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, watch } from 'vue'
const props = defineProps({
  modelValue: [String, Number],
  api: { type: String, default: '请选择' },
  disabled: { type: Boolean, default: false },
  clearable: { type: Boolean, default: false },
  placeholder: { type: String, default: '请选择' },
  props: { type: Object, default: () => ({ value: 'key', label: 'label' }) },
  selected: { type: Array, default: () => [] },
  params: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['update:modelValue', 'change'])

const disabled = computed(() => props.disabled)
const clearable = computed(() => props.clearable)
const placeholder = computed(() => props.placeholder)
const key = computed(() => props.props.value || 'key')
const label = computed(() => props.props.label || 'label')

const selectedValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const loading = ref(false)
const keyword = ref('')
const pageNo = ref(1)
const pageSize = ref(10)
const totals = ref(0)
const tableData = ref([])

const handleQuery = (val) => {
  if (keyword.value || val) {
    keyword.value = val
    loading.value = true
    pageNo.value = 1
    totals.value = 0
    handleGetData()
  }
}
const handleScroll = () => {
  if (pageNo.value * pageSize.value >= totals.value) return
  pageNo.value += 1
  handleGetData()
}
const handleGetData = async () => {
  const { data, total } = await api[props.api]({
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    keyword: keyword.value,
    ...props.params
  })
  if (pageNo.value === 1) {
    tableData.value = []
  }
  tableData.value = [...tableData.value, ...data]
  totals.value = total
  loading.value = false
}
const handleChange = (value) => {
  if (value) {
    for (let index = 0; index < tableData.value.length; index++) {
      const item = tableData.value[index]
      if (item[key.value] === value) {
        emit('change', value, item)
      }
    }
  } else {
    emit('change', '', {})
  }
}
watch(
  () => props.selected,
  (val) => {
    tableData.value = val
    pageNo.value = 1
    totals.value = 0
    handleGetData()
  },
  { immediate: true }
)
</script>
