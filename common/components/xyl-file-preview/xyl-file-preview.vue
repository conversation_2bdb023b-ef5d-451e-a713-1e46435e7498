<template>
  <div class="xyl-file-preview-wrapper" @click="handleClose">
    <transition name="el-zoom-in-bottom">
      <div class="xyl-file-preview" v-show="elIsShow" @click.stop>
        <div class="xyl-file-preview-head">
          <div class="xyl-file-preview-name ellipsis" :title="fileObj.fileName">{{ fileObj.fileName }}</div>
          <div class="xyl-file-preview-button">
            <div class="xyl-file-preview-icon" @click="handleDownload">
              <svg
                t="1717642874928"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="10967"
                width="32"
                height="32">
                <path
                  d="M502 662.3c3.1 3 7.1 4.4 11.1 4.4s8-1.5 11.1-4.4l211.2-201.6c2.6-2.5 4.1-5.5 4.7-8.6 1.8-9.2-5-19.2-15.8-19.2H595.5V165.8c0-8.8-7.1-16-16-16H446.6c-8.8 0-16 7.1-16 16V433H301.8c-10.9 0-17.6 10-15.8 19.2 0.6 3.1 2.1 6 4.7 8.6L502 662.3z"
                  fill="#000000"
                  p-id="10968"></path>
                <path
                  d="M859.6 653H626.7l-58.3 55.6c-15 14.3-34.6 22.2-55.3 22.2-20.7 0-40.4-7.9-55.3-22.2L399.5 653H166.6c-8.9 0-16.1 7.2-16.1 16v190.1c0 8.9 7.2 16.1 16.1 16.1h693c8.9 0 16-7.2 16-16.1v-190c0-8.9-7.2-16.1-16-16.1zM674.8 834.6c-19.5 0-35.2-15.8-35.2-35.2 0-19.5 15.8-35.2 35.2-35.2 19.5 0 35.2 15.8 35.2 35.2 0 19.4-15.8 35.2-35.2 35.2z m122.8 0c-19.5 0-35.2-15.8-35.2-35.2 0-19.5 15.8-35.2 35.2-35.2 19.5 0 35.2 15.8 35.2 35.2 0.1 19.4-15.7 35.2-35.2 35.2z"
                  fill="#000000"
                  p-id="10969"></path>
              </svg>
            </div>
            <div class="xyl-file-preview-icon" @click="handleClose">
              <svg
                t="1717642327842"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="5911"
                width="28"
                height="28">
                <path
                  d="M512.922 63.583c248.335 0 449.712 201.384 449.712 449.71 0 248.333-201.377 449.702-449.712 449.702-248.333 0-449.71-201.369-449.71-449.702 0-248.326 201.377-449.71 449.71-449.71z m148.683 213.634l-35.351 61.247a206.8 206.8 0 0 1 34.37 27.739c37.359 37.351 60.475 88.96 60.475 145.955 0 57.004-23.117 108.607-60.475 145.965-37.344 37.352-88.945 60.469-145.949 60.469-56.987 0-108.606-23.117-145.965-60.469-37.359-37.359-60.461-88.962-60.461-145.965 0-56.995 23.117-108.605 60.461-145.955a209.04 209.04 0 0 1 27.762-23.286l-35.43-61.359a278.69 278.69 0 0 0-42.279 34.69c-50.156 50.148-81.18 119.417-81.18 195.91 0 76.504 31.041 145.773 81.18 195.929 50.139 50.139 119.408 81.166 195.912 81.166 76.502 0 145.771-31.026 195.91-81.166 50.156-50.156 81.182-119.425 81.182-195.929 0-76.494-31.026-145.763-81.182-195.91a277.704 277.704 0 0 0-48.98-39.031zM473.618 128.865v337.849h75.458V128.865h-75.458z"
                  fill="#000000"
                  p-id="5912"></path>
              </svg>
            </div>
          </div>
        </div>
        <div
          class="xyl-file-preview-body"
          v-loading="loading"
          :element-loading-spinner="svg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-text="文件加载中...">
          <iframe class="xyl-file-preview-iframe" frameborder="0" :src="fileUrl"></iframe>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default { name: 'XylFilePreview' }
</script>
<script setup>
import api from '@/api'
import config from '../../config'
import { ref, computed, onMounted } from 'vue'
import store from '@/store'
import { downloadFile } from '../../config/MicroGlobal'
import { getFileInfo } from '../../js/file_preview.js'
import { ElMessage } from 'element-plus'
const props = defineProps({
  fileObj: { type: Object, default: () => ({}) },
  closeCallback: { type: Function }
})
const svg = `<path class="path" d="M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15" style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>`
const fileObj = computed(() => props.fileObj)
const elIsShow = ref(false)
const loading = ref(true)
const fileUrl = ref('')
onMounted(() => {
  file_preview(config.API_URL + '/file/preview/' + fileObj.value.fileId)
  setTimeout(() => {
    elIsShow.value = true
  }, 168)
})
const handleClose = () => {
  elIsShow.value = false
  setTimeout(() => {
    props.closeCallback()
  }, 168)
}
const file_preview = async (url) => {
  const res = await api.file_preview({ fileUrl: url })
  const data = res?.data?.data
  if (data) {
    file_preview_url(url, data)
  } else {
    handleClose()
    ElMessage({ type: 'error', message: '打开失败，请重试' })
  }
}
const file_preview_url = async (url, path) => {
  const res = await api.file_preview_url({
    srcRelativePath: path,
    convertType: getFileInfo(path.substring(path.lastIndexOf('.'))) || '0',
    isDccAsync: 1,
    isCopy: 0,
    noCache: 0,
    fileUrl: url,
    showFooter: 0,
    isHeaderBar: 0,
    htmlTitle: '详情',
    acceptTracks: 0
  })
  const viewUrl = res?.data?.viewUrl
  if (viewUrl) {
    fileUrl.value = viewUrl
    loading.value = false
  } else {
    handleClose()
    ElMessage({ type: 'error', message: '打开失败，请重试' })
  }
}
const handleDownload = () => {
  if (window.__POWERED_BY_QIANKUN__) {
    downloadFile({
      fileId: fileObj.value.fileId,
      fileType: fileObj.value.fileType,
      fileName: fileObj.value.fileName,
      fileSize: fileObj.value.fileSize
    })
  } else {
    console.log(window.__PUBLIC__)
    if (window.__PUBLIC__) {
      api.globalFileDownload(fileObj.value.fileId, fileObj.value.fileName)
    } else {
      store.commit('setDownloadFile', {
        fileId: fileObj.value.fileId,
        fileType: fileObj.value.fileType,
        fileName: fileObj.value.fileName,
        fileSize: fileObj.value.fileSize
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.xyl-file-preview-wrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 999;

  .xyl-file-preview {
    width: 100%;
    height: 90%;
    background: #fff;
    border-radius: 10px 10px 0px 0px;

    .xyl-file-preview-head {
      width: 100%;
      height: 52px;
      box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.05);
      padding: 0 32px 0 22px;
      z-index: 999;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #eeeeee;

      .xyl-file-preview-name {
        cursor: pointer;
        font-weight: bold;
        font-size: var(--zy-name-font-size);
      }

      .xyl-file-preview-button {
        display: flex;
        align-items: center;

        .xyl-file-preview-icon {
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          margin-left: 12px;

          path {
            fill: var(--zy-el-text-color-regular);
          }
        }
      }
    }

    .xyl-file-preview-body {
      width: 100%;
      height: calc(100% - 52px);

      .xyl-file-preview-iframe {
        width: 100%;
        height: 100%;
        overflow: auto;
      }
    }
  }
}
</style>
