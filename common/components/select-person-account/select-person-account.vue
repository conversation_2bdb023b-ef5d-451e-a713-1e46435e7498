<template>
  <div class="select-person-account">
    <div class="select-person-account-body">
      <div class="select-person-account-left">
        <div class="select-person-account-input">
          <el-input v-model="keyword" placeholder="根据用户名称搜索" @keyup.enter="handleSearch" clearable />
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </div>
        <div class="select-person-account-group">
          <div class="select-person-account-group-name">地区列表</div>
          <el-scrollbar class="select-person-account-group-tree">
            <el-tree
              ref="treeRef"
              node-key="id"
              :data="regionData"
              highlight-current
              :props="{ label: 'name' }"
              @node-click="handleNodeClick" />
          </el-scrollbar>
        </div>
        <div class="select-person-account-user-list">
          <div class="select-person-account-user-list-head">
            <div class="select-person-account-user-list-name">人员列表（{{ userDataList.length }}）</div>
            <el-checkbox
              v-model="checkAll"
              v-if="!props.max"
              :indeterminate="isIndeterminate"
              @change="handleCheckAll"></el-checkbox>
            <!-- <div class="select-person-account-user-list-checkbox">
              <el-checkbox v-model="checkAll" v-if="!props.max" :indeterminate="isIndeterminate"
                @change="handleCheckAll">全选</el-checkbox>
              <el-checkbox v-model="isNoSelect">仅显示未选人员</el-checkbox>
            </div> -->
            <el-radio-group v-model="isNoSelect">
              <el-radio :value="0">全部</el-radio>
              <el-radio :value="1">未选人员</el-radio>
            </el-radio-group>
          </div>
          <virtual-user
            v-model="userId"
            :data="userDataList"
            :disabledUser="disabledUser"
            @handleChange="handleChange"></virtual-user>
        </div>
      </div>
      <div class="select-person-account-right">
        <div class="select-person-account-right-name">
          已选人员（{{ userData.length }}）
          <div @click="deleteAll">清空</div>
        </div>
        <virtual-select-user :data="userData" @handleDel="deleteclick"></virtual-select-user>
      </div>
    </div>
    <div class="select-person-account-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'SelectPersonAccount' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted, nextTick, defineAsyncComponent } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const VirtualUser = defineAsyncComponent(() => import('../virtualElement/virtual-user.vue'))
const VirtualSelectUser = defineAsyncComponent(() => import('../virtualElement/virtual-select-user.vue'))
const props = defineProps({
  userApi: { type: String, default: 'businessUserAccountList' },
  userId: { type: Array, default: () => [] },
  userData: { type: Array, default: () => [] },
  max: { type: Number, default: 0 },
  levelType: { type: String, default: 'all' }
})
const emit = defineEmits(['callback'])
const isNoSelect = ref(0)
const userDataList = computed(() => {
  return isNoSelect.value ? userDataArr.value.filter((item) => !userId.value.includes(item.id)) : userDataArr.value
})

onMounted(() => {
  if (props.userData.length || props.userId.length) {
    if (props.userData.length) {
      props.userData.forEach((item) => {
        userData.value.push(item)
        userIndexData.value[item.id] = item.id
      })
      getAreaTree()
    }
    if (props.userId.length) {
      getSelectUser(props.userId)
    }
  } else {
    getAreaTree()
  }
})

const disabledUser = (id) => {
  var show = false
  if (props.max) {
    show = userData.value.length >= props.max
    show = userData.value.length >= props.max ? !userData.value.map((v) => v.id).includes(id) : false
  }
  return show
}

const getSelectUser = async (userIds) => {
  const res = await api[props.userApi]({ ids: userIds })
  var { data } = res
  data.forEach((item) => {
    userIndexData.value[item.id] = item.id
  })
  userData.value = data
  getAreaTree()
}

const keyword = ref('')
const handleSearch = () => {
  getUserData()
}

const treeRef = ref()
const regionId = ref('')
const regionData = ref([])
const handleNodeClick = (data) => {
  regionId.value = data.id
  getUserData()
}

const getAreaTree = async () => {
  const { data } = await api.businessAreaTree({ levelType: props.levelType, isFilterUnUsing: 1 })
  regionId.value = data[0]?.id
  regionData.value = data
  nextTick(() => {
    treeRef.value.setCurrentKey(regionId.value)
    getUserData()
  })
}
const getUserData = async () => {
  const res = await api[props.userApi]({ keyword: keyword.value, areaId: regionId.value, query: { isUsing: 1 } })
  var { data } = res
  userDataArr.value = data
  memoryChecked()
}

const checkAll = ref(false)
const isIndeterminate = ref(false)
const userId = ref([])
const userData = ref([])
const userDataArr = ref([])
const userIndexData = ref([])

const handleChange = (value) => {
  const checkedCount = value.length
  checkAll.value = checkedCount > userDataArr.value.length || checkedCount === userDataArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userDataArr.value.length
  var values = []
  for (let i = 0, len = value.length; i < len; i++) {
    values[value[i]] = value[i]
  }
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (!Object.prototype.hasOwnProperty.call(values, userDataArr.value[i].id)) {
      deleteData(userDataArr.value[i])
      delete userIndexData.value[userDataArr.value[i].id]
    }
  }
  for (let i = 0, len = value.length; i < len; i++) {
    if (!Object.prototype.hasOwnProperty.call(userIndexData.value, value[i])) {
      pushData(value[i])
      userIndexData.value[value[i]] = value[i]
    }
  }
}

const handleCheckAllData = (val) => {
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (Object.prototype.hasOwnProperty.call(userIndexData.value, userDataArr.value[i].id)) {
      if (!val) {
        deleteData(userDataArr.value[i])
      }
      val ? null : delete userIndexData.value[userDataArr.value[i].id] // eslint-disable-line
    } else {
      pushData(userDataArr.value[i].id)
      userIndexData.value[userDataArr.value[i].id] = userDataArr.value[i].id
    }
  }
}
const handleCheckAll = (val) => {
  userId.value = val ? [...new Set([...userId.value, ...userDataArr.value.map((v) => v.id)])] : []
  isIndeterminate.value = false
  handleCheckAllData(val)
}

const deleteData = (data) => {
  userData.value = userData.value.filter((item) => item.id !== data.id)
}
const pushData = (id) => {
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (userDataArr.value[i].id === id) {
      userData.value.push(userDataArr.value[i])
    }
  }
}

const deleteAll = () => {
  ElMessageBox.confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      userData.value = []
      userIndexData.value = []
      memoryChecked()
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消删除' })
    })
}
const deleteclick = (data) => {
  deleteData(data)
  delete userIndexData.value[data.id]
  memoryChecked()
}
const memoryChecked = () => {
  var userIdArr = []
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (Object.prototype.hasOwnProperty.call(userIndexData.value, userDataArr.value[i].id)) {
      userIdArr.push(userDataArr.value[i].id)
    }
  }
  userId.value = userIdArr
  const checkedCount = userIdArr.length
  checkAll.value = checkedCount > userDataArr.value.length || checkedCount === userDataArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userDataArr.value.length
  if (checkedCount === 0 && userDataArr.value.length === 0) {
    checkAll.value = false
  }
}
const submitForm = () => {
  emit('callback', userData.value)
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.select-person-account {
  width: 990px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-four) var(--zy-distance-two) var(--zy-distance-two) var(--zy-distance-one);

  .select-person-account-body {
    height: calc(100% - (var(--zy-height) + var(--zy-distance-two)));
    display: flex;
    justify-content: space-between;

    .select-person-account-left {
      width: 600px;
      height: 100%;
      display: flex;
      flex-wrap: wrap;

      .select-person-account-input {
        width: 100%;
        display: flex;
        padding-bottom: var(--zy-distance-three);

        .zy-el-input {
          margin-right: var(--zy-distance-two);
        }
      }

      .select-person-account-group {
        width: 310px;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);

        .select-person-account-group-name {
          padding: var(--zy-distance-four) var(--zy-distance-three);
          font-weight: bold;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
        }

        .select-person-account-group-tree {
          width: 100%;
          height: calc(100% - (var(--zy-name-font-size) * (var(--zy-line-height)) + (var(--zy-distance-four) * 2)));

          .zy-el-tree-node.is-current {
            & > .zy-el-tree-node__content {
              position: relative;

              .zy-el-tree-node__label {
                color: var(--zy-el-color-primary);

                &::after {
                  content: '';
                  position: absolute;
                  top: 0;
                  right: 0;
                  width: 4px;
                  height: 100%;
                  background: var(--zy-el-color-primary);
                }
              }
            }
          }

          .zy-el-tree-node__content {
            height: auto;
            padding: var(--zy-distance-five) 0;

            .zy-el-tree-node__label {
              width: calc(100% - 28px);
              text-overflow: clip;
              white-space: normal;
              line-height: var(--zy-line-height);
              font-size: var(--zy-text-font-size);
              padding-right: var(--zy-distance-one);
            }
          }
        }
      }

      .select-person-account-user-list {
        width: 290px;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);
        border-left: 0;

        .virtual-user {
          height: calc(
            100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + var(--zy-height) + var(--zy-distance-four))
          );
        }

        .select-person-account-user-list-head {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          padding: 0 var(--zy-distance-three);
          padding-top: var(--zy-distance-four);

          .select-person-account-user-list-name {
            font-weight: bold;
            font-size: var(--zy-name-font-size);
            line-height: var(--zy-line-height);
          }

          .zy-el-checkbox {
            height: auto;
          }

          // .select-person-account-user-list-checkbox {
          //   height: 100%;
          //   display: flex;
          //   align-items: flex-end;
          //   flex-direction: column;
          //   justify-content: space-around;
          //   padding: 3px 0;

          //   .zy-el-checkbox {
          //     height: auto;
          //     position: relative;
          //     padding-right: 22px;
          //     margin: 0;

          //     .zy-el-checkbox__input {
          //       position: absolute;
          //       top: 50%;
          //       right: 0;
          //       transform: translateY(-50%);
          //     }
          //   }
          // }
          .zy-el-radio-group {
            width: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .select-person-account-right {
      width: 310px;
      height: 100%;

      .select-person-account-right-name {
        line-height: var(--zy-height);
        padding: var(--zy-distance-three);
        font-size: var(--zy-name-font-size);
        padding-top: 0;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;

        div {
          font-size: var(--zy-text-font-size);
          color: var(--zy-el-text-color-regular);
          font-weight: normal;
          cursor: pointer;
        }
      }
    }
  }

  .select-person-account-button {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);
    padding-right: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
