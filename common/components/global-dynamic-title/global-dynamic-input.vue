<template>
  <div class="global-dynamic-input">
    <el-input v-model="content" :disabled="props.disabled" :style="{ width: inputWid }" />
  </div>
</template>
<script>
export default { name: 'GlobalDynamicInput' }
</script>
<script setup>
import { computed } from 'vue'
const props = defineProps({
  modelValue: [String, Number],
  disabled: { type: Boolean, default: false }
})
const emit = defineEmits(['update:modelValue'])

const content = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const inputWid = computed(() => {
  if (!content.value) {
    return '120px'
  } else {
    return props.disabled ? String(content.value).length * 6 + 60 + 'px' : String(content.value).length * 13 + 70 + 'px'
  }
})
</script>
