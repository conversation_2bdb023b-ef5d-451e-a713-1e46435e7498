<template>
  <div class="xyl-global-tree">
    <div class="xyl-global-tree-name">
      {{ props.name }}
      <div @click="treeNew" v-if="elWhether">
        <el-icon>
          <Plus />
        </el-icon>
        {{ props.name }}
      </div>
    </div>
    <div class="xyl-global-tree-info">
      <slot name="info"></slot>
    </div>
    <el-input v-model="keyWord" placeholder="请输入关键词" clearable />
    <el-scrollbar class="xyl-global-tree-body" :class="{ 'is-active': !elWhether }">
      <el-tree
        ref="treeRef"
        highlight-current
        :data="treeData"
        :props="props.props"
        :draggable="draggable && elWhether"
        :node-key="props.nodeKey"
        :allow-drop="allowDrop"
        :default-expanded-keys="defaultExpandIds"
        @node-drop="handleDrop"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
        :filter-node-method="filterNode">
        <template #default="{ node, data }">
          <span
            :class="[
              'zy-el-tree-node__label',
              props.isControl && !isManage ? 'tree-is-control-label' : '',
              isManage ? 'tree-manage-label' : ''
            ]"
            :title="node.label">
            {{ node.label }}
            <div class="tree-is-control" v-if="props.isControl && !isManage" @click.stop>
              <slot name="icon" :treeObj="data"></slot>
            </div>
            <div class="tree-manage" v-if="isManage" @click.stop>
              <template v-if="!props.noManage.includes(data[props.nodeKey])">
                <el-icon @click.stop="treeEdit(node, data)">
                  <Edit />
                </el-icon>
                <el-icon @click.stop="treeDel(node, data)">
                  <Delete />
                </el-icon>
              </template>
            </div>
          </span>
        </template>
      </el-tree>
    </el-scrollbar>
    <div
      class="xyl-global-tree-manage"
      :class="{ 'xyl-global-tree-is-manage': isManage }"
      @click="isManage = !isManage"
      v-if="elWhether">
      <el-icon>
        <Operation />
      </el-icon>
      {{ isManage ? '取消管理' : '管理' }}
    </div>
  </div>
</template>
<script>
export default { name: 'XylGlobalTree' }
</script>
<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { hasPermission } from '../../js/permissions'
const props = defineProps({
  modelValue: [String, Number],
  data: { type: Array, default: () => [] },
  nodeKey: { type: String, default: 'id' },
  has: { type: String, default: '' },
  noManage: { type: Array, default: () => [] },
  isControl: { type: Boolean, default: false },
  draggable: { type: Boolean, default: false },
  isOne: { type: Boolean, default: false },
  noDraggable: { type: Array, default: () => [] },
  name: { type: String, default: '栏目' },
  props: {
    type: Object,
    default: () => {
      return { children: 'children', label: 'label' }
    }
  }
})
const emit = defineEmits(['update:modelValue', 'select', 'draggable', 'treeNew', 'treeEdit', 'treeDel'])
const delay = (function () {
  let timer = 0
  return function (callback, ms) {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
const elWhether = ref(true)
const treeRef = ref()
const treeId = ref()
const treeData = ref()
const isManage = ref(false)
const keyWord = ref('')
const defaultExpandIds = ref([])

onMounted(() => {
  if (props.has) {
    elWhether.value = hasPermission(props.has)
  }
})

watch(
  () => props.modelValue,
  () => {
    treeId.value = props.modelValue
    if (props.modelValue) {
      delay(() => {
        selectedMethods(props.data, treeId.value)
      }, 200)
    }
  },
  { immediate: true }
)
watch(
  () => props.data,
  () => {
    treeData.value = props.data
    if (props.modelValue) {
      delay(() => {
        selectedMethods(props.data, treeId.value)
      }, 200)
    }
  },
  { immediate: true }
)
watch(
  () => keyWord.value,
  () => {
    treeRef.value.filter(keyWord.value)
  }
)

const filterNode = (value, data) => {
  if (!value) return true
  return data[props.props.label].includes(value)
}
const allowDrop = (draggingNode, dropNode, type) => {
  if (props.noDraggable.includes(draggingNode.data[props.nodeKey])) return false
  if (props.noDraggable.includes(dropNode.data[props.nodeKey]) && type === 'prev') return false
  if (props.isOne) {
    return type !== 'inner'
  } else {
    return true
  }
}
const handleDrop = (draggingNode, dropNode) => {
  emit('draggable', treeRef.value.data, draggingNode, dropNode)
}

const handleNodeClick = (data) => {
  emit('update:modelValue', data[props.nodeKey])
}
// 首次进来默认选中
const selectedMethods = (data, id) => {
  data.forEach((item) => {
    if (item[props.nodeKey] === id) {
      emit('select', item)
      nextTick(() => {
        treeRef.value.setCurrentKey(id)
      })
    }
    if (item[props.props.children] && item[props.props.children].length) {
      selectedMethods(item[props.props.children], id)
    }
  })
}
// 树节点展开
const handleNodeExpand = (data) => {
  // 保存当前展开的节点
  let flag = false
  defaultExpandIds.value.some((item) => {
    if (item === data[props.nodeKey]) {
      // 判断当前节点是否存在， 存在不做处理
      flag = true
      return true
    }
  })
  if (!flag) {
    // 不存在则存到数组里
    defaultExpandIds.value.push(data[props.nodeKey])
  }
}
// 树节点关闭
const handleNodeCollapse = (data) => {
  // 删除当前关闭的节点
  defaultExpandIds.value.some((item, i) => {
    if (item === data[props.nodeKey]) {
      defaultExpandIds.value.splice(i, 1)
    }
  })
  removeChildrenIds(data) // 这里主要针对多级树状结构，当关闭父节点时，递归删除父节点下的所有子节点
}
// 删除树子节点
const removeChildrenIds = (data) => {
  if (data[props.props.children]) {
    data[props.props.children].forEach((item) => {
      const index = defaultExpandIds.value.indexOf(item[props.nodeKey])
      if (index > 0) {
        defaultExpandIds.value.splice(index, 1)
      }
      removeChildrenIds(item)
    })
  }
}
const treeNew = () => {
  emit('treeNew')
}
const treeEdit = (node, data) => {
  emit('treeEdit', node, data)
}
const treeDel = (node, data) => {
  emit('treeDel', node, data)
}
</script>
<style lang="scss">
.xyl-global-tree {
  width: 240px;
  height: 100%;
  padding: var(--zy-distance-four) 0;
  display: flex;
  flex-direction: column;

  .xyl-global-tree-name {
    border: 1px solid var(--zy-el-border-color-lighter);
    border-bottom: 0;
    padding: var(--zy-distance-four) var(--zy-distance-three);
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);

    div {
      height: auto;
      display: flex;
      align-items: center;
      font-weight: normal;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      border: 1px solid var(--zy-el-color-primary);
      padding: 0 var(--zy-font-name-distance-five);
      color: var(--zy-el-color-primary);
      cursor: pointer;
      border-radius: var(--el-border-radius-small);

      .zy-el-icon {
        margin-right: 2px;
      }
    }
  }

  .xyl-global-tree-body {
    flex: 1;
    width: 100%;
    height: calc(
      100% -
        (
          var(--zy-height) + (var(--zy-name-font-size) * var(--zy-line-height)) +
            (var(--zy-text-font-size) * var(--zy-line-height)) + (var(--zy-distance-four) * 4)
        )
    );
    border: 1px solid var(--zy-el-border-color-lighter);
    border-top: 0;

    .zy-el-tree-node.is-current {
      & > .zy-el-tree-node__content {
        .zy-el-tree-node__label {
          color: var(--zy-el-color-primary);
        }
      }
    }

    .zy-el-tree-node__content {
      height: auto;
      padding: var(--zy-distance-five) 0;

      .zy-el-tree-node__label {
        width: calc(100% - 28px);
        padding: 0 20px 0 6px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: relative;
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);

        .tree-manage {
          position: absolute;
          top: 0;
          right: 0;
          height: 100%;
          display: flex;
          align-items: center;

          .zy-el-icon {
            font-size: 20px;
            margin-right: 12px;
          }
        }

        .tree-is-control {
          position: absolute;
          top: 0;
          right: 0;
          height: 100%;
          display: flex;
          align-items: center;

          .zy-el-icon {
            font-size: 20px;
            margin-right: 12px;
          }
        }
      }

      .tree-manage-label {
        padding-right: 66px;
      }

      .tree-is-control-label {
        padding-right: 42px;
      }
    }
  }

  .xyl-global-tree-body.is-active {
    height: calc(
      100% - (var(--zy-height) + (var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-four) * 2))
    );
  }

  .xyl-global-tree-manage {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: var(--zy-line-height);
    font-size: var(--zy-text-font-size);
    padding: var(--zy-distance-four) var(--zy-distance-three);
    border: 1px solid var(--zy-el-border-color-lighter);
    border-top: 0;
    cursor: pointer;

    .zy-el-icon {
      font-size: 20px;
      margin-right: 6px;
    }
  }

  .xyl-global-tree-is-manage {
    color: var(--zy-el-color-primary);
  }
}
</style>
