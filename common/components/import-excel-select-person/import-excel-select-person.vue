<template>
  <div class="import-excel-select-person" v-if="!show">
    <div class="import-excel-select-person-title">
      <div>1</div>
      下载导入模板
      <el-button type="primary" @click="importTemplate">下载模板</el-button>
    </div>
    <div class="import-excel-select-person-info">
      <div class="import-excel-select-person-text">
        按照下载的
        <span>导入模板</span>
        在模板中维护好内容；
      </div>
      <div class="import-excel-select-person-text">请勿修改文件扩展名，防止文件导入失败。</div>
    </div>
    <div class="import-excel-select-person-title">
      <div>2</div>
      上传文件
    </div>
    <div class="import-excel-select-person-info">
      <div class="import-excel-select-person-text">
        将完善好的模板文件上传至系统，仅支持上传文件格式为：*.xls *.xlsx
      </div>
      <el-upload
        drag
        action="/"
        :before-upload="handleFile"
        :on-remove="fileRemove"
        :http-request="fileUpload"
        :file-list="fileData"
        multiple>
        <el-icon class="zy-el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="zy-el-upload__text">
          将文件拖拽至此区域，或
          <em>点击上传</em>
        </div>
      </el-upload>
    </div>
    <div class="import-excel-select-person-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
  <div class="import-excel-select-person is-success" v-if="show">
    <div class="import-excel-select-person-result">
      导入成功 {{ successUsers.length }} 人（{{ duplicateUsers.length }}人重名），失败 {{ failUsers.length }} 人
    </div>
    <div class="import-excel-select-person-name">导入成功：{{ successUsers.length }}人</div>
    <div class="import-excel-select-person-user-list">
      <div
        class="import-excel-select-person-user ellipsis"
        v-for="(item, index) in successUsers"
        :key="index + 'success'">
        {{ item.userName }}{{ item.mobile ? ` - ${item.mobile}` : '' }}
      </div>
    </div>
    <div class="import-excel-select-person-name">重名：{{ duplicateUsers.length }}人</div>
    <div class="import-excel-select-person-user-list">
      <div
        class="import-excel-select-person-user ellipsis"
        v-for="(item, index) in duplicateUsers"
        :key="index + 'duplicate'">
        {{ item.userName }}{{ item.mobile ? ` - ${item.mobile}` : '' }}
      </div>
    </div>
    <div class="import-excel-select-person-name">导入失败：{{ failUsers.length }}人</div>
    <div class="import-excel-select-person-user-list">
      <div
        class="import-excel-select-person-user ellipsis"
        v-for="(item, index) in failUsers"
        :key="index + 'failUsers'">
        {{ item.userName }}{{ item.mobile ? ` - ${item.mobile}` : '' }}
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'ImportExcelSelectPerson' }
</script>
<script setup>
import api from '@/api'
import { ref } from 'vue'
import { useStore } from 'vuex'
import { extendDownloadFile } from 'common/config/MicroGlobal.js'
import { ElMessage } from 'element-plus'
const emit = defineEmits(['callback'])
const store = useStore()
const show = ref(false)
const fileName = ref('')
const fileData = ref([])
const failUsers = ref([])
const successUsers = ref([])
const duplicateUsers = ref([])
const importTemplate = async () => {
  const param = {
    url: `/excel/import/template/userImportExcel`,
    params: {},
    fileSize: 0,
    fileName: 'Excel导入匹配系统人员 --- 导入模板.xlsx',
    fileType: 'xlsx'
  }
  if (window.__POWERED_BY_QIANKUN__) {
    extendDownloadFile(param)
  } else {
    store.commit('setExtendDownloadFile', param)
  }
}
const handleFile = (file) => {
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
  const isShow = ['xls', 'xlsx'].includes(fileType)
  if (!isShow) {
    ElMessage({ type: 'warning', message: '仅支持.xls或.xlsx格式!' })
  }
  return isShow
}
const fileUpload = (file) => {
  fileName.value = file.file.name
  fileData.value = [file.file]
}
const fileRemove = () => {
  fileName.value = ''
  fileData.value = []
}
const submitForm = async () => {
  if (!fileName.value) return ElMessage({ type: 'warning', message: '请先上传导入文件！' })
  globalExcelImport()
}
const globalExcelImport = async () => {
  const param = new FormData()
  param.append('file', fileData.value[0])
  const { data } = await api.globalExcelImport('/choose/users/import', param)
  show.value = true
  failUsers.value = data?.failUsers || []
  successUsers.value = data?.successUsers || []
  duplicateUsers.value = data?.duplicateUsers || []
  emit('callback', data?.users || [], true)
}
const resetForm = () => {
  emit('callback', [], false)
}
</script>
<style lang="scss">
.import-excel-select-person {
  width: 680px;
  padding: var(--zy-distance-one);

  &.is-success {
    padding: var(--zy-distance-two) var(--zy-distance-two) 0 var(--zy-distance-one);
  }

  .import-excel-select-person-title {
    display: flex;
    align-items: center;
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    margin-bottom: var(--zy-distance-five);
    position: relative;

    div {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      width: calc(var(--zy-text-font-size) * var(--zy-line-height));
      height: calc(var(--zy-text-font-size) * var(--zy-line-height));
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      border-radius: 50%;
      background: var(--zy-el-color-primary);
      margin-right: 6px;
    }

    .zy-el-button {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      height: var(--zy-height-secondary);
    }
  }

  .import-excel-select-person-name {
    font-weight: bold;
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
  }

  .import-excel-select-person-info {
    padding: var(--zy-distance-three) var(--zy-distance-two);
    margin-bottom: var(--zy-distance-one);
    background-color: var(--zy-el-fill-color-light);

    .import-excel-select-person-text {
      padding: var(--zy-font-text-distance-five) 0;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);

      span {
        color: var(--zy-el-color-primary);
        margin: 0 6px;
      }
    }

    .zy-el-upload {
      margin-top: var(--zy-distance-five);

      .zy-el-upload-dragger {
        padding: var(--zy-distance-five) var(--zy-distance-five) var(--zy-distance-two) var(--zy-distance-five);
      }
    }
  }

  .import-excel-select-person-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
  .import-excel-select-person-result {
    color: var(--zy-el-color-error);
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding-bottom: var(--zy-distance-five);
  }
  .import-excel-select-person-user-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    padding-bottom: var(--zy-distance-two);
    .import-excel-select-person-user {
      width: calc(33.33% - var(--zy-distance-two));
      height: var(--zy-height-routine);
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-height-routine);
      padding: 0 var(--zy-distance-five);
      margin-top: var(--zy-distance-five);
      margin-right: var(--zy-distance-two);
      background: var(--zy-el-color-info-light-9);
      border-radius: var(--el-border-radius-small);
    }
  }
}
</style>
