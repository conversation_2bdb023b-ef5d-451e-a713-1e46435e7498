<template>
  <div class="global-tag" @click.stop="handleClick">
    <span class="forbidSelect">
      <slot></slot>
    </span>
    <el-icon @click.stop="handleClose">
      <Close />
    </el-icon>
  </div>
</template>
<script>
export default { name: 'GlobalTag' }
</script>
<script setup>
const emit = defineEmits(['click', 'close'])
const handleClick = () => {
  emit('click')
}
const handleClose = () => {
  emit('close')
}
</script>
<style lang="scss">
.global-tag {
  display: inline-flex;
  align-items: center;
  padding: 0 var(--zy-distance-four);
  height: var(--zy-height-secondary);
  border: 1px solid var(--zy-el-color-primary);
  border-radius: var(--el-border-radius-small);
  font-size: var(--zy-text-font-size);
  background-color: var(--zy-el-color-primary-light-9);
  color: var(--zy-el-color-primary);
  margin: 5px;

  span {
    line-height: normal;
  }

  .zy-el-icon {
    border-radius: 50%;
    margin-left: var(--zy-distance-five);
    cursor: pointer;

    &:hover {
      color: #fff;
      background-color: var(--zy-el-color-primary);
    }
  }
}
</style>
