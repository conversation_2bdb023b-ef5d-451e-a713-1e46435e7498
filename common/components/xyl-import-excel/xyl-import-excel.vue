<template>
  <div class="xyl-import-excel">
    <div class="xyl-import-excel-title">
      <div>1</div>
      下载导入模板
      <el-button type="primary" @click="importTemplate">下载模板</el-button>
    </div>
    <div class="xyl-import-excel-info">
      <div class="xyl-import-excel-text">
        按照下载的
        <span>导入模板</span>
        在模板中维护好内容；
      </div>
      <div class="xyl-import-excel-text">请勿修改文件扩展名，防止文件导入失败。</div>
    </div>
    <div class="xyl-import-excel-title">
      <div>2</div>
      上传文件
      <el-button type="primary" @click="importRecord" v-if="!props.noResult">导入记录</el-button>
    </div>
    <div class="xyl-import-excel-info">
      <div class="xyl-import-excel-text">将完善好的模板文件上传至系统，仅支持上传文件格式为：*.xls *.xlsx</div>
      <el-upload
        drag
        action="/"
        :before-upload="handleFile"
        :on-remove="fileRemove"
        :http-request="fileUpload"
        :file-list="fileData"
        multiple>
        <el-icon class="zy-el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="zy-el-upload__text">
          将文件拖拽至此区域，或
          <em>点击上传</em>
        </div>
      </el-upload>
    </div>
    <div class="xyl-import-excel-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
    <xyl-popup-window v-model="show" name="导入记录">
      <xyl-import-record :name="props.name" :module="props.type"></xyl-import-record>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'XylImportExcel' }
</script>
<script setup>
import api from '@/api'
import { ref, defineAsyncComponent } from 'vue'
import store from '@/store'
import { importList, extendDownloadFile } from 'common/config/MicroGlobal'
import { ElMessage } from 'element-plus'
const XylImportRecord = defineAsyncComponent(() => import('./xyl-import-record.vue'))
const props = defineProps({
  name: { type: String, default: '' },
  type: { type: String, default: '' },
  params: { type: Object, default: () => ({}) },
  importUrl: { type: String, default: '' },
  importData: { type: Object, default: () => ({}) },
  importParams: { type: Object, default: () => ({}) },
  noResult: { type: Boolean, default: false }
})
const emit = defineEmits(['callback'])

const fileName = ref('')
const fileData = ref([])
const show = ref(false)

const importTemplate = async () => {
  if (window.__POWERED_BY_QIANKUN__) {
    extendDownloadFile({
      url: `/excel/import/template/${props.type}`,
      params: props.params,
      fileSize: 0,
      fileName: `${props.name}导入模板.xlsx`,
      fileType: 'xlsx'
    })
  } else {
    store.commit('setExtendDownloadFile', {
      url: `/excel/import/template/${props.type}`,
      params: props.params,
      fileSize: 0,
      fileName: `${props.name}.xlsx`,
      fileType: 'xlsx'
    })
  }
}
const importRecord = () => {
  show.value = true
}
const handleFile = (file) => {
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
  const isShow = ['xls', 'xlsx'].includes(fileType)
  if (!isShow) {
    ElMessage({ type: 'warning', message: '仅支持.xls或.xlsx格式!' })
  }
  return isShow
}
const fileUpload = (file) => {
  fileName.value = file.file.name
  fileData.value = [file.file]
}
const fileRemove = () => {
  fileName.value = ''
  fileData.value = []
}
const submitForm = async () => {
  if (!fileName.value) return ElMessage({ type: 'warning', message: '请先上传导入文件！' })
  globalExcelImport()
}
const globalExcelImport = async () => {
  const param = new FormData()
  param.append('file', fileData.value[0])
  for (let key in props.importParams) {
    param.append(key, props.importParams[key])
  }
  const { data } = await api.globalExcelImport(props.importUrl || `/excel/importAsync/${props.type}`, param, {
    params: props.importData
  })
  if (props.noResult) return emit('callback')
  if (window.__POWERED_BY_QIANKUN__) {
    importList({
      id: data,
      fileSize: 0,
      downloadName: `${props.name}导入结果.xlsx`,
      fileName: `${fileName.value}`,
      fileType: 'xlsx'
    })
  } else {
    store.commit('setImportList', {
      id: data,
      fileSize: 0,
      downloadName: `${props.name}导入结果.xlsx`,
      fileName: `${fileName.value}`,
      fileType: 'xlsx'
    })
  }
  emit('callback')
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.xyl-import-excel {
  width: 680px;
  margin: auto;
  padding: var(--zy-distance-one);
  padding-bottom: 0;

  .xyl-import-excel-title {
    display: flex;
    align-items: center;
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    margin-bottom: var(--zy-distance-five);
    position: relative;

    div {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      width: calc(var(--zy-text-font-size) * var(--zy-line-height));
      height: calc(var(--zy-text-font-size) * var(--zy-line-height));
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      border-radius: 50%;
      background: var(--zy-el-color-primary);
      margin-right: 6px;
    }

    .zy-el-button {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      height: var(--zy-height-secondary);
    }
  }

  .xyl-import-excel-info {
    padding: var(--zy-distance-three) var(--zy-distance-two);
    margin-bottom: var(--zy-distance-one);
    background-color: var(--zy-el-fill-color-light);

    .xyl-import-excel-text {
      padding: var(--zy-font-text-distance-five) 0;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);

      span {
        color: var(--zy-el-color-primary);
        margin: 0 6px;
      }
    }

    .zy-el-upload {
      margin-top: var(--zy-distance-five);

      .zy-el-upload-dragger {
        padding: var(--zy-distance-five) var(--zy-distance-five) var(--zy-distance-two) var(--zy-distance-five);
      }
    }
  }

  .xyl-import-excel-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: var(--zy-distance-one);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
