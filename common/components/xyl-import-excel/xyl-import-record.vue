<template>
  <div class="xyl-import-record">
    <div class="globalTable">
      <el-table :data="tableData">
        <el-table-column label="导入日期" min-width="160" prop="importDate" />
        <el-table-column label="导入数据条数" min-width="120" show-overflow-tooltip prop="receiveLine" />
        <el-table-column label="成功条数" min-width="120" show-overflow-tooltip prop="successLine" />
        <el-table-column label="失败条数" min-width="120" show-overflow-tooltip prop="falseLine" />
        <el-table-column label="未处理条数" min-width="120" show-overflow-tooltip prop="noHandleLine" />
        <el-table-column label="操作" width="260" fixed="right" class-name="globalTableCustom">
          <template #default="scope">
            <el-button @click="handleDownload(scope.row)" type="primary" plain>下载源文件</el-button>
            <el-button @click="handleResult(scope.row)" type="primary" plain>下载结果</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default { name: 'XylImportRecord' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { user } from '../../js/system_var.js'
import { downloadFile, extendDownloadFile } from '../../config/MicroGlobal'
const props = defineProps({ name: { type: String, default: '' }, module: { type: String, default: '' } })
const emit = defineEmits(['callback'])
const tableData = ref([])
onMounted(() => {
  globalExcelHistoryImport()
})
const globalExcelHistoryImport = async () => {
  const res = await api.globalExcelHistoryImport(props.module)
  var { data } = res
  tableData.value = data.map((v) => ({ ...v, isDisabled: data.map((v) => v.areaId).includes(user.value.areaId) }))
}
const handleDownload = (row) => {
  if (window.__POWERED_BY_QIANKUN__) {
    downloadFile({
      fileId: row.importFileId,
      fileType: 'xlsx',
      fileName: `${props.name}导入源文件 -- 批次号：${row.batchNumber} -- 导入日期：${row.importDate}.xlsx`
    })
  } else {
    store.commit('setDownloadFile', {
      fileId: row.importFileId,
      fileType: 'xlsx',
      fileName: `${props.name}导入源文件 -- 批次号：${row.batchNumber} -- 导入日期：${row.importDate}.xlsx`
    })
  }
}
const handleResult = (row) => {
  if (window.__POWERED_BY_QIANKUN__) {
    extendDownloadFile({
      url: `/excel/downloadImportResult/${row.batchNumber}`,
      params: {},
      fileType: 'xlsx',
      fileName: `${props.name}导入结果 -- 批次号：${row.batchNumber} -- 导入日期：${row.importDate}.xlsx`
    })
  } else {
    store.commit('setExtendDownloadFile', {
      url: `/excel/downloadImportResult/${row.batchNumber}`,
      params: {},
      fileType: 'xlsx',
      fileName: `${props.name}导入结果 -- 批次号：${row.batchNumber} -- 导入日期：${row.importDate}.xlsx`
    })
  }
}
</script>
<style lang="scss">
.xyl-import-record {
  width: 990px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-three) var(--zy-distance-two);
  border: 1px solid var(--zy-el-border-color-lighter);

  .globalTable {
    width: 100%;
    height: 100%;
  }
}
</style>
