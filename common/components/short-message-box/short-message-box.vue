<template>
  <div class="short-message-box">
    <div class="short-message-box-name">
      短信模板
      <el-button @click="templateInfo" type="primary">更新模板</el-button>
    </div>
    <el-input v-model="content" placeholder="请输入短信模板" type="textarea" :rows="5" />
  </div>
</template>
<script>
export default { name: 'ShortMessageBox' }
</script>
<script setup>
import api from '@/api'
import { computed, onMounted } from 'vue'
const props = defineProps({
  modelValue: [String, Number],
  code: { type: String, default: '' }
})
const emit = defineEmits(['update:modelValue'])

const content = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

onMounted(() => {
  if (props.code) {
    templateInfo()
  }
})

const templateInfo = async () => {
  const { data } = await api.templateInfo({ businessCode: props.code })
  content.value = data
}
</script>
<style lang="scss">
.short-message-box {
  width: 100%;

  .short-message-box-name {
    display: flex;
    align-items: flex-end;
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    margin-bottom: var(--zy-distance-four);

    .zy-el-button {
      margin-left: var(--zy-distance-two);
      height: var(--zy-height-secondary);
    }
  }
}
</style>
