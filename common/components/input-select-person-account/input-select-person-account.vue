<template>
  <el-input v-model="userName" @focus="userFocus" :disabled="disabled" :placeholder="placeholder" readonly />
  <xyl-popup-window v-model="show" name="选择用户">
    <template v-if="isShow">
      <select-person-account
        :userApi="props.userApi"
        :userData="userData"
        :levelType="props.levelType"
        @callback="callback"
        :max="1"></select-person-account>
    </template>
    <template v-if="!isShow">
      <standard-select-person-account
        :userData="userData"
        @callback="callback"
        :max="1"></standard-select-person-account>
    </template>
  </xyl-popup-window>
</template>
<script>
export default { name: 'InputSelectPersonAccount' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, watch, computed } from 'vue'
import { readConfig } from 'common/js/system_var.js'
const props = defineProps({
  userApi: { type: String, default: 'businessUserAccountList' },
  soleKey: { type: String, default: '' },
  modelValue: [String, Number],
  disabled: { type: Boolean, default: false },
  placeholder: { type: String, default: '请选择用户' },
  levelType: { type: String, default: 'all' }
})
const emit = defineEmits(['update:modelValue', 'callback'])
const isShow = computed(() => readConfig.value.systemType === 'platform')

const show = ref(false)
const userId = ref(props.modelValue)
const userName = ref('')
const userData = ref([])
const disabled = computed(() => props.disabled)
const placeholder = computed(() => props.placeholder)
onMounted(() => {
  if (props.modelValue && !userData.value.length) {
    getSelectUser()
  }
})

watch(
  () => props.modelValue,
  () => {
    userId.value = props.modelValue
    if (props.modelValue) {
      if (userData.value.length) {
        if (props.modelValue !== userData.value[0].id) {
          getSelectUser()
        }
      } else {
        getSelectUser()
      }
    } else {
      callback([])
    }
  }
)

const getSelectUser = async () => {
  const res = await api[props.userApi]({ ids: [userId.value], key: props.soleKey, type: props.placeholder })
  var { data } = res
  userName.value = data[0].userName
  userData.value = data
}
const userFocus = () => {
  show.value = true
}
const callback = (data) => {
  show.value = false
  if (data) {
    userData.value = data.length ? data : []
    userName.value = data.length ? data[0].userName : ''
    emit('update:modelValue', data.length ? data[0].id : '')
    emit('callback', data.length ? data[0] : null)
  }
}
</script>
