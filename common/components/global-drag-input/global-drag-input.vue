<template>
  <div class="global-drag-input" ref="optionsRef">
    <div class="global-drag-input-item" v-for="item in options" :key="item.uid">
      <div class="global-drag-input-icon"></div>
      <el-input v-model="item.name" @change="optionsChange" placeholder="请输入" :disabled="disabled" clearable />
      <div class="global-drag-input-button">
        <el-icon @click="optionsNew" v-if="!disabled">
          <CirclePlus />
        </el-icon>
        <el-icon @click="optionsDel(item)" v-if="options.length > 1 && !disabled">
          <Remove />
        </el-icon>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'GlobalDragInput' }
</script>
<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import Sortable from 'sortablejs' // 引入插件
const props = defineProps({
  data: { type: Array, default: () => [] },
  disabled: { type: Boolean, default: false }
})
const emit = defineEmits(['callback'])
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const optionsRef = ref()
const options = ref([{ uid: guid(), name: '' }])
const disabled = computed(() => props.disabled)

onMounted(() => {
  nextTick(() => {
    if (!props.disabled) {
      rowDrop()
    }
  })
})

const optionsNew = () => {
  options.value.push({ uid: guid(), name: '' })
}
const optionsChange = () => {
  emit(
    'callback',
    options.value.filter((v) => v.name.replace(/(^\s*)|(\s*$)/g, '')).map((v) => v.name.replace(/(^\s*)|(\s*$)/g, ''))
  )
}
const optionsDel = (row) => {
  options.value = options.value.filter((v) => v.uid !== row.uid)
  if (row.name) {
    optionsChange()
  }
}
const rowDrop = () => {
  Sortable.create(optionsRef.value, {
    handle: '.global-drag-input-icon',
    animation: 150,
    onEnd({ newIndex, oldIndex }) {
      if (disabled.value) return
      if (newIndex == oldIndex) return
      options.value.splice(newIndex, 0, options.value.splice(oldIndex, 1)[0])
      const newArray = options.value.slice(0)
      options.value = []
      nextTick(() => {
        options.value = newArray
        optionsChange()
      })
    }
  })
}
watch(
  () => props.data,
  () => {
    if (props.data.length) {
      options.value = props.data.map((v) => ({ uid: guid(), name: v }))
      optionsChange()
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.global-drag-input {
  width: 100%;
  padding-right: 20px;
  padding-bottom: 22px;

  .global-drag-input-item + .global-drag-input-item {
    margin-top: 10px;
  }

  .global-drag-input-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .global-drag-input-icon {
      width: 20px;
      height: 20px;
      background: url('./img/global_form_options.png') no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }

    .zy-el-input {
      width: calc(100% - 88px);
    }

    .global-drag-input-button {
      width: 52px;
      display: flex;
      align-items: center;

      .zy-el-icon {
        color: var(--zy-el-color-primary);
        font-size: 17px;
        margin-left: 9px;
        cursor: pointer;
      }
    }
  }
}
</style>
