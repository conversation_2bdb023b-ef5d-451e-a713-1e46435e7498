<template>
  <div class="submit-select-person-group">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="分组名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入分组名称" clearable />
      </el-form-item>
      <el-form-item label="上级分组">
        <el-tree-select
          v-model="form.parentId"
          :data="groupData"
          check-strictly
          node-key="id"
          :render-after-expand="false"
          :props="{ label: 'name' }"
          clearable />
      </el-form-item>
      <el-form-item label="序号" prop="sort">
        <el-input
          v-model="form.sort"
          maxlength="10"
          show-word-limit
          @input="form.sort = validNum(form.sort)"
          placeholder="请输入序号"
          clearable />
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SubmitSelectPersonGroup' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { validNum } from 'common/js/utils.js'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' }, typeId: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  parentId: '', // 上级分组
  name: '', // 分组名称
  sort: '' // 排序
})
const rules = reactive({
  name: [{ required: true, message: '请输入分组名称', trigger: ['blur', 'change'] }],
  sort: [{ required: true, message: '请输入序号', trigger: ['blur', 'change'] }]
})

const groupData = ref()
onMounted(() => {
  chooseMineTagTree()
  if (props.id) chooseMineTagInfo()
})

const chooseMineTagTree = async () => {
  const { data } = await api.chooseMineTagTree()
  groupData.value = props.id ? filterData(data) : data
}
const filterData = (list) => {
  return list
    .filter((item) => {
      return item.id !== props.id
    })
    .map((item) => {
      item = Object.assign({}, item)
      if (item.children) {
        item.children = filterData(item.children)
      }
      return item
    })
}
const chooseMineTagInfo = async () => {
  const res = await api.chooseMineTagInfo({ detailId: props.id })
  var { data } = res
  form.parentId = data.parentId === '0' ? '' : data.parentId
  form.name = data.name
  form.sort = data.sort
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(props.id ? '/chooseMineTag/edit' : '/chooseMineTag/add', {
    form: {
      id: props.id,
      typeId: props.typeId,
      parentId: form.parentId || '0',
      tagName: form.name,
      sort: form.sort
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.submit-select-person-group {
  width: 680px;
}
</style>
