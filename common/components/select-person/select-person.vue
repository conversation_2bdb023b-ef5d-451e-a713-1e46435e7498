<template>
  <div class="select-person">
    <div class="select-person-head">
      <el-tabs v-model="tabId" @tab-change="handleTab">
        <el-tab-pane v-for="item in tabData" :key="item.id" :name="item.id">
          <template #label>
            <div class="select-person-tab forbidSelect">{{ item.name }}</div>
          </template>
        </el-tab-pane>
      </el-tabs>
      <xyl-tag v-model="tagId" @tagClick="handleTag">
        <xyl-tag-item v-for="item in tagData" :key="item.id" :value="item.id">{{ item.name }}</xyl-tag-item>
      </xyl-tag>
    </div>
    <div class="select-person-body">
      <div class="select-person-left">
        <div class="select-person-input">
          <el-input v-model="keyword" placeholder="根据用户名称搜索" @keyup.enter="handleSearch" clearable />
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </div>
        <div class="select-person-group">
          <div class="select-person-group-name">
            分组列表
            <div class="select-person-group-plus" @click="treeNew" v-if="isManage">
              <div>
                <el-icon><Plus /></el-icon>
                分组
              </div>
            </div>
            <div class="select-person-group-icon" @click="groupRefresh" v-if="!isManage">
              <el-icon :class="{ 'is-loading': groupLoading }"><RefreshRight /></el-icon>
            </div>
          </div>
          <el-scrollbar
            :class="['select-person-group-tree', { 'select-person-group-tree-manage': tabId === 'mine_tag' }]">
            <el-tree
              ref="treeRef"
              node-key="code"
              :data="treeData"
              highlight-current
              :props="{ label: 'name' }"
              @node-click="handleNodeClick">
              <template #default="{ node, data }">
                <span :class="['zy-el-tree-node__label', { 'is-manage': isManage }]">
                  {{ node.label }}
                  <div class="is-manage-icon" @click.stop v-if="isManage && node.key !== 'all'">
                    <el-icon @click.stop="treeUser(node, data)"><User /></el-icon>
                    <el-icon @click.stop="treeEdit(node, data)"><Edit /></el-icon>
                    <el-icon @click.stop="treeDel(node, data)"><Delete /></el-icon>
                  </div>
                </span>
              </template>
            </el-tree>
          </el-scrollbar>
          <div
            :class="['select-person-group-manage', { 'select-person-group-is-manage': isManage }]"
            @click="isManage = !isManage"
            v-if="tabId === 'mine_tag'">
            <el-icon><Operation /></el-icon>
            {{ isManage ? '取消管理' : '管理' }}
          </div>
        </div>
        <div class="select-person-user-list">
          <div class="select-person-user-list-head">
            <div class="select-person-user-list-name">人员列表（{{ userDataList.length }}）</div>
            <el-checkbox
              v-model="checkAll"
              v-if="!props.max"
              :indeterminate="isIndeterminate"
              @change="handleCheckAll"></el-checkbox>
            <!-- <div class="select-person-user-list-checkbox">
              <el-checkbox v-model="checkAll" v-if="!props.max" :indeterminate="isIndeterminate"
                @change="handleCheckAll">全选</el-checkbox>
              <el-checkbox v-model="isNoSelect">仅显示未选人员</el-checkbox>
            </div> -->
            <el-radio-group v-model="isNoSelect">
              <el-radio :value="0">全部</el-radio>
              <el-radio :value="1">未选人员</el-radio>
            </el-radio-group>
          </div>
          <virtual-user
            v-model="userId"
            :data="userDataList"
            :disabledUser="disabledUser"
            @handleChange="handleChange"></virtual-user>
        </div>
      </div>
      <div class="select-person-right">
        <div class="select-person-right-name">
          已选人员（{{ userData.length }}）
          <div @click="deleteAll">清空</div>
        </div>
        <virtual-select-user :data="userData" @handleDel="deleteclick"></virtual-select-user>
      </div>
    </div>
    <div class="select-personButton">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
  <xyl-popup-window v-model="show" :name="id ? '编辑分组' : '新增分组'">
    <submit-select-person-group :id="id" @callback="callback"></submit-select-person-group>
  </xyl-popup-window>
  <xyl-popup-window v-model="isShow" name="选择人员">
    <select-person custom :userData="itemUserData" @callback="handleUserCallback"></select-person>
  </xyl-popup-window>
</template>
<script>
export default { name: 'SelectPerson' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted, nextTick, defineAsyncComponent } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const VirtualUser = defineAsyncComponent(() => import('../virtualElement/virtual-user.vue'))
const VirtualSelectUser = defineAsyncComponent(() => import('../virtualElement/virtual-select-user.vue'))
const SubmitSelectPersonGroup = defineAsyncComponent(() => import('./submit-select-person-group.vue'))
// import Worker from '../../workers/global.worker.js'
const props = defineProps({
  custom: { type: Boolean, default: false },
  userId: { type: Array, default: () => [] },
  userData: { type: Array, default: () => [] },
  filterUser: { type: Array, default: () => [] },
  disabledUser: { type: Array, default: () => [] },
  params: { type: Object, default: () => ({}) },
  urlParams: { type: Object, default: () => ({}) },
  dataMethod: { type: Object, default: () => ({}) },
  max: { type: Number, default: 0 },
  tabCode: { type: Array, default: () => ['userOffice'] }
})
const emit = defineEmits(['callback'])
const isNoSelect = ref(0)
const userDataList = computed(() => {
  return isNoSelect.value ? userDataArr.value.filter((item) => !userId.value.includes(item.id)) : userDataArr.value
})
onMounted(() => {
  if (props.userData.length || props.userId.length) {
    if (props.userData.length) {
      props.userData.forEach((item) => {
        userData.value.push(item)
        userIndexData.value[item.id] = item.id
      })
      SelectPersonTab()
    }
    if (props.userId.length) {
      getSelectUser(props.userId)
    }
  } else {
    SelectPersonTab()
  }
})
const getSelectUser = async (userIds) => {
  const res = await api.getSelectUser({ existsUserIds: userIds })
  var { data } = res
  data.forEach((item) => {
    userIndexData.value[item.id] = item.id
  })
  userData.value = data
  SelectPersonTab()
}

const disabledUser = (id) => {
  var show = false
  if (props.max) {
    show = userData.value.length >= props.max
    show = userData.value.length >= props.max ? !userData.value.map((v) => v.id).includes(id) : false
  }
  return show
}

const tabId = ref('')
const tabData = ref([])
const handleTab = () => {
  isManage.value = false
  for (let index = 0; index < tabData.value.length; index++) {
    const item = tabData.value[index]
    if (tabId.value === item.id) {
      tagId.value = item?.tag[0]?.id
      tagData.value = item.tag
      handleTag()
    }
  }
}

const tagId = ref('1')
const tagData = ref([])
const isManage = ref(false)
const id = ref('')
const show = ref(false)
const isShow = ref(false)
const itemUserData = ref([])
const handleTag = () => {
  SelectPersonGroup()
}
const treeNew = () => {
  id.value = ''
  show.value = true
}
const treeEdit = (node, data) => {
  id.value = data.code
  show.value = true
}
const treeUser = (node, data) => {
  id.value = data.code
  getUserData()
}
const getUserData = async () => {
  const { data } = await api.SelectPersonUser({
    tabCode: tabId.value,
    labelCode: tagId.value,
    nodeId: id.value
  })
  itemUserData.value = data
  isShow.value = true
}
const handleUserCallback = (data) => {
  if (data) {
    chooseMineTagJoinAllUser(data.map((v) => v.id))
  }
  isShow.value = false
}
const chooseMineTagJoinAllUser = async (dataIds) => {
  const { code } = await api.chooseMineTagJoinAllUser({ chooseTagId: id.value, userIds: dataIds })
  if (code === 200) {
    ElMessage({ type: 'success', message: '删除成功' })
    SelectPersonGroup()
  }
}
const treeDel = (node, data) => {
  ElMessageBox.confirm('此操作将删除当前选中的分组, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      chooseMineTagDel(data)
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消删除' })
    })
}
const chooseMineTagDel = async (data) => {
  const { code } = await api.chooseMineTagDel({ ids: [data.code] })
  if (code === 200) {
    ElMessage({ type: 'success', message: '删除成功' })
    SelectPersonGroup()
  }
}
const callback = () => {
  groupRefresh()
  show.value = false
}

const groupLoading = ref(false)
const groupRefresh = () => {
  groupLoading.value = true
  SelectPersonGroup()
}
const SelectPersonTab = async () => {
  const { data } = await api.SelectPersonTab(
    { tabCodes: props.tabCode, ...(props.params?.tab || {}) },
    { ...(props.urlParams?.tab || {}) }
  )
  const newData = []
  const finallyData = props.dataMethod?.tab ? props.dataMethod?.tab(data) : data
  for (let index = 0; index < finallyData.length; index++) {
    const item = finallyData[index]
    if (props.custom) {
      if (item.tabCode !== 'mine_tag') {
        let itemTag = [...(item?.chooseLabels?.map((row) => ({ id: row.labelCode, name: row.name })) || [])]
        if (['user_tag', 'mine_tag'].includes(item.tabCode) && !itemTag.length) itemTag = [{ id: 'all', name: '所有' }]
        newData.push({ id: item.tabCode, name: item.name, tag: itemTag })
      }
    } else {
      let itemTag = [...(item?.chooseLabels?.map((row) => ({ id: row.labelCode, name: row.name })) || [])]
      if (['user_tag', 'mine_tag'].includes(item.tabCode) && !itemTag.length) itemTag = [{ id: 'all', name: '所有' }]
      newData.push({ id: item.tabCode, name: item.name, tag: itemTag })
    }
  }
  const selectTab = newData[0]
  tabId.value = selectTab.id
  tabData.value = newData
  tagId.value = selectTab?.tag[0]?.id
  tagData.value = selectTab.tag
  handleTab()
}

const keyword = ref('')
const handleSearch = () => {
  SelectPersonUser()
}

const treeRef = ref()
const nodeId = ref('')
const treeData = ref([])
const handleNodeClick = (data) => {
  nodeId.value = data.code
  SelectPersonUser()
}

const SelectPersonGroup = async () => {
  const soleId = tabId.value + tagId.value
  const { data } = await api.SelectPersonGroup(
    { tabCode: tabId.value, labelCode: tagId.value, ...(props.params?.group || {}) },
    { ...(props.urlParams?.group || {}) }
  )
  if (soleId === tabId.value + tagId.value) {
    groupLoading.value = false
    treeData.value = props.dataMethod?.group
      ? props.dataMethod?.group(data)
      : [{ code: 'all', name: '所有人员', children: [] }, ...data]
    nextTick(() => {
      const selectCode = treeData.value[0].code || 'all'
      nodeId.value = selectCode
      treeRef.value.setCurrentKey(selectCode)
      SelectPersonUser()
    })
  }
}
const SelectPersonUser = async () => {
  if (['relationBookSystemTemp', 'relationBookMineTemp'].includes(tabId.value)) {
    SelectPersonBookUser()
    return
  }
  const soleId = tabId.value + tagId.value + nodeId.value
  const { data } = await api.SelectPersonUser(
    {
      tabCode: tabId.value,
      labelCode: tagId.value,
      nodeId: nodeId.value === 'all' ? '' : nodeId.value,
      keyword: keyword.value,
      ...(props.params?.user || {})
    },
    { ...(props.urlParams?.user || {}) }
  )
  if (soleId === tabId.value + tagId.value + nodeId.value) {
    userDataArr.value = (props.dataMethod?.user ? props.dataMethod?.user(data) : data).filter(
      (v) => !props.filterUser.includes(v.id)
    )
    memoryChecked()
  }
}
const SelectPersonBookUser = async () => {
  const soleId = tabId.value + tagId.value + nodeId.value
  const { data } = await api.SelectPersonBookUser({
    tabCode: tabId.value,
    labelCode: tagId.value,
    relationBookId: nodeId.value === 'all' ? '' : nodeId.value,
    keyword: keyword.value
  })
  if (soleId === tabId.value + tagId.value + nodeId.value) {
    userDataArr.value = data.filter((v) => !props.filterUser.includes(v.id))
    memoryChecked()
  }
}

const checkAll = ref(false)
const isIndeterminate = ref(false)
const userId = ref([])
const userData = ref([])
const userDataArr = ref([])
const userIndexData = ref([])

const handleChange = (value) => {
  const checkedCount = value.length
  checkAll.value = checkedCount > userDataArr.value.length || checkedCount === userDataArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userDataArr.value.length
  var values = []
  for (let i = 0, len = value.length; i < len; i++) {
    values[value[i]] = value[i]
  }
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (!Object.prototype.hasOwnProperty.call(values, userDataArr.value[i].id)) {
      deleteData(userDataArr.value[i])
      delete userIndexData.value[userDataArr.value[i].id]
    }
  }
  for (let i = 0, len = value.length; i < len; i++) {
    if (!Object.prototype.hasOwnProperty.call(userIndexData.value, value[i])) {
      pushData(value[i])
      userIndexData.value[value[i]] = value[i]
    }
  }
}

const handleCheckAllData = (val) => {
  // const worker = new Worker()
  // worker.postMessage('开启线程')
  // worker.onmessage = e => {
  //   console.log(e.data)
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (Object.prototype.hasOwnProperty.call(userIndexData.value, userDataArr.value[i].id)) {
      if (!val) {
        deleteData(userDataArr.value[i])
      }
      val ? null : delete userIndexData.value[userDataArr.value[i].id] // eslint-disable-line
    } else {
      pushData(userDataArr.value[i].id)
      userIndexData.value[userDataArr.value[i].id] = userDataArr.value[i].id
    }
  }
  //   setTimeout(() => {
  //     worker.postMessage('线程关闭')
  //     worker.terminate()
  //   }, 1000)
  // }
}
const handleCheckAll = (val) => {
  userId.value = val ? [...new Set([...userId.value, ...userDataArr.value.map((v) => v.id)])] : []
  isIndeterminate.value = false
  handleCheckAllData(val)
}

const deleteData = (data) => {
  userData.value = userData.value.filter((item) => item.id !== data.id)
}
const pushData = (id) => {
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (userDataArr.value[i].id === id) {
      userData.value.push(userDataArr.value[i])
    }
  }
}

const deleteAll = () => {
  ElMessageBox.confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const arr = userData.value.filter((item) => item.disabled)
      userData.value = arr
      userIndexData.value = []
      arr.forEach((item) => {
        userIndexData.value[item.id] = item.id
      })
      if (arr.length) {
        ElMessage({ type: 'info', message: '当前选中用户有部分不能移除' })
      }
      memoryChecked()
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消删除' })
    })
}
const deleteclick = (data) => {
  deleteData(data)
  delete userIndexData.value[data.id]
  memoryChecked()
}
const memoryChecked = () => {
  var userIdArr = []
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (Object.prototype.hasOwnProperty.call(userIndexData.value, userDataArr.value[i].id)) {
      userIdArr.push(userDataArr.value[i].id)
    }
  }
  userId.value = userIdArr
  const checkedCount = userIdArr.length
  checkAll.value = checkedCount > userDataArr.value.length || checkedCount === userDataArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userDataArr.value.length
  if (checkedCount === 0 && userDataArr.value.length === 0) {
    checkAll.value = false
  }
}
const submitForm = () => {
  emit('callback', userData.value)
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.select-person {
  width: 990px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-four) var(--zy-distance-two) var(--zy-distance-two) var(--zy-distance-one);

  .select-person-head {
    padding-right: var(--zy-distance-two);

    .zy-el-tabs {
      width: 100%;

      .zy-el-tabs__header {
        margin: 0;

        .zy-el-tabs__nav-next,
        .zy-el-tabs__nav-prev {
          font-size: var(--zy-navigation-font-size);
          line-height: 48px;
        }

        .zy-el-tabs__nav-wrap {
          .zy-el-tabs__item {
            height: 43px;
            line-height: 43px;
            font-size: var(--zy-name-font-size);

            .select-person-tab {
              display: flex;
              align-items: center;
            }
          }

          .is-active {
            font-weight: bold;
          }

          .zy-el-tabs__active-bar {
            height: 2px;
          }
        }
      }

      .zy-el-tabs__content {
        display: none;
      }
    }

    .xyl-tag {
      margin-top: var(--zy-font-name-distance-five);
    }
  }

  .select-person-body {
    height: calc(100% - (var(--zy-height) + var(--zy-distance-two) + 92px));
    display: flex;
    justify-content: space-between;

    .select-person-left {
      width: 600px;
      height: 100%;
      display: flex;
      flex-wrap: wrap;

      .select-person-input {
        width: 100%;
        display: flex;
        padding-bottom: var(--zy-distance-three);

        .zy-el-input {
          margin-right: var(--zy-distance-two);
        }
      }

      .select-person-group {
        width: 310px;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);

        .select-person-group-name {
          position: relative;
          padding: var(--zy-distance-four) var(--zy-distance-three);
          font-weight: bold;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);

          .select-person-group-icon {
            height: 100%;
            position: absolute;
            top: 0;
            right: var(--zy-distance-three);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            .zy-el-icon {
              vertical-align: middle;
              color: var(--zy-el-text-color-secondary);
              font-size: var(--zy-title-font-size);
            }
          }

          .select-person-group-plus {
            height: 100%;
            display: flex;
            align-items: center;
            position: absolute;
            top: 0;
            right: var(--zy-distance-three);

            div {
              height: auto;
              display: flex;
              align-items: center;
              font-weight: normal;
              font-size: var(--zy-text-font-size);
              line-height: var(--zy-line-height);
              border: 1px solid var(--zy-el-color-primary);
              padding: 0 var(--zy-font-name-distance-five);
              color: var(--zy-el-color-primary);
              cursor: pointer;
              border-radius: var(--el-border-radius-small);

              .zy-el-icon {
                margin-right: 2px;
              }
            }
          }
        }

        .select-person-group-tree {
          width: 100%;
          height: calc(100% - (var(--zy-name-font-size) * (var(--zy-line-height)) + (var(--zy-distance-four) * 2)));

          &.select-person-group-tree-manage {
            height: calc(
              100% - ((var(--zy-name-font-size) * (var(--zy-line-height)) + (var(--zy-distance-four) * 2)) * 2)
            );
          }
          .zy-el-tree-node.is-current {
            & > .zy-el-tree-node__content {
              position: relative;

              &::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 4px;
                height: 100%;
                background: var(--zy-el-color-primary);
              }

              .zy-el-tree-node__label {
                color: var(--zy-el-color-primary);
              }
            }
          }

          .zy-el-tree-node__content {
            height: auto;
            padding: var(--zy-distance-five) 0;

            .zy-el-tree-node__label {
              width: calc(100% - 28px);
              text-overflow: clip;
              white-space: normal;
              line-height: var(--zy-line-height);
              font-size: var(--zy-text-font-size);
              padding-right: var(--zy-distance-one);
              position: relative;

              &.is-manage {
                padding-right: 102px;
              }

              .is-manage-icon {
                position: absolute;
                top: 0;
                right: 0;
                height: 100%;
                display: flex;
                align-items: center;

                .zy-el-icon {
                  font-size: 20px;
                  margin-right: 12px;
                }
              }
            }
          }
        }
        .select-person-group-manage {
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: var(--zy-line-height);
          font-size: var(--zy-text-font-size);
          border-top: 1px solid var(--zy-el-border-color-lighter);
          padding: var(--zy-distance-four) var(--zy-distance-three);
          cursor: pointer;

          .zy-el-icon {
            font-size: 20px;
            margin-right: 6px;
          }
        }
        .select-person-group-is-manage {
          color: var(--zy-el-color-primary);
        }
      }

      .select-person-user-list {
        width: 290px;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);
        border-left: 0;

        .virtual-user {
          height: calc(
            100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + var(--zy-height) + var(--zy-distance-four))
          );
        }

        .select-person-user-list-head {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          padding: 0 var(--zy-distance-three);
          padding-top: var(--zy-distance-four);

          .select-person-user-list-name {
            font-weight: bold;
            font-size: var(--zy-name-font-size);
            line-height: var(--zy-line-height);
          }

          .zy-el-checkbox {
            height: auto;
          }

          // .select-person-user-list-checkbox {
          //   height: 100%;
          //   display: flex;
          //   align-items: flex-end;
          //   flex-direction: column;
          //   justify-content: space-around;
          //   padding: 3px 0;

          //   .zy-el-checkbox {
          //     height: auto;
          //     position: relative;
          //     padding-right: 22px;
          //     margin: 0;

          //     .zy-el-checkbox__input {
          //       position: absolute;
          //       top: 50%;
          //       right: 0;
          //       transform: translateY(-50%);
          //     }
          //   }
          // }

          .zy-el-radio-group {
            width: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .select-person-right {
      width: 310px;
      height: 100%;

      .select-person-right-name {
        line-height: var(--zy-height);
        padding: var(--zy-distance-three);
        font-size: var(--zy-name-font-size);
        padding-top: 0;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;

        div {
          font-size: var(--zy-text-font-size);
          color: var(--zy-el-text-color-regular);
          font-weight: normal;
          cursor: pointer;
        }
      }
    }
  }

  .select-personButton {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);
    padding-right: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
