<template>
  <div class="wrongly-written-characters">
    <div class="wrongly-written-characters-body">
      <el-scrollbar class="wrongly-written-characters-word-scrollbar" ref="scrollbarRef">
        <div class="wrongly-written-characters-word" ref="wordRef" v-html="content"></div>
      </el-scrollbar>
      <div class="globalTable">
        <el-table :data="checklist">
          <el-table-column label="错误类型" min-width="120" prop="type.name" />
          <el-table-column label="错误内容" min-width="120">
            <template #default="scope">
              <el-link @click="handleDetails(scope.row)" :disabled="arrId.includes(scope.row.id)" type="primary">
                {{ scope.row.word }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="修改建议" min-width="120">
            <template #default="scope">{{ scope.row?.suggest[0] || scope.row?.explanation }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right" class-name="globalTableCustom">
            <template #default="scope">
              <el-button
                @click="handleReplace(scope.row)"
                :disabled="arrId.includes(scope.row.id) || !scope.row?.suggest?.length"
                type="primary"
                plain>
                替换
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="wrongly-written-characters-button">
      <el-button @click="submitForm" type="primary">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'WronglyWrittenCharacters' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
const props = defineProps({ content: { type: String, default: '' } })
const emit = defineEmits(['callback'])
const scrollbarRef = ref()
const wordRef = ref()
const oldId = ref('')
const arrId = ref([])
const content = ref('')
const checklist = ref([])
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
onMounted(() => {
  wrongWord()
})
const wrongWord = async () => {
  const { data } = await api.typingVerification({
    text: props.content.replace(/&ldquo;/gi, '“').replace(/&rdquo;/gi, '”')
  })
  checklist.value = data?.checklist?.map((v) => ({ ...v, id: guid() })) || []
  content.value = data?.replace_text || ''
}
const elAttr = (elList, id) => {
  let elArr = []
  let styleObj = {}
  for (let index = 0; index < elList.length; index++) {
    const item = elList[index]
    if (item.nodeName !== '#text') {
      if (item.getAttribute('data-umpos') === id) {
        const elParent = item.parentNode
        let is = 0
        while (elParent.style[is]) {
          const elParentStyleKey = elParent.style[is].replace(/-([a-z])/g, (p, m) => m.toUpperCase())
          styleObj[elParent.style[is]] = elParent.style[elParentStyleKey]
          is++
        }
        elArr.push(item)
      }
    }
    if (item.childNodes.length) {
      const obj = elAttr(item.childNodes, id)
      elArr = [...elArr, ...obj.elArr]
      styleObj = { ...styleObj, ...obj.styleObj }
    }
  }

  return { elArr, styleObj }
}
const handleDetails = (row) => {
  const elList = wordRef.value.childNodes
  if (oldId.value) {
    const oldObj = elAttr(elList, oldId.value)
    if (oldObj.elArr.length) {
      for (let index = 0; index < oldObj.elArr.length; index++) {
        const item = oldObj.elArr[index]
        if (!index) {
          scrollbarRef.value?.setScrollTop(item.offsetTop - 100)
        }
        item.style.color = ''
        item.style.backgroundColor = ''
      }
    }
  }
  oldId.value = row.position + ''
  const obj = elAttr(elList, row.position + '')
  if (obj.elArr.length) {
    for (let index = 0; index < obj.elArr.length; index++) {
      const item = obj.elArr[index]
      if (!index) {
        scrollbarRef.value?.setScrollTop(item.offsetTop - 100)
      }
      item.style.color = '#fff'
      item.style.backgroundColor = 'red'
    }
  }
}
const handleReplace = (row) => {
  const elList = wordRef.value.childNodes
  const obj = elAttr(elList, row.position + '')
  if (obj.elArr.length > 1) {
    let styleStr = ''
    for (let key in obj.styleObj) {
      styleStr += `${key}:${obj.styleObj[key]};`
    }
    for (let index = 0; index < obj.elArr.length; index++) {
      const item = obj.elArr[index]
      const elParent = item
      if (!index) {
        elParent.insertAdjacentHTML('beforebegin', `<span style="${styleStr}">${row.suggest[0]}</span>`)
      }
      elParent.parentNode.removeChild(elParent)
    }
  } else {
    obj.elArr[0].insertAdjacentHTML('beforebegin', row.suggest[0])
    obj.elArr[0].parentNode.removeChild(obj.elArr[0])
  }
  arrId.value.push(row.id)
}

const getAllElAttr = (elList) => {
  let elArr = []
  for (let index = 0; index < elList.length; index++) {
    const item = elList[index]
    if (item.nodeName !== '#text') {
      if (item.getAttribute('data-umpos')) {
        elArr.push(item)
      }
    }
    if (item.childNodes.length) {
      elArr = [...elArr, ...getAllElAttr(item.childNodes)]
    }
  }
  return elArr
}
const submitForm = () => {
  let htmlContent = ''
  if (arrId.value.length) {
    const elList = wordRef.value.childNodes
    const elArr = getAllElAttr(elList)
    for (let index = 0; index < elArr.length; index++) {
      const item = elArr[index]
      item.insertAdjacentHTML('beforebegin', item.innerText)
      item.parentNode.removeChild(item)
    }
    for (let index = 0; index < elList.length; index++) {
      htmlContent += elList[index]?.outerHTML || ''
    }
  }
  emit('callback', htmlContent)
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.wrongly-written-characters {
  width: 1260px;
  height: calc(85vh - 52px);
  padding-bottom: var(--zy-distance-two);

  .wrongly-written-characters-body {
    width: 100%;
    height: calc(100% - (var(--zy-height) + var(--zy-distance-two)));
    background: var(--zy-el-color-info-light-9);
    display: flex;
    justify-content: space-between;

    .wrongly-written-characters-word-scrollbar {
      width: 690px;
      height: 100%;
      background-color: #fff;

      .wrongly-written-characters-word {
        // width: 595.3pt;
        // min-height: 842pt;
        // box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
        // margin: 20px auto;
        // padding: 104.9pt 73pt;
        width: 660px;
        min-height: 842pt;
        box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
        margin: 20px auto;
        padding: 99pt 52pt;

        span {
          span {
            font-family: inherit;
            font-size: inherit;
          }
        }
      }
    }

    .globalTable {
      width: calc(100% - 700px);
      background-color: #fff;
      height: 100%;
      padding: 20px;
    }
  }

  .wrongly-written-characters-button {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
