<template>
  <div class="global-countdown">{{ time.dd }}天{{ time.hh }}小时{{ time.mm }}分{{ time.ss }}秒</div>
</template>
<script>
export default { name: 'GlobalCountdown' }
</script>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
const props = defineProps({
  type: { type: Boolean, default: false },
  time: { type: Number, default: 0 },
  timeEnd: { type: Number, default: 0 }
})
const emit = defineEmits(['callback'])
const timer = ref()
const time = ref({})
const timeDifference = (ksTime) => {
  let dateEnd = props.timeEnd ? props.timeEnd : new Date().getTime() //获取当前时间
  let dateDiff //时间差的毫秒数
  if (props.type) {
    dateDiff = dateEnd - ksTime
    if (dateEnd < ksTime) {
      emit('callback', false)
      clearInterval(timer.value)
      return { dd: '0', hh: '00', mm: '00', ss: '00' }
    }
  } else {
    dateDiff = ksTime - dateEnd
    if (ksTime < dateEnd) {
      emit('callback', false)
      clearInterval(timer.value)
      return { dd: '0', hh: '00', mm: '00', ss: '00' }
    }
  }
  let dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)) //计算出相差天数
  let leave1 = dateDiff % (24 * 3600 * 1000) //计算天数后剩余的毫秒数
  let hours = Math.floor(leave1 / (3600 * 1000)) //计算出小时数
  //计算相差分钟数
  let leave2 = leave1 % (3600 * 1000) //计算小时数后剩余的毫秒数
  let minutes = Math.floor(leave2 / (60 * 1000)) //计算相差分钟数
  //计算相差秒数
  let leave3 = leave2 % (60 * 1000) //计算分钟数后剩余的毫秒数
  let seconds = Math.round(leave3 / 1000)
  // console.log('相差' + dayDiff + '天' + hours + '小时' + minutes + '分钟' + seconds + '秒')
  return { dd: dayDiff, hh: hours, mm: minutes, ss: seconds }
}
const playTimer = () => {
  emit('callback', true)
  timer.value = setInterval(() => {
    time.value = timeDifference(props.time)
  }, 1000)
}
onMounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
  playTimer()
})
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>
