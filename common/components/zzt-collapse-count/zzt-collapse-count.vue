<template>
  <el-scrollbar class="zztCollapsecount-scrollbar">
    <el-collapse class="zztCollapsecount" v-model="activeNames" :accordion="props.accordion">
      <el-collapse-item
        :title="collapseItem[props.props.name]"
        :name="collapseItem[props.props.name]"
        v-for="(collapseItem, index) in modelValue"
        :key="index">
        <template #title>
          <div class="zztCollapseItemTitle">{{ collapseItem[props.props.name] }}</div>
        </template>
        <el-empty description="暂无数据" v-if="!collapseItem[props.props.list].length" size="20" />
        <div
          class="zztCollapseItemBody"
          :class="chooseList.find((v) => v[props.nodeId] === item[props.nodeId]) ? 'itemCheck' : ''"
          v-for="item in collapseItem[props.props.list]"
          :key="item[props.nodeId]"
          @click="countItem(item)"
          :title="item.name + ' ( ' + item.count + ' ) '">
          {{ item.name }}
          <span v-if="item?.count > -1">( {{ item.count }} )</span>
        </div>
      </el-collapse-item>
    </el-collapse>
  </el-scrollbar>
</template>

<script>
export default { name: 'ZztCollapseCount' }
</script>

<script setup>
import { ref, watch, onMounted } from 'vue'
const emit = defineEmits(['update:modelValue', 'callback'])
const props = defineProps({
  modelValue: { type: Array, default: () => [] }, // 数据数组
  collapseChoose: { type: Array, default: () => [] }, // 外部选中数据传入
  accordion: { type: Boolean, default: false },
  nodeId: { type: String, default: 'id' },
  props: {
    type: Object,
    default: () => {
      return { name: 'name', list: 'list' }
    }
  }
})
const activeNames = ref([])
const chooseList = ref([])
const isOnMounted = ref(false)

onMounted(() => {
  isOnMounted.value = true
})

watch(
  () => props.modelValue.length,
  (val) => {
    if (isOnMounted.value) {
      activeNames.value = props.modelValue.map((v) => v[props.props.name])
    }
  }
)

watch(
  () => props.collapseChoose,
  (val) => {
    chooseList.value = val
  }
)

const countItem = (item) => {
  if (!chooseList.value.find((v) => v[props.nodeId] === item[props.nodeId])) {
    chooseList.value.push(item)
  } else {
    chooseList.value = chooseList.value.filter((v) => item[props.nodeId] !== v[props.nodeId])
  }
  emit('callback', chooseList.value)
}
</script>

<style lang="scss">
.zztCollapsecount-scrollbar {
  width: 240px;
  height: auto;
  border: 1px solid #eeeeee;
  border-top: 0;

  .zztCollapsecount {
    height: 100%;

    .zztCollapseItemTitle {
      padding-left: 20px;
      font-weight: bold;
      font-size: var(--zy-name-font-size);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 10px;
        width: 0px;
        height: 16px;
        transform: translateY(-50%);
        border: 2px solid var(--zy-el-color-primary);
      }
    }

    .zztCollapseItemBody {
      padding: 10px 20px;
      cursor: pointer;
      font-size: var(--zy-text-font-size);
      line-height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      position: relative;

      &:hover {
        color: var(--zy-el-color-primary);
      }
    }

    .itemCheck {
      color: var(--zy-el-color-primary);
    }
  }
}
</style>
