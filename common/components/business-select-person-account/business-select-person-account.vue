<template>
  <div class="business-select-person-account">
    <div class="business-select-person-account-button">
      <el-button type="primary" @click="show = !show" :disabled="props.disabled">选择人员</el-button>
      <el-input v-model="keyWord" placeholder="请输入人员姓名" clearable />
    </div>
    <el-scrollbar class="business-select-person-account-body">
      <el-empty :image-size="120" description="暂未选中人员" v-if="!userData.length" />
      <div class="business-select-person-account-user" v-for="item in userData" :key="item.id">
        <div
          class="business-select-person-account-text row1"
          :class="row.class"
          v-for="row in userList"
          :key="row.key"
          :title="row.key === 'mobile' ? handleMobile(item[row.key]) : item[row.key]">
          {{ row.key === 'mobile' ? handleMobile(item[row.key]) : item[row.key] }}
        </div>
        <div class="business-select-person-account-del" @click="userDel(item)">
          <el-icon>
            <Delete />
          </el-icon>
        </div>
      </div>
    </el-scrollbar>
    <div class="business-select-person-account-pagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="show" name="选择人员">
      <template v-if="isShow">
        <select-person-account
          :userApi="props.userApi"
          :userData="userDataArr"
          :levelType="props.levelType"
          @callback="callback"></select-person-account>
      </template>
      <template v-if="!isShow">
        <standard-select-person-account :userData="userDataArr" @callback="callback"></standard-select-person-account>
      </template>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'BusinessSelectPersonAccount' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, watch } from 'vue'
import { encryptPhone } from 'common/js/utils.js'
import { readConfig, systemMobileEncrypt } from 'common/js/system_var.js'
const props = defineProps({
  userApi: { type: String, default: 'businessUserAccountList' },
  modelValue: { type: Array, default: () => [] },
  userList: {
    type: Array,
    default: () => [
      { key: 'userName', class: 'row2' },
      { key: 'mobile', class: 'row2' },
      { key: 'areaNames', class: 'row4' }
    ]
  },
  levelType: { type: String, default: 'all' }
})
const emit = defineEmits(['update:modelValue', 'callback'])
const keyWord = ref('')
const isShow = computed(() => readConfig.value.systemType === 'platform')
const userList = computed(() => props.userList)
const pageNo = ref(1)
const pageSize = ref(10)
const show = ref(false)
const userId = ref(props.modelValue)
const userData = ref([])
const userDataArr = ref([])
const userDataArrFilter = computed(() => {
  return userDataArr.value.filter((item) => item.userName.includes(keyWord.value))
})
const totals = computed(() => userDataArrFilter.value.length)
const handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)

const getSelectUser = async () => {
  if (!props.modelValue.length) {
    userDataArr.value = []
    handleQuery()
    emit('callback', [])
    return
  }
  const { data } = await api[props.userApi]({ ids: props.modelValue })
  userDataArr.value = data
  handleQuery()
  emit('callback', userDataArr.value)
}
const handleQuery = () => {
  userData.value = userDataArrFilter.value.slice(pageSize.value * (pageNo.value - 1), pageSize.value * pageNo.value)
}
const callback = (data) => {
  show.value = false
  if (data) {
    userDataArr.value = data
    handleQuery()
    handleUserId()
  }
}
const userDel = (row) => {
  userDataArr.value = userDataArr.value.filter((item) => item.id !== row.id)
  handleQuery()
  handleUserId()
}
const handleUserId = () => {
  emit(
    'update:modelValue',
    userDataArr.value.map((v) => v.id)
  )
  emit('callback', userDataArr.value)
}
watch(
  () => keyWord.value,
  () => {
    pageNo.value = 1
    handleQuery()
  },
  { immediate: true }
)
watch(
  () => props.modelValue,
  () => {
    userId.value = props.modelValue
    if (props.modelValue.length !== userDataArr.value.length) {
      getSelectUser()
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.business-select-person-account {
  width: 100%;
  border: 1px solid var(--zy-el-border-color-lighter);
  border-radius: var(--el-border-radius-base);

  .business-select-person-account-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: normal;
    padding: var(--zy-distance-five) var(--zy-distance-two);
    border-bottom: 1px solid var(--zy-el-border-color-lighter);

    .zy-el-button {
      height: var(--zy-height-secondary);
    }
    .zy-el-input {
      width: 220px;
      height: var(--zy-height-routine);
    }
  }

  .business-select-person-account-body {
    width: 100%;
    height: 288px;

    .business-select-person-account-user {
      display: flex;
      justify-content: space-between;
      padding: var(--zy-distance-four) 0;
      border-bottom: 1px solid var(--zy-el-border-color-lighter);

      .business-select-person-account-text {
        padding-left: var(--zy-distance-two);
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .row1 {
        flex: 1;
      }

      .row2 {
        flex: 2;
      }

      .row3 {
        flex: 3;
      }

      .row4 {
        flex: 4;
      }

      .business-select-person-account-del {
        width: 60px;
        display: flex;
        align-items: center;
        cursor: pointer;
        padding-left: var(--zy-distance-two);
      }
    }
  }

  .business-select-person-account-pagination {
    width: 100%;
    height: 42px;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid var(--zy-el-border-color-lighter);
    padding-right: var(--zy-distance-two);

    .zy-el-pagination {
      --zy-height: var(--zy-height-routine);
      --zy-el-component-size: var(--zy-height);

      .zy-el-select {
        width: 128px !important;
      }
    }
  }
}
</style>
