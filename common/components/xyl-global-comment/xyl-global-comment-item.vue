<template>
  <div class="xyl-global-comment-item">
    <div class="xyl-global-comment-info">
      <div class="xyl-global-comment-img">
        <el-image :src="imgUrl(commentObj.headImg)" fit="cover" />
      </div>
      <div class="xyl-global-comment-body">
        <div class="xyl-global-comment-status xyl-global-comment-centre" v-if="!commentObj.checkedStatus">审核中</div>
        <div class="xyl-global-comment-status" v-if="commentObj.checkedStatus === 2">审核不通过</div>
        <div class="xyl-global-comment-name">
          {{ commentObj.commentUserName }}
          <div v-if="user.accountId !== commentObj.publishAccountId && roleName">{{ roleName }}</div>
          <div v-if="user.accountId === commentObj.publishAccountId" class="xyl-global-comment-name-my">我</div>
          <span :title="commentObj.userArea">（{{ userArea }}）</span>
        </div>
        <text-expansion-collapse textClass="name">{{ commentObj.commentContent }}</text-expansion-collapse>
        <xyl-global-file :fileData="commentObj.fileInfos"></xyl-global-file>
        <div class="xyl-global-comment-time">
          {{ format(commentObj.createDate) }}
          <span
            @click="handleComment(commentObj)"
            v-if="user.accountId !== commentObj.publishAccountId && props.isReply">
            回复
          </span>
          <span @click="handleDel(commentObj)" v-if="user.accountId === commentObj.publishAccountId && props.isDel">
            删除
          </span>
          <slot :row="commentObj"></slot>
        </div>
        <div class="xyl-global-comment-input" v-if="commentId === commentObj.id">
          <el-input v-model="content" type="textarea" :autosize="{ minRows: 2 }" :placeholder="`请输入${props.text}`" />
          <div class="xyl-global-comment-button">
            <el-button @click="commentId = ''" link>取消回复</el-button>
            <el-button type="primary" @click="commentNew">回复</el-button>
          </div>
        </div>
        <div class="xyl-global-comment-child-list" v-if="commentObj.children.length">
          <div class="xyl-global-comment-child" v-for="row in commentObj.children.slice(0, commentNum)" :key="row.id">
            <div class="xyl-global-comment-img">
              <el-image :src="imgUrl(row.headImg)" fit="cover" />
            </div>
            <div class="xyl-global-comment-body">
              <div class="xyl-global-comment-status xyl-global-comment-centre" v-if="!row.checkedStatus">审核中</div>
              <div class="xyl-global-comment-status" v-if="row.checkedStatus === 2">审核不通过</div>
              <div class="xyl-global-comment-name">
                {{ row.commentUserName }}
                <span v-if="row.toCommenter && row.parentId !== commentObj.id">回复</span>
                {{ row.parentId !== commentObj.id ? row.toCommenter : '' }}
              </div>
              <text-expansion-collapse textClass="name">{{ row.commentContent }}</text-expansion-collapse>
              <xyl-global-file :fileData="row.fileInfos"></xyl-global-file>
              <div class="xyl-global-comment-time">
                {{ format(row.createDate) }}
                <span @click="handleComment(row)" v-if="user.accountId !== row.publishAccountId && props.isReply">
                  回复
                </span>
                <span @click="handleDel(row)" v-if="user.accountId === row.publishAccountId && props.isDel">删除</span>
                <slot :row="row"></slot>
              </div>
              <div class="xyl-global-comment-input" v-if="commentId === row.id">
                <el-input v-model="content" type="textarea" :autosize="{ minRows: 2 }" placeholder="请输入回复" />
                <div class="xyl-global-comment-button">
                  <el-button @click="commentId = ''" link>取消回复</el-button>
                  <el-button type="primary" @click="commentNew">回复</el-button>
                </div>
              </div>
            </div>
          </div>
          <div class="xyl-global-comment-more" v-if="commentObj.children.length > 2">
            <div
              class="xyl-global-comment-unfold"
              @click="moreComment(true)"
              v-if="commentObj.children.length > commentNum">
              展开更多
              <el-icon>
                <DArrowRight />
              </el-icon>
            </div>
            <div class="xyl-global-comment-put-away" @click="moreComment()" v-if="commentNum > 2">
              收起
              <el-icon>
                <DArrowRight />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'XylGlobalCommentItem' }
</script>
<script setup>
import api from '@/api'
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { format } from 'common/js/time.js'
import { ElMessage, ElMessageBox } from 'element-plus'
const store = useStore()
const props = defineProps({
  id: { type: String, default: '' },
  type: { type: String, default: '' },
  text: { type: String, default: '评论' },
  prompt: { type: Boolean, default: true },
  isDel: { type: Boolean, default: true },
  isReply: { type: Boolean, default: true },
  commentObj: { type: Object, default: () => ({}) },
  checked: { type: Boolean, default: true }
})
const emit = defineEmits(['refresh'])
// 用户信息
const user = computed(() => store.getters.getUserFn)

// 图片地址拼接组合
const imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))

const commentObj = computed(() => props.commentObj)
const userArea = computed(() => {
  const userAreaList = props.commentObj?.userArea?.split(',') || []
  if (userAreaList.length > 2) {
    return userAreaList.splice(0, 2).join('，') + ' 等'
  } else {
    return props.commentObj.userArea
  }
})
const roleName = computed(() => {
  if (props.commentObj.roles && props.commentObj.roles.length) {
    return props.commentObj?.roles[0]?.roleName
  } else {
    return ''
  }
})
const commentNum = ref(2)
const commentId = ref('')
const content = ref('')
const handleComment = (row) => {
  if (commentId.value === row.id) return
  commentId.value = row.id
  content.value = ''
}
const commentNew = async () => {
  const { code } = await api.commentNew({
    form: {
      businessCode: props.type,
      businessId: props.id,
      parentId: commentId.value,
      commentContent: content.value,
      terminalName: 'PC',
      checkedStatus: props.checked ? 1 : 0
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: `${props.text}成功` })
    commentId.value = ''
    content.value = ''
    emit('refresh', true)
  }
}
const moreComment = (type) => {
  if (type) {
    commentNum.value += 5
  } else {
    commentNum.value = 2
  }
}
const handleDel = (row) => {
  if (props.prompt) {
    ElMessageBox.confirm(`此操作将删除当前选中的${props.text}, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        commentDel(row.id)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消删除' })
      })
  } else {
    commentDel(row.id)
  }
}
const commentDel = async (id) => {
  const { code } = await api.commentDel({ ids: [id] })
  if (code === 200) {
    ElMessage({ type: 'success', message: '删除成功' })
    commentId.value = ''
    content.value = ''
    emit('refresh', commentObj.value.id !== id)
  }
}
</script>
<style lang="scss">
.xyl-global-comment-item {
  width: 100%;
  padding: 0 var(--zy-distance-two);

  .xyl-global-comment-info {
    width: 100%;
    display: flex;
    padding: var(--zy-distance-one) 0 var(--zy-distance-two) 0;
    border-bottom: 1px solid var(--zy-el-border-color-lighter);

    .xyl-global-comment-img {
      width: 66px;

      .zy-el-image {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
      }
    }

    .xyl-global-comment-body {
      width: calc(100% - 66px);
      position: relative;

      .xyl-global-comment-status {
        position: absolute;
        top: 0;
        right: 0;
        color: var(--el-color-info);
        font-weight: normal;
        font-size: var(--zy-text-font-size);
        line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));
      }

      .xyl-global-comment-centre {
        color: var(--el-color-warning);
      }

      .xyl-global-comment-name {
        display: flex;
        align-items: center;
        font-weight: bold;
        padding-bottom: var(--zy-distance-four);
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);

        div {
          font-weight: normal;
          display: inline-block;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          padding: 0 6px;
          color: var(--el-color-warning);
          background-color: var(--el-color-warning-light-9);
          border-radius: var(--el-border-radius-small);
          margin-left: var(--zy-distance-two);
        }

        .xyl-global-comment-name-my {
          color: var(--el-color-danger);
          background-color: var(--el-color-danger-light-9);
        }

        span {
          font-weight: 400;
          display: inline-block;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          margin-left: var(--zy-distance-three);
        }
      }

      .xyl-global-file {
        padding-top: var(--zy-font-name-distance-five);
        padding-bottom: 0;
      }

      .xyl-global-comment-time {
        color: var(--zy-el-text-color-regular);
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);
        padding-bottom: var(--zy-distance-four);

        span {
          font-weight: 400;
          display: inline-block;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
          margin-left: var(--zy-distance-three);
          cursor: pointer;
        }
      }

      .xyl-global-comment-input {
        .xyl-global-comment-button {
          display: flex;
          justify-content: flex-end;
          padding: var(--zy-distance-five) 0;
        }
      }

      .xyl-global-comment-child-list {
        width: 100%;
        background: var(--zy-el-color-info-light-9);
        margin-bottom: var(--zy-distance-four);
        padding-top: var(--zy-distance-three);

        .xyl-global-comment-child {
          width: 100%;
          display: flex;
          padding: var(--zy-distance-four) var(--zy-distance-two) 0 var(--zy-distance-two);

          .xyl-global-comment-img {
            width: 46px;

            .zy-el-image {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              overflow: hidden;
            }
          }

          .xyl-global-comment-body {
            width: calc(100% - 46px);

            .xyl-global-comment-name {
              span {
                margin: 0 var(--zy-distance-five);
                color: var(--zy-el-text-color-regular);
                font-size: var(--zy-name-font-size);
                line-height: var(--zy-line-height);
              }
            }
          }
        }

        .xyl-global-comment-more {
          position: relative;
          display: flex;
          align-items: center;
          cursor: pointer;
          padding-left: 98px;
          font-size: var(--zy-text-font-size);
          height: var(--zy-height);

          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            transform: translate(68px, -50%);
            width: 22px;
            height: 1px;
            background: var(--zy-el-color-info-light-5);
          }

          .xyl-global-comment-unfold {
            display: flex;
            align-items: center;
            margin-right: 32px;

            .zy-el-icon {
              transform: rotateZ(90deg);
            }
          }

          .xyl-global-comment-put-away {
            display: flex;
            align-items: center;

            .zy-el-icon {
              transform: rotateZ(-90deg);
            }
          }
        }
      }
    }
  }
}
</style>
