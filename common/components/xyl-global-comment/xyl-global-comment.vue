<template>
  <div class="xyl-global-comment">
    <div class="xyl-global-comment-head">
      <div class="xyl-global-comment-head-name">所有{{ props.text }}</div>
      <div class="xyl-global-comment-head-text">（共{{ totals }}条{{ props.text }}意见）</div>
    </div>
    <el-scrollbar class="xyl-global-comment-scrollbar" :class="{ 'xyl-global-comment-scroll': props.scroll }">
      <template v-if="props.scroll">
        <div v-infinite-scroll="load" :infinite-scroll-distance="50" class="xyl-global-comment-scroll">
          <xyl-global-comment-item
            v-for="item in commentList"
            :key="item.id"
            :commentObj="item"
            :id="props.id"
            :type="props.type"
            :prompt="props.prompt"
            :isDel="props.isDel"
            :isReply="props.isReply"
            @refresh="refresh"
            v-slot="{ row }">
            <slot :row="row"></slot>
          </xyl-global-comment-item>
          <div class="xyl-global-comment-loading-text" v-if="loading">加载中...</div>
          <div class="xyl-global-comment-loading-text" v-if="isShow">没有更多了</div>
        </div>
      </template>
      <template v-if="!props.scroll">
        <xyl-global-comment-item
          v-for="item in commentList"
          :key="item.id"
          :commentObj="item"
          :id="props.id"
          :type="props.type"
          :prompt="props.prompt"
          :isDel="props.isDel"
          :isReply="props.isReply"
          :checked="props.checked"
          @refresh="refresh"
          v-slot="{ row }">
          <slot :row="row"></slot>
        </xyl-global-comment-item>
      </template>
    </el-scrollbar>
    <div class="xyl-global-comment-pagination" v-if="!props.scroll">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
  </div>
</template>
<script>
export default { name: 'XylGlobalComment' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, defineAsyncComponent } from 'vue'
const XylGlobalCommentItem = defineAsyncComponent(() => import('./xyl-global-comment-item.vue'))
const props = defineProps({
  id: { type: String, default: '' },
  type: { type: String, default: '' },
  text: { type: String, default: '评论' },
  prompt: { type: Boolean, default: true },
  scroll: { type: Boolean, default: false },
  isDel: { type: Boolean, default: true },
  isReply: { type: Boolean, default: true },
  checked: { type: Boolean, default: true },
  params: { type: Object, default: () => ({}) }
})
onMounted(() => {
  twoLevelTree()
})

const pageNo = ref(1)
const pageSize = ref(10)
const totals = ref(0)
const isShow = ref(false)
const loading = ref(true)
const commentList = ref([])

const load = () => {
  if (pageNo.value * pageSize.value >= totals.value) return
  pageNo.value += 1
  twoLevelTree()
}
const handleQuery = () => {
  twoLevelTree()
}
const twoLevelTree = async (num) => {
  const { data, total } = await api.twoLevelTree({
    businessCode: props.type,
    businessId: props.id,
    pageNo: num ? 1 : pageNo.value,
    pageSize: num ? num : pageSize.value,
    ...props.params
  })
  commentList.value = num || !props.scroll ? data : [...commentList.value, ...data]
  totals.value = total
  loading.value = pageNo.value * pageSize.value < totals.value
  isShow.value = pageNo.value * pageSize.value >= totals.value
}
const refresh = (type) => {
  if (type) {
    twoLevelTree(props.scroll ? commentList.value.length + 1 : '')
  } else {
    twoLevelTree(props.scroll ? commentList.value.length - 1 : '')
  }
}
defineExpose({ id: props.id, refresh })
</script>
<style lang="scss">
.xyl-global-comment {
  width: 100%;
  height: 600px;

  .xyl-global-comment-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--zy-height);

    .xyl-global-comment-head-name {
      font-weight: bold;
      font-size: var(--zy-navigation-font-size);
    }

    .xyl-global-comment-head-text {
      font-size: var(--zy-text-font-size);
    }
  }

  .xyl-global-comment-scrollbar {
    width: 100%;
    height: calc(100% - (var(--zy-height) + 42px));
    border: 1px solid var(--zy-el-border-color-lighter);

    .xyl-global-comment-loading-text {
      color: var(--zy-el-text-color-regular);
      text-align: center;
      padding: var(--zy-distance-three) 0;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
    }
  }

  .xyl-global-comment-scroll {
    height: calc(100% - var(--zy-height));
  }

  .xyl-global-comment-pagination {
    width: 100%;
    height: 42px;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: var(--zy-distance-two);

    .zy-el-pagination {
      --zy-height: var(--zy-height-routine);
      --zy-el-component-size: var(--zy-height);

      .zy-el-select {
        width: 128px;
      }
    }
  }
}
</style>
