<template>
  <div class="global-collection-type">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入分类名称" clearable />
      </el-form-item>
      <el-form-item label="上级分类">
        <el-tree-select
          v-model="form.parentId"
          :data="typeData"
          check-strictly
          node-key="id"
          :render-after-expand="false"
          clearable />
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'GlobalCollectionType' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' }, typeId: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  parentId: '', // 上级分类
  name: '' // 分类名称
})
const rules = reactive({ name: [{ required: true, message: '请输入分类名称', trigger: ['blur', 'change'] }] })

const typeData = ref()
onMounted(() => {
  favoriteFolderList()
  if (props.id) {
    favoriteFolderInfo()
  }
})

const favoriteFolderList = async () => {
  const res = await api.favoriteFolderList()
  var { data } = res
  typeData.value = props.id ? filterData(data) : data
}
const filterData = (list) => {
  return list
    .filter((item) => {
      return item.id !== props.id
    })
    .map((item) => {
      item = Object.assign({}, item)
      if (item.children) {
        item.children = filterData(item.children)
      }
      return item
    })
}
const favoriteFolderInfo = async () => {
  const res = await api.favoriteFolderInfo({ detailId: props.id })
  var { data } = res
  form.name = data.name
  form.parentId = data.parentId === '0' ? '' : data.parentId
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(props.id ? '/favoriteFolder/edit' : '/favoriteFolder/add', {
    form: {
      id: props.id,
      name: form.name,
      parentId: form.parentId || '0'
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.global-collection-type {
  width: 680px;
}
</style>
