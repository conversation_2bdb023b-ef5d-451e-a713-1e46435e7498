<template>
  <div class="global-collection">
    <div class="global-collection-input">
      <el-input v-model="filterText" placeholder="搜索分类名称" @keyup.enter="handleFilter" clearable />
      <el-button type="primary" @click="handleFilter">搜索</el-button>
      <div class="global-collection-pack-up-key" @click="handlePackUpkey">一键收起</div>
    </div>
    <div class="transfer-cloud-dis-new">
      <el-button type="primary" @click="show = true">新建分类</el-button>
    </div>
    <el-scrollbar class="global-collection-scrollbar">
      <el-tree
        ref="treeRef"
        node-key="id"
        highlight-current
        :data="folderData"
        @node-click="handleNodeClick"
        :filter-node-method="filterNode">
        <template #default="{ data }">
          <div class="global-collection-text">{{ data.label }}</div>
        </template>
      </el-tree>
    </el-scrollbar>
    <div class="global-collection-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
    <xyl-popup-window v-model="show" name="新建分类">
      <GlobalCollectionType @callback="callback"></GlobalCollectionType>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'GlobalCollection' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, nextTick, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
const GlobalCollectionType = defineAsyncComponent(() => import('./global-collection-type.vue'))
const props = defineProps({
  id: { type: String, default: '' },
  code: { type: String, default: '' },
  theme: { type: String, default: '' }
})
const emit = defineEmits(['callback'])
const treeRef = ref()
const treeId = ref('#')
const filterText = ref('')
const folderData = ref([])
const show = ref(false)

onMounted(() => {
  favoriteFolderList()
})

const callback = () => {
  favoriteFolderList()
  show.value = false
}
const favoriteFolderList = async () => {
  const res = await api.favoriteFolderList()
  var { data } = res
  folderData.value = data
  nextTick(() => {
    treeRef.value.setCurrentKey(treeId.value)
  })
}
const handleFilter = () => {
  treeRef.value.filter(filterText.value)
}
const filterNode = (value, data) => {
  if (!value) return true
  return data.label.includes(value)
}
const handlePackUpkey = () => {
  const nodes = treeRef.value.store.nodesMap
  for (let i in nodes) {
    nodes[i].expanded = false
  }
}

const handleNodeClick = (data) => {
  treeId.value = data.id
}
const submitForm = async () => {
  transferFile()
}
const transferFile = async () => {
  const { code } = await api.favoriteAdd({
    form: {
      folderId: treeId.value,
      businessId: props.id,
      businessCode: props.code,
      theme: props.theme
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '收藏成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.global-collection {
  width: 680px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-three) 0;

  .global-collection-input {
    display: flex;
    align-items: center;
    padding: 0 var(--zy-distance-three);
    padding-bottom: var(--zy-distance-three);
    position: relative;

    .zy-el-input {
      width: 290px;
      margin-right: var(--zy-distance-two);
    }

    .global-collection-pack-up-key {
      position: absolute;
      top: calc(var(--zy-height) / 2);
      right: 16px;
      transform: translateY(-50%);
      color: var(--zy-el-color-primary);
      font-size: var(--zy-text-font-size);
      cursor: pointer;
    }
  }

  .transfer-cloud-dis-new {
    width: 100%;
    display: flex;
    align-items: center;
    padding: var(--zy-distance-five) var(--zy-distance-three);
    border: 1px solid var(--zy-el-border-color-lighter);

    .zy-el-button {
      height: var(--zy-height-secondary);
    }
  }

  .global-collection-scrollbar {
    width: 100%;
    height: calc(
      100% -
        (
          (var(--zy-height) * 2) + var(--zy-distance-two) + var(--zy-distance-three) + var(--zy-height-secondary) + 2px +
            (var(--zy-distance-five) * 2)
        )
    );
    border: 1px solid var(--zy-el-border-color-lighter);
    border-top: 0;

    .zy-el-scrollbar__view {
      padding: 0 var(--zy-distance-three);
    }

    .zy-el-tree-node.is-current {
      & > .zy-el-tree-node__content {
        color: var(--zy-el-color-primary);
      }
    }

    .zy-el-tree-node__content {
      height: auto;
      padding: var(--zy-distance-five) 0;

      .global-collection-text {
        width: calc(100% - 28px);
        text-overflow: clip;
        white-space: normal;
        position: relative;
        padding-left: var(--zy-distance-one);
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          width: var(--zy-title-font-size);
          height: var(--zy-title-font-size);
          background: url('./img/folder_icon.png') no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }

  .global-collection-button {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
