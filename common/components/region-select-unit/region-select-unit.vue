<template>
  <div class="region-select-unit">
    <div class="region-select-unit-body">
      <div class="region-select-unit-left">
        <div class="region-select-unit-input">
          <el-input v-model="keyword" placeholder="根据机构名称搜索" @keyup.enter="handleSearch" clearable />
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </div>
        <div class="region-select-unit-group">
          <div class="region-select-unit-group-name">地区列表</div>
          <el-scrollbar class="region-select-unit-group-tree">
            <el-tree
              ref="regionRef"
              node-key="id"
              :data="regionData"
              highlight-current
              :props="{ label: 'name' }"
              @node-click="handleNodeClick" />
          </el-scrollbar>
        </div>
        <div class="region-select-unit-tree-body">
          <div class="region-select-unit-name">
            机构列表
            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAll"></el-checkbox>
          </div>
          <el-scrollbar class="region-select-unit-tree">
            <el-tree
              ref="treeRef"
              node-key="id"
              show-checkbox
              check-strictly
              :data="officeData"
              @check-change="selectedClick"
              :filter-node-method="filterNode" />
          </el-scrollbar>
        </div>
      </div>
      <div class="region-select-unit-right">
        <div class="region-select-unit-right-name">
          已选机构（{{ selectData.length }}）
          <div @click="deleteAll">清空</div>
        </div>
        <el-scrollbar class="region-select-unit-right-body">
          <el-collapse v-model="regionCollapse">
            <el-collapse-item :name="item.id" v-for="item in regionUnitData" :key="item.id">
              <template #title>{{ item.name }}（{{ item.unitData.length }}）</template>
              <div class="region-select-unit-item" v-for="row in item.unitData" :key="row.id">
                <div class="region-select-unit-item-name ellipsis" :title="row.label">{{ row.label }}</div>
                <div class="region-select-unit-del" @click="deleteclick(row)">
                  <el-icon>
                    <Close />
                  </el-icon>
                </div>
              </div>
              <div class="zy-el-tree__empty-block" v-if="!item.unitData.length">
                <span class="zy-el-tree__empty-text">暂无数据</span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-scrollbar>
      </div>
    </div>
    <div class="region-select-unit-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'RegionSelectUnit' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted, nextTick } from 'vue'
const props = defineProps({
  unitData: { type: Array, default: () => [] },
  levelType: { type: String, default: 'all' }
})
const emit = defineEmits(['callback'])
const treeRef = ref()
const regionRef = ref()
const regionId = ref('')
const regionData = ref([])
const keyword = ref('')
const officeData = ref([])
const checkAll = ref(false)
const isIndeterminate = ref(false)
const idList = ref([])
const idAllList = ref([])
const selectData = ref([])
const selectAllList = ref([])
const regionCollapse = ref([])
const regionUnitData = computed(() => {
  var arrData = []
  var arrIndex = []
  selectData.value.forEach((item) => {
    if (arrIndex.includes(item.areaId)) {
      arrData.forEach((row) => {
        if (item.areaId === row.id) {
          row.unitData.push({ ...item })
        }
      })
    } else {
      arrIndex.push(item.areaId)
      arrData.push({ id: item.areaId, name: item.areaName, unitData: [{ ...item }] })
    }
  })
  return arrData
})
onMounted(() => {
  if (props.unitData.length) {
    unitSelectorList()
  }
  businessAreaTree()
})
const unitSelectorList = async () => {
  const { data } = await api.unitSelectorList({ ids: props.unitData.map((v) => v.id) })
  data.forEach((item) => {
    idList.value.push(item.id)
    selectData.value.push(item)
  })
  nextTick(() => {
    treeRef.value.setCheckedKeys(idList.value)
  })
}
const businessAreaTree = async () => {
  const { data } = await api.businessAreaTree({ levelType: props.levelType, isFilterUnUsing: 1 })
  regionId.value = data[0]?.id
  regionData.value = data
  nextTick(() => {
    regionRef.value.setCurrentKey(regionId.value)
    unitData()
  })
}
const handleNodeClick = (data) => {
  regionId.value = data.id
  unitData()
}
const unitData = async () => {
  const res = await api.unitData({ query: { areaId: regionId.value } })
  var { data } = res
  idAllList.value = []
  selectAllList.value = []
  officeData.value = data
  selectedMethods(data)
  checkAll.value = idAllList.value.every((elem) => idList.value.indexOf(elem) > -1)
  isIndeterminate.value = checkAll.value ? false : idList.value.length > 0
  nextTick(() => {
    treeRef.value.setCheckedKeys(idList.value)
  })
}
const selectedMethods = (data) => {
  data.forEach((item) => {
    idAllList.value.push(item.id)
    selectAllList.value.push(item)
    if (item.children && item.children.length > 0) {
      selectedMethods(item.children)
    }
  })
}
const handleSearch = () => {
  treeRef.value.filter(keyword.value)
  nextTick(() => {
    idAllList.value = []
    selectAllList.value = []
    if (keyword.value) {
      const data = traverseVisible(traverseNode(treeRef.value.root.childNodes))
      selectedFilter(data)
    } else {
      selectedFilter(officeData.value)
    }
    checkAll.value = idAllList.value.every((elem) => idList.value.indexOf(elem) > -1)
    isIndeterminate.value = checkAll.value ? false : idList.value.length > 0
    if (idAllList.value.length === 0) {
      checkAll.value = false
    }
  })
}
// 首次进来默认选中
const selectedFilter = (data) => {
  data.forEach((item) => {
    idAllList.value.push(item.id)
    selectAllList.value.push(item)
    if (item.children && item.children.length > 0) {
      selectedFilter(item.children)
    }
  })
}
const filterNode = (value, data) => {
  if (!value) return true
  return data.label.includes(value)
}
const traverseNode = (node) => {
  if (Object.prototype.toString.call(node) === '[object Array]') {
    return node.map((t) => traverseNode(t))
  }
  const data = { id: node.data.id, label: node.label, visible: node.visible, children: [] }
  const childNodes = node.childNodes
  childNodes.forEach((child) => {
    const item = traverseNode(child)
    if (item.visible) {
      data.visible = true
    }
    data.children.push(item)
  })
  return data
}
const traverseVisible = (arr) => {
  return arr.filter((t) => {
    let visible = t.visible
    if (!visible) {
      return false
    }
    if (t.children) {
      t.children = traverseVisible(t.children)
    }
    delete t.visible
    return visible
  })
}
// 下拉框选中事件
const selectedClick = (data, type) => {
  if (type) {
    if (!idList.value.includes(data.id)) {
      idList.value.push(data.id)
      selectData.value.push(data)
    }
  } else {
    idList.value = idList.value.filter((item) => item !== data.id)
    selectData.value = selectData.value.filter((item) => item.id !== data.id)
  }
  checkAll.value = idAllList.value.every((elem) => idList.value.indexOf(elem) > -1)
  isIndeterminate.value = checkAll.value ? false : idList.value.length > 0
  if (idAllList.value.length === 0) {
    checkAll.value = false
  }
}
const handleCheckAll = (val) => {
  idList.value = val
    ? [...new Set([...idList.value, ...idAllList.value])]
    : checkAllDel(idList.value, idAllList.value, true)
  selectData.value = val
    ? objTrim([...selectData.value, ...selectAllList.value])
    : checkAllDel(
        selectData.value,
        selectAllList.value.map((v) => v.id)
      )
  checkAll.value = idAllList.value.every((elem) => idList.value.indexOf(elem) > -1)
  isIndeterminate.value = false
  if (idList.value.length === 0 && idList.value.length === 0) {
    checkAll.value = false
  }
  nextTick(() => {
    treeRef.value.setCheckedKeys(idList.value)
  })
}
const objTrim = (data) => {
  let obj = {}
  data = data.reduce((cur, next) => {
    obj[next.id] ? '' : (obj[next.id] = true && cur.push(next))
    return cur
  }, [])
  return data
}
const checkAllDel = (data, idArr, type) => {
  return type ? data.filter((v) => !idArr.includes(v)) : data.filter((v) => !idArr.includes(v.id))
}
const deleteclick = (data) => {
  idList.value = idList.value.filter((item) => item !== data.id)
  selectData.value = selectData.value.filter((item) => item.id !== data.id)
  nextTick(() => {
    treeRef.value.setCheckedKeys(idList.value)
  })
}
const deleteAll = () => {
  ElMessageBox.confirm('此操作将删除当前已选的所有单位, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      idList.value = []
      selectData.value = []
      nextTick(() => {
        treeRef.value.setCheckedKeys([])
      })
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消删除' })
    })
}
const submitForm = () => {
  emit('callback', selectData.value)
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.region-select-unit {
  width: 990px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-four) var(--zy-distance-two) var(--zy-distance-two) var(--zy-distance-one);

  .region-select-unit-body {
    height: calc(100% - (var(--zy-height) + var(--zy-distance-two)));
    display: flex;
    justify-content: space-between;

    .region-select-unit-left {
      width: 600px;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .region-select-unit-input {
        width: 100%;
        display: flex;
        padding-bottom: var(--zy-distance-three);

        .zy-el-input {
          margin-right: var(--zy-distance-two);
        }
      }

      .region-select-unit-group {
        width: 300px;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);

        .region-select-unit-group-name {
          padding: var(--zy-distance-four) var(--zy-distance-three);
          font-weight: bold;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
        }

        .region-select-unit-group-tree {
          width: 100%;
          height: calc(100% - 45px);

          .zy-el-tree-node.is-current {
            & > .zy-el-tree-node__content {
              position: relative;

              .zy-el-tree-node__label {
                color: var(--zy-el-color-primary);

                &::after {
                  content: '';
                  position: absolute;
                  top: 0;
                  right: 0;
                  width: 4px;
                  height: 100%;
                  background: var(--zy-el-color-primary);
                }
              }
            }
          }

          .zy-el-tree-node__content {
            height: auto;
            padding: var(--zy-distance-five) 0;

            .zy-el-tree-node__label {
              width: calc(100% - 28px);
              text-overflow: clip;
              white-space: normal;
              line-height: var(--zy-line-height);
              font-size: var(--zy-text-font-size);
              padding-right: var(--zy-distance-one);
            }
          }
        }
      }

      .region-select-unit-tree-body {
        width: 300px;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);
        border-left: 0;

        .region-select-unit-name {
          padding: var(--zy-distance-four) var(--zy-distance-three);
          font-weight: bold;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          display: flex;
          align-items: center;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
          justify-content: space-between;

          .zy-el-checkbox {
            height: auto;
          }
        }

        .region-select-unit-tree {
          width: 100%;
          height: calc(100% - (var(--zy-name-font-size) * (var(--zy-line-height)) + (var(--zy-distance-four) * 2)));

          .zy-el-tree-node__content {
            height: auto;
            padding: var(--zy-distance-five) 0;
            position: relative;

            .zy-el-tree-node__label {
              width: calc(100% - 28px);
              text-overflow: clip;
              white-space: normal;
              line-height: var(--zy-line-height);
              font-size: var(--zy-text-font-size);
              padding-right: var(--zy-distance-one);
            }

            .zy-el-checkbox {
              position: absolute;
              top: 50%;
              margin: 0;
              right: var(--zy-distance-three);
              transform: translateY(-50%);
              font-size: 0;
              text-align: center;
            }
          }
        }
      }
    }

    .region-select-unit-right {
      width: 310px;
      height: 100%;

      .region-select-unit-right-name {
        line-height: var(--zy-height);
        padding: var(--zy-distance-three);
        font-size: var(--zy-name-font-size);
        padding-top: 0;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;

        div {
          font-size: var(--zy-text-font-size);
          color: var(--zy-el-text-color-regular);
          font-weight: normal;
          cursor: pointer;
        }
      }

      .region-select-unit-right-body {
        width: 100%;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));

        .zy-el-collapse {
          .zy-el-collapse-item__header {
            height: auto;
            line-height: var(--zy-line-height);
            font-size: var(--zy-text-font-size);
            padding: var(--zy-distance-four) var(--zy-distance-two);
          }

          .zy-el-collapse-item__content {
            border-top: 1px solid var(--zy-el-collapse-border-color);
            padding-bottom: var(--zy-distance-four);
          }
        }

        .region-select-unit-item {
          width: 290px;
          background: var(--el-color-info-light-9);
          padding: var(--zy-distance-five) var(--zy-distance-one);
          cursor: pointer;
          position: relative;
          margin-top: var(--zy-distance-four);
          border-radius: var(--el-border-radius-small);

          &::after {
            content: '';
            position: absolute;
            top: calc(var(--zy-distance-five) + var(--zy-font-text-distance-five));
            left: var(--zy-distance-two);
            transform: translateX(-50%);
            width: var(--zy-text-font-size);
            height: calc(var(--zy-text-font-size) * var(--zy-line-height));
            background: url('./img/select_person_unit_icon.png') no-repeat;
            background-size: var(--zy-text-font-size) var(--zy-text-font-size);
            background-position: center;
          }

          .region-select-unit-del {
            position: absolute;
            top: 0;
            right: 0;
            width: var(--zy-distance-one);
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--zy-navigation-font-size);
          }

          .region-select-unit-item-name {
            font-size: var(--zy-text-font-size);
            line-height: var(--zy-line-height);
            padding: var(--zy-font-text-distance-five) 0;
            height: calc(var(--zy-text-font-size) * var(--zy-line-height));
            box-sizing: content-box;
          }
        }
      }
    }
  }

  .region-select-unit-button {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);
    padding-right: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
