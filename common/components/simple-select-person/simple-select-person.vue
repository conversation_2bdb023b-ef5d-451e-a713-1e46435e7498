<template>
  <el-scrollbar class="simple-select-person" @click="handleUserClick">
    <div class="simple-select-person-placeholder forbidSelect" v-if="!userData.length">{{ placeholder }}</div>
    <global-tag v-for="item in userData" @close="handleClose(item)" :key="item.id">{{ item.userName }}</global-tag>
  </el-scrollbar>
  <xyl-popup-window v-model="show" name="选择用户">
    <select-person
      :max="props.max"
      :tabCode="props.tabCode"
      :userData="userData"
      :filterUser="props.filterUser"
      :params="props.params"
      :urlParams="props.urlParams"
      :dataMethod="props.dataMethod"
      @callback="callback"></select-person>
  </xyl-popup-window>
</template>
<script>
export default { name: 'SimpleSelectPerson' }
</script>
<script setup>
import api from '@/api'
import { ref, watch, computed } from 'vue'
const props = defineProps({
  modelValue: { type: Array, default: () => [] },
  filterUser: { type: Array, default: () => [] },
  disabled: { type: Boolean, default: false },
  max: { type: Number, default: 0 },
  placeholder: { type: String, default: '请选择用户' },
  tabCode: { type: Array, default: () => ['userOffice'] },
  params: { type: Object, default: () => ({}) },
  urlParams: { type: Object, default: () => ({}) },
  dataMethod: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['update:modelValue', 'callback', 'closeCallback'])

const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const show = ref(false)
const userId = ref(props.modelValue)
const isUserId = ref([])
const userData = ref([])
const placeholder = computed(() => props.placeholder)

const getSelectUser = async () => {
  const res = await api.getSelectUser({ logo: guid(), existsUserIds: userId.value })
  var { data } = res
  userData.value = data
  emit('callback', data)
}
const handleUserClick = () => {
  if (props.disabled) return
  show.value = true
}
const handleClose = (item) => {
  if (props.disabled) return
  userId.value = userId.value.filter((v) => v !== item.id)
  isUserId.value = isUserId.value.filter((v) => v !== item.id)
  userData.value = userData.value.filter((v) => v.id !== item.id)
  emit(
    'update:modelValue',
    userId.value.filter((v) => v !== item.id)
  )
  emit('callback', userData.value)
  emit('closeCallback', item, userData.value)
}
const callback = (data) => {
  show.value = false
  if (data) {
    emit(
      'update:modelValue',
      data.map((v) => v.id)
    )
  }
}
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue.length) {
      if (JSON.stringify(isUserId.value) !== JSON.stringify(props.modelValue)) {
        userId.value = props.modelValue
        isUserId.value = props.modelValue.map((v) => v)
        getSelectUser()
      }
    } else {
      userId.value = []
      isUserId.value = []
      userData.value = []
      emit('callback', [])
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.simple-select-person {
  width: 100%;
  min-height: var(--zy-height);
  max-height: 120px;
  line-height: normal;
  border-radius: var(--zy-el-border-radius-base);
  box-shadow: 0 0 0 1px var(--zy-el-border-color) inset;

  .zy-el-scrollbar__view {
    padding: 5px 10px;
  }

  .simple-select-person-placeholder {
    line-height: var(--zy-height);
    color: var(--zy-el-text-color-placeholder);
  }
}
</style>
