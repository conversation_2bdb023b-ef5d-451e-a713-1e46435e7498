<template>
  <div class="new-folder">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="上级文件夹">
        <el-tree-select
          v-model="form.parentId"
          :data="folderData"
          check-strictly
          node-key="id"
          :render-after-expand="false"
          :props="{ label: 'fileName' }"
          clearable />
      </el-form-item>
      <el-form-item label="文件夹名称" prop="fileName" class="globalFormTitle">
        <el-input v-model="form.fileName" placeholder="请输入文件夹名称" clearable />
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'NewFolder' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  fileName: '', // 文件夹名称
  parentId: '' // 上级文件夹
})
const rules = reactive({
  fileName: [{ required: true, message: '请输入文件夹名称', trigger: ['blur', 'change'] }]
})

const folderData = ref([])

onMounted(() => {
  myFolderTree()
})
const myFolderTree = async () => {
  const res = await api.myFolderTree()
  var { data } = res
  folderData.value = data
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const globalJson = async () => {
  const { code } = await api.myNewFolder({
    parentId: form.parentId || 0,
    fileName: form.fileName
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '新增成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.new-folder {
  width: 680px;
}
</style>
