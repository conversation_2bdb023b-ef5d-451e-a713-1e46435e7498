<template>
  <div class="transfer-cloud-disk">
    <div class="transfer-cloud-disk-input">
      <el-input v-model="filterText" placeholder="搜索文件夹名称" @keyup.enter="handleFilter" clearable />
      <el-button type="primary" @click="handleFilter">搜索</el-button>
      <div class="transfer-cloud-disk-PackUpKey" @click="handlePackUpkey">一键收起</div>
    </div>
    <div class="transfer-cloud-dis-new">
      <el-button type="primary" @click="show = true">新建文件夹</el-button>
    </div>
    <el-scrollbar class="transfer-cloud-disk-scrollbar">
      <el-tree
        ref="treeRef"
        highlight-current
        :data="folderData"
        @node-click="handleNodeClick"
        :filter-node-method="filterNode">
        <template #default="{ data }">
          <div class="transfer-cloud-disk-text">{{ data.fileName }}</div>
        </template>
      </el-tree>
    </el-scrollbar>
    <div class="transfer-cloud-disk-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
    <xyl-popup-window v-model="show" name="新建文件夹">
      <new-folder @callback="callback"></new-folder>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'TransferCloudDisk' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
const NewFolder = defineAsyncComponent(() => import('./new-folder.vue'))
const props = defineProps({
  module: { type: String, default: '' },
  fileId: { type: Array, default: () => [] }
})
const emit = defineEmits(['callback'])
const treeRef = ref()
const treeId = ref('')
const filterText = ref('')
const folderData = ref([])
const show = ref(false)

onMounted(() => {
  transferFolder()
})

const callback = () => {
  transferFolder()
  show.value = false
}
const transferFolder = async () => {
  const res = await api.transferFolder()
  var { data } = res
  folderData.value = data
}
const handleFilter = () => {
  treeRef.value.filter(filterText.value)
}
const filterNode = (value, data) => {
  if (!value) return true
  return data.fileName.includes(value)
}
const handlePackUpkey = () => {
  const nodes = treeRef.value.store.nodesMap
  for (let i in nodes) {
    nodes[i].expanded = false
  }
}

const handleNodeClick = (data) => {
  treeId.value = data.id
}
const submitForm = async () => {
  transferFile()
}
const transferFile = async () => {
  const { code } = await api.transferFile({ ids: props.fileId, parentId: treeId.value || 0, fileSource: props.module })
  if (code === 200) {
    ElMessage({ type: 'success', message: '转存成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.transfer-cloud-disk {
  width: 680px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-three) 0;

  .transfer-cloud-disk-input {
    display: flex;
    align-items: center;
    padding: 0 var(--zy-distance-three);
    padding-bottom: var(--zy-distance-three);
    position: relative;

    .zy-el-input {
      width: 290px;
      margin-right: var(--zy-distance-two);
    }

    .transfer-cloud-disk-PackUpKey {
      position: absolute;
      top: calc(var(--zy-height) / 2);
      right: 16px;
      transform: translateY(-50%);
      color: var(--zy-el-color-primary);
      font-size: var(--zy-text-font-size);
      cursor: pointer;
    }
  }

  .transfer-cloud-dis-new {
    width: 100%;
    display: flex;
    align-items: center;
    padding: var(--zy-distance-five) var(--zy-distance-three);
    border: 1px solid var(--zy-el-border-color-lighter);

    .zy-el-button {
      height: var(--zy-height-secondary);
    }
  }

  .transfer-cloud-disk-scrollbar {
    width: 100%;
    height: calc(
      100% -
        (
          (var(--zy-height) * 2) + var(--zy-distance-two) + var(--zy-distance-three) + var(--zy-height-secondary) + 2px +
            (var(--zy-distance-five) * 2)
        )
    );
    border: 1px solid var(--zy-el-border-color-lighter);
    border-top: 0;

    .zy-el-scrollbar__view {
      padding: 0 var(--zy-distance-three);
    }

    .zy-el-tree-node.is-current {
      & > .zy-el-tree-node__content {
        color: var(--zy-el-color-primary);
      }
    }

    .zy-el-tree-node__content {
      height: auto;
      padding: var(--zy-distance-five) 0;

      .transfer-cloud-disk-text {
        width: calc(100% - 28px);
        text-overflow: clip;
        white-space: normal;
        position: relative;
        padding-left: var(--zy-distance-one);
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          width: var(--zy-title-font-size);
          height: var(--zy-title-font-size);
          background: url('./img/folder_icon.png') no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }

  .transfer-cloud-disk-button {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
