<template>
  <div class="business-select-region">
    <div class="business-select-region-button">
      <el-button type="primary" @click="show = !show">选择地区</el-button>
      <el-input v-model="keyWord" placeholder="请输入地区名称" clearable />
    </div>
    <el-scrollbar class="usiness-select-region-body">
      <el-empty :image-size="120" description="暂未选中地区" v-if="!regionData.length" />
      <div class="usiness-select-region-user" v-for="item in regionData" :key="item.id">
        <div class="usiness-select-region-text row1">{{ item.name }}</div>
        <div class="usiness-select-region-del" @click="userDel(item)">
          <el-icon>
            <Delete />
          </el-icon>
        </div>
      </div>
    </el-scrollbar>
    <div class="business-select-region-pagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="show" name="选择地区">
      <select-region
        :regionData="regionDataArr"
        :levelType="props.levelType"
        :params="props.params"
        :urlParams="props.urlParams"
        @callback="callback"></select-region>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'BusinessSelectRegion' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, watch } from 'vue'
const props = defineProps({
  modelValue: { type: Array, default: () => [] },
  levelType: { type: String, default: 'all' },
  params: { type: Object, default: () => ({}) },
  urlParams: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['update:modelValue', 'callback'])
const keyWord = ref('')
const pageNo = ref(1)
const pageSize = ref(10)
const show = ref(false)
const userId = ref(props.modelValue)
const regionData = ref([])
const regionDataArr = ref([])
const regionDataArrFilter = computed(() => {
  return regionDataArr.value.filter((item) => item.name.includes(keyWord.value))
})
const totals = computed(() => regionDataArrFilter.value.length)
const getAreaTree = async () => {
  const { data } = await api.businessAreaTree({ levelType: props.levelType, isFilterUnUsing: 1 })
  selectedMethods(data)
  handleQuery()
  emit('callback', regionDataArr.value)
}
// 首次进来默认选中
const selectedMethods = (data) => {
  data.forEach((item) => {
    if (props.modelValue.includes(item.id)) {
      regionDataArr.value.push(item)
    }
    if (item.children && item.children.length > 0) {
      selectedMethods(item.children)
    }
  })
}
const handleQuery = () => {
  regionData.value = regionDataArrFilter.value.slice(pageSize.value * (pageNo.value - 1), pageSize.value * pageNo.value)
}
const callback = (data) => {
  show.value = false
  if (data) {
    regionDataArr.value = data
    emit(
      'update:modelValue',
      data.map((v) => v.id)
    )
    emit('callback', regionDataArr.value)
    handleQuery()
  }
}
const userDel = (row) => {
  regionDataArr.value = regionDataArr.value.filter((item) => item.id !== row.id)
  emit(
    'update:modelValue',
    regionDataArr.value.map((v) => v.id)
  )
  emit('callback', regionDataArr.value)
  handleQuery()
}
watch(
  () => keyWord.value,
  () => {
    pageNo.value = 1
    handleQuery()
  },
  { immediate: true }
)
watch(
  () => props.modelValue,
  () => {
    userId.value = props.modelValue
    if (props.modelValue.length !== regionDataArr.value.length) {
      getAreaTree()
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.business-select-region {
  width: 100%;
  height: 380px;
  border: 1px solid var(--zy-el-border-color-lighter);
  border-radius: var(--el-border-radius-base);

  .business-select-region-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: normal;
    padding: var(--zy-distance-five) var(--zy-distance-two);
    border-bottom: 1px solid var(--zy-el-border-color-lighter);

    .zy-el-button {
      height: var(--zy-height-secondary);
    }
    .zy-el-input {
      width: 220px;
      height: var(--zy-height-routine);
    }
  }

  .usiness-select-region-body {
    width: 100%;
    height: 288px;

    .usiness-select-region-user {
      display: flex;
      justify-content: space-between;
      padding: var(--zy-distance-four) 0;
      border-bottom: 1px solid var(--zy-el-border-color-lighter);

      .usiness-select-region-text {
        padding-left: var(--zy-distance-two);
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .row1 {
        flex: 1;
      }

      .row2 {
        flex: 2;
      }

      .row3 {
        flex: 3;
      }

      .row4 {
        flex: 4;
      }

      .usiness-select-region-del {
        width: 60px;
        display: flex;
        align-items: center;
        cursor: pointer;
        padding-left: var(--zy-distance-two);
      }
    }
  }

  .business-select-region-pagination {
    width: 100%;
    height: 42px;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid var(--zy-el-border-color-lighter);
    padding-right: var(--zy-distance-two);

    .zy-el-pagination {
      --zy-height: var(--zy-height-routine);
      --zy-el-component-size: var(--zy-height);

      .zy-el-select {
        width: 128px !important;
      }
    }
  }
}
</style>
