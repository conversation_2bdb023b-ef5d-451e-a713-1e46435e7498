<template>
  <div class="text-expansion-collapse" :style="{ maxHeight: height }">
    <div :class="['text-expansion-collapse-text', `font-size-${props.textClass}`]" ref="text">
      <slot></slot>
    </div>
    <div class="text-expansion-collapse-placeholder" :style="{ height: height }"></div>
    <div :class="['text-expansion-collapse-more', `font-size-${props.textClass}`]" @click="handleMore">查看更多</div>
    <div
      :class="['text-expansion-collapse-put-away', `font-size-${props.textClass}`]"
      @click="handleMore"
      v-if="isShow">
      收起
    </div>
  </div>
</template>
<script>
export default { name: 'TextExpansionCollapse' }
</script>
<script setup>
import { ref, onMounted, nextTick } from 'vue'
const props = defineProps({
  numberRow: { type: Number, default: 3 },
  textClass: { type: String, default: 'text' }
})
const text = ref()
const isShow = ref(false)
const height = ref('0px')
const rowHeight = ref('0px')
const rowLineHeight = ref('0px')
onMounted(() => {
  nextTick(() => {
    handleRowHeight()
  })
})

const handleRowHeight = () => {
  const divCss = getComputedStyle(text.value)
  rowHeight.value = Number(divCss.height.replace('px', ''))
  rowLineHeight.value = Number(divCss.lineHeight.replace('px', ''))
  height.value =
    rowHeight.value < rowLineHeight.value * props.numberRow
      ? `${rowHeight.value + 2}px`
      : `${rowLineHeight.value * props.numberRow}px`
}
const handleMore = () => {
  isShow.value = !isShow.value
  height.value = isShow.value
    ? `${rowHeight.value + rowLineHeight.value}px`
    : `${rowLineHeight.value * props.numberRow}px`
}
</script>
<style lang="scss">
.text-expansion-collapse {
  position: relative;
  width: 100%;
  max-height: 60px;
  overflow: hidden;
  margin-bottom: 6px;

  .text-expansion-collapse-text {
    width: 100%;
    float: right;
    margin-left: -99px;
    line-height: var(--zy-line-height);
    word-break: break-all;
    white-space: pre-wrap;
  }

  .text-expansion-collapse-placeholder {
    width: 99px;
    height: 60px;
    opacity: 0.8;
    float: right;
    pointer-events: none;
  }

  .text-expansion-collapse-more {
    position: relative;
    left: 100%;
    transform: translate(-100%, -100%);
    width: 99px;
    text-align: right;
    float: right;
    color: var(--zy-el-color-primary);
    line-height: var(--zy-line-height);
    cursor: pointer;
    background-image: linear-gradient(to left, white 52%, rgba(255, 255, 255, 0.8) 90%, transparent 100%);
  }

  .text-expansion-collapse-put-away {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 99px;
    text-align: right;
    color: var(--zy-el-color-primary);
    cursor: pointer;
    line-height: var(--zy-line-height);
  }

  .font-size-text {
    font-size: var(--zy-text-font-size);
  }

  .font-size-name {
    font-size: var(--zy-name-font-size);
  }
}
</style>
