<template>
  <div class="edit-record">
    <div class="globalTable">
      <el-table :data="tableData">
        <el-table-column label="编辑人" prop="recordUserName" show-overflow-tooltip />
        <el-table-column label="编辑时间">
          <template #default="scope">{{ format(scope.row.recordTime) }}</template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right" class-name="globalTableCustom">
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)" type="primary" plain>查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="show" name="编辑记录详情">
      <edit-record-info :id="id"></edit-record-info>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'EditRecord' }
</script>
<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { format } from 'common/js/time.js'
const EditRecordInfo = defineAsyncComponent(() => import('./edit-record-info.vue'))
const props = defineProps({
  id: { type: String, default: '' },
  type: { type: String, default: '' }
})
const id = ref('')
const show = ref(false)
const { totals, pageNo, pageSize, tableData, handleQuery } = GlobalTable({
  tableApi: 'editRecordList',
  tableDataObj: { query: { businessId: props.id, recordType: props.type } }
})

onMounted(() => {
  handleQuery()
})

const handleEdit = (item) => {
  id.value = item.id
  show.value = true
}
</script>
<style lang="scss" scoped>
.edit-record {
  width: 680px;
  height: calc(85vh - 52px);
  padding: 20px;
  padding-bottom: 0;

  .globalTable {
    width: 100%;
    height: calc(100% - 52px);
  }
}
</style>
