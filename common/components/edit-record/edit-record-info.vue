<template>
  <div class="edit-record-info">
    <div class="globalTable">
      <el-table :data="tableData">
        <el-table-column label="字段名称" prop="editFieldName" show-overflow-tooltip />
        <el-table-column label="修改前" prop="oldValue" show-overflow-tooltip />
        <el-table-column label="修改后" prop="newValue" show-overflow-tooltip />
      </el-table>
    </div>
  </div>
</template>
<script>
export default { name: 'EditRecordInfo' }
</script>
<script setup>
import { onMounted } from 'vue'
import { GlobalTable } from 'common/js/GlobalTableTree.js'
const props = defineProps({ id: { type: String, default: '' } })
const { tableData, handleQuery } = GlobalTable({ tableApi: 'editRecordInfo', tableDataObj: { detailId: props.id } })

onMounted(() => {
  handleQuery()
})
</script>
<style lang="scss" scoped>
.edit-record-info {
  width: 990px;
  height: calc(85vh - 52px);
  padding: 20px;

  .globalTable {
    width: 100%;
    height: 100%;
  }
}
</style>
