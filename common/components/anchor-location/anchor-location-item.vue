<template>
  <div class="anchor-location-item" ref="elRef">
    <slot></slot>
  </div>
</template>

<script>
export default { name: 'AnchorLocationItem' }
</script>

<script setup>
import { ref } from 'vue'
const props = defineProps({ value: { type: String, default: '' }, label: { type: String, default: '' } })
const elRef = ref()
defineExpose({ name: 'anchor-location-item', elRef, value: props.value, label: props.label })
</script>

<style lang="scss">
.anchor-location-item {
  padding: var(--zy-distance-one);
}
</style>
