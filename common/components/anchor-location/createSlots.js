import { h, ref, watch, inject, nextTick } from 'vue'
export default (slots) => {
  return {
    setup () {
      const slotRefs = ref({})
      const handleSlotRef = inject('handleSlotRef')
      const handleSlotUpdate = inject('handleSlotUpdate')
      const handleRef = (el) => {
        if (el?.name === 'anchor-location-item') {
          slotRefs.value[el.value] = el
        }
      }
      watch(() => slots.default(), () => {
        handleSlotUpdate()
        slotRefs.value = {}
        nextTick(() => {
          let slotElRefs = []
          const defaultSlots = slots.default()
          for (let index = 0; index < defaultSlots.length; index++) {
            const item = defaultSlots[index]
            if (item.type.name === 'AnchorLocationItem') {
              slotElRefs.push(slotRefs.value[item.props.value])
            }
          }
          handleSlotRef(slotElRefs)
        })
      }, { immediate: true })
      return () => slots.default().map(item => h(item, { ref: handleRef }))
    }
  }
}
