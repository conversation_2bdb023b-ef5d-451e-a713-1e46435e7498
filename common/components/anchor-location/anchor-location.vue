<template>
  <el-scrollbar class="anchor-location" ref="scrollbarRef" @scroll="scroll">
    <anchor-location-navigation
      v-model="activeValue"
      v-show="navigationList.length > 1"
      :data="navigationList"
      @change="AnchorLinkTo"></anchor-location-navigation>
    <div class="anchor-location-function">
      <div class="anchor-location-function-list">
        <slot name="function"></slot>
      </div>
    </div>
    <div class="anchor-location-container">
      <div class="anchor-location-body">
        <MySlot />
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
export default { name: 'AnchorLocation' }
</script>

<script setup>
import { ref, computed, provide, onMounted, onBeforeUnmount, nextTick, useSlots, defineAsyncComponent } from 'vue'
import createSlot from './createSlots.js'
import elementResizeDetectorMaker from 'element-resize-detector'
const AnchorLocationNavigation = defineAsyncComponent(() => import('./anchor-location-navigation.vue'))
const erd = elementResizeDetectorMaker()
const props = defineProps({ modelValue: [String, Number] })
const emit = defineEmits(['update:modelValue'])
const slots = useSlots()
const MySlot = createSlot(slots)
const scrollbarRef = ref()
const scrollbarTop = ref(0)
const isScrollTop = ref(0)
const activeValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const timer = ref()
const elRefs = ref({})
const navigationList = ref([])
const handleSlotRef = (data) => {
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    elRefs.value[item.value] = item.elRef
    navigationList.value.push({ value: item.value, label: item.label })
  }
}
const handleSlotUpdate = () => {
  elRefs.value = {}
  navigationList.value = []
}
const scroll = ({ scrollTop }) => {
  scrollbarTop.value = scrollTop
  if (!navigationList.value.length) return
  let keyValue = ''
  for (let key in elRefs.value) {
    if (scrollTop < elRefs.value[key]?.offsetTop + elRefs.value[key]?.offsetHeight / 2 && !keyValue) {
      keyValue = key
    }
  }
  if (scrollTop > isScrollTop.value || !keyValue) {
    const array = Object.keys(elRefs.value)
    keyValue = array[array.length - 1]
  }
  activeValue.value = keyValue
}
const AnchorLinkTo = (item) => {
  clearInterval(timer.value)
  if (scrollbarTop.value >= elRefs.value[item.value].offsetTop) {
    timer.value = setInterval(() => {
      if (scrollbarTop.value <= elRefs.value[item.value].offsetTop) {
        clearInterval(timer.value)
        activeValue.value = item.value
      } else {
        if (scrollbarTop.value - 52 <= elRefs.value[item.value].offsetTop) {
          scrollbarRef.value?.setScrollTop(elRefs.value[item.value].offsetTop)
        } else {
          scrollbarRef.value?.setScrollTop(scrollbarTop.value - 52)
        }
      }
    }, 2)
  } else {
    timer.value = setInterval(() => {
      if (scrollbarTop.value >= elRefs.value[item.value].offsetTop || scrollbarTop.value > isScrollTop.value) {
        clearInterval(timer.value)
        activeValue.value = item.value
      } else {
        if (scrollbarTop.value + 52 >= elRefs.value[item.value].offsetTop) {
          scrollbarRef.value?.setScrollTop(elRefs.value[item.value].offsetTop)
        } else {
          scrollbarRef.value?.setScrollTop(scrollbarTop.value + 52)
        }
      }
    }, 6)
  }
}
onMounted(() => {
  nextTick(() => {
    erd.listenTo(scrollbarRef.value.$el.querySelector('.anchor-location-body'), () => {
      const body = scrollbarRef.value.$el.querySelector('.anchor-location-body')
      isScrollTop.value = body.offsetHeight - scrollbarRef.value.$el.offsetHeight + 38
    })
  })
})
onBeforeUnmount(() => {
  erd.uninstall(scrollbarRef.value.$el.querySelector('.anchor-location-body'))
})
provide('handleSlotRef', handleSlotRef)
provide('handleSlotUpdate', handleSlotUpdate)
</script>

<style lang="scss">
.anchor-location {
  width: 100%;
  height: 100%;

  .anchor-location-function {
    width: 960px;
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);

    .anchor-location-function-list {
      position: absolute;
      top: 0;
      right: -10px;
      transform: translateX(100%);
    }
  }

  & > .zy-el-scrollbar__wrap {
    & > .zy-el-scrollbar__view {
      .anchor-location-container {
        padding: 20px;

        .anchor-location-body {
          max-width: 960px;
          margin: auto;
          background-color: #fff;
          box-shadow: 0px 2px 12px 1px rgba(0, 0, 0, 0.16);
          position: relative;
        }
      }
    }
  }
}
</style>
