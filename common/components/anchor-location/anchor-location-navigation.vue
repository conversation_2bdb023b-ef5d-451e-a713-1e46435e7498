<template>
  <div class="anchor-location-navigation">
    <div class="anchor-location-navigation-list">
      <div
        class="anchor-location-navigation-item"
        v-for="item in data"
        :key="item.value"
        :title="item.label"
        :class="{ 'is-active': value === item.value }"
        @click="change(item)">
        <div class="anchor-location-navigation-name">{{ item.label }}</div>
        <div class="anchor-location-navigation-info">
          <div class="anchor-location-navigation-icon" v-html="icon"></div>
          <div class="anchor-location-navigation-name">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default { name: 'AnchorLocationNavigation' }
</script>

<script setup>
import { computed } from 'vue'
const props = defineProps({ modelValue: [String, Number], data: { type: Array, default: () => [] } })
const emit = defineEmits(['update:modelValue', 'change'])
const icon =
  '<svg t="1757321584896" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6865" id="mx_n_1757321584897" width="38" height="38"><path d="M897.5872 423.8336l-121.1392-76.4928c-3.0208-1.6896-6.4-2.6112-9.8304-2.6112-3.4304 0-6.8608 0.8192-9.9328 2.6112l-121.1392 76.4928c-6.144 3.4816-9.9328 9.9328-9.9328 16.7936v153.088c0 6.912 3.7888 13.312 9.9328 16.7424l121.1392 76.544c3.072 1.792 6.5024 2.6112 9.9328 2.6112 3.4304 0 6.8096-0.8704 9.8304-2.6112l121.1392-76.544c6.144-3.4304 9.9328-9.8304 9.9328-16.7424v-153.088c0-6.912-3.7888-13.312-9.9328-16.7936z" fill="#4B81EE" opacity=".5" p-id="6866"></path><path d="M744.4992 308.736L473.9584 137.7792a45.184 45.184 0 0 0-44.288 0L159.1296 308.736c-13.6704 7.7312-22.1184 22.0672-22.1184 37.5296v341.9136c0 15.4624 8.448 29.7472 22.1184 37.5296l270.5408 170.9568a45.184 45.184 0 0 0 44.288 0l270.5408-170.9568c13.6704-7.7312 22.1184-22.0672 22.1184-37.5296V346.2144c0-15.4624-8.448-29.7472-22.1184-37.4784z m-220.672 354.048H379.7504c-37.9392 0-68.6592-30.0544-68.6592-67.1744 0-37.12 30.72-67.2256 68.6592-67.2256h49.5104V470.1184c-20.736-8.5504-35.2768-28.672-35.2768-52.1216 0-31.232 25.856-56.576 57.7536-56.576 31.9488 0 57.8048 25.344 57.8048 56.576 0 23.3984-14.5408 43.5712-35.2768 52.1216v58.2656h49.5104c37.9392 0 68.6592 30.1056 68.6592 67.2256 0.0512 37.12-30.6688 67.1744-68.608 67.1744z" fill="#4B81EE" p-id="6867"></path></svg>'
const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const data = computed(() => props.data)
const change = (item) => {
  emit('change', item)
  value.value = item.value
}
</script>

<style lang="scss">
.anchor-location-navigation {
  width: 960px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;

  .anchor-location-navigation-list {
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translate(-100%, -50%);

    .anchor-location-navigation-item {
      padding-right: 42px;
      margin-bottom: 40px;
      position: relative;
      cursor: pointer;

      .anchor-location-navigation-name {
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);
      }

      .anchor-location-navigation-info {
        height: 42px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        border-radius: 21px;
        position: absolute;
        top: 50%;
        left: 100%;
        transform: translate(-47.8px, -50%);

        .anchor-location-navigation-icon {
          width: 38px;
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 38px;
            height: 38px;

            path {
              fill: var(--zy-el-color-info);
            }
          }
        }

        .anchor-location-navigation-name {
          display: none;
          white-space: nowrap;
          padding: 0 var(--zy-font-name-distance-five);
        }
      }

      &::before {
        content: '';
        position: absolute;
        top: calc(50% + 5px);
        right: 20px;
        width: 2px;
        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + 30px);
        background: var(--zy-el-border-color-lighter);
      }

      &:last-child {
        &::before {
          background: transparent;
        }
      }
    }

    .is-active {
      font-weight: bold;

      &::after {
        border: 2px solid var(--zy-el-color-primary);
      }
      .anchor-location-navigation-info {
        .anchor-location-navigation-icon {
          svg {
            path {
              fill: var(--zy-el-color-primary);
            }
            path + path {
              fill: var(--zy-el-color-primary);
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1560px) {
  .anchor-location-navigation {
    .anchor-location-navigation-list {
      .anchor-location-navigation-item {
        .anchor-location-navigation-name {
          color: transparent;
        }

        .anchor-location-navigation-info {
          &:hover {
            background: #fff;
            box-shadow: var(--zy-el-box-shadow-lighter);

            .anchor-location-navigation-name {
              display: block;
              color: var(--zy-el-text-color-primary);
            }
          }
        }
      }
    }
  }
}
</style>
