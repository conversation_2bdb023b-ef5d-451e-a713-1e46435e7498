<template>
  <div class="anchor-location-navigation">
    <div class="anchor-location-navigation-list">
      <div
        class="anchor-location-navigation-item"
        v-for="item in data"
        :key="item.value"
        :title="item.label"
        :class="{ 'is-active': value === item.value }"
        @click="change(item)">
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script>
export default { name: 'AnchorLocationNavigation' }
</script>

<script setup>
import { computed } from 'vue'
const props = defineProps({ modelValue: [String, Number], data: { type: Array, default: () => [] } })
const emit = defineEmits(['update:modelValue', 'change'])
const value = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const data = computed(() => props.data)
const change = (item) => {
  emit('change', item)
  value.value = item.value
}
</script>

<style lang="scss">
.anchor-location-navigation {
  width: 960px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  .anchor-location-navigation-list {
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translate(-100%, -50%);

    .anchor-location-navigation-item {
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      padding-right: 20px;
      margin-bottom: 40px;
      position: relative;
      cursor: pointer;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        border: 2px solid var(--zy-el-border-color-lighter);
        transform: translateY(-50%);
      }

      &::before {
        content: '';
        position: absolute;
        top: calc(50% + 5px);
        right: 4px;
        width: 2px;
        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + 30px);
        background-color: var(--zy-el-border-color-lighter);
      }

      &:last-child {
        &::before {
          background-color: transparent;
        }
      }
    }

    .is-active {
      font-weight: bold;

      &::after {
        border: 2px solid var(--zy-el-color-primary);
      }
    }
  }
}

@media screen and (max-width: 1580px) {
  .anchor-location-navigation {
    .anchor-location-navigation-list {
      .anchor-location-navigation-item {
        color: transparent;
      }
    }
  }
}
</style>
