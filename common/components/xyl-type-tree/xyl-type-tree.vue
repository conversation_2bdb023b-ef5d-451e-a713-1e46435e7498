<template>
  <div class="xyl-type-tree">
    <div class="xyl-type-tree-name">{{ props.name }}</div>
    <el-input v-model="keyWord" placeholder="请输入关键词" clearable />
    <el-scrollbar class="xyl-type-tree-scrollbar">
      <el-tree
        ref="treeRef"
        highlight-current
        :data="treeData"
        :props="props.props"
        :node-key="props.nodeKey"
        :default-expand-all="props.expandedAll"
        :default-expanded-keys="props.expandedKeys"
        :filter-node-method="filterNode"
        @node-click="handleNodeClick">
        <template #default="{ node }">
          <span class="zy-el-tree-node__label" :title="node.label">{{ node.label }}</span>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>
<script>
export default { name: 'XylTypeTree' }
</script>
<script setup>
import { ref, watch, nextTick } from 'vue'
const props = defineProps({
  modelValue: [String, Number],
  data: { type: Array, default: () => [] },
  nodeKey: { type: String, default: 'id' },
  name: { type: String, default: '' },
  props: {
    type: Object,
    default: () => {
      return { children: 'children', label: 'label' }
    }
  },
  isChild: { type: Boolean, default: false },
  expandedAll: { type: Boolean, default: false },
  expandedKeys: { type: Array, default: () => [] }
})
const emit = defineEmits(['update:modelValue', 'select'])
const delay = (function () {
  let timer = 0
  return function (callback, ms) {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
const treeRef = ref()
const treeId = ref()
const treeData = ref()
const keyWord = ref('')

watch(
  () => props.modelValue,
  () => {
    treeId.value = props.modelValue
    delay(() => {
      selectedMethods(props.data)
    }, 200)
  },
  { immediate: true }
)
watch(
  () => props.data,
  () => {
    treeData.value = props.data
    delay(() => {
      selectedMethods(props.data)
    }, 200)
  },
  { immediate: true }
)
watch(
  () => keyWord.value,
  () => {
    treeRef.value.filter(keyWord.value)
  }
)
const filterNode = (value, data) => {
  if (!value) return true
  return data[props.props.label].includes(value)
}

const handleNodeClick = (data) => {
  if (props.isChild) {
    if (data[props.props.children] && data[props.props.children].length) {
      nextTick(() => {
        treeRef.value.setCurrentKey(treeId.value, false)
      })
    } else {
      emit('update:modelValue', data[props.nodeKey])
    }
  } else {
    emit('update:modelValue', data[props.nodeKey])
  }
}
// 首次进来默认选中
const selectedMethods = (data) => {
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item[props.nodeKey] === treeId.value) {
      emit('select', item)
      nextTick(() => {
        treeRef.value.setCurrentKey(treeId.value)
      })
    }
    if (item[props.props.children] && item[props.props.children].length) {
      selectedMethods(item[props.props.children])
    }
  }
}
</script>
<style lang="scss">
.xyl-type-tree {
  width: 240px;
  height: 100%;
  padding: var(--zy-distance-four) 0;

  .xyl-type-tree-name {
    border: 1px solid var(--zy-el-border-color-lighter);
    border-bottom: 0;
    padding: var(--zy-distance-four) var(--zy-distance-three);
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
  }

  .xyl-type-tree-scrollbar {
    width: 100%;
    height: calc(
      100% - (var(--zy-height) + (var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-four) * 2))
    );
    border: 1px solid var(--zy-el-border-color-lighter);
    border-top: 0;

    .zy-el-tree-node.is-current {
      & > .zy-el-tree-node__content {
        .zy-el-tree-node__label {
          color: var(--zy-el-color-primary);
        }
      }
    }

    .zy-el-tree-node__content {
      height: auto;
      padding: var(--zy-distance-five) 0;

      .zy-el-tree-node__label {
        width: calc(100% - 28px);
        text-overflow: clip;
        white-space: normal;
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);
        padding-right: var(--zy-distance-one);
      }
    }
  }
}
</style>
