<template>
  <div class="xyl-upload-file">
    <el-upload
      drag
      action="/"
      :class="{ 'zy-is-upload-disabled': disabled }"
      :before-upload="handleFile"
      :http-request="fileUpload"
      :show-file-list="false"
      :disabled="disabled"
      multiple
      @click="handleClick">
      <el-icon class="zy-el-icon--upload">
        <upload-filled />
      </el-icon>
      <div class="zy-el-upload__text">
        将附件拖拽至此区域，或
        <em>点击上传</em>
      </div>
      <div class="zy-el-upload__tip">仅支持{{ props.fileType.join('、') }}格式</div>
    </el-upload>
    <div class="xyl-upload-file-list">
      <div
        class="xyl-upload-file-item ellipsis"
        :class="{ 'xyl-upload-file-item-disabled': disabled }"
        v-for="item in fileSucceed"
        :key="item.uid">
        <div class="xyl-upload-file-icon">
          <el-icon>
            <Document />
          </el-icon>
        </div>
        {{ item.fileName }}
        <slot :row="item"></slot>
        <div class="xyl-upload-file-succes" v-if="item.fileURL">
          <el-icon>
            <CircleCheck />
          </el-icon>
        </div>
        <div class="xyl-upload-file-preview" @click="handlePreview(item)" v-if="item.fileURL">
          <el-icon>
            <View />
          </el-icon>
        </div>
        <div class="xyl-upload-file-download" @click="handleDownload(item)" v-if="item.fileURL">
          <el-icon>
            <Download />
          </el-icon>
        </div>
        <div class="xyl-upload-file-del" @click="handleDel(item)" v-if="item.fileURL && !disabled">
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
      <div class="xyl-upload-file-item ellipsis" v-for="item in fileArrAy" :key="item.uid">
        <div class="xyl-upload-file-icon">
          <el-icon>
            <Document />
          </el-icon>
        </div>
        {{ item.fileName }}
        <el-progress :percentage="item.progress" :show-text="false" v-if="!item.fileURL" :stroke-width="2" />
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'XylUploadFile' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, watch } from 'vue'
import { downloadFile } from '../../config/MicroGlobal'
import { globalFileLocation } from '../../config/location'
import { ElMessage } from 'element-plus'
const emit = defineEmits(['click', 'fileUpload', 'isAllSucceed'])
const props = defineProps({
  max: { type: Number, default: 0 },
  apiName: { type: String, default: 'globalUpload' }, // 上传的api
  fileData: { type: Array, default: () => [] },
  fileType: {
    type: Array,
    default: () => [
      'jpg',
      'png',
      'gif',
      'jpeg',
      'txt',
      'doc',
      'docx',
      'wps',
      'ppt',
      'pptx',
      'pdf',
      'ofd',
      'xls',
      'xlsx',
      'zip',
      'rar',
      'amr',
      'mp4',
      'avi',
      'wav'
    ]
  },
  disabled: { type: Boolean, default: false }
})

const disabled = computed(() => props.disabled)
const fileSucceed = ref([])
const fileArrAy = ref([])

const defaultFile = () => {
  fileSucceed.value = props.fileData.map((v) => ({
    ...v,
    uid: guid(),
    fileName: v.originalFileName,
    fileURL: api.fileURL(v.newFileName),
    progress: 100
  }))
}
const handleClick = (e) => {
  if (disabled.value) return
  emit('click', e)
}
const handleFile = (file) => {
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
  // const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
  const isShow = props.fileType.includes(fileType)
  if (!isShow) {
    ElMessage({ type: 'warning', message: `仅支持${props.fileType.join('、')}格式!` })
  }
  return isShow
}
const fileUpload = (file) => {
  if (props.max && fileSucceed.value.length + fileArrAy.value.length >= props.max) {
    ElMessage({ type: 'warning', message: `最多只能上传${props.max}个文件！` })
    return false
  }
  const param = new FormData()
  param.append('file', file.file)
  globalUpload(param, guid(), file.file.name, file.file.uid)
}
const globalUpload = async (params, uid, name, time) => {
  try {
    fileArrAy.value.push({ uid, fileName: name, progress: 0 })
    emit('isAllSucceed', !fileArrAy.value.length)
    const res = await api[props.apiName](params, onUploadProgress, uid)
    var { data } = res
    fileArrAy.value = fileArrAy.value.filter((item) => item.uid !== uid)

    const newData = []
    const newSortData = []
    const newSucceedData = [
      ...fileSucceed.value,
      {
        ...data,
        uid: uid,
        time: time,
        fileName: data.originalFileName,
        fileURL: api.fileURL(data.newFileName),
        progress: 100
      }
    ]
    for (let index = 0; index < newSucceedData.length; index++) {
      const item = newSucceedData[index]
      if (item.time) {
        newSortData.push(item)
      } else {
        newData.push(item)
      }
    }
    emit('fileUpload', [...newData, ...newSortData.sort((a, b) => a.time - b.time)])
    emit('isAllSucceed', !fileArrAy.value.length)
  } catch (err) {
    fileArrAy.value = fileArrAy.value.filter((item) => item.uid !== uid)
  }
}
const onUploadProgress = (progressEvent, uid) => {
  if (progressEvent?.event?.lengthComputable) {
    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)
    fileArrAy.value.forEach((item) => {
      if (item.uid === uid) {
        item.progress = parseInt(progress)
      }
    })
  }
}
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const handlePreview = (row) => {
  globalFileLocation({
    name: process.env.VUE_APP_NAME,
    fileId: row.id,
    fileType: row.extName,
    fileName: row.originalFileName,
    fileSize: row.fileSize
  })
}
const handleDownload = (row) => {
  if (props.public) {
    if (window.__POWERED_BY_QIANKUN__) {
      extendDownloadFile({
        url: `/in_system/file/download/${row.id}`,
        params: {},
        fileType: row.extName,
        fileName: row.originalFileName,
        fileSize: row.fileSize
      })
    } else {
      store.commit('setExtendDownloadFile', {
        url: `/in_system/file/download/${row.id}`,
        params: {},
        fileType: row.extName,
        fileName: row.originalFileName,
        fileSize: row.fileSize
      })
    }
  } else {
    if (window.__POWERED_BY_QIANKUN__) {
      downloadFile({ fileId: row.id, fileType: row.extName, fileName: row.originalFileName, fileSize: row.fileSize })
    } else {
      store.commit('setDownloadFile', {
        fileId: row.id,
        fileType: row.extName,
        fileName: row.originalFileName,
        fileSize: row.fileSize
      })
    }
  }
}
const handleDel = (file) => {
  emit(
    'fileUpload',
    fileSucceed.value.filter((item) => item.uid !== file.uid)
  )
}
watch(
  () => props.fileData,
  (val) => {
    if (val.length) {
      defaultFile()
    } else {
      fileSucceed.value = []
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.xyl-upload-file {
  width: 100%;

  .zy-el-upload {
    --zy-el-upload-dragger-padding-horizontal: 20px;
    --zy-el-upload-dragger-padding-vertical: 10px;

    .zy-el-upload__text {
      line-height: var(--zy-line-height);
    }

    .zy-el-upload__tip {
      padding: 0 var(--zy-distance-one);
      line-height: var(--zy-line-height);
    }
  }

  .xyl-upload-file-list {
    width: 100%;
    padding-top: 12px;

    .xyl-upload-file-item {
      width: 100%;
      position: relative;
      padding: var(--zy-font-text-distance-five) var(--zy-distance-one);
      padding-right: calc(var(--zy-distance-one) * 3);
      line-height: var(--zy-line-height);
      font-size: var(--zy-text-font-size);
      cursor: pointer;

      .zy-el-progress {
        position: absolute;
        left: 50%;
        bottom: 0;
        width: 100%;
        transform: translateX(-50%);
      }

      .xyl-upload-file-icon {
        position: absolute;
        top: 50%;
        left: var(--zy-distance-two);
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);
      }

      .xyl-upload-file-preview,
      .xyl-upload-file-download,
      .xyl-upload-file-succes,
      .xyl-upload-file-del {
        position: absolute;
        top: 50%;
        right: var(--zy-distance-two);
        transform: translate(50%, -50%);
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);
        display: flex;
        align-items: center;
      }

      .xyl-upload-file-preview {
        right: calc(var(--zy-distance-two) * 5);
      }

      .xyl-upload-file-download {
        right: calc(var(--zy-distance-two) * 3);
      }

      .xyl-upload-file-succes {
        color: var(--zy-el-color-success);
      }

      .xyl-upload-file-del {
        display: none;
      }

      &:hover {
        .xyl-upload-file-succes {
          display: none;
        }

        .xyl-upload-file-del {
          display: flex;
        }
      }
    }

    .xyl-upload-file-item-disabled {
      &:hover {
        .xyl-upload-file-succes {
          display: flex;
        }
      }
    }
  }

  .zy-is-upload-disabled {
    cursor: not-allowed;

    .zy-el-upload-dragger {
      cursor: not-allowed;
      background-color: var(--el-disabled-bg-color);
      border: 1px dashed var(--zy-el-border-color-darker);
    }
  }
}
</style>
