<template>
  <div class="business-region-select-person">
    <div class="business-region-select-person-button">
      <div class="business-region-select-person-button-left">
        <el-button type="primary" @click="show = !show" :disabled="props.disabled">选择人员</el-button>
        <div class="business-region-select-person-num">已选人员（{{ userDataNum }}）</div>
      </div>
      <el-input v-model="keyWord" placeholder="请输入人员姓名" clearable />
    </div>
    <el-scrollbar class="business-region-select-person-body">
      <el-collapse v-model="regionCollapse">
        <el-empty :image-size="120" description="暂未选中人员" v-if="!userData.length" />
        <el-collapse-item :name="item.id" v-for="item in userData" :key="item.id">
          <template #title>
            {{ item.name }}（{{ item.userData.length }}）
            <div class="business-region-select-person-collapse-del" @click.stop="regionDel(item)">
              <el-icon>
                <Delete />
              </el-icon>
            </div>
          </template>
          <div class="business-region-select-person-user" v-for="user in item.userData" :key="user.userId">
            <div
              class="business-region-select-person-text row1"
              :class="row.class"
              v-for="row in userList"
              :key="row.key"
              :title="row.key === 'mobile' ? handleMobile(user[row.key]) : user[row.key]">
              {{ row.key === 'mobile' ? handleMobile(user[row.key]) : user[row.key] }}
            </div>
            <div class="business-region-select-person-del" @click="userDel(item.id, user)" v-if="!props.disabled">
              <el-icon>
                <Delete />
              </el-icon>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-scrollbar>
    <div class="business-region-select-person-pagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="show" name="选择人员">
      <region-select-person
        :levelType="props.levelType"
        :userData="userDataArr"
        @callback="callback"></region-select-person>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'BusinessRegionSelectPerson' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, watch } from 'vue'
import { encryptPhone } from 'common/js/utils.js'
import { systemMobileEncrypt } from 'common/js/system_var.js'
const props = defineProps({
  modelValue: { type: Array, default: () => [] },
  userList: {
    type: Array,
    default: () => [
      { key: 'userName', class: 'row1' },
      { key: 'mobile', class: 'row2' },
      { key: 'position', class: 'row4' }
    ]
  },
  disabled: { type: Boolean, default: false },
  levelType: { type: String, default: 'all' }
})
const emit = defineEmits(['update:modelValue', 'callback'])
const keyWord = ref('')
const userList = computed(() => props.userList)
const pageNo = ref(1)
const pageSize = ref(10)
const show = ref(false)
const userId = ref(props.modelValue)
const userData = ref([])
const userDataArr = ref([])
const regionCollapse = ref([])
const userDataNum = computed(() => eval(userDataArr.value.map((v) => v.userData.length).join('+')) || 0)
const userDataArrFilter = computed(() => {
  if (!keyWord.value) return userDataArr.value
  const newArr = []
  for (let index = 0; index < userDataArr.value.length; index++) {
    const item = userDataArr.value[index]
    const newItemUserData = item.userData.filter((v) => v.userName.includes(keyWord.value))
    if (newItemUserData.length) newArr.push({ ...item, userData: newItemUserData })
  }
  return newArr
})
const totals = computed(() => userDataArrFilter.value.length)
const handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)

const getSelectUser = async () => {
  if (!props.modelValue.length) return userInitData()
  const { data } = await api.getSelectUser({ existsUserIds: props.modelValue })
  userInitData(data)
}
const userInitData = (data = []) => {
  var arrData = []
  var arrIndex = []
  data.forEach((item) => {
    if (arrIndex.includes(item.areaId)) {
      arrData.forEach((row) => {
        if (item.areaId === row.id) {
          row.userData.push({ ...item, userId: item.id })
        }
      })
    } else {
      arrIndex.push(item.areaId)
      arrData.push({ id: item.areaId, name: item.areaName, userData: [{ ...item, userId: item.id }] })
    }
  })
  userDataArr.value = arrData
  handleQuery()
  emit('callback', userDataArr.value)
}
const handleQuery = () => {
  userData.value = userDataArrFilter.value.slice(pageSize.value * (pageNo.value - 1), pageSize.value * pageNo.value)
}
const callback = (data) => {
  show.value = false
  if (data) {
    userDataArr.value = data
    handleUserId()
    handleQuery()
  }
}
const regionDel = (row) => {
  userDataArr.value = userDataArr.value.filter((item) => item.id !== row.id)
  handleUserId()
  handleQuery()
}
const userDel = (id, data) => {
  userDataArr.value = userDataArr.value.map((v) =>
    id === v.id ? { id: v.id, name: v.name, userData: v.userData.filter((item) => item.userId !== data.userId) } : v
  )
  handleUserId()
  handleQuery()
}
const handleUserId = () => {
  var userIdData = []
  userDataArr.value.forEach((item) => {
    userIdData = [...userIdData, ...item.userData]
  })
  emit(
    'update:modelValue',
    userIdData.map((v) => v.userId)
  )
  emit('callback', userDataArr.value)
}
watch(
  () => keyWord.value,
  () => {
    pageNo.value = 1
    handleQuery()
  },
  { immediate: true }
)
watch(
  () => props.modelValue,
  () => {
    userId.value = props.modelValue
    if (props.modelValue.length !== userDataArr.value.length) {
      getSelectUser()
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.business-region-select-person {
  width: 100%;
  border: 1px solid var(--zy-el-border-color-lighter);
  border-radius: var(--el-border-radius-base);

  .business-region-select-person-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: normal;
    padding: var(--zy-distance-five) var(--zy-distance-two);
    .business-region-select-person-button-left {
      display: flex;
      align-items: center;
      .zy-el-button {
        height: var(--zy-height-secondary);
      }

      .business-region-select-person-num {
        font-weight: bold;
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
        margin-left: var(--zy-distance-one);
      }
    }
    .zy-el-input {
      width: 220px;
      height: var(--zy-height-routine);
    }
  }

  .business-region-select-person-body {
    width: 100%;
    height: 288px;

    .zy-el-collapse {
      .zy-el-collapse-item__header {
        height: auto;
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);
        padding: var(--zy-distance-four) var(--zy-distance-two);
      }

      .zy-el-collapse-item__content {
        border-top: 1px solid var(--zy-el-collapse-border-color);
        padding-bottom: var(--zy-distance-four);
      }

      .business-region-select-person-collapse-del {
        width: 60px;
        height: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
        font-size: var(--zy-text-font-size);
        margin-left: var(--zy-distance-two);
      }
    }

    .business-region-select-person-user {
      display: flex;
      justify-content: space-between;
      padding: var(--zy-distance-four) 0;
      border-bottom: 1px solid var(--zy-el-border-color-lighter);

      .business-region-select-person-text {
        padding-left: var(--zy-distance-two);
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .row1 {
        flex: 1;
      }

      .row2 {
        flex: 2;
      }

      .row3 {
        flex: 3;
      }

      .row4 {
        flex: 4;
      }

      .business-region-select-person-del {
        width: 60px;
        display: flex;
        align-items: center;
        cursor: pointer;
        padding-left: var(--zy-distance-two);
      }
    }
  }

  .business-region-select-person-pagination {
    width: 100%;
    height: 42px;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid var(--zy-el-border-color-lighter);
    padding-right: var(--zy-distance-two);

    .zy-el-pagination {
      --zy-height: var(--zy-height-routine);
      --zy-el-component-size: var(--zy-height);

      .zy-el-select {
        width: 128px !important;
      }
    }
  }
}
</style>
