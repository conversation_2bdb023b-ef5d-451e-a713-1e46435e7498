<template>
  <div class="RegulationFilePreviewPdf">
    <div id="viewerContainer"
         ref="containerRef">
      <div id="viewer"
           ref="viewerRef"
           class="pdfViewer"></div>
    </div>
  </div>
</template>
<script>
export default { name: 'RegulationFilePreviewPdf' }
</script>
<script setup>
import { ref, onMounted } from 'vue'
import { Print } from '@/assets/js/print'
import 'pdfjs-dist/web/pdf_viewer.css'
import { GlobalWorkerOptions, getDocument } from 'pdfjs-dist'
import { PDFLinkService, PDFViewer, PDFFindController, EventBus } from 'pdfjs-dist/web/pdf_viewer'
import * as pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry'
GlobalWorkerOptions.workerSrc = pdfjsWorker
const emit = defineEmits(['matchesCountCallback'])
const props = defineProps({ fileUrl: { type: String, default: '' } })
const currentPage = ref(1) // 当前页码
const totalPages = ref(0) // 总页码
const ScaleValue = ref('page-actual') // 缩放比例
var eventBus = '' // pdf定义监听事件
var newViewer = '' // pdf实例
const containerRef = ref() // pdf渲染元素
const viewerRef = ref()
const keywordCurrents = ref(0) // 
const keywordTotal = ref(0) // 

onMounted(() => { loadPdf(props.fileUrl) })

/*加载PDF文件*/
const loadPdf = (url) => {
  eventBus = new EventBus()
  const linkService = new PDFLinkService({ eventBus })
  const findController = new PDFFindController({ eventBus, linkService })
  newViewer = new PDFViewer({ container: containerRef.value, eventBus, linkService, findController })
  linkService.setViewer(newViewer)
  eventBus.on('pagesinit', () => {
    totalPages.value = newViewer.pagesCount
    newViewer.currentScaleValue = ScaleValue.value
    // 监听页码变化
    eventBus._on('pagechanging', e => { currentPage.value = e.pageNumber })
    // 关键字搜索的数量和位置
    eventBus._on('updatefindcontrolstate', e => {
      keywordCurrents.value = e.matchesCount.current
      keywordTotal.value = e.matchesCount.total
      emit('matchesCountCallback', keywordCurrents.value, keywordTotal.value)
    })
    eventBus._on('updatefindmatchescount', e => {
      keywordCurrents.value = e.matchesCount.current
      keywordTotal.value = e.matchesCount.total
      emit('matchesCountCallback', keywordCurrents.value, keywordTotal.value)
    })
  })
  const loadingTask = getDocument({ url })
  loadingTask.promise.then(pdf => {
    if (pdf) {
      newViewer.setDocument(pdf)
      linkService.setDocument(pdf)
    }
  })
}
const previousPageClick = () => { // 上一页
  newViewer.currentPageNumber = currentPage.value === 1 ? totalPages.value : currentPage.value - 1
}
const nextPageClick = () => { // 下一页
  newViewer.currentPageNumber = currentPage.value === totalPages.value ? 1 : currentPage.value + 1
}
const zoomInClick = () => {
  newViewer.currentScaleValue = 1
}
const zoomOutClick = () => {
  newViewer.currentScaleValue = 0.5
}
// 关键字搜索
const searchClick = (keyword) => {
  eventBus.dispatch('find', { phraseSearch: true, query: keyword, findPrevious: false, highlightAll: true })
}
// 上一个下一个
/**
 * 
 * @param {boolean} type 
 * @param {*} keyword 
 */
const againClick = (type, keyword) => {
  eventBus.dispatch('find', { type: 'again', phraseSearch: true, query: keyword, findPrevious: type, highlightAll: true })
}

const printClick = () => {
  Print.init(viewerRef.value)
}

defineExpose({
  previousPageClick,
  nextPageClick,
  zoomInClick,
  zoomOutClick,
  searchClick,
  againClick,
  keywordCurrents,
  keywordTotal,
  printClick
})

</script>
<style lang="scss">
.RegulationFilePreviewPdf {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;

  #viewerContainer {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: auto;

    #viewer {
      margin: auto;

      * {
        box-sizing: content-box;
      }
    }
  }
}
</style>
