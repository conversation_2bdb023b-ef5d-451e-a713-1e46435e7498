<template>
  <div class="RegulationFilePreview">
    <template v-if="fileUrl">
      <RegulationFilePreviewPdf :fileUrl="fileUrl" ref="previewPdfRef" @matchesCountCallback="matcheCsountCallback" />
    </template>
  </div>
</template>
<script>
export default { name: 'RegulationFilePreview' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, defineAsyncComponent } from 'vue'
const RegulationFilePreviewPdf = defineAsyncComponent(() => import('./components/RegulationFilePreviewPdf.vue'))
const emit = defineEmits(['matchesCountCallback'])
const props = defineProps({
  fileId: { type: String, default: '' },
  fileType: { type: String, default: '' },
  fileName: { type: String, default: '' },
  fileUrl: { type: String, default: '' }
})

const fileUrl = ref()
const previewPdfRef = ref()
const currents = ref(0)
const total = ref(0)
const pdfPrintLink = ref('')

onMounted(() => {
  if (props.fileId) {
    inSystemGlobalDownload()
  } else if (props.fileUrl) {
    BlobToPdf()
  }
})
const seach = (keyword) => {
  previewPdfRef.value.searchClick(keyword)
}
/**
 *
 * @param {boolean} type true 上一个、下一个
 * @param {*} keyword
 */
const againClick = (type, keyword) => {
  previewPdfRef.value.againClick(type, keyword)
}
const matcheCsountCallback = (currentNum, totals) => {
  currents.value = currentNum || 0
  total.value = totals || 0
  emit('matchesCountCallback', currents.value, total.value)
}
const inSystemGlobalDownload = async () => {
  const res = await api.inSystemGlobalDownload(props.fileId)
  if (['doc', 'docx', 'wps'].includes(props.fileType)) {
    wordTopdf(res)
  } else {
    fileUrl.value = URL.createObjectURL(new Blob([res]))
    pdfPrintLink.value = URL.createObjectURL(new Blob([res], { type: 'application/pdf' }))
  }
}
const wordTopdf = async (file) => {
  const param = new FormData()
  param.append('file', file)
  const res = await api.inSystemWordTopdf(param)
  fileUrl.value = URL.createObjectURL(new Blob([res]))
  pdfPrintLink.value = URL.createObjectURL(new Blob([res], { type: 'application/pdf' }))
}
const print = () => {
  const iframe = document.createElement('iframe')
  iframe.style.visibility = 'hidden'
  iframe.src = pdfPrintLink.value
  document.body.appendChild(iframe)
  iframe.contentWindow.focus()
  iframe.contentWindow.print()
}
const BlobToPdf = async () => {
  var params = { urlPath: props.fileUrl }
  const res = await api.openApisOpenStatuteLawGetPDFStreamByPath(params)
  fileUrl.value = URL.createObjectURL(new Blob([res]), { type: 'application/pdf' })
  pdfPrintLink.value = URL.createObjectURL(new Blob([res], { type: 'application/pdf' }))
}
defineExpose({
  seach,
  print,
  againClick
})
</script>
<style lang="scss">
.RegulationFilePreview {
  height: 1120px;
  background: #eeeeee;
}
</style>
