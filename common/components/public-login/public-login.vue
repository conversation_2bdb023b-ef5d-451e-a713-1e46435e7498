<template>
  <div class="PublicLogin">
    <div class="PublicLoginBox">
      <div class="PublicLoginClose" @click="handleClose">
        <el-icon>
          <CircleClose />
        </el-icon>
      </div>
      <div class="PublicLoginLogo">
        <el-image :src="systemLogo" fit="cover" />
      </div>
      <div class="PublicLoginName">{{ systemName }}</div>
      <el-form ref="LoginForm" :model="form" :rules="rules" class="PublicLoginForm">
        <el-form-item prop="account">
          <el-input v-model="form.account" placeholder="账号/手机号" clearable />
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" v-model="form.password" placeholder="密码" show-password clearable />
        </el-form-item>
        <el-form-item class="smsValidation" v-if="whetherVerifyCode" prop="verifyCode">
          <el-input v-model="form.verifyCode" placeholder="短信验证码" clearable></el-input>
          <el-button type="primary" @click="handleGetVerifyCode" :disabled="countDownText != '获取验证码'">
            {{ countDownText }}
          </el-button>
        </el-form-item>
        <div class="PublicLoginSlideVerify" v-if="!whetherVerifyCode">
          <xyl-slide-verify ref="slideVerify" @again="onAgain" @success="onSuccess" :disabled="disabled" />
        </div>
        <div class="PublicLoginFormOperation">
          <el-checkbox v-model="checked">记住用户名和密码</el-checkbox>
          <div class="PublicLoginFormOperationText"></div>
          <!-- <div class="PublicLoginFormOperationText">忘记密码？</div> -->
        </div>
        <el-form-item>
          <el-button type="primary" @click="submitForm(LoginForm)" class="PublicLoginFormButton" :loading="loading">
            {{ loading ? '登录中' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      <div class="PublicLoginOperation" v-if="appDownloadUrl">
        <div class="PublicLoginOperationBox">
          <el-popover placement="top" width="auto" @show="refresh" @hide="hideQrcode">
            <div class="PublicLoginQrCodeBox">
              <div class="PublicLoginQrCodeNameBody">
                <div class="PublicLoginQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="PublicLoginQrCodeName">APP扫码登录</div>
              </div>
              <div class="PublicLoginQrCodeRefreshBody">
                <qrcode-vue :value="loginQrcode" :size="120" />
                <div class="PublicLoginQrCodeRefresh" v-show="loginQrcodeShow">
                  <el-button type="primary" @click="refresh">刷新</el-button>
                </div>
              </div>
              <div class="PublicLoginQrCodeText">请使用{{ systemName }}APP扫码登录</div>
            </div>
            <template #reference>
              <div class="PublicLoginQrCode"></div>
            </template>
          </el-popover>
          <div class="PublicLoginOperationText">APP扫码登录</div>
        </div>
        <div class="PublicLoginOperationBox">
          <el-popover placement="top" width="auto">
            <div class="PublicLoginQrCodeBox">
              <div class="PublicLoginQrCodeNameBody">
                <div class="PublicLoginQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="PublicLoginQrCodeName">手机APP下载</div>
              </div>
              <qrcode-vue :value="appDownloadUrl" :size="120" />
              <div class="PublicLoginQrCodeText">使用其他软件扫码下载{{ systemName }}APP</div>
            </div>
            <template #reference>
              <div class="PublicLoginApp"></div>
            </template>
          </el-popover>
          <div class="PublicLoginOperationText">手机APP下载</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'PublicLogin' }
</script>
<script setup>
import { onMounted, defineAsyncComponent } from 'vue'
import { systemLogo, systemName, appDownloadUrl } from 'common/js/system_var.js'
import { login } from './public-login.js'
const QrcodeVue = defineAsyncComponent(() => import('qrcode.vue')) // 二维码
const emit = defineEmits(['callback'])
const {
  whetherVerifyCode,
  loading,
  checked,
  LoginForm,
  form,
  rules,
  countDownText,
  slideVerify,
  disabled,
  loginQrcode,
  loginQrcodeShow,
  verifyLoginCode,
  handleGetVerifyCode,
  onAgain,
  onSuccess,
  submitForm,
  loginInfo,
  refresh,
  hideQrcode
} = login(emit)
onMounted(() => {
  verifyLoginCode()
  loginInfo()
})
const handleClose = () => {
  emit('callback')
}
</script>
<style lang="scss">
.PublicLogin {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 99;

  .PublicLoginBox {
    position: relative;
    z-index: 2;
    padding: var(--zy-distance-one);
    background-color: #fff;
    border-radius: var(--el-border-radius-base);

    .PublicLoginClose {
      position: absolute;
      top: 0;
      right: 0;
      transform: translate(76%, -76%);
      color: #fff;
      font-size: 22px;
      width: 22px;
      height: 22px;
      cursor: pointer;
    }

    .PublicLoginLogo {
      width: 60px;
      margin: auto;
      margin-bottom: var(--zy-distance-two);

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .PublicLoginName {
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: var(--zy-system-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      letter-spacing: 2px;
      padding-bottom: var(--zy-distance-one);
    }

    .PublicLoginForm {
      width: 320px;
      margin: auto;

      input:-webkit-autofill {
        transition: background-color 5000s ease-in-out 0s;
      }

      .zy-el-form-item {
        margin-bottom: var(--zy-form-distance-bottom);

        .PublicLoginFormButton {
          width: 100%;
        }
      }

      .smsValidation {
        .zy-el-form-item__content {
          display: flex;
          justify-content: space-between;
        }

        .zy-el-input {
          width: 56%;
        }
      }

      .PublicLoginSlideVerify {
        margin-bottom: var(--zy-form-distance-bottom);
      }

      .PublicLoginFormOperation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--zy-distance-three);

        .zy-el-checkbox {
          height: var(--zy-height-secondary);
        }

        .PublicLoginFormOperationText {
          font-size: var(--zy-text-font-size);
        }
      }
    }

    .PublicLoginOperation {
      width: 100%;
      padding-top: var(--zy-distance-two);
      display: flex;
      justify-content: space-between;

      .PublicLoginOperationBox {
        margin: 0 var(--zy-distance-two);
        cursor: pointer;

        .PublicLoginQrCode {
          width: 50px;
          height: 50px;
          background: url('./img/login_qr_code.png');
          background-size: 100% 100%;
          margin: auto;
        }

        .PublicLoginApp {
          width: 50px;
          height: 50px;
          background: url('./img/login_app.png') no-repeat;
          background-size: auto 100%;
          background-position: center;
          margin: auto;
        }

        .PublicLoginOperationText {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          padding: var(--el-border-radius-small) 0;
          text-align: center;
        }
      }
    }
  }
}

.PublicLoginQrCodeBox {
  width: 320px;
  background-color: #fff;

  canvas {
    display: block;
    margin: auto;
  }

  .PublicLoginQrCodeNameBody {
    padding: var(--zy-distance-three);
    display: flex;
    align-items: center;
    justify-content: center;

    .PublicLoginQrCodeLogo {
      width: 26px;
      margin-right: 6px;

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .PublicLoginQrCodeName {
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
    }
  }

  .LoginViewQrCodeRefreshBody {
    position: relative;

    .LoginViewQrCodeRefresh {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 120px;
      background-color: rgba(000, 000, 000, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;

      .zy-el-button {
        height: var(--zy-height-secondary);
      }
    }
  }

  .PublicLoginQrCodeText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-three);
    color: var(--zy-el-color-primary);
  }
}
</style>
