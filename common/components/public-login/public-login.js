import api from '@/api'
import { reactive, ref } from 'vue'
import store from '@/store'
import utils from 'common/js/utils.js'
import { ElMessage } from 'element-plus'
export const login = (emit) => {
  const whetherVerifyCode = ref(false)
  const loading = ref(false)
  const checked = ref(false)
  const LoginForm = ref()
  const form = reactive({ account: '', password: '', verifyCode: '' })
  const rules = reactive({
    account: [{ required: true, message: '请输入账号/手机号', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
  })
  const slideVerify = ref()
  const disabled = ref(false)
  const verifyCodeId = ref('')
  const countDownText = ref('获取验证码')
  const countDown = ref(0)
  const timer = ref()
  const loginQrcode = ref('')
  const loginQrcodeId = ref('')
  const loginQrcodeShow = ref(false)
  const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  const verifyLoginCode = async () => {
    const { data } = await api.verifyLoginCode()
    whetherVerifyCode.value = data
    rules.verifyCode = [{ required: data, message: '请输入验证码', trigger: 'blur' }]
  }
  const handleGetVerifyCode = async () => {
    if (!form.account) return ElMessage({ type: 'warning', message: '请输入账号/手机号！' })
    const { data, code } = await api.openVerifyCodeSend({ mobile: form.account, sendType: 'no_login' })
    if (code === 200) {
      verifyCodeId.value = data
      countDown.value = 60
      handleCountDown()
      ElMessage({ type: 'success', message: '短信验证码已发送！' })
    }
  }
  const handleCountDown = () => {
    if (countDown.value === 0) {
      countDownText.value = '获取验证码'
      countDown.value = 60
      return
    } else {
      countDownText.value = '重新发送' + countDown.value + 'S'
      countDown.value--
    }
    setTimeout(() => { handleCountDown() }, 1000)
  }
  const onAgain = () => {
    ElMessage.error('检测到非人为操作的哦！!')
    handleFefresh()
  }
  const onSuccess = () => { disabled.value = true }
  const handleFefresh = () => {
    disabled.value = false
    slideVerify.value?.refresh()
  }
  const submitForm = async (formEl) => {
    if (!formEl) return
    loading.value = true
    await formEl.validate((valid) => {
      if (valid) {
        if (disabled.value || whetherVerifyCode.value) { login() } else {
          loading.value = false
          ElMessage.error('请先通过验证在登录!')
        }
      } else { loading.value = false }
    })
  }
  const login = async () => {
    try {
      const { data } = await api.login({
        grant_type: 'password',
        username: form.account,
        password: utils.encrypt(form.password, new Date().getTime(), '1'),
        ...(whetherVerifyCode.value ? { verifyCodeId: verifyCodeId.value, verifyCode: form.verifyCode } : {})
      })
      ElMessage({ message: '登录成功！', showClose: true, type: 'success' })
      sessionStorage.setItem('token', data.token)
      sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)
      sessionStorage.setItem('expires', data.expires_in)
      sessionStorage.setItem('expiration', data.refreshToken.expiration)
      sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)
      store.dispatch('loginUser')
      const loginUserInfo = {
        account: utils.encrypt(form.account, new Date().getTime(), '3'),
        password: utils.encrypt(form.password, new Date().getTime(), '2'),
        checked: checked.value
      }
      localStorage.setItem('loginUserInfo', JSON.stringify(loginUserInfo))
      emit('callback')
    } catch {
      loading.value = false
      handleFefresh()
    }
  }
  const loginInfo = () => {
    const loginUserInfo = JSON.parse(localStorage.getItem('loginUserInfo')) || ''
    if (loginUserInfo && loginUserInfo.checked) {
      checked.value = loginUserInfo.checked
      form.account = utils.decrypt(loginUserInfo.account, new Date().getTime(), '3')
      form.password = utils.decrypt(loginUserInfo.password, new Date().getTime(), '2')
    }
  }
  const refresh = () => {
    clearTimeout(timer.value)
    loginQrcodeShow.value = false
    loginQrcodeId.value = guid()
    loginQrcode.value = `${appOnlyHeader.value}|login|${loginQrcodeId.value}`
    setTimeout(() => { loginQrcodeShow.value = true }, 180000)
    appToken()
  }
  const hideQrcode = () => {
    clearTimeout(timer.value)
  }
  const appToken = async () => {
    const { data } = await api.appToken({ qrCodeId: loginQrcodeId.value })
    if (!data?.token && !loginQrcodeShow.value) {
      timer.value = setTimeout(() => { appToken() }, 2000)
    }
    if (data?.token) {
      clearTimeout(timer.value)
      ElMessage({ message: '登录成功！', showClose: true, type: 'success' })
      sessionStorage.setItem('token', data.token)
      store.dispatch('loginUser')
      emit('callback')
    }
  }
  return { whetherVerifyCode, loading, checked, LoginForm, form, rules, countDownText, slideVerify, disabled, loginQrcode, loginQrcodeShow, handleGetVerifyCode, onAgain, onSuccess, submitForm, verifyLoginCode, loginInfo, refresh, hideQrcode }
}