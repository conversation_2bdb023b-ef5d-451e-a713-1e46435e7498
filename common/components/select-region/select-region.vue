<template>
  <div class="select-region">
    <div class="select-region-body">
      <div class="select-region-input">
        <el-input v-model="keyword" placeholder="根据地区名称搜索" @keyup.enter="handleSearch" clearable />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
      <div class="select-region-tree-body">
        <div class="select-region-name">
          地区列表
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAll"></el-checkbox>
        </div>
        <el-scrollbar class="select-region-tree">
          <el-tree
            ref="treeRef"
            node-key="id"
            show-checkbox
            check-strictly
            :data="regionData"
            :props="{ label: 'name' }"
            @check-change="selectedClick"
            :filter-node-method="filterNode" />
        </el-scrollbar>
      </div>
    </div>
    <div class="select-region-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'SelectRegion' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, nextTick } from 'vue'
const props = defineProps({
  regionData: { type: Array, default: () => [] },
  levelType: { type: String, default: 'all' }
})
const emit = defineEmits(['callback'])
const treeRef = ref()
const keyword = ref('')
const regionData = ref([])
const checkAll = ref(false)
const isIndeterminate = ref(false)
const idList = ref([])
const idAllList = ref([])
const selectData = ref([])
const selectAllList = ref([])
onMounted(() => {
  getAreaTree()
})

const getAreaTree = async () => {
  const { data } = await api.businessAreaTree({ levelType: props.levelType, isFilterUnUsing: 1 })
  regionData.value = data
  selectedMethods(data)
}
// 首次进来默认选中
const selectedMethods = (data) => {
  data.forEach((item) => {
    idAllList.value.push(item.id)
    selectAllList.value.push(item)
    if (props.regionData.map((v) => v.id).includes(item.id)) {
      idList.value.push(item.id)
      selectData.value.push(item)
      nextTick(() => {
        treeRef.value.setCheckedKeys(idList.value)
      })
    }
    if (item.children && item.children.length > 0) {
      selectedMethods(item.children)
    }
  })
}
const handleSearch = () => {
  treeRef.value.filter(keyword.value)
  nextTick(() => {
    idAllList.value = []
    selectAllList.value = []
    if (keyword.value) {
      const data = traverseVisible(traverseNode(treeRef.value.root.childNodes))
      selectedFilter(data)
    } else {
      selectedFilter(regionData.value)
    }
    checkAll.value = idAllList.value.every((elem) => idList.value.indexOf(elem) > -1)
    isIndeterminate.value = checkAll.value ? false : idList.value.length > 0
    if (idAllList.value.length === 0) {
      checkAll.value = false
    }
  })
}
// 首次进来默认选中
const selectedFilter = (data) => {
  data.forEach((item) => {
    idAllList.value.push(item.id)
    selectAllList.value.push(item)
    if (item.children && item.children.length > 0) {
      selectedFilter(item.children)
    }
  })
}
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}
const traverseNode = (node) => {
  if (Object.prototype.toString.call(node) === '[object Array]') {
    return node.map((t) => traverseNode(t))
  }
  const data = { id: node.data.id, name: node.label, visible: node.visible, children: [] }
  const childNodes = node.childNodes
  childNodes.forEach((child) => {
    const item = traverseNode(child)
    if (item.visible) {
      data.visible = true
    }
    data.children.push(item)
  })
  return data
}
const traverseVisible = (arr) => {
  return arr.filter((t) => {
    let visible = t.visible
    if (!visible) {
      return false
    }
    if (t.children) {
      t.children = traverseVisible(t.children)
    }
    delete t.visible
    return visible
  })
}
// 下拉框选中事件
const selectedClick = (data, type) => {
  if (type) {
    if (!idList.value.includes(data.id)) {
      idList.value.push(data.id)
      selectData.value.push(data)
    }
  } else {
    idList.value = idList.value.filter((item) => item !== data.id)
    selectData.value = selectData.value.filter((item) => item.id !== data.id)
  }
  checkAll.value = idAllList.value.every((elem) => idList.value.indexOf(elem) > -1)
  isIndeterminate.value = checkAll.value ? false : idList.value.length > 0
  if (idAllList.value.length === 0) {
    checkAll.value = false
  }
}
const handleCheckAll = (val) => {
  idList.value = val
    ? [...new Set([...idList.value, ...idAllList.value])]
    : checkAllDel(idList.value, idAllList.value, true)
  selectData.value = val
    ? objTrim([...selectData.value, ...selectAllList.value])
    : checkAllDel(
        selectData.value,
        selectAllList.value.map((v) => v.id)
      )
  checkAll.value = idAllList.value.every((elem) => idList.value.indexOf(elem) > -1)
  isIndeterminate.value = false
  if (idList.value.length === 0 && idList.value.length === 0) {
    checkAll.value = false
  }
  nextTick(() => {
    treeRef.value.setCheckedKeys(idList.value)
  })
}
const objTrim = (data) => {
  let obj = {}
  data = data.reduce((cur, next) => {
    obj[next.id] ? '' : (obj[next.id] = true && cur.push(next))
    return cur
  }, [])
  return data
}
const checkAllDel = (data, idArr, type) => {
  return type ? data.filter((v) => !idArr.includes(v)) : data.filter((v) => !idArr.includes(v.id))
}
const submitForm = () => {
  emit('callback', selectData.value)
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.select-region {
  width: 680px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-two) var(--zy-distance-one);

  .select-region-body {
    width: 100%;
    height: calc(100% - (var(--zy-height) + var(--zy-distance-two)));

    .select-region-input {
      width: 100%;
      display: flex;
      padding-bottom: var(--zy-distance-three);

      .zy-el-input {
        margin-right: var(--zy-distance-two);
      }
    }

    .select-region-tree-body {
      width: 100%;
      height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
      border: 1px solid var(--zy-el-border-color-lighter);

      .select-region-name {
        padding: var(--zy-distance-four) var(--zy-distance-three);
        font-weight: bold;
        border-bottom: 1px solid var(--zy-el-border-color-lighter);
        display: flex;
        align-items: center;
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);
        justify-content: space-between;

        .zy-el-checkbox {
          height: auto;
        }
      }

      .select-region-tree {
        width: 100%;
        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-four) * 2) + 1px));

        .zy-el-tree-node__content {
          height: auto;
          padding: var(--zy-distance-five) 0;
          position: relative;

          .zy-el-tree-node__label {
            width: calc(100% - 28px);
            text-overflow: clip;
            white-space: normal;
            font-size: var(--zy-text-font-size);
            padding-right: var(--zy-distance-one);
          }

          .zy-el-checkbox {
            position: absolute;
            top: 50%;
            margin: 0;
            right: var(--zy-distance-three);
            transform: translateY(-50%);
            font-size: 0;
            text-align: center;
          }
        }
      }
    }
  }

  .select-region-button {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);
    padding-right: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
