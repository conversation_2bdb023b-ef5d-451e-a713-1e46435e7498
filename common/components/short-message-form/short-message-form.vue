<template>
  <div class="short-message-form">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item prop="content" class="globalFormTitle">
        <template #label>
          短信模板
          <el-button type="primary" @click="templateInfo">更新模板</el-button>
        </template>
        <el-input v-model="form.content" placeholder="请输入短信模板" type="textarea" :rows="5" />
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'ShortMessageForm' }
</script>
<script setup>
import api from '@/api'
import { ref, reactive, onMounted } from 'vue'
const props = defineProps({ code: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({ content: '' })
const rules = reactive({ content: [{ required: true, message: '请输入短信模板', trigger: ['blur', 'change'] }] })

onMounted(() => {
  if (props.code) {
    templateInfo()
  }
})

const templateInfo = async () => {
  const { data } = await api.templateInfo({ businessCode: props.code })
  form.content = data
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      emit('callback', form.content)
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.short-message-form {
  width: 680px;
}
</style>
