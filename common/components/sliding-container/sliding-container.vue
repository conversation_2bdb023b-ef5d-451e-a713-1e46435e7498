<template>
  <div class="sliding-container" ref="label">
    <div class="sliding-container-wrap">
      <div class="sliding-container-scroll" :style="scrollStyle" ref="scroll">
        <slot></slot>
      </div>
      <div class="sliding-container-prev" v-if="prevShow" @click="scrollClick('prev')">
        <el-icon>
          <DArrowLeft />
        </el-icon>
      </div>
      <div class="sliding-container-next" v-if="nextShow" @click="scrollClick('next')">
        <el-icon>
          <DArrowRight />
        </el-icon>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'SlidingContainer' }
</script>
<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
const props = defineProps({ distance: { type: Number, default: 168 } })

const label = ref()
const scroll = ref()
const prevShow = ref(false)
const nextShow = ref(false)
const scrollLeft = ref(0)
const scrollStyle = computed(() => ({ transform: `translateX(${scrollLeft.value}px)` }))
onMounted(() => {
  if (label.value.offsetWidth < scroll.value.offsetWidth) {
    nextShow.value = true
  }
  nextTick(() => {
    erd.listenTo(label.value, () => {
      delay(() => {
        const left = label.value.offsetWidth - scroll.value.scrollWidth
        prevShow.value = scrollLeft.value !== 0
        nextShow.value = left < 0 && scrollLeft.value !== left
      }, 520)
    })
    erd.listenTo(scroll.value, () => {
      delay(() => {
        const left = label.value.offsetWidth - scroll.value.scrollWidth
        prevShow.value = scrollLeft.value !== 0
        nextShow.value = left < 0 && scrollLeft.value !== left
      }, 520)
    })
  })
})

const delay = (() => {
  let timer = 0
  return (callback, ms) => {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
const scrollClick = (type) => {
  const left = label.value.offsetWidth - scroll.value.scrollWidth
  if (type === 'prev') {
    scrollLeft.value = scrollLeft.value + props.distance > 0 ? 0 : scrollLeft.value + props.distance
  } else if (type === 'next') {
    scrollLeft.value = scrollLeft.value - props.distance < left ? left : scrollLeft.value - props.distance
  }
  delay(() => {
    prevShow.value = scrollLeft.value !== 0
    nextShow.value = scrollLeft.value !== left
  }, 520)
}
</script>
<style lang="scss">
.sliding-container {
  width: 100%;
  background-color: #fff;
  position: relative;

  .sliding-container-wrap {
    width: 100%;
    overflow: hidden;

    .sliding-container-prev,
    .sliding-container-next {
      position: absolute;
      top: 0;
      height: 100%;
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      z-index: 9;
    }

    .sliding-container-prev {
      left: 0;
    }

    .sliding-container-next {
      right: 0;
    }

    .sliding-container-scroll {
      white-space: nowrap;
      position: relative;
      transition: transform 0.3s;
      float: left;
      display: block;
      z-index: 3;
    }
  }
}
</style>
