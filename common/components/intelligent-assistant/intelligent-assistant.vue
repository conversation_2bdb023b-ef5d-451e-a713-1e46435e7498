<template>
  <el-popover
    placement="left-start"
    :visible="visibleIsShow"
    :disabled="disabled"
    popper-class="intelligent-assistant-popper"
    transition="zy-el-zoom-in-top">
    <template #reference>
      <div class="intelligent-assistant" v-show="elIsShow" @click="handleVisible">
        <el-image :src="IntelligentAssistant" fit="cover"></el-image>
      </div>
    </template>
    <div class="intelligent-assistant-body">
      <div class="intelligent-assistant-title">智能小助手</div>
    </div>
    <slot></slot>
  </el-popover>
</template>
<script>
export default { name: 'IntelligentAssistant' }
</script>
<script setup>
import { ref, computed, onActivated } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { IntelligentAssistant } from 'common/js/system_var.js'
const props = defineProps({
  modelValue: { type: Boolean, default: false },
  elIsShow: { type: Boolean, default: false }
})
const emit = defineEmits(['update:modelValue', 'update:elIsShow', 'callback'])

const elIsShow = computed({
  get() {
    return props.elIsShow
  },
  set(value) {
    emit('update:elIsShow', value)
  }
})
const visibleIsShow = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const disabled = ref(false)
const recordsVisibleIsShow = ref(false)

const handleVisible = () => {
  visibleIsShow.value = !visibleIsShow.value
  emit('callback')
}

onActivated(() => {
  if (!disabled.value) return
  disabled.value = false
  visibleIsShow.value = false
  setTimeout(() => {
    visibleIsShow.value = recordsVisibleIsShow.value
  }, 50)
})

onBeforeRouteLeave(() => {
  disabled.value = true
  recordsVisibleIsShow.value = visibleIsShow.value
})
</script>
<style lang="scss">
.intelligent-assistant {
  position: absolute;
  top: 100%;
  right: -106px;
  width: 66px;
  height: 66px;

  .zy-el-image {
    width: 100%;
    height: 100%;
  }
}

.intelligent-assistant-popper {
  width: auto !important;
  padding: 0 !important;
  z-index: 998 !important;

  .intelligent-assistant-body {
    min-width: 380px;
    padding: var(--zy-distance-three) var(--zy-distance-two);
    padding-bottom: var(--zy-font-name-distance-five);

    .intelligent-assistant-title {
      font-weight: bold;
      font-size: var(--zy-navigation-font-size);
      line-height: var(--zy-line-height);
      padding-left: calc(var(--zy-navigation-font-size) + var(--zy-font-name-distance-five));
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: var(--zy-navigation-font-size);
        height: var(--zy-navigation-font-size);
        transform: translateY(-50%);
        background: url('./img/intelligent_assistant_icon.png');
        background-color: var(--zy-el-color-primary);
        background-size: 100% 100%;
        background-position: center center;
      }
    }
  }
}
</style>
