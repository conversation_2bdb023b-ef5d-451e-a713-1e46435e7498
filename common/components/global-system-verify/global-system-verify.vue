<template>
  <el-config-provider :locale="locale" namespace="zy-el">
    <div class="global-system-verify-wrapper">
      <transition name="global-system-verify-fade">
        <div class="global-system-verify" v-show="visible">
          <div class="global-system-verify-title">正宇软件正版授权</div>
          <div class="global-system-verify-item">
            <div class="global-system-verify-label">MAC地址：</div>
            <div class="global-system-verify-content">{{ details.macs }}</div>
          </div>
          <div class="global-system-verify-item">
            <div class="global-system-verify-label">CPU编号：</div>
            <div class="global-system-verify-content">{{ details.cpuIds }}</div>
          </div>
          <div class="global-system-verify-item">
            <div class="global-system-verify-label">硬盘编号：</div>
            <div class="global-system-verify-content">{{ details.hardNumbers }}</div>
          </div>
          <div class="global-system-verify-item">
            <div class="global-system-verify-label">数据库签名：</div>
            <div class="global-system-verify-content">{{ details.databaseSign }}</div>
          </div>
          <div class="global-system-verify-item">
            <div class="global-system-verify-label">授权码：</div>
            <div class="global-system-verify-content">
              <el-input v-model="licence" type="textarea" :rows="9" placeholder="请输入授权码" />
            </div>
          </div>
          <div class="global-system-verify-button">
            <el-button @click="handleCopy(copyText)">复制授权所需信息</el-button>
            <el-button type="primary" @click="licenceUpdate">更新授权码</el-button>
          </div>
        </div>
      </transition>
    </div>
  </el-config-provider>
</template>

<script>
export default { name: 'GlobalSystemVerify' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { ElMessage } from 'element-plus'
const props = defineProps({ closeCallback: { type: Function } })

const locale = zhCn
const licence = ref('')
const details = ref({})
const visible = ref(false)
const copyText = ref('')

onMounted(() => {
  visible.value = true
  licenceInfo()
})

const licenceInfo = async () => {
  const { data } = await api.licenceInfo()
  licence.value = data.licence
  details.value = data
  copyText.value = `MAC地址：${data.macs}\nCPU编号：${data.cpuIds}\n硬盘编号：${data.hardNumbers}\n数据库签名： ${data.databaseSign}\n授权码：${data.licence}`
}
const handleCopy = (value) => {
  if (!value) return ElMessage({ message: '无复制内容', type: 'warning' })
  const textarea = document.createElement('textarea')
  textarea.readOnly = 'readonly'
  textarea.style.position = 'absolute'
  textarea.style.left = '-9999px'
  textarea.value = value
  document.body.appendChild(textarea)
  textarea.select()
  const result = document.execCommand('Copy')
  if (result) ElMessage({ message: '复制成功', type: 'success' })
  document.body.removeChild(textarea)
}
const licenceUpdate = async () => {
  const { code } = await api.licenceUpdate({ licence: licence.value })
  if (code === 200) {
    ElMessage({ type: 'success', message: '更新成功' })
    handleCancel()
  }
}
const handleCancel = () => {
  visible.value = false
  setTimeout(() => {
    props.closeCallback()
  }, 300)
}
</script>
<style lang="scss">
.global-system-verify-wrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .global-system-verify {
    width: 880px;
    padding: 40px;
    background: #fff;
    border-radius: var(--el-border-radius-base);
    box-shadow: var(--zy-el-box-shadow);
    backface-visibility: hidden;

    .global-system-verify-title {
      font-weight: bold;
      text-align: center;
      font-size: var(--zy-title-font-size);
      line-height: var(--zy-line-height);
      padding: var(--zy-distance-four) var(--zy-distance-one);
      border-bottom: 1px solid var(--zy-el-color-primary);
    }

    .global-system-verify-item {
      display: flex;
      padding-top: 12px;
      line-height: var(--zy-line-height);
      font-size: var(--zy-name-font-size);

      .global-system-verify-label {
        width: 120px;
        text-align: right;
      }

      .global-system-verify-content {
        width: calc(100% - 120px);
      }
    }

    .global-system-verify-button {
      display: flex;
      justify-content: space-between;
      padding: 20px 200px 0 200px;
    }
  }

  .global-system-verify-fade-enter-active {
    -webkit-animation: global-system-verify-fade-in 0.3s;
    animation: global-system-verify-fade-in 0.3s;
  }

  .global-system-verify-fade-leave-active {
    -webkit-animation: global-system-verify-fade-out 0.3s;
    animation: global-system-verify-fade-out 0.3s;
  }

  @keyframes global-system-verify-fade-in {
    0% {
      transform: translate3d(0, -20px, 0);
      opacity: 0;
    }

    100% {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }
  }

  @keyframes global-system-verify-fade-out {
    0% {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }

    100% {
      transform: translate3d(0, -20px, 0);
      opacity: 0;
    }
  }
}
</style>
