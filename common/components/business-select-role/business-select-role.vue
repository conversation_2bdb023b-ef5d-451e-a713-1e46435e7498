<template>
  <div class="business-select-role">
    <div class="business-select-role-button">
      <el-button type="primary" @click="show = !show">{{ props.name }}</el-button>
    </div>
    <div class="business-select-role-body">
      <el-tag closable v-for="item in roleList" :key="item.key" @close="roleDel(item)">{{ item.name }}</el-tag>
    </div>
    <xyl-popup-window v-model="show" name="选择角色">
      <select-role
        :roleData="roleList"
        :rowKey="props.rowKey"
        :params="props.params"
        @callback="callback"></select-role>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'BusinessSelectRole' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, watch } from 'vue'
const props = defineProps({
  modelValue: { type: Array, default: () => [] },
  name: { type: String, default: '选择角色' },
  rowKey: { type: String, default: 'key' },
  params: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['update:modelValue', 'callback'])
const show = ref(false)
const roleKey = ref([])
const roleList = ref([])
const roleAllList = ref([])

onMounted(() => {
  roleData()
})
const roleData = async () => {
  const res = await api.roleData()
  var { data } = res
  roleAllList.value = data
  if (roleKey.value.length) {
    roleChange(roleKey.value)
  }
}
const roleChange = (val) => {
  var arrData = []
  roleAllList.value.forEach((item) => {
    item.roles.forEach((row) => {
      if (val.includes(row[props.rowKey])) {
        arrData.push(row)
      }
    })
  })
  roleList.value = arrData
  emit('callback', roleList.value)
}
const callback = (data) => {
  if (data) {
    roleList.value = data
    emit(
      'update:modelValue',
      data.map((v) => v[props.rowKey])
    )
    emit('callback', roleList.value)
  }
  show.value = false
}
const roleDel = (role) => {
  emit(
    'update:modelValue',
    roleList.value.filter((item) => item[props.rowKey] !== role[props.rowKey]).map((v) => v[props.rowKey])
  )
}
watch(
  () => props.modelValue,
  () => {
    roleKey.value = props.modelValue
    if (roleAllList.value.length) {
      roleChange(roleKey.value)
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.business-select-role {
  width: 100%;
  display: flex;
  padding-top: var(--zy-distance-five);

  .business-select-role-button {
    margin-bottom: var(--zy-distance-two);
    line-height: normal;
  }

  .business-select-role-body {
    line-height: normal;

    .zy-el-tag {
      height: var(--zy-height);
      margin-left: var(--zy-distance-two);
      margin-bottom: var(--zy-distance-two);
    }
  }
}
</style>
