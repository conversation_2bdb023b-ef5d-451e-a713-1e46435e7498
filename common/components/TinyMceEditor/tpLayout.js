
const layoutOpt = (editor) => editor.getParam('tp_layout_options', { selection: 'div,p,table,tr,td,h1,h2,h3,h4,h5,h6,ul,blockquote', clearStyle: [], filterTags: ['table>*', 'img'], style: { 'text-align': 'justify', 'text-indent': '2em', 'line-height': 1.5 }, tagsStyle: {} })
var layout_filterTags = {}
var layout_filterTagsRegex = {}
const doAct = (editor) => {
  var dom = editor.dom
  var blocks = []
  var global$1 = tinymce.util.Tools.resolve('tinymce.util.Tools')
  editor.execCommand('selectAll')
  var layout_opt = layoutOpt(editor)
  for (var key in layout_opt.filterTags) {
    layout_opt.filterTags[key].indexOf('>*') != -1 ? layout_filterTagsRegex[layout_opt.filterTags[key].replace('>*', '').toUpperCase()] = true : layout_filterTags[layout_opt.filterTags[key].toUpperCase()] = true
  }
  layout_opt.selection = layout_opt.selection || 'div,p,table,tr,td,h1,h2,h3,h4,h5,h6,ul,blockquote'
  for (var key in layout_opt.tagsStyle) {
    var ckeyList = key.split(',')
    layout_opt.selection += ',' + key
    for (var ckey in ckeyList)
      ckeyList[ckey].indexOf('>*') != -1 ? layout_filterTagsRegex[ckeyList[ckey].replace('>*', '').toUpperCase()] = key : layout_filterTags[ckeyList[ckey].toUpperCase()] = key
  }
  blocks = editor.selection.getNode().querySelectorAll(layout_opt.selection)
  const _indent2$getValue = (key2, str) => {
    var m = str.match(new RegExp(key2 + ':?(.+?)"?[;}]'))
    return m ? m[1] : false
  }
  const filterFun = (el, _r) => {
    var parentSelector = 'BODY'
    var parents = el.tagName
    if (layout_filterTags[parents] || layout_filterTagsRegex[parents]) {
      !layout_opt.tagsStyle[layout_filterTags[parents]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]])
      return true
    }
    var _p = el.parentNode
    var _pName = _p.tagName
    while (_pName !== parentSelector) {
      var o = _p
      parents = _pName + '>' + parents
      if (layout_filterTags[parents] || layout_filterTagsRegex[_pName]) {
        !layout_opt.tagsStyle[layout_filterTagsRegex[_pName]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTagsRegex[_pName]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTagsRegex[_pName]])
        !layout_opt.tagsStyle[layout_filterTags[parents]] || _r ? _r ? removeStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]]) : '' : setStyleFun(el, layout_opt.tagsStyle[layout_filterTags[parents]])
        return true
      }
      _p = o.parentNode
      _pName = _p.tagName
    }
    return false
  }
  const clearStyleFun = (_block) => {
    var style = dom.getAttrib(_block, 'style')
    for (var key2 in layout_opt.clearStyle) {
      var reg = new RegExp(layout_opt.clearStyle[key2] + ':?(.+?)"?[;}]')
      style = style.replace(reg, '')
    }
    dom.setAttrib(_block, 'style', style)
  }
  const removeStyleFun = (_block, _style) => {
    var style = dom.getAttrib(_block, 'style')
    for (var key2 in _style) {
      var reg = new RegExp(key2 + ':?(.+?)"?[;}]')
      style = style.replace(reg, '')
    }
    dom.setAttrib(_block, 'style', style)
  }
  const setStyleFun = (_block, _style) => {
    for (var key2 in _style) {
      dom.setStyle(_block, key2, _style[key2])
    }
    if (_style['text-indent']) {
      var kv = '', kl = ''
      if (_block && _block.children['0'] && _block.children['0'].attributes && _block.children['0'].attributes.style) {
        kv = _indent2$getValue('font-size', _block.children['0'].attributes.style.textContent)
        kl = _indent2$getValue('letter-spacing', _block.children['0'].attributes.style.textContent)
        if (kv) {
          kv = (parseInt(kv) + parseInt(kl ? kl : 0)) * 2 + 'pt'
        } else
          kv = (parseInt(kl ? kl : 0) + 16) * 2 + 'pt'
      }
      dom.setStyle(_block, 'text-indent', layout_opt.style['text-indent'] && layout_opt.style['text-indent'] != '2em' ? layout_opt.style['text-indent'] : kv ? kv : '2em')
    }
  }
  // var layoutAct = ''
  // if (blocks[0]) {
  //   blocks[0].dataset.layoutFv = blocks[0].dataset.layoutFv ? '' : blocks[0].dataset.layoutFv = 'layoutFV'
  // }
  global$1.each(blocks, (block) => {
    //   if (layoutAct == '') {
    //     if (dom.hasClass(block, 'layoutFV')) {
    //       layoutAct = 'remove'
    //       dom.removeClass(block, 'layoutFV')
    //     } else {
    //       layoutAct = 'add'
    //       dom.addClass(block, 'layoutFV')
    //     }
    //   }
    //   if (layoutAct == 'add') {
    //     !filterFun(block) ? setStyleFun(block, layout_opt.style) : ''
    //     layout_opt.clearStyle ? clearStyleFun(block) : ''
    //   } else {
    //     !filterFun(block, 'remove') ? removeStyleFun(block, layout_opt.style) : ''
    //   }
    !filterFun(block) ? setStyleFun(block, layout_opt.style) : ''
    layout_opt.clearStyle ? clearStyleFun(block) : ''
  })
}
const create = (editor) => {
  editor.undoManager.transact(() => {
    editor.focus()
    doAct(editor)
  })
}
const setup = (editor) => {
  editor.ui.registry.addToggleButton('tpLayout', { icon: 'tpLayout', tooltip: '一键排版', onAction: () => create(editor) })
  editor.ui.registry.addMenuItem('tpLayout', { icon: 'tpLayout', text: '一键排版', onAction: () => create(editor) })
}
const register = (editor) => editor.addCommand('mceTpLayout', () => { create(editor) })
export const initTpLayout = (editor) => {
  setup(editor)
  register(editor)
}
