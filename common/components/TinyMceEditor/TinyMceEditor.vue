<template>
  <div
    class="TinyMceEditor"
    :class="{ TinyMceEditorMaxCount: isMaxCount }"
    v-loading="loading"
    :element-loading-spinner="svg"
    :lement-loading-text="loadingText"
    element-loading-svg-view-box="-10, -10, 50, 50">
    <div :id="state.id" :placeholder="placeholder"></div>
    <p v-if="state.err">{{ state.err }}</p>
    <div class="TinyMceRead" v-if="readAloudShow" @click.stop>
      <div
        class="TinyMceReadButton"
        title="暂停朗读"
        v-if="!whetherPause"
        @click.stop="handlePause()"
        v-html="icon.readPause"></div>
      <div
        class="TinyMceReadButton"
        title="从暂停处继续朗读"
        v-if="whetherPause"
        @click.stop="handleResume()"
        v-html="icon.readStart"></div>
      <div class="TinyMceReadButton" title="停止朗读" @click.stop="handleCancel()" v-html="icon.readStop"></div>
      <div v-if="!whetherPause" class="playIcon"></div>
    </div>
    <xyl-popup-window v-model="textRectifyShow" name="一键校正">
      <wrongly-written-characters :content="textRectifycontent" @callback="wordCallback"></wrongly-written-characters>
    </xyl-popup-window>
  </div>
</template>

<script>
export default { name: 'TinyMceEditor' }
</script>
<script setup>
import api from '@/api'
import config from 'common/config'
import { ref, reactive, watch, nextTick, onMounted, onUnmounted, onActivated, onDeactivated } from 'vue'
import {
  uuid,
  getTinymce,
  getContent,
  setContent,
  resetContent,
  setModeDisabled,
  getContentStyle,
  imageUploadHandler,
  initCustomButton
} from './utils.js'
import { scriptLoader } from './scriptLoader.js'
import store from '@/store'
import { MicroGlobal } from 'common/config/qiankun.js'
import { whetherUseIntelligentize } from 'common/js/system_var.js'
import Speech from 'common/speak-tts/speak-tts.js'
import { icon } from './icon'
import { ElMessage } from 'element-plus'
const props = defineProps({
  modelValue: String,
  setup: Function,
  scriptSrc: { type: String, default: '' },
  setting: { type: Object, default: () => ({}) },
  external_plugins: { type: Object, default: () => ({}) },
  default_style: { type: Object, default: () => ({ fontName: '微软雅黑', fontSize: '12pt' }) },
  content_style: { type: String, default: '' },
  textRectify: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
  max_count: { type: Number, default: 0 },
  debug: { type: Boolean, default: false },
  placeholder: { type: String, default: '请输入' }
})

const emit = defineEmits(['update:modelValue', 'init', 'change', 'count', 'focus', 'blur'])

let mounting = true
let isDefaultStyle = true
const svg =
  '<path class="path" d="M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15" style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>'

const loading = ref(false)
const loadingText = ref('')
const textRectifycontent = ref('')
const textRectifyShow = ref(false)
const isMaxCount = ref(false)

const state = reactive({ editor: null, id: uuid('tinymce'), err: '' })

const getModelValue = () => String(props.modelValue ?? '')

const updateValue = (val) => emit('update:modelValue', val)

const printLog = (e, val, oldVal) => {
  if (!props.debug) return
  console.log(`来自：${e.type} | \n ${val} \n ${oldVal || '--'}`)
}

const onChanged = (e, editor) => {
  if (!editor) editor = state.editor
  const content = getContent(editor)
  printLog(e, content)
  updateValue(content)
  handleMethod(editor)
}
const handleMethod = (editor) => {
  if (!editor) editor = state.editor
  const content = getContent(editor)
  const count = editor.plugins.wordcount.getCount()
  if (props.max_count) {
    isMaxCount.value = props.max_count < count
  } else {
    isMaxCount.value = false
  }
  emit('change', content)
  emit('count', count, isMaxCount.value)
}

const onInited = (editor) => {
  setContent(getModelValue(), editor)
  if (props.disabled && editor.mode.get() !== 'readonly') {
    setModeDisabled(editor)
  }
  // change input undo redo keyup
  editor.on('change input undo redo', (e) => {
    onChanged(e, editor)
  })
  editor.on('PastePreProcess', (e) => {
    e.content = e.content.replace(/<div>/g, '<p>').replace(/<\/div>/g, '</p>')
  })
  editor.on('focus', (e) => {
    emit('focus')
    if (!getModelValue() && isDefaultStyle) {
      isDefaultStyle = false
      for (const key in props.default_style) {
        if (Object.hasOwnProperty.call(props.default_style, key)) {
          editor.editorCommands.execCommand(key, false, props.default_style[key])
        }
      }
    }
  })
  editor.on('blur', (e) => {
    emit('blur')
    if (isMaxCount.value) {
      const count = editor.plugins.wordcount.getCount()
      ElMessage({
        type: 'error',
        message: `当前已输入字数：${count}，限制输入字数：${props.max_count}，当前已超出限制输入字数请精简！`
      })
    }
  })
  emit('init', editor)
}

const trigerUpload = () => {
  return new Promise((resolve) => {
    let input = document.createElement('input')
    input.setAttribute('type', 'file')
    input.setAttribute('multiple', 'multiple')
    input.addEventListener('change', (e) => {
      resolve(e.target.files[0])
    })
    input.click()
  })
}
const fileWordUpload = async (file) => {
  try {
    const param = new FormData()
    param.append('file', file)
    const { data } = await api.fileword2html(param)
    const content = data
      .replace(/<\/?html[^>]*>/g, '')
      .replace(/<head\b[^<]*(?:(?!<\/head>)<[^<]*)*<\/head>/gi, '')
      .replace(/<\/?body[^>]*>/g, '')
      .replace(/<\/?div[^>]*>/g, '')
    emit('update:modelValue', content)
    if (props.setting.import_word_callback) props.setting.import_word_callback(state.editor)
    loading.value = false
  } catch (err) {
    loading.value = false
  }
}
const wordCallback = (newContent) => {
  if (newContent) {
    emit('update:modelValue', newContent)
  }
  textRectifyShow.value = false
}
const elSpeech = ref(null)
const speechInit = () => {
  elSpeech.value = new Speech()
  elSpeech.value?.setLanguage('zh-CN')
  elSpeech.value?.init()
}
const readAloudShow = ref(false)
const whetherPause = ref(false)
const handleReadAloud = () => {
  elSpeech.value.cancel()
  readAloudShow.value = true
  whetherPause.value = false
  elSpeech.value.speak({ text: props.modelValue.replace(/<[^>]+>/g, '').replace(/&nbsp;/gi, '') })
}
const handlePause = () => {
  whetherPause.value = true
  elSpeech.value.pause()
}
const handleResume = () => {
  whetherPause.value = false
  elSpeech.value.resume()
}
const handleCancel = () => {
  readAloudShow.value = false
  elSpeech.value.cancel()
}
const customButton = {
  importWord: {
    icon: 'importWord',
    tooltip: '导入Word',
    onAction: () => {
      trigerUpload().then((file) => {
        const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
        const isShow = ['doc', 'docx', 'wps'].includes(fileType)
        if (!isShow) return ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'wps'].join('、')}格式!` })
        loading.value = true
        fileWordUpload(file)
      })
    }
  },
  textRectify: {
    icon: 'textRectify',
    tooltip: '一键校正',
    onAction: () => {
      if (!props.modelValue) return ElMessage({ type: 'warning', message: '请输入内容进行一键校正！' })
      textRectifycontent.value = props.modelValue
      textRectifyShow.value = true
    }
  },
  exportWord: {
    icon: 'exportWord',
    tooltip: '导出Word',
    onAction: () => {
      if (!props.modelValue) return ElMessage({ type: 'warning', message: '请输入内容再进行导出！' })
      if (window.__POWERED_BY_QIANKUN__) {
        const qiankunMicro = new MicroGlobal()
        qiankunMicro.setGlobalState({
          exportWordHtmlObj: {
            code: 'exportWord',
            name: '导出内容',
            key: 'content',
            data: { content: props.modelValue }
          }
        })
      } else {
        store.commit('setExportWordHtmlObj', {
          code: 'exportWord',
          name: '导出内容',
          key: 'content',
          data: { content: props.modelValue }
        })
      }
    }
  },
  readAloud: {
    icon: 'readAloud',
    tooltip: '朗读',
    onAction: () => {
      if (!props.modelValue) return ElMessage({ type: 'warning', message: '请输入内容再进行朗读！' })
      handleReadAloud()
    }
  }
}

const initEditor = () => {
  if (getTinymce() === null) {
    state.err = 'tinymce is null'
    return
  }
  if (props.debug) {
    console.warn('进入debug模式')
  }
  speechInit()
  const setting = {
    branding: false,
    auto_focus: false,
    selector: `#${state.id}`,
    height: 480,
    language: 'zh_CN',
    language_url: `${config.API_URL}/static_config/langs/zh_CN.js`,
    external_plugins: { ...props.external_plugins }, // , powerpaste: `${config.API_URL}/static_config/powerpaste/plugin.min.js`
    menubar: `edit insert format table importWord${
      whetherUseIntelligentize.value && props.textRectify ? ' textRectify' : ''
    }`,
    menu: {
      importWord: { title: '导入Word', items: 'importWord' },
      textRectify: { title: '一键校正', items: 'textRectify' }
    },
    toolbar: `code undo redo | tpLayout removeformat${
      whetherUseIntelligentize.value && props.textRectify ? ' | importWord textRectify' : ' importWord'
    } | wordcount fullscreen searchreplace readAloud exportWord print | formatselect | fontselect | fontsizeselect | lineheight forecolor backcolor | alignleft aligncenter alignright alignjustify | indent2em indent outdent | bold italic underline strikethrough | link unlink | numlist bullist | image media table`,
    toolbar_mode: 'wrap', // floating / sliding / scrolling / wrap
    plugins: 'code paste wordcount link image media table lists fullscreen searchreplace print indent2em',
    contextmenu: 'copy | link image inserttable | cell row column deletetable',
    font_formats:
      'timesNewRoman=times new roman;宋体=宋体, SimSun;方正小标宋_GBK=方正小标宋_GBK, 宋体, SimSun;仿宋=仿宋, FangSong;仿宋_GB2312=仿宋_GB2312, 仿宋, FangSong;方正仿宋_GBK=方正仿宋_GBK, 仿宋, FangSong;楷体=楷体, SimKai;楷体_GB2312=楷体_GB2312, 楷体, SimKai;方正楷体_GBK=方正楷体_GBK, 楷体, SimKai;方正黑体_GBK=方正黑体_GBK, 黑体, SimHei;微软雅黑=微软雅黑, Microsoft YaHei;黑体=黑体, SimHei;隶书=隶书, SimLi',
    fontsize_formats: '42pt 36pt 26pt 24pt 22pt 18pt 16pt 15pt 14pt 12pt 10.5pt 8pt 7.5pt 6.5pt 5.5pt 5pt',
    nonbreaking_force_tab: true,
    link_context_toolbar: false,
    tp_layout_options: {
      style: {
        'text-align': 'justify',
        'text-indent': '2em',
        'line-height': 1.5,
        'font-size': '16pt',
        'font-family': '仿宋_GB2312'
      },
      tagsStyle: {
        span: {
          'text-align': 'justify',
          'text-indent': 0,
          'line-height': 1.5,
          'font-size': '16pt',
          'font-family': '仿宋_GB2312'
        }
      }
    },
    valid_children: '-div[div],-p[p]',
    ...props.setting,
    paste_data_images: true,
    paste_convert_word_fake_lists: false,
    content_style: getContentStyle(props.content_style),
    // 自定义 图片上传模式
    convert_urls: false,
    images_upload_handler: imageUploadHandler,
    setup: (editor) => {
      state.editor = editor
      if (props.setup) props.setup(editor)
      editor.on('init', (e) => {
        onInited(editor)
      })
      initCustomButton(editor, customButton)
    }
  }
  getTinymce().init(setting)
  mounting = false
}
const mceTpLayout = () => {
  nextTick(() => {
    state.editor.execCommand('mceTpLayout')
  })
}
watch(
  () => props.modelValue,
  (val, oldVal) => {
    if (!state.editor || !state.editor.initialized) return
    if (oldVal === val || val === getContent(state.editor)) return
    printLog({ type: 'propsChanged to setContent' }, val, oldVal)
    if (val === null) return resetContent('', state.editor)
    setContent(getModelValue(), state.editor)
    handleMethod(state.editor)
  }
)
watch(
  () => props.disabled,
  (val) => {
    if (!state.editor || !state.editor.initialized) return
    setModeDisabled(state.editor, val)
  }
)

defineExpose({ id: state.id, editor: state.editor, mceTpLayout, getEditor: () => state.editor })
onMounted(() => {
  if (getTinymce() !== null) {
    initEditor()
    return
  }
  const scriptSrc = props.scriptSrc || `${config.API_URL}/static_config/tinymce.min.js`
  scriptLoader.load(scriptSrc, initEditor)
})

onActivated(() => {
  nextTick(() => {
    if (!mounting) initEditor()
  })
})

onDeactivated(() => {
  if (!state.editor) return
  getTinymce()?.editors[state.id]?.destroy()
  readAloudShow.value = false
  elSpeech.value.cancel()
})

onUnmounted(() => {
  if (!state.editor) return
  getTinymce()?.editors[state.id]?.destroy()
  elSpeech.value.cancel()
})
</script>
<style lang="scss">
.TinyMceEditor {
  width: 100%;
  margin: auto;
  position: relative;

  &.TinyMceEditorMaxCount {
    .tox-statusbar__wordcount {
      color: var(--zy-el-color-error) !important;
    }
  }

  .TinyMceRead {
    position: absolute;
    left: 0;
    bottom: 20%;
    background: rgba(51, 51, 51, 0.5);
    border-radius: 22px;
    padding: 6px 8px;
    display: flex;
    z-index: 999;
    cursor: pointer;

    .TinyMceReadButton {
      width: 32px;
      height: 32px;
      margin: 0 6px;
    }

    .playIcon {
      width: 69px;
      height: 32px;
      background: url('./img/playIcon.gif') no-repeat;
      background-size: 100% 100%;
      margin: 0 6px;
    }
  }
}
</style>
