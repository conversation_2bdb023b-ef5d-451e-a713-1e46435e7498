<!--
 * @FileDescription: 筛选组件
 * @Author: 谢育林
 * @Date: 2022-10-1
 * @LastEditors: 谢育林
 * @LastEditTime: 2022-10-9
 -->
<template>
  <div class="xyl-search-button">
    <div class="xyl-button">
      <el-tooltip placement="top" v-for="item in buttonShow" :key="item.id" :disabled="!item.tip">
        <template #content>
          <div v-if="item.tip">{{ item.tip }}</div>
        </template>
        <el-button @click="handleButton(item.id, item.params)" :type="item.type">
          {{ item.name }}
        </el-button>
      </el-tooltip>
      <template v-if="buttonHidden.length">
        <el-popover
          width="auto"
          trigger="click"
          v-model:visible="isShow"
          placement="right-start"
          transition="zy-el-zoom-in-top"
          popper-class="xyl-button-popover">
          <template #reference>
            <el-button class="xyl-button-more">
              <span class="point"></span>
              <span class="point"></span>
              <span class="point"></span>
            </el-button>
          </template>
          <el-tooltip placement="right" v-for="item in buttonHidden" :key="item.id" :disabled="!item.tip">
            <template #content>
              <div v-if="item.tip">{{ item.tip }}</div>
            </template>
            <el-button @click="handleButton(item.id, item.params)">{{ item.name }}</el-button>
          </el-tooltip>
        </el-popover>
      </template>
      <slot name="button"></slot>
    </div>
    <div class="xyl-search" v-if="searchShow && !searchPopover">
      <slot name="search"></slot>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button v-if="!optionData.length" @click="handleReset">重置</el-button>
      <template v-if="optionData.length">
        <el-popover
          width="auto"
          :visible="show"
          placement="bottom-end"
          transition="zy-el-zoom-in-top"
          :popper-class="['xyl-search-popover', hierarchy ? 'xyl-search-popover-z-index' : '']">
          <template #reference>
            <el-button @click="handleMoreQuery">高级搜索</el-button>
          </template>
          <div class="xyl-search-body">
            <div class="xyl-search-body-name">
              <div class="xyl-search-body-type">
                筛选：
                <el-radio-group v-model="isAnd">
                  <el-radio :label="1">满足所有条件</el-radio>
                  <el-radio :label="0">满足任一条件</el-radio>
                </el-radio-group>
              </div>
              <div class="xyl-search-body-new" @click="searchItemNew">
                新增筛选
                <el-icon>
                  <CirclePlus />
                </el-icon>
              </div>
            </div>
            <el-scrollbar class="xyl-search-body-list">
              <div class="xyl-search-body-item" v-for="item in tableHead" :key="item.id">
                <xyl-search-item
                  v-model:columnId="item.columnId"
                  v-model:queryType="item.queryType"
                  v-model:value="item.value"
                  v-model:valueName="item.valueName"
                  :optionData="optionData"
                  :queryTypeData="queryTypeData"
                  :screeningData="screeningData"
                  :defaultData="defaultData"
                  :dictTypeData="dictTypeData"
                  :queryTypeShow="queryTypeShow"></xyl-search-item>
                <div class="xyl-search-item-del" v-if="tableHead.length > 1" @click="searchItemDel(item.id)">
                  <el-icon>
                    <CircleClose />
                  </el-icon>
                </div>
                <div class="xyl-search-item-del" v-else></div>
              </div>
            </el-scrollbar>
            <div class="xyl-search-item-button">
              <el-checkbox v-model="checked">自动隐藏窗口</el-checkbox>
              <div class="xyl-search-item-button-box">
                <el-button type="primary" @click="handleQuery">搜索</el-button>
                <el-button @click="handleReset">重置筛选</el-button>
              </div>
            </div>
          </div>
        </el-popover>
      </template>
      <el-popover
        width="auto"
        :visible="screenShow"
        placement="bottom-end"
        transition="zy-el-zoom-in-top"
        :popper-class="['xyl-screen-popover', hierarchy ? 'xyl-search-popover-z-index' : '']">
        <template #reference>
          <div class="xyl-screen-button"></div>
        </template>
        <div class="xyl-screen-body">
          <div class="xyl-screen-name">筛选：</div>
          <div class="xyl-screen-list">
            <el-tag v-for="item in screenData" :key="item.id" @close="handleClose(item)" type="info" closable>
              {{ item.valueName }}
            </el-tag>
            <el-button @click="handleEmpty" type="primary" plain>清空</el-button>
          </div>
        </div>
      </el-popover>
    </div>
    <div class="xyl-search" v-if="searchPopover">
      <slot name="search"></slot>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button v-if="!isSearchPopover" @click="handleReset">重置</el-button>
      <template v-if="isSearchPopover">
        <el-popover
          width="auto"
          :visible="show"
          placement="bottom-end"
          transition="zy-el-zoom-in-top"
          :popper-class="['xyl-search-popover', hierarchy ? 'xyl-search-popover-z-index' : '']">
          <template #reference>
            <el-button @click="show = !show">高级搜索</el-button>
          </template>
          <div class="xyl-search-body">
            <el-scrollbar class="xyl-search-body-list">
              <div class="xyl-search-body-popover">
                <slot name="searchPopover"></slot>
              </div>
            </el-scrollbar>
            <div class="xyl-search-item-button">
              <el-checkbox v-model="checked">自动隐藏窗口</el-checkbox>
              <div class="xyl-search-item-button-box">
                <el-button type="primary" @click="handleQuery">搜索</el-button>
                <el-button @click="handleReset">重置筛选</el-button>
              </div>
            </div>
          </div>
        </el-popover>
      </template>
    </div>
  </div>
</template>
<script>
export default { name: 'XylSearchButton' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, computed, watch, useSlots, nextTick, defineAsyncComponent } from 'vue'
import { screenWindowDefault, queryTypeShow } from 'common/js/system_var.js'
import { hasPermission } from '../../js/permissions'
const XylSearchItem = defineAsyncComponent(() => import('./xyl-search-item.vue'))
const props = defineProps({
  data: { type: Array, default: () => [] },
  buttonList: { type: Array, default: () => [] },
  buttonNumber: { type: Number, default: 3 },
  searchShow: { type: Boolean, default: true },
  searchPopover: { type: Boolean, default: false },
  hierarchy: { type: Boolean, default: false }
})
const emit = defineEmits(['handleButton', 'queryClick', 'resetClick'])
const slots = useSlots()
const searchShow = computed(() => props.searchShow)
const searchPopover = computed(() => props.searchPopover)
const isSearchPopover = computed(() => slots.searchPopover().length)
// 按钮的显示隐藏
const show = ref(false)
// 按钮的显示隐藏
const isShow = ref(false)
const checked = ref(screenWindowDefault.value)
// 1 满足所有条件 / 0 满足任一条件
const isAnd = ref(1)
// 拿到高级搜索的所有字典标识
const dictType = ref([])
// 对应字典标识的字典数据
const dictTypeData = ref({})
// 对应内置筛选的数据
const defaultData = ref([])
// 对应个性接口的数据
const screeningData = ref([])
// 拿到高级搜索的筛选条件
const optionData = ref([])
// 筛选类型数组
const queryTypeData = ref([])
// 高级搜索的筛选项
const tableHead = ref([])
const buttonShow = ref([])
const buttonHidden = ref([])
// 高级搜索的筛选项
const screenShow = ref(false)
const screenData = ref([])
/**
 * @description: 初始化高级搜索的筛选条件
 * @param {Array} 开启了高级搜索的筛选条件
 * @return void
 */
const initOptionData = (val) => {
  optionData.value = val.map((v) => ({
    id: v.id,
    name: v.columnComment,
    dictType: v.dictType,
    valType: handleValType(v)
  }))
}

const handleValType = (item) => {
  if (['date', 'YYYY-MM-DD HH:mm', 'YYYY-MM-DD', 'YYYY-MM', 'YYYY'].includes(item.viewType)) {
    return item.viewType
  } else {
    if (item.dictType === 'default') return 'selectTree'
    return item.dictType ? (item.dictType.indexOf('/') !== -1 ? 'selectTree' : 'select') : 'input'
  }
}
/**
 * @description: 初始化高级搜索的默认筛选条件
 * @param {Array} 开启了高级搜索且设为默认筛选条件
 * @return void
 */
const initDictType = (val) => {
  dictType.value = val.map((v) => v.dictType)
  if (val.length) {
    dictionaryData()
  }
}
const initDefaultType = (val) => {
  defaultData.value = val.map((v) => ({ id: v.id, url: v.dictType, data: [] }))
  defaultData.value.forEach((item) => {
    customColumnSelector(item.id, (data) => {
      item.data = data
    })
  })
}
const customColumnSelector = async (columnId, cb) => {
  const { data } = await api.customColumnSelector(columnId)
  cb(data)
}
const initScreeningData = (val) => {
  screeningData.value = val.map((v) => ({ id: v.id, url: v.dictType, data: [] }))
  screeningData.value.forEach((item) => {
    globalJson(item.url, (data) => {
      item.data = data
    })
  })
}
const globalJson = async (url, cb) => {
  const { data } = await api.globalJson(url, {})
  cb(data)
}
/**
 * @description: 初始化高级搜索的对应字典值
 * @param {Array} 开启了高级搜索是字典筛选的筛选条件
 * @return void
 */
const initTableHead = (val, text) => {
  const data = val.map((v) => ({
    id: guid(),
    columnId: v.id,
    queryType: v.queryType,
    value: v.defaultValue,
    valueName: ''
  }))
  tableHead.value = data.length ? data : [{ id: guid(), columnId: '', queryType: '', value: '', valueName: '' }]
  text ? emit(text) : ''
  nextTick(() => {
    screenData.value = tableHead.value.filter((v) => v.columnId && v.value)
    screenShow.value = screenData.value.length ? true : false
  })
}
const buttonList = () => {
  var newButtonList = []
  for (let index = 0; index < props.buttonList.length; index++) {
    const item = props.buttonList[index]
    if (item.has) {
      if (hasPermission(item.has, item.arg)) {
        newButtonList.push(item)
      }
    } else {
      newButtonList.push(item)
    }
  }
  buttonShow.value = newButtonList.slice(0, props.buttonNumber)
  buttonHidden.value = newButtonList.slice(props.buttonNumber)
}
/**
 * @description: 按钮点击事件
 * @return void
 */
const handleButton = (id, params) => {
  isShow.value = false
  emit('handleButton', id, params)
}
/**
 * @description: 查询按钮点击事件
 * @return void
 */
const handleQuery = () => {
  emit('queryClick')
  if (checked.value) {
    show.value = false
  }
  screenData.value = tableHead.value.filter((v) => v.columnId && v.value)
  if (!show.value) {
    screenShow.value = screenData.value.length ? true : false
  }
}
/**
 * @description: 重置按钮点击事件
 * @return void
 */
const handleReset = () => {
  if (optionData.value.length && !searchPopover.value) {
    initTableHead(
      props.data.filter((v) => v.isQuery && v.isQuerydefault),
      'resetClick'
    )
  } else {
    emit('resetClick')
  }
  if (checked.value) {
    show.value = false
  }
}
const handleMoreQuery = () => {
  show.value = !show.value
  if (show.value) {
    screenShow.value = false
  } else {
    screenShow.value = screenData.value.length ? true : false
  }
}
const handleClose = (row) => {
  let newData = []
  for (let index = 0; index < tableHead.value.length; index++) {
    const item = tableHead.value[index]
    if (item.id === row.id) {
      newData.push({ ...item, value: '' })
    } else {
      newData.push(item)
    }
  }
  tableHead.value = newData
  handleQuery()
}
const handleEmpty = () => {
  let newData = []
  for (let index = 0; index < tableHead.value.length; index++) {
    const item = tableHead.value[index]
    newData.push({ ...item, value: '' })
  }
  tableHead.value = newData
  handleQuery()
}
/**
 * @description: 新增筛选条件按钮点击事件
 * @return void
 */
const searchItemNew = () => {
  tableHead.value.push({ id: guid(), columnId: '', queryType: '', value: '', valueName: '' })
}
/**
 * @description: 删除筛选条件按钮点击事件
 * @return void
 */
const searchItemDel = (id) => {
  tableHead.value = tableHead.value.filter((v) => v.id !== id)
}
/**
 * @description: 根据字典值获取字典数据
 * @return void
 */
const dictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: dictType.value })
  dictTypeData.value = data
}
/**
 * @description: 筛选类型数据
 * @return void
 */
const queryTypeList = async () => {
  const { data } = await api.queryTypeList({ uid: guid() })
  queryTypeData.value = data
}
/**
 * @description: 生成uuid
 * @return  {String} xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
 */
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const handleData = () => {
  const getOptionData = []
  const getDictType = []
  const getDefaultType = []
  const getScreeningData = []
  const getTableHead = []
  for (let index = 0; index < props.data.length; index++) {
    const item = props.data[index]
    if (item.isQuery) {
      getOptionData.push(item)
    }
    if (item.isQuery && item.dictType && item.dictType.indexOf('/') === -1 && item.dictType !== 'default') {
      getDictType.push(item)
    }
    if (item.isQuery && item.dictType && item.dictType === 'default') {
      getDefaultType.push(item)
    }
    if (item.isQuery && item.dictType && item.dictType.indexOf('/') !== -1) {
      getScreeningData.push(item)
    }
    if (item.isQuery && item.isQuerydefault) {
      getTableHead.push(item)
    }
  }
  // 初始化高级搜索的筛选条件
  initOptionData(getOptionData)
  // 初始化高级搜索的对应字典值
  initDictType(getDictType)
  initDefaultType(getDefaultType)
  initScreeningData(getScreeningData)
  // 初始化高级搜索的默认筛选条件
  initTableHead(getTableHead)
}
onMounted(() => {
  buttonList()
  if (props.searchShow) {
    queryTypeList()
  }
  if (props.data.length) {
    handleData()
  }
})
// 监听筛选条件的变化
watch(
  () => props.data,
  () => {
    handleData()
  }
)
watch(
  () => props.buttonList,
  () => {
    buttonList()
  }
)
/**
 * @description: 返回以选择的筛选
 * @return  {Array} 筛选数据
 */
const getData = () => ({
  isAnd: isAnd.value,
  wheres: tableHead.value
    .filter((v) => v.columnId && v.value)
    .map((v) => ({
      columnId: v.columnId,
      queryType: v.queryType,
      value: v.value
    }))
})
/**
 * @description: 返回以选择的筛选
 * @return  {Array} 筛选数据
 */
const getIsAnd = () => isAnd.value
/**
 * @description: 返回以选择的筛选
 * @return  {Array} 筛选数据
 */
const getWheres = () =>
  tableHead.value
    .filter((v) => v.columnId && v.value)
    .map((v) => ({
      columnId: v.columnId,
      queryType: v.queryType,
      value: v.value
    }))
defineExpose({ getData, getIsAnd, getWheres })
</script>
<style lang="scss">
.xyl-search-button {
  width: 100%;
  padding: var(--zy-distance-four) 0;
  min-height: calc(var(--zy-height) + (var(--zy-distance-four) * 2));
  display: flex;
  align-items: center;
  background-color: #fff;
  position: relative;

  .xyl-button {
    width: calc(100% - 460px);

    .xyl-button-more {
      font-size: 38px;
      height: var(--zy-height);
      margin-left: var(--zy-distance-four);

      span {
        .point {
          display: inline-block;
          width: 5px;
          height: 5px;
          background: #666666;
          border-radius: 50%;
        }

        .point + .point {
          margin-left: 5px;
        }
      }
    }
  }

  .xyl-search {
    width: 460px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    & > .zy-el-input {
      width: 220px;
    }

    & > .zy-el-select {
      width: 220px;
    }

    .zy-el-button {
      margin-left: var(--zy-distance-two);
    }

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-five);
    }

    .xyl-screen-button {
      height: var(--zy-height);
    }
  }
}

.xyl-search-popover {
  z-index: 998 !important;
  padding: 0 !important;

  .xyl-search-body {
    padding: var(--zy-distance-five) 0 var(--zy-distance-two) 0;

    .xyl-search-body-name {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 var(--zy-distance-two);

      .xyl-search-body-type {
        display: flex;
        align-items: center;
        font-size: var(--zy-text-font-size);
      }

      .xyl-search-body-new {
        display: flex;
        align-items: center;
        color: var(--zy-el-color-primary);
        font-size: var(--zy-text-font-size);
        cursor: pointer;

        .zy-el-icon {
          font-size: var(--zy-name-font-size);
          margin-left: 6px;
        }
      }
    }

    .xyl-search-body-list {
      max-height: 50vh;

      > .zy-el-scrollbar__wrap {
        max-height: 50vh;
      }

      .zy-el-scrollbar__view {
        padding: 0 var(--zy-distance-two);
      }
    }

    .xyl-search-body-item {
      display: flex;
      align-items: center;
      padding: calc(var(--zy-distance-two) / 2) 0;

      .xyl-search-item-del {
        cursor: pointer;
        width: 22px;
        height: 100%;
        line-height: 1;
        padding-left: 6px;

        .zy-el-icon {
          font-size: var(--zy-name-font-size);
        }
      }
    }

    .xyl-search-body-popover {
      width: 460px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      & > .zy-el-input {
        width: 220px;
        margin-top: var(--zy-distance-two);

        .zy-el-input__wrapper {
          width: 100%;
        }
      }

      & > .zy-el-select {
        width: 220px;
        margin-top: var(--zy-distance-two);

        .zy-el-input__wrapper {
          width: 100%;
        }
      }

      & > .zy-el-date-editor {
        width: 220px;
        margin-top: var(--zy-distance-two);
      }

      & > .zy-el-date-editor--daterange {
        width: 100%;
        margin-top: var(--zy-distance-two);
      }

      & > .zy-el-date-editor--datetimerange {
        width: 100%;
        margin-top: var(--zy-distance-two);
      }
    }

    .xyl-search-item-button {
      margin-top: var(--zy-distance-two);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 var(--zy-distance-two);

      .xyl-search-item-button-box {
        .zy-el-button + .zy-el-button {
          margin-left: var(--zy-distance-five);
        }
      }
    }
  }
}

.xyl-search-popover-z-index {
  z-index: 999 !important;
}

.xyl-button-popover {
  padding: 0 !important;
  margin: 0 !important;
  min-width: 88px !important;

  .zy-el-button + .zy-el-button {
    margin-left: 0;
  }

  .zy-el-button {
    width: 100%;
    display: block;
    border-radius: 0px;
  }
}

.xyl-screen-popover {
  z-index: 998 !important;
  padding: 0 !important;

  .xyl-screen-body {
    padding: var(--zy-distance-two);
    padding-top: var(--zy-distance-five);
    display: flex;
    max-width: calc(100vw - 273px);

    .xyl-screen-name {
      min-width: calc(var(--zy-text-font-size) * 3);
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-height-secondary);
      margin-top: var(--zy-distance-five);
    }

    .xyl-screen-list {
      display: flex;
      flex-wrap: wrap;

      .zy-el-tag {
        font-size: var(--zy-text-font-size);
        height: var(--zy-height-secondary);
        margin-top: var(--zy-distance-five);
        margin-left: var(--zy-distance-five);
        border-radius: var(--el-border-radius-base);
      }

      .zy-el-button {
        font-size: var(--zy-text-font-size);
        height: var(--zy-height-secondary);
        margin-top: var(--zy-distance-five);
        margin-left: var(--zy-distance-five);
        border-radius: var(--el-border-radius-small);
      }
    }
  }
}
</style>
