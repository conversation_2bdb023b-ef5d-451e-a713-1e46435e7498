<template>
  <div class="auroral-push">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="推送内容" prop="content" class="globalFormTitle">
        <el-input v-model="form.content" placeholder="请输入推送内容" clearable />
      </el-form-item>
      <el-form-item label="推送用户" class="globalFormTitle">
        <business-select-person-account v-model="form.pushUserIds"></business-select-person-account>
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'AuroralPush' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({
  id: { type: String, default: '' },
  code: { type: String, default: '' },
  content: { type: String, default: '' }
})
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  content: '',
  pushUserIds: []
})
const rules = reactive({
  content: [{ required: true, message: '请输入推送内容', trigger: ['blur', 'change'] }]
})

onMounted(() => {
  if (props.content) {
    form.content = props.content
  }
})

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const globalJson = async () => {
  const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区
  const { code } = await api.globalJson('/aurora', {
    extras: { code: props.code, id: props.id, areaId: AreaId },
    alert: form.content,
    isPublish: form.pushUserIds.length ? 0 : 1,
    accountIds: form.pushUserIds
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '推送成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.auroral-push {
  width: 690px;
}
</style>
