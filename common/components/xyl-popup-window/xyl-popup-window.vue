<!--
 * @FileDescription: 弹窗组件
 * @Author: 谢育林
 * @Date: 2022-10-1
 * @LastEditors: 谢育林
 * @LastEditTime: 2022-10-1
 -->
<template>
  <teleport to="body">
    <div class="xyl-popup-window" ref="XylPopupWindow" v-if="boxShow" v-show="elShow">
      <transition
        enter-active-class="animate__animated animate__zoomIn animate__faster"
        leave-active-class="animate__animated animate__zoomOut animate__faster">
        <div class="xyl-popup-window-box" ref="XylPopupWindowBox" v-show="isShow">
          <div class="xyl-popup-window-head" @mousedown="onMovedown">
            <div class="xyl-popup-window-title ellipsis">{{ name }}</div>
            <div class="xyl-popup-window-close" @click="closeClick">
              <el-icon>
                <Close />
              </el-icon>
            </div>
          </div>
          <el-scrollbar class="xyl-popup-window-body">
            <slot></slot>
          </el-scrollbar>
        </div>
      </transition>
    </div>
  </teleport>
</template>
<script>
export default { name: 'XylPopupWindow' }
</script>
<script setup>
import { ref, computed, watch, nextTick, onUnmounted, onActivated, onDeactivated } from 'vue'
import elementResizeDetectorMaker from 'element-resize-detector'

const erd = elementResizeDetectorMaker()

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  name: { type: String, default: '' },
  beforeClose: Function
})

const emit = defineEmits(['update:modelValue'])

const name = computed(() => props.name)
const elShow = ref(true)
const isShow = ref(false)
const boxShow = ref(false)
const positionX = ref(0)
const positionY = ref(0)
const XylPopupWindow = ref()
const XylPopupWindowBox = ref()

// 拖拽相关状态
let isDragging = false
let startX = 0
let startY = 0
let initialLeft = 0
let initialTop = 0

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 清理所有事件监听器
const cleanupEventListeners = () => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  isDragging = false
}

// 鼠标移动处理
const handleMouseMove = (e) => {
  if (!isDragging) return

  const pdiv = XylPopupWindow.value
  const odiv = XylPopupWindowBox.value
  if (!pdiv || !odiv) return

  let left = e.clientX - startX + initialLeft
  let top = e.clientY - startY + initialTop

  // 边界检查
  const minLeft = 0
  const minTop = 0
  const maxLeft = pdiv.offsetWidth - odiv.offsetWidth
  const maxTop = pdiv.offsetHeight - odiv.offsetHeight

  left = Math.max(minLeft, Math.min(left, maxLeft))
  top = Math.max(minTop, Math.min(top, maxTop))

  positionX.value = top
  positionY.value = left
  odiv.style.left = `${left}px`
  odiv.style.top = `${top}px`
}

// 鼠标抬起处理
const handleMouseUp = () => {
  cleanupEventListeners()
}

// 防抖的定位函数
const debouncedLocation = debounce(() => {
  location()
}, 16) // 约60fps

const location = () => {
  nextTick(() => {
    const cover = XylPopupWindow.value
    const content = XylPopupWindowBox.value
    if (!cover || !content) return
    positionX.value = (cover.offsetHeight - content.offsetHeight) / 2
    positionY.value = (cover.offsetWidth - content.offsetWidth) / 2
    content.style.top = `${positionX.value}px`
    content.style.left = `${positionY.value}px`
  })
}

// 鼠标按下移动
const onMovedown = (e) => {
  const pdiv = XylPopupWindow.value
  const odiv = XylPopupWindowBox.value
  if (!pdiv || !odiv) return

  // 检查是否可拖拽
  const diffWidth = pdiv.offsetWidth - odiv.offsetWidth
  const diffHeight = pdiv.offsetHeight - odiv.offsetHeight
  if (diffWidth <= 0 || diffHeight <= 0) return

  isDragging = true
  startX = e.clientX
  startY = e.clientY
  initialLeft = odiv.offsetLeft
  initialTop = odiv.offsetTop

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const closeClick = () => {
  if (typeof props.beforeClose === 'function') {
    props.beforeClose(() => {
      emit('update:modelValue', false)
    })
  } else {
    emit('update:modelValue', false)
  }
}

// keep-alive 生命周期处理
onDeactivated(() => {
  if (props.modelValue) {
    elShow.value = false
  }
})

onActivated(() => {
  if (props.modelValue && !elShow.value) {
    elShow.value = true
  }
})

// 监听显示状态变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      boxShow.value = true
      nextTick(() => {
        isShow.value = true
        // 添加resize监听
        if (XylPopupWindowBox.value) {
          erd.listenTo(XylPopupWindowBox.value, debouncedLocation)
        }
      })
    } else {
      // 清理resize监听
      if (XylPopupWindowBox.value) {
        erd.uninstall(XylPopupWindowBox.value)
      }
      isShow.value = false
      setTimeout(() => {
        boxShow.value = false
      }, 99)
    }
  },
  { immediate: true }
)

// 组件卸载时清理
onUnmounted(() => {
  cleanupEventListeners()
  if (XylPopupWindowBox.value) {
    erd.uninstall(XylPopupWindowBox.value)
  }
})
</script>
<style lang="scss">
.xyl-popup-window {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;

  .xyl-popup-window-box {
    position: fixed;
    background: #fff;
    overflow: hidden;

    .xyl-popup-window-head {
      width: 100%;
      height: 52px;
      background: var(--zy-el-color-primary);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 var(--zy-distance-two);
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      cursor: move;

      .xyl-popup-window-title {
        width: 80%;
        color: #fff;
        font-size: var(--zy-name-font-size);
      }

      .xyl-popup-window-close {
        width: 52px;
        min-width: 52px;
        height: 100%;
        color: #fff;
        cursor: pointer;
        font-size: var(--zy-navigation-font-size);
        text-align: right;
        line-height: 52px;
      }
    }

    .xyl-popup-window-body {
      max-height: calc(85vh - 52px);

      & > .zy-el-scrollbar__wrap {
        max-height: calc(85vh - 52px);
      }
    }
  }
}
</style>
