<template>
  <div class="visible-range">
    <el-radio-group v-model="type" @change="typeChange">
      <el-radio v-for="item in typeData" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
    </el-radio-group>
    <template v-if="['exclude_user', 'some_user'].includes(type)">
      <business-select-person
        v-model="userData"
        :tabCode="props.tabCode"
        :params="props.params"
        :urlParams="props.urlParams"
        @callback="callback"></business-select-person>
    </template>
  </div>
</template>
<script>
export default { name: 'VisibleRange' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted } from 'vue'
const props = defineProps({
  modelValue: [String, Number],
  userData: { type: Array, default: () => [] },
  tabCode: { type: Array, default: () => ['userOffice'] },
  params: { type: Object, default: () => ({}) },
  urlParams: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['update:modelValue', 'update:userData', 'change', 'callback', 'isPropCallback'])

const type = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const typeData = ref([])
const userData = computed({
  get() {
    return props.userData
  },
  set(value) {
    emit('update:userData', value)
  }
})
const userListData = ref([])

onMounted(() => {
  dictionaryData()
})

const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['visible_type_person'] })
  var { data } = res
  type.value = type.value ? type.value : data.visible_type_person[0].key
  typeData.value = data.visible_type_person
  isProp()
}
const typeChange = () => {
  emit('change')
  isProp()
}
const callback = (data) => {
  if (data) {
    userListData.value = data
  }
  emit('callback', data)
  isProp()
}
const isProp = () => {
  if (['exclude_user', 'some_user'].includes(type.value)) {
    emit('isPropCallback', userListData.value.length)
  } else {
    emit('isPropCallback', 1)
  }
}
</script>
<style lang="scss">
.visible-range {
  width: 100%;

  .zy-el-radio-group {
    .zy-el-radio {
      height: var(--zy-height);
    }
  }

  .zy-el-radio-group + .business-select-person {
    margin-top: var(--zy-distance-four);
  }
}
</style>
