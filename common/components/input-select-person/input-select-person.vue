<template>
  <el-input v-model="userName" @focus="userFocus" :disabled="disabled" :placeholder="placeholder" readonly />
  <xyl-popup-window v-model="show" name="选择用户">
    <select-person
      :tabCode="props.tabCode"
      :userData="userData"
      :filterUser="props.filterUser"
      :params="props.params"
      :urlParams="props.urlParams"
      :dataMethod="props.dataMethod"
      @callback="callback"
      :max="1"></select-person>
  </xyl-popup-window>
</template>
<script>
export default { name: 'InputSelectPerson' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, watch, computed } from 'vue'
const props = defineProps({
  soleKey: { type: String, default: '' },
  modelValue: [String, Number],
  filterUser: { type: Array, default: () => [] },
  disabled: { type: Boolean, default: false },
  placeholder: { type: String, default: '请选择用户' },
  tabCode: { type: Array, default: () => ['userOffice'] },
  params: { type: Object, default: () => ({}) },
  urlParams: { type: Object, default: () => ({}) },
  dataMethod: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['update:modelValue', 'callback'])

const show = ref(false)
const userId = ref(props.modelValue)
const userName = ref('')
const userData = ref([])
const disabled = computed(() => props.disabled)
const placeholder = computed(() => props.placeholder)
onMounted(() => {
  if (props.modelValue && !userData.value.length) {
    getSelectUser()
  }
})

watch(
  () => props.modelValue,
  () => {
    userId.value = props.modelValue
    if (props.modelValue) {
      if (userData.value.length) {
        if (props.modelValue !== userData.value[0].id) {
          getSelectUser()
        }
      } else {
        getSelectUser()
      }
    } else {
      callback([])
    }
  }
)

const getSelectUser = async () => {
  const res = await api.getSelectUser({ existsUserIds: [userId.value], key: props.soleKey, type: props.placeholder })
  var { data } = res
  userName.value = data[0].userName
  userData.value = data
}
const userFocus = () => {
  show.value = true
}
const callback = (data) => {
  show.value = false
  if (data) {
    userData.value = data.length ? data : []
    userName.value = data.length ? data[0].userName : ''
    emit('update:modelValue', data.length ? data[0].id : '')
    emit('callback', data.length ? data[0] : null)
  }
}
</script>
