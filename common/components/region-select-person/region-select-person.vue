<template>
  <div class="region-select-person">
    <div class="region-select-person-body">
      <div class="region-select-person-left">
        <div class="region-select-person-input">
          <el-tree-select
            v-model="regionId"
            lazy
            :load="load"
            check-strictly
            :render-after-expand="false"
            :props="{ value: 'id', label: 'name', children: 'children', isLeaf: 'isLeaf' }"
            @change="regionChange"
            placeholder="请选择地区" />
          <el-input v-model="keyword" placeholder="根据用户名称搜索" @keyup.enter="handleSearch" clearable />
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </div>
        <div class="region-select-person-group">
          <div class="region-select-person-group-name">分类列表</div>
          <el-scrollbar class="region-select-person-group-tree">
            <el-tree
              ref="typeRef"
              node-key="id"
              :data="typeData"
              highlight-current
              :props="{ label: 'name' }"
              @node-click="handleTypeClick" />
          </el-scrollbar>
        </div>
        <div class="region-select-person-group">
          <div class="region-select-person-group-name">分组列表</div>
          <el-scrollbar class="region-select-person-group-tree">
            <el-tree
              ref="groupRef"
              node-key="id"
              :data="groupData"
              highlight-current
              :props="{ label: 'name' }"
              @node-click="handleGroupClick" />
          </el-scrollbar>
        </div>
        <div class="region-select-person-user-list">
          <div class="region-select-person-user-list-head">
            <div class="region-select-person-user-list-name">人员列表（{{ userDataList.length }}）</div>
            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAll"></el-checkbox>
            <!-- <div class="region-select-person-user-list-checkbox">
              <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAll">全选</el-checkbox>
              <el-checkbox v-model="isNoSelect">仅显示未选人员</el-checkbox>
            </div> -->
            <el-radio-group v-model="isNoSelect">
              <el-radio :value="0">全部</el-radio>
              <el-radio :value="1">未选人员</el-radio>
            </el-radio-group>
          </div>
          <virtual-user
            v-model="userId"
            node-key="userId"
            :data="userDataList"
            @handleChange="handleChange"></virtual-user>
        </div>
      </div>
      <div class="region-select-person-right">
        <div class="region-select-person-right-name">
          已选人员（{{ userDataNum }}）
          <div @click="deleteAll">清空</div>
        </div>
        <el-scrollbar class="region-select-person-right-box">
          <el-collapse v-model="regionCollapse">
            <el-collapse-item :name="item.id" v-for="item in userData" :key="item.id">
              <template #title>{{ item.name }}（{{ item.userData.length }}）</template>
              <div class="region-select-person-user-item" v-for="row in item.userData" :key="row.userId">
                <div
                  class="region-select-person-user-item-name ellipsis"
                  :title="`${row.userName} - ${handleMobile(row.mobile)}`">
                  {{ row.userName }} - {{ handleMobile(row.mobile) }}
                </div>
                <div class="region-select-person-user-item-text ellipsis">{{ row.position }}</div>
                <div class="region-select-person-user-del" @click="deleteclick(item.id, row)">
                  <el-icon>
                    <Close />
                  </el-icon>
                </div>
              </div>
              <div class="zy-el-tree__empty-block" v-if="!item.userData.length">
                <span class="zy-el-tree__empty-text">暂无数据</span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-scrollbar>
      </div>
    </div>
    <div class="region-select-person-button">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'RegionSelectPerson' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted, nextTick, defineAsyncComponent } from 'vue'
import { encryptPhone } from 'common/js/utils.js'
import { systemMobileEncrypt } from 'common/js/system_var.js'
import { ElMessage, ElMessageBox } from 'element-plus'
const VirtualUser = defineAsyncComponent(() => import('../virtualElement/virtual-user.vue'))
const props = defineProps({
  userId: { type: Array, default: () => [] },
  userData: { type: Array, default: () => [] },
  levelType: { type: String, default: 'all' }
})
const emit = defineEmits(['callback'])

const regionId = ref('')
const regionData = ref([])
const typeRef = ref()
const typeId = ref('')
const typeData = ref([])
const groupRef = ref()
const groupId = ref('')
const groupData = ref([])
const keyword = ref('')
const isNoSelect = ref(0)
const userDataList = computed(() => {
  return isNoSelect.value ? userDataArr.value.filter((item) => !userId.value.includes(item.userId)) : userDataArr.value
})
const handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)

onMounted(() => {
  if (props.userData.length) {
    userData.value = props.userData.map((v) => v)
  }
  if (props.userId.length) {
    getSelectUser(props.userId)
  }
})

const getSelectUser = async (userIds) => {
  const { data } = await api.getSelectUser({ existsUserIds: userIds })
  userInitData(data)
}
const userInitData = (data) => {
  var arrData = []
  var arrIndex = []
  data.forEach((item) => {
    if (arrIndex.includes(item.areaId)) {
      arrData.forEach((row) => {
        if (item.areaId === row.id) {
          row.userData.push({ ...item, userId: item.id })
        }
      })
    } else {
      arrIndex.push(item.areaId)
      arrData.push({ id: item.areaId, name: item.areaName, userData: [{ ...item, userId: item.id }] })
    }
  })
  userData.value = arrData
}
const load = (node, resolve) => {
  if (node.isLeaf) return resolve([])
  if (regionData.value.length) {
    resolve(dataList(regionData.value, node.data.id))
  } else {
    businessAreaTree(resolve, node.data.id)
  }
}
const dataList = (data, id) => {
  var arr = []
  if (id) {
    data.forEach((item) => {
      if (item.id === id) {
        item.children.forEach((row) => {
          var obj = row
          obj.isLeaf = obj.children && obj.children.length ? false : true // eslint-disable-line
          arr.push({ ...obj, children: [] })
        })
      }
      if (item.children.length) {
        arr = [...arr, ...dataList(item.children, id)]
      }
    })
  } else {
    data.forEach((item) => {
      var obj = item
      obj.isLeaf = obj.children && obj.children.length ? false : true // eslint-disable-line
      arr.push({ ...obj, children: [] })
    })
  }
  return arr
}
const businessAreaTree = async (resolve, id) => {
  const { data } = await api.businessAreaTree({ levelType: props.levelType, isFilterUnUsing: 1 })
  regionId.value = data[0]?.id
  regionData.value = data
  resolve(dataList(regionData.value, id))
  regionChange()
}
const defaultObj = (data) => {
  data.forEach((item) => {
    if (regionId.value === item.id) {
      userData.value.push({ id: item.id, name: item.name, userData: [] })
    }
    if (item.children.length) {
      defaultObj(item.children)
    }
  })
}
const regionChange = () => {
  typeData.value = []
  groupData.value = []
  userDataArr.value = []
  userIndexData.value = []
  if (userData.value.map((v) => v.id).includes(regionId.value)) {
    userData.value.forEach((item) => {
      if (regionId.value === item.id) {
        item.userData.forEach((row) => {
          userIndexData.value[row.userId] = row.userId
        })
      }
    })
  } else {
    userData.value = userData.value.filter((item) => item.userData.length)
    defaultObj(regionData.value)
  }
  typeId.value = ''
  typeData.value = []
  groupId.value = ''
  groupData.value = []
  businessAreaType()
}
const businessAreaType = async () => {
  const { data } = await api.businessAreaType({
    query: { areaId: regionId.value, isOpen: 1, ownerType: 'system' }
  })
  typeData.value = data
  if (!data.length) {
    groupData.value = []
    userDataArr.value = []
    return
  }
  nextTick(() => {
    typeId.value = data[0]?.id
    typeRef.value.setCurrentKey(typeId.value)
    businessAreaGroup()
  })
}
const handleTypeClick = (data) => {
  typeId.value = data.id
  businessAreaGroup()
}
const businessAreaGroup = async () => {
  const { data } = await api.businessAreaGroup({
    query: { typeId: typeId.value, areaId: regionId.value, isOpen: 1, ownerType: 'system' }
  })
  groupData.value = data
  if (!data.length) {
    userDataArr.value = []
    return
  }
  nextTick(() => {
    groupId.value = data[0]?.id
    groupRef.value.setCurrentKey(groupId.value)
    businessAreaUser()
  })
}
const handleGroupClick = (data) => {
  groupId.value = data.id
  businessAreaUser()
}
const businessAreaUser = async () => {
  const { data } = await api.businessAreaUser({
    keyword: keyword.value,
    query: { relationBookId: groupId.value, areaId: regionId.value, isOpen: 1, isTempUser: 0, ownerType: 'system' }
  })
  userDataArr.value = data
  memoryChecked()
}
const handleSearch = () => {
  businessAreaUser()
}
const checkAll = ref(false)
const isIndeterminate = ref(false)
const userId = ref([])
const userData = ref([])
const userDataArr = ref([])
const userIndexData = ref([])
const userDataNum = computed(() => eval(userData.value.map((v) => v.userData.length).join('+')))
const regionCollapse = ref([])

const handleChange = (value) => {
  const checkedCount = value.length
  checkAll.value = checkedCount > userDataArr.value.length || checkedCount === userDataArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userDataArr.value.length
  var values = []
  value.forEach((item) => {
    values[item] = item
  })
  userDataArr.value.forEach((item) => {
    if (!Object.prototype.hasOwnProperty.call(values, item.userId)) {
      deleteData(item)
      delete userIndexData.value[item.userId]
    }
  })
  value.forEach((item) => {
    if (!Object.prototype.hasOwnProperty.call(userIndexData.value, item)) {
      pushData(item)
      userIndexData.value[item] = item
    }
  })
}

const handleCheckAllData = (val) => {
  for (let i = 0, len = userDataArr.value.length; i < len; i++) {
    if (Object.prototype.hasOwnProperty.call(userIndexData.value, userDataArr.value[i].userId)) {
      if (!val) {
        deleteData(userDataArr.value[i])
      }
      val ? null : delete userIndexData.value[userDataArr.value[i].userId] // eslint-disable-line
    } else {
      pushData(userDataArr.value[i].userId)
      userIndexData.value[userDataArr.value[i].userId] = userDataArr.value[i].userId
    }
  }
}
const handleCheckAll = (val) => {
  userId.value = val ? [...new Set([...userId.value, ...userDataArr.value.map((v) => v.userId)])] : []
  isIndeterminate.value = false
  handleCheckAllData(val)
}

const deleteData = (data) => {
  userData.value = userData.value.map((v) =>
    regionId.value === v.id
      ? { id: v.id, name: v.name, userData: v.userData.filter((item) => item.userId !== data.userId) }
      : v
  )
}
const pushData = (userId) => {
  userDataArr.value.forEach((item) => {
    if (item.userId === userId) {
      userData.value = userData.value.map((v) =>
        regionId.value === v.id ? { id: v.id, name: v.name, userData: [...v.userData, item] } : v
      )
    }
  })
}

const deleteAll = () => {
  ElMessageBox.confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      userData.value = []
      userIndexData.value = []
      defaultObj(regionData.value)
      memoryChecked()
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消删除' })
    })
}
const deleteclick = (id, data) => {
  userData.value = userData.value.map((v) =>
    id === v.id ? { id: v.id, name: v.name, userData: v.userData.filter((item) => item.userId !== data.userId) } : v
  )
  delete userIndexData.value[data.userId]
  memoryChecked()
}
const memoryChecked = () => {
  var userIdArr = []
  userDataArr.value.forEach((row) => {
    if (Object.prototype.hasOwnProperty.call(userIndexData.value, row.userId)) {
      userIdArr.push(row.userId)
    }
  })
  userId.value = userIdArr
  const checkedCount = userIdArr.length
  checkAll.value = checkedCount > userDataArr.value.length || checkedCount === userDataArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userDataArr.value.length
  if (checkedCount === 0 && userDataArr.value.length === 0) {
    checkAll.value = false
  }
}
const submitForm = () => {
  emit(
    'callback',
    userData.value.filter((item) => item.userData.length)
  )
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.region-select-person {
  width: 1100px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-four) var(--zy-distance-two) var(--zy-distance-two) var(--zy-distance-one);

  .region-select-person-body {
    height: calc(100% - (var(--zy-height) + var(--zy-distance-two)));
    display: flex;
    justify-content: space-between;

    .region-select-person-left {
      width: 710px;
      height: 100%;
      display: flex;
      flex-wrap: wrap;

      .region-select-person-input {
        width: 100%;
        display: flex;
        padding-bottom: var(--zy-distance-three);

        .zy-el-select {
          min-width: 200px;
          margin-right: var(--zy-distance-two);

          .zy-el-input {
            margin-right: 0;
          }
        }

        .zy-el-input {
          margin-right: var(--zy-distance-two);
        }
      }

      .region-select-person-group {
        width: 220px;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);

        .region-select-person-group-name {
          padding: var(--zy-distance-four) var(--zy-distance-three);
          font-weight: bold;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
        }

        .region-select-person-group-tree {
          width: 100%;
          height: calc(100% - (var(--zy-name-font-size) * (var(--zy-line-height)) + (var(--zy-distance-four) * 2)));

          .zy-el-tree-node.is-current {
            & > .zy-el-tree-node__content {
              position: relative;

              .zy-el-tree-node__label {
                color: var(--zy-el-color-primary);

                &::after {
                  content: '';
                  position: absolute;
                  top: 0;
                  right: 0;
                  width: 4px;
                  height: 100%;
                  background: var(--zy-el-color-primary);
                }
              }
            }
          }

          .zy-el-tree-node__content {
            height: auto;
            padding: var(--zy-distance-five) 0;

            .zy-el-tree-node__label {
              width: calc(100% - 28px);
              text-overflow: clip;
              white-space: normal;
              line-height: var(--zy-line-height);
              font-size: var(--zy-text-font-size);
              padding-right: var(--zy-distance-one);
            }
          }
        }
      }

      .region-select-person-user-list {
        width: 270px;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));
        border: 1px solid var(--zy-el-border-color-lighter);
        border-left: 0;

        .virtual-user {
          height: calc(
            100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + var(--zy-height) + var(--zy-distance-four))
          );
        }

        .region-select-person-user-list-head {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          padding: 0 var(--zy-distance-three);
          padding-top: var(--zy-distance-four);

          .region-select-person-user-list-name {
            font-weight: bold;
            font-size: var(--zy-name-font-size);
            line-height: var(--zy-line-height);
          }

          .zy-el-checkbox {
            height: auto;
          }

          // .region-select-person-user-list-checkbox {
          //   height: 100%;
          //   display: flex;
          //   align-items: flex-end;
          //   flex-direction: column;
          //   justify-content: space-around;
          //   padding: 3px 0;

          //   .zy-el-checkbox {
          //     height: auto;
          //     position: relative;
          //     padding-right: 22px;
          //     margin: 0;

          //     .zy-el-checkbox__input {
          //       position: absolute;
          //       top: 50%;
          //       right: 0;
          //       transform: translateY(-50%);
          //     }
          //   }
          // }

          .zy-el-radio-group {
            width: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .region-select-person-right {
      width: 310px;
      height: 100%;

      .region-select-person-right-name {
        line-height: var(--zy-height);
        padding: var(--zy-distance-three);
        font-size: var(--zy-name-font-size);
        padding-top: 0;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;

        div {
          font-size: var(--zy-text-font-size);
          color: var(--zy-el-text-color-regular);
          font-weight: normal;
          cursor: pointer;
        }
      }

      .region-select-person-right-box {
        width: 100%;
        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));

        .zy-el-collapse {
          .zy-el-collapse-item__header {
            height: auto;
            line-height: var(--zy-line-height);
            font-size: var(--zy-text-font-size);
            padding: var(--zy-distance-four) var(--zy-distance-two);
          }

          .zy-el-collapse-item__content {
            border-top: 1px solid var(--zy-el-collapse-border-color);
            padding-bottom: var(--zy-distance-four);
          }
        }

        .region-select-person-user-item {
          width: 290px;
          background: var(--el-color-info-light-9);
          padding: var(--zy-distance-five) var(--zy-distance-one);
          cursor: pointer;
          position: relative;
          margin-top: var(--zy-distance-four);
          border-radius: var(--el-border-radius-small);

          &::after {
            content: '';
            position: absolute;
            top: calc(var(--zy-distance-five) + var(--zy-font-text-distance-five));
            left: var(--zy-distance-two);
            transform: translateX(-50%);
            width: var(--zy-text-font-size);
            height: calc(var(--zy-text-font-size) * var(--zy-line-height));
            background: url('./img/select_person_user_icon.png') no-repeat;
            background-size: var(--zy-text-font-size) var(--zy-text-font-size);
            background-position: center;
          }

          .region-select-person-user-del {
            position: absolute;
            top: 0;
            right: 0;
            width: var(--zy-distance-one);
            height: 100%;
            padding-top: var(--zy-distance-five);
            text-align: center;
            font-size: var(--zy-navigation-font-size);
            line-height: var(--zy-line-height);
          }

          .region-select-person-user-item-name {
            font-size: var(--zy-text-font-size);
            line-height: var(--zy-line-height);
            padding: var(--zy-font-text-distance-five) 0;
            height: calc(var(--zy-text-font-size) * var(--zy-line-height));
            box-sizing: content-box;
          }

          .region-select-person-user-item-text {
            font-size: var(--zy-text-font-size);
            line-height: var(--zy-line-height);
            padding: var(--zy-font-text-distance-five) 0;
            height: calc(var(--zy-text-font-size) * var(--zy-line-height));
            color: var(--zy-el-text-color-secondary);
            box-sizing: content-box;
          }
        }
      }
    }
  }

  .region-select-person-button {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--zy-distance-two);
    padding-right: var(--zy-distance-two);

    .zy-el-button + .zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
