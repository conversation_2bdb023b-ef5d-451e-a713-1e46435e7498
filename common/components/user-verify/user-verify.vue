<template>
  <div class="user-verify">
    <div class="user-verify-text">
      <span>注：</span>
      1、手机号重复的情况下，系统检测到相同手机号的用户信息，可引用已存在的用户信息。
    </div>
    <div class="user-verify-text">
      2、若与本地区已存在用户的手机号重复，不可直接新增，只可引用已存在的用户信息，或修改手机号。
    </div>
    <div class="globalTable">
      <el-table :data="tableData">
        <el-table-column label="所属地区" min-width="120" show-overflow-tooltip prop="areaName" />
        <el-table-column label="姓名" min-width="160" prop="userName" />
        <el-table-column label="性别" min-width="100" prop="sex" />
        <el-table-column label="手机号" min-width="160" prop="mobile" />
        <el-table-column label="民族" min-width="120" prop="nation" />
        <el-table-column label="单位及职位" min-width="180" show-overflow-tooltip prop="position" />
        <el-table-column label="操作" width="180" fixed="right" class-name="globalTableCustom">
          <template #default="scope">
            <el-button
              @click="handleEdit(scope.row)"
              type="primary"
              :disabled="scope.row.isDisabled && scope.row.areaId !== user.areaId"
              plain>
              {{ scope.row.areaId === user.areaId ? '编辑用户信息' : '引用用户信息' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default { name: 'UserVerify' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { user } from 'common/js/system_var.js'
const props = defineProps({ mobile: { type: String, default: '' } })
const emit = defineEmits(['callback'])
const tableData = ref([])
onMounted(() => {
  userExists()
})
const userExists = async () => {
  const res = await api.userExists({ mobile: props.mobile })
  var { data } = res
  tableData.value = data.map((v) => ({ ...v, isDisabled: data.map((v) => v.areaId).includes(user.value.areaId) }))
}
const handleEdit = (row) => {
  emit('callback', row, row.areaId === user.value.areaId)
}
</script>
<style lang="scss">
.user-verify {
  border: 1px solid var(--zy-el-border-color-lighter);
  width: 990px;
  height: calc(85vh - 52px);
  padding: var(--zy-distance-three) 0;

  .user-verify-text {
    line-height: var(--zy-line-height);
    font-size: var(--zy-text-font-size);
    position: relative;
    padding: var(--zy-font-text-distance-five) var(--zy-distance-one);

    span {
      position: absolute;
      top: 0;
      left: var(--zy-distance-four);
      display: inline-block;
      line-height: var(--zy-line-height);
      font-size: var(--zy-text-font-size);
      padding: var(--zy-font-text-distance-five) 0;
      color: red;
    }
  }

  .user-verify-text + .user-verify-text {
    margin-bottom: var(--zy-distance-three);
  }

  .globalTable {
    width: 100%;
    height: calc(
      100% -
        (
          ((var(--zy-text-font-size) * var(--zy-line-height)) * 2) + (var(--zy-font-text-distance-five) * 4) +
            var(--zy-distance-three)
        )
    );
  }
}
</style>
