<!--
 * @FileDescription: 列表自定义渲染组件
 * @Author: 谢育林
 * @Date: 2022-10-1
 * @LastEditors: 谢育林
 * @LastEditTime: 2022-10-9
 -->
<template>
  <el-table-column
    v-for="item in tableHead"
    :key="item.id"
    :label="item.value"
    :min-width="item.width"
    :label-class-name="item.labelClassName"
    :class-name="item.className"
    :show-overflow-tooltip="item.showOverflowTooltip"
    :fixed="item.fixed"
    :sortable="item.isSort"
    :prop="item.id">
    <template #header v-if="headerSlot[item.key]">
      <slot :name="`header-${item.key}`" :row="item"></slot>
    </template>
    <template #default="scope">
      <template v-if="item.viewType === 'img'">
        <el-image
          :src="imgUrl(item.key, scope.row[item.key])"
          :preview-src-list="[imgUrl(item.key, scope.row[item.key])]"
          preview-teleported
          fit="cover" />
      </template>
      <template v-if="item.viewType === 'link'">
        <el-link type="primary" @click="tableClick(item.clickType, scope.row)">
          {{ item.dictType ? isType(item, scope.row) : scope.row[item.key] }}
        </el-link>
      </template>
      <template v-if="item.viewType === 'date'">{{ format(Number(scope.row[item.key])) }}</template>
      <template v-if="item.viewType === 'YYYY-MM-DD HH:mm'">
        {{ format(Number(scope.row[item.key]), item.viewType) }}
      </template>
      <template v-if="item.viewType === 'YYYY-MM-DD'">
        {{ format(Number(scope.row[item.key]), item.viewType) }}
      </template>
      <template v-if="item.viewType === 'YYYY-MM'">
        {{ format(Number(scope.row[item.key]), item.viewType) }}
      </template>
      <template v-if="item.viewType === 'YYYY'">{{ scope.row[item.key] }}</template>
      <template v-if="item.viewType === 'switch'">
        <el-switch v-model="scope.row[item.key + 'Switch']" @change="tableClick(item.clickType, scope.row)" />
      </template>
      <template v-if="item.viewType === 'isIcon'">
        <el-icon :class="[Number(scope.row[item.key]) ? 'globalTableCheck' : 'globalTableClose']">
          <CircleCheck v-if="Number(scope.row[item.key])" />
          <CircleClose v-if="!Number(scope.row[item.key])" />
        </el-icon>
      </template>
      <template v-if="item.viewType === 'lockIcon'">
        <el-icon :class="[Number(scope.row[item.key]) ? 'globalTableLock' : 'globalTableUnlock']">
          <Lock v-if="Number(scope.row[item.key])" />
          <Unlock v-if="!Number(scope.row[item.key])" />
        </el-icon>
      </template>
      <template v-if="item.viewType === 'sort'">
        <div v-if="sortId !== scope.row.id" @click="handleSort(item.clickType, scope.row)" class="globalTableSortText">
          {{ scope.row.sort }}
        </div>
        <div class="globalTableSortInput" v-if="sortId === scope.row.id">
          <el-input
            v-model="sortInput"
            ref="inputRef"
            @input="sortInput = validNum(sortInput)"
            @blur="handleSortBlur"
            maxlength="10"></el-input>
        </div>
      </template>
      <template v-if="item.viewType === 'slot'">
        <slot :name="item.key" :row="scope.row"></slot>
      </template>
      <template v-if="!item.viewType || item.viewType === 'text'">
        {{ item.dictType ? isType(item, scope.row) : scope.row[item.key] }}
      </template>
    </template>
  </el-table-column>
  <el-table-column v-if="!tableHead.length">
    <template></template>
  </el-table-column>
</template>
<script>
export default { name: 'XylGlobalTable' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, nextTick } from 'vue'
import { format } from 'common/js/time.js'
import { validNum } from 'common/js/utils.js'
const props = defineProps({
  tableHead: { type: Array, default: () => [] },
  headerSlot: { type: Object, default: () => ({}) },
  defaultImgURL: { type: Object, default: () => ({}) },
  noTooltip: { type: Array, default: () => [] }
})
const emit = defineEmits(['tableClick'])
const headerSlot = computed(() => props.headerSlot)
// 过滤拿到定义显示的数据
const tableHead = computed(() => {
  const newTableHead = []
  for (let index = 0; index < props.tableHead.length; index++) {
    const item = props.tableHead[index]
    if (item.isShow) {
      newTableHead.push({
        id: item.id, // 唯一ID
        key: item.javaName, // 数据库返回字段名
        value: item.columnComment, // 页面显示名称
        width: item.showWidth, // 单元格宽度
        fixed: item.isLock ? item.lockType : false, // 靠左靠右锁定
        viewType: item.viewType, // 页面显示类型
        clickType: item.clickType, // 点击事件类型
        dictType: item.dictType, // 字典
        labelClassName: headerSlot.value[item.javaName] ? `table-header-${item.javaName}` : '', // 字典
        className: item.viewType === 'slot' ? `table-slot-${item.javaName}` : className(item.viewType), // 字典
        showOverflowTooltip: !(['img', 'sort'].includes(item.viewType) || props.noTooltip.includes(item.javaName)), // 是否显示tooltip
        isSort: item.isSort ? 'custom' : false // 是否支持数据排序
      })
    }
  }
  return newTableHead
})
// 图片地址拼接组合
const imgUrl = (key, url) =>
  url ? api.fileURL(url) : props.defaultImgURL[key] ? api.defaultImgURL(props.defaultImgURL[key]) : ''
// 判断如何显示
const isType = (item, row) =>
  Object.prototype.toString.call(row[item.key]) === '[object Object]' ? row[item.key]?.label : row[item.key]

// 单元格不同类型展示的class
const className = (type) => {
  var txt = ''
  switch (type) {
    case 'img':
      txt = 'globalTableImg'
      break
    case 'isIcon':
      txt = 'globalTableIcon'
      break
    case 'lockIcon':
      txt = 'globalTableIcon'
      break
    case 'switch':
      txt = 'globalTableSwitch'
      break
    case 'sort':
      txt = 'globalTableSort'
      break
    default:
      break
  }
  return txt
}
const clickType = ref('')
const inputRef = ref()
const sortId = ref('')
const sortInput = ref('')
const handleSort = (type, row) => {
  clickType.value = type
  sortId.value = row.id
  sortInput.value = row.sort
  nextTick(() => {
    inputRef.value[0]?.focus()
  })
}
const handleSortBlur = () => {
  emit('tableClick', clickType.value, { id: sortId.value, sort: sortInput.value })
  clickType.value = ''
  sortId.value = ''
  sortInput.value = ''
}
// 页面显示可以点击单元格的点击事件
const tableClick = (key, row) => {
  emit('tableClick', key, row)
}
</script>
