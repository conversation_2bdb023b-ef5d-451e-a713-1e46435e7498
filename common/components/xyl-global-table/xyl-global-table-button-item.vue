<!--
 * @FileDescription: 权限控制按钮每一行
 * @Author: 谢育林
 * @Date: 2024-4-2
 * @LastEditors: 谢育林
 * @LastEditTime: 2024-4-2
 -->
<template>
  <el-button
    v-for="item in dataOne"
    :key="item.id"
    :disabled="item.disabled"
    @click="handleButton(item.id)"
    type="primary"
    plain>
    {{ item.name }}
  </el-button>
  <el-dropdown v-if="dataTwo.length" @command="handleCommand" trigger="click">
    <el-button type="primary" plain>管理</el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="item in dataTwo" :key="item.id" :disabled="item.disabled" :command="{ id: item.id }">
          {{ item.name }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<script>
export default { name: 'XylGlobalTableButtonItem' }
</script>
<script setup>
import { ref, watch } from 'vue'
const props = defineProps({
  dataRow: { type: Object, default: () => ({}) },
  data: { type: Array, default: () => [] },
  max: { type: Number, default: 1 },
  elWhetherDisabled: Function,
  elWhetherShow: Function
})
const emit = defineEmits(['click'])

const dataOne = ref([])
const dataTwo = ref([])

/**
 * @description: 按钮点击事件
 * @return void
 */
const handleButton = (id) => {
  emit('click', props.dataRow, id)
}
const handleCommand = ({ id }) => {
  emit('click', props.dataRow, id)
}
/**
 * @description: 按钮禁用方法
 * @return true 禁用  /  false 不禁用
 */
const handleElWhetherDisabled = (id, type) => {
  if (!type) return false
  if (typeof props.elWhetherDisabled === 'function') {
    return props.elWhetherDisabled(props.dataRow, id)
  } else {
    return false
  }
}
/**
 * @description: 按钮显示方法
 * @return true 显示  /  false 不显示
 */
const handleElWhetherShow = (id, type) => {
  if (!type) return true
  if (typeof props.elWhetherShow === 'function') {
    return props.elWhetherShow(props.dataRow, id)
  } else {
    return true
  }
}
const initButton = () => {
  var newTableData = []
  for (let index = 0; index < props.data.length; index++) {
    const item = props.data[index]
    if (handleElWhetherShow(item.id, item.whetherShow)) {
      const disabled = handleElWhetherDisabled(item.id, item.whetherDisabled)
      newTableData.push({ id: item.id, name: item.name, disabled: disabled })
    }
  }
  if (newTableData.length === props.max || newTableData.length === props.max + 1) {
    dataOne.value = newTableData
    dataTwo.value = []
  } else {
    dataOne.value = newTableData.slice(0, props.max)
    dataTwo.value = newTableData.slice(props.max)
  }
}
watch(
  [() => props.dataRow, () => props.data],
  () => {
    initButton()
  },
  { immediate: true }
)
</script>
