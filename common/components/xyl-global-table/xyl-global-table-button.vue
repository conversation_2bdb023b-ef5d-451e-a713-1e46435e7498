<!--
 * @FileDescription: 权限控制按钮组件
 * @Author: 谢育林
 * @Date: 2024-4-2
 * @LastEditors: 谢育林
 * @LastEditTime: 2024-4-2
 -->
<template>
  <el-table-column v-if="elWhetherShow" fixed="right" :width="elWidth" :class-name="props.className">
    <template #header>
      {{ buttonList.length ? props.label : '' }}
      <template v-if="editCustomTableHeadShow">
        <div v-has="'table_custom'" class="TableCustomIcon" @click="handleEditCustomTableHead"></div>
      </template>
    </template>
    <template #default="scope">
      <slot :row="scope.row"></slot>
      <xyl-global-table-button-item
        :dataRow="scope.row"
        :data="buttonList"
        :max="props.max"
        :elWhetherShow="props.elWhetherShow"
        :elWhetherDisabled="props.elWhetherDisabled"
        @click="handleClick"></xyl-global-table-button-item>
    </template>
  </el-table-column>
</template>
<script>
export default { name: 'XylGlobalTableButton' }
</script>
<script setup>
import { ref, watch, useSlots, defineAsyncComponent } from 'vue'
import { hasPermission } from '../../js/permissions'
const XylGlobalTableButtonItem = defineAsyncComponent(() => import('./xyl-global-table-button-item.vue'))
const props = defineProps({
  label: { type: String, default: '操作' },
  className: { type: String, default: 'globalTableCustom' },
  data: { type: Array, default: () => [] },
  max: { type: Number, default: 1 },
  editCustomTableHead: Function,
  elWhetherDisabled: Function,
  elWhetherShow: Function
})
const emit = defineEmits(['buttonClick'])

const slots = useSlots()

const elWhetherShow = ref(false)
const editCustomTableHeadShow = ref(false)
const elWidth = ref(0)
const buttonList = ref([])

const initButton = () => {
  var width = 0
  var overallWidth = 0
  var overallButtonList = []
  for (let index = 0; index < props.data.length; index++) {
    const item = props.data[index]
    if (item.has) {
      if (hasPermission(item.has, item.arg)) {
        width += item.width
        overallButtonList.push(item)
        if (index < props.max) {
          overallWidth += item.width
        }
      }
    } else {
      width += item.width
      overallButtonList.push(item)
      if (index < props.max) {
        overallWidth += item.width
      }
    }
  }
  if (width) {
    elWhetherShow.value = true
    if (hasPermission('table_custom') && typeof props.editCustomTableHead === 'function') {
      editCustomTableHeadShow.value = true
    } else {
      editCustomTableHeadShow.value = false
    }
  } else {
    if (hasPermission('table_custom') && typeof props.editCustomTableHead === 'function') {
      overallWidth = 28
      elWhetherShow.value = true
      editCustomTableHeadShow.value = true
    } else if (slots.default) {
      overallWidth = 28
      elWhetherShow.value = true
    } else {
      elWhetherShow.value = false
      editCustomTableHeadShow.value = false
    }
  }
  if (overallButtonList.length === props.max || overallButtonList.length === props.max + 1) {
    elWidth.value = width + 32
  } else {
    elWidth.value = (overallButtonList.length < props.max ? overallWidth : overallWidth + 80) + 32
  }
  buttonList.value = overallButtonList
}
const handleEditCustomTableHead = () => {
  if (typeof props.editCustomTableHead === 'function') {
    props.editCustomTableHead()
  }
}
/**
 * @description: 按钮点击事件
 * @return void
 */
const handleClick = (row, id) => {
  emit('buttonClick', row, id)
}
watch(
  () => props.data,
  () => {
    initButton()
  },
  { immediate: true }
)
</script>
