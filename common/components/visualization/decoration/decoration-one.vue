<template>
  <div class="decoration-one">
    <div class="decoration-one-left">
      <div v-for="item in number" :key="'left' + item" class="dot"></div>
    </div>
    <div class="decoration-one-center">
      <slot></slot>
    </div>
    <div class="decoration-one-right">
      <div v-for="item in number" :key="'right' + item" class="dot"></div>
    </div>
  </div>
</template>
<script>
export default { name: 'DecorationOne' }
</script>
<script setup>
import { computed } from 'vue'
const props = defineProps({ number: { type: Number, default: 9 } })
const number = computed(() => props.number)
</script>
<style lang="scss">
.decoration-one {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .decoration-one-center {
    font-size: 20px;
    font-weight: bold;
    line-height: 1.6;
  }

  .decoration-one-left {
    padding: 0 20px;
    transform: rotateY(180deg);

    .dot {
      display: inline-block;
      width: 9px;
      height: 10px;
      margin-left: 8px;
      transform: skew(-52deg);
      background-color: var(--zy-el-color-primary);
    }
  }

  .decoration-one-right {
    padding: 0 20px;

    .dot {
      display: inline-block;
      width: 9px;
      height: 10px;
      margin-left: 8px;
      transform: skew(-52deg);
      background-color: var(--zy-el-color-primary);
    }
  }

  /*然后设置动画效果*/
  .dot:nth-child(1) {
    animation: blink 1.6s ease-out 0s infinite alternate;
  }

  .dot:nth-child(2) {
    animation: blink 1.6s ease-out 0.05s infinite alternate;
  }

  .dot:nth-child(3) {
    animation: blink 1.6s ease-out 0.1s infinite alternate;
  }

  .dot:nth-child(4) {
    animation: blink 1.6s ease-out 0.15s infinite alternate;
  }

  .dot:nth-child(5) {
    animation: blink 1.6s ease-out 0.2s infinite alternate;
  }

  .dot:nth-child(6) {
    animation: blink 1.6s ease-out 0.25s infinite alternate;
  }

  .dot:nth-child(7) {
    animation: blink 1.6s ease-out 0.3s infinite alternate;
  }

  .dot:nth-child(8) {
    animation: blink 1.6s ease-out 0.35s infinite alternate;
  }

  .dot:nth-child(9) {
    animation: blink 1.6s ease-out 0.4s infinite alternate;
  }

  .dot:nth-child(10) {
    animation: blink 1.6s ease-out 0.45s infinite alternate;
  }

  .dot:nth-child(11) {
    animation: blink 1.6s ease-out 0.5s infinite alternate;
  }

  .dot:nth-child(12) {
    animation: blink 1.6s ease-out 0.55s infinite alternate;
  }

  @keyframes blink {
    0% {
      background-color: var(--zy-el-color-primary-light-9);
    }

    30% {
      background-color: var(--zy-el-color-primary-light-3);
    }

    50% {
      background-color: var(--zy-el-color-primary);
    }

    100% {
      background-color: var(--zy-el-color-primary);
    }
  }
}
</style>
