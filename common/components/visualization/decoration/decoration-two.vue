<template>
  <div class="decoration-two">
    <div class="decoration-two-scan"></div>
    <div class="decoration-two-title">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default { name: 'DecorationTwo' }
</script>
<script setup></script>
<style lang="scss">
.decoration-two {
  position: relative;
  display: inline-block;

  .decoration-two-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 1.6;
    color: #fff;
    padding-left: 20px;
    padding-right: 80px;
    background: linear-gradient(to right, var(--zy-el-color-primary-light-3) 30%, #fff 100%);
  }

  .decoration-two-scan {
    position: absolute;
    overflow: hidden;
    width: 100%;
    height: 100%;

    &::after {
      content: ' ';
      display: block;
      position: absolute;
      top: 0;
      width: 30px;
      height: 100%;
      background: linear-gradient(to right, transparent 0%, var(--zy-el-color-primary) 100%);
      z-index: 10;
      animation: scanning 2s infinite linear;
      opacity: 0.7;
    }
  }

  /* 定义一个从左向右的扫描动画 */
  @keyframes scanning {
    0% {
      left: 0;
      opacity: 0.7;
    }

    90% {
      left: 100%;
      opacity: 0.3;
    }

    100% {
      right: -20px;
      opacity: 0;
    }
  }
}
</style>
