<template>
  <div class="ChartContainer">
    <div :style="bgStyle">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default { name: 'ChartContainer' }
</script>
<script setup>
import { computed, onMounted } from 'vue'
import autofit from 'common/js/autofit.js'
const props = defineProps({ bgUrl: { type: String, default: '' } })
const bgStyle = computed(() =>
  props.bgUrl ? `background: url('${props.bgUrl}') no-repeat;background-size: 100% 100%;` : ''
)
onMounted(() => {
  autofit.init({ el: '.ChartContainer', dw: 1920, dh: 1080 })
})
</script>
<style lang="scss">
.ChartContainer {
  width: 100%;
  height: 100%;

  & > div {
    width: 1920px;
    height: 1080px;
    margin: auto;
  }
}
</style>
