<!--
 * @FileDescription: 该文件的描述信息
 * @Author: 作者信息
 * @Date: 文件创建时间
 * @LastEditors: 最后更新作者
 * @LastEditTime: 最后更新时间
 -->
<template>
  <video class="xyl-video-item" controlslist="nodownload" ref="videoRef" controls></video>
</template>
<script>
export default { name: 'XylVideoItem' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, watch } from 'vue'
import flvjs from 'flv.js'
const props = defineProps({ id: { type: String, default: '' } })
const videoRef = ref()
const flvPlayer = ref()

onMounted(() => {
  if (props.id) {
    createdPlay()
  }
})

// 检测浏览器是否支持 flv.js
const createdPlay = () => {
  if (flvjs.isSupported()) {
    // 创建一个播放器实例
    const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/
    const pathExp = new RegExp(Expression)
    flvPlayer.value = flvjs.createPlayer(
      {
        type: 'mp4', // 媒体类型，默认是 flv
        isLive: true, // 是否是直播流
        hasAudio: true, // 是否有音频
        hanVideo: true, // 是否有视频
        url: pathExp.test(props.id) ? props.id : api.filePreview(props.id)
      },
      { autoCleanupMinBackwardDuration: true }
    )
    flvPlayer.value.attachMediaElement(videoRef.value)
    flvPlayer.value.load()
  }
}
watch(
  () => props.id,
  () => {
    if (props.id) {
      createdPlay()
    }
  }
)
</script>
<style lang="scss">
.xyl-video-item {
  width: 100%;
  height: 100%;
  display: block;
}
</style>
