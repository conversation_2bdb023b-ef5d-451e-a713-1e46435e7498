<template>
  <div class="xyl-upload-video">
    <template v-if="props.max !== 1">
      <div
        class="xyl-upload-video-item"
        :style="{ width: props.width, height: props.height }"
        v-for="item in fileSucceed"
        :key="item.id">
        <xyl-video-item :id="item.id"></xyl-video-item>
        <span class="xyl-upload-video-actions" v-if="!disabled">
          <span class="xyl-upload-video-delete" @click="handleDel(item)">
            <el-icon>
              <CircleClose />
            </el-icon>
          </span>
        </span>
      </div>
      <div
        class="xyl-upload-video-item"
        :style="{ width: props.width, height: props.height }"
        v-for="item in fileArrAy"
        :key="item.uid">
        <el-progress type="circle" :percentage="item.progress" />
      </div>
    </template>
    <template v-if="props.max === 1 && fileObj.uid">
      <div class="xyl-upload-video-item" :style="{ width: props.width, height: props.height }">
        <xyl-video-item :id="fileObj.id" v-if="fileObj.id"></xyl-video-item>
        <el-progress type="circle" v-if="!fileObj.id" :percentage="fileObj.progress" />
        <span class="xyl-upload-video-actions" v-if="fileObj.id && !disabled" @click.stop>
          <span class="xyl-upload-video-delete" @click="handleDel">
            <el-icon>
              <CircleClose />
            </el-icon>
          </span>
        </span>
      </div>
    </template>
    <div
      class="xyl-upload-body"
      :class="{ 'zy-upload-body-disabled': disabled }"
      v-if="(props.max !== 1 || !fileObj.uid) && (!disabled || !fileObj.id || !fileSucceed.length)"
      :style="{ width: props.width, height: props.height }">
      <el-icon>
        <Plus />
      </el-icon>
      <div class="xyl-upload-type">
        <el-upload
          action="/"
          class="xyl-upload"
          :before-upload="handleImg"
          :http-request="imgUpload"
          :show-file-list="false">
          <div class="xyl-upload-type-item">
            <el-icon>
              <upload-filled />
            </el-icon>
            <div class="xyl-upload-type-name">上传视频</div>
          </div>
        </el-upload>
        <div class="xyl-upload-type-item" @click="show = !show">
          <el-icon>
            <Link />
          </el-icon>
          <div class="xyl-upload-type-name">链接获取</div>
        </div>
      </div>
    </div>
    <xyl-popup-window v-model="show" name="链接获取">
      <xyl-video-link @callback="callback"></xyl-video-link>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'XylUploadVideo' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, computed, watch, defineAsyncComponent } from 'vue'
import TcVod from 'vod-js-sdk-v6'
import { ElMessage } from 'element-plus'
const XylVideoItem = defineAsyncComponent(() => import('./xyl-video-item.vue'))
const XylVideoLink = defineAsyncComponent(() => import('./xyl-video-link.vue'))
const props = defineProps({
  max: { type: Number, default: 0 },
  fileId: { type: String, default: '' },
  fileData: { type: Array, default: () => [] },
  fileType: { type: Array, default: () => ['amr', 'mp4', 'avi', 'wav'] },
  width: { type: String, default: '290px' },
  height: { type: String, default: '164px' },
  disabled: { type: Boolean, default: false }
})
const emit = defineEmits(['fileUpload', 'isAllSucceed'])
const getSignature = () => api.tencentVideo({}).then((res) => res.data.signature)
const tcVod = new TcVod({ getSignature: getSignature })
const disabled = computed(() => props.disabled)
const fileObj = ref({})
const fileArrAy = ref([])
const fileSucceed = ref([])
const videoUploadType = ref('')
const show = ref(false)

onMounted(() => {
  globalReadConfig()
})

const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: ['videoUploadType'] })
  videoUploadType.value = data?.videoUploadType || ''
}
const defaultFile = () => {
  if (props.max === 1) {
    if (props.fileId) {
      fileArrAy.value.push({ uid: guid(), id: props.fileId })
      fileObj.value = { uid: guid(), id: props.fileId }
    }
  } else {
    fileSucceed.value = props.fileData.map((item) => ({ uid: guid(), id: item }))
  }
}
const handleImg = (file) => {
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
  const isShow = props.fileType.includes(fileType)
  if (!isShow) {
    ElMessage({ type: 'warning', message: `仅支持${props.fileType.join('、')}格式!` })
  }
  return isShow
}
const imgUpload = (file) => {
  if (props.max && fileSucceed.value.length + fileArrAy.value.length >= props.max) {
    ElMessage({ type: 'warning', message: `最多只能上传${props.max}个文件` })
    return false
  }
  if (videoUploadType.value === 'Tencent') {
    globalOtherUpload(file.file, guid())
  } else {
    const param = new FormData()
    param.append('file', file.file)
    globalUpload(param, guid())
  }
}
const globalUpload = async (params, uid) => {
  if (props.max === 1) {
    fileObj.value = { uid, progress: 0 }
    emit('isAllSucceed', true)
  } else {
    fileArrAy.value.push({ uid, progress: 0 })
    emit('isAllSucceed', !fileArrAy.value.length)
  }
  const res = await api.globalUpload(params, onUploadProgress, uid)
  var { data } = res
  if (props.max === 1) {
    fileObj.value = { ...fileObj.value, ...data }
    emit('fileUpload', fileObj.value)
    emit('isAllSucceed', false)
  } else {
    fileArrAy.value = fileArrAy.value.filter((item) => item.uid !== uid)
    emit('fileUpload', [...fileSucceed.value, { ...data, uid: uid }])
    emit('isAllSucceed', !fileArrAy.value.length)
  }
}
const onUploadProgress = (progressEvent, uid) => {
  if (progressEvent?.event?.lengthComputable) {
    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)
    if (props.max === 1) {
      fileObj.value.progress = parseInt(progress)
    } else {
      fileArrAy.value.forEach((item) => {
        if (item.uid === uid) {
          item.progress = parseInt(progress)
        }
      })
    }
  }
}
const callback = (id) => {
  if (id) {
    if (props.max === 1) {
      fileObj.value = { id, uid: guid(), progress: 100 }
      emit('fileUpload', fileObj.value)
    } else {
      emit('fileUpload', [...fileSucceed.value, { id, uid: guid(), progress: 100 }])
    }
  }
  show.value = false
}
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const globalOtherUpload = async (file, uid) => {
  if (props.max === 1) {
    fileObj.value = { uid, progress: 0 }
    emit('isAllSucceed', true)
  } else {
    fileArrAy.value.push({ uid, progress: 0 })
    emit('isAllSucceed', !fileArrAy.value.length)
  }
  const uploader = tcVod.upload({ mediaFile: file })
  uploader.on('media_progress', (info) => {
    const progress = (info.percent * 100).toFixed(0)
    if (props.max === 1) {
      fileObj.value.progress = parseInt(progress)
    } else {
      fileArrAy.value.forEach((item) => {
        if (item.uid === uid) {
          item.progress = parseInt(progress)
        }
      })
    }
  })
  uploader
    .done()
    .then((res) => {
      if (props.max === 1) {
        fileObj.value = { ...fileObj.value, id: res.video.url }
        emit('fileUpload', fileObj.value)
        emit('isAllSucceed', false)
      } else {
        fileArrAy.value = fileArrAy.value.filter((item) => item.uid !== uid)
        emit('fileUpload', [...fileSucceed.value, { id: res.video.url, uid: uid }])
        emit('isAllSucceed', !fileArrAy.value.length)
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
const handleDel = (file) => {
  if (props.max === 1) {
    emit('fileUpload', {})
  } else {
    emit(
      'fileUpload',
      fileSucceed.value.filter((item) => item.uid !== file.uid)
    )
  }
}
watch(
  () => props.fileId,
  (val) => {
    if (val) {
      defaultFile()
    } else {
      fileObj.value = {}
      fileArrAy.value = []
    }
  },
  { immediate: true }
)
watch(
  () => props.fileData,
  (val) => {
    if (val.length) {
      defaultFile()
    } else {
      fileSucceed.value = []
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.xyl-upload-video {
  display: flex;
  flex-wrap: wrap;

  .xyl-upload-video-item {
    width: 290px;
    height: 164px;
    margin: calc(var(--zy-distance-two) / 2) 0;
    margin-right: var(--zy-distance-two);
    position: relative;
    box-shadow: 0 0 0 1px var(--zy-el-border-color-darker) inset;
    overflow: hidden;

    .zy-el-image {
      width: 100%;
      height: 100%;
    }

    .zy-el-progress {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .xyl-upload-video-actions {
      position: absolute;
      top: 0;
      right: 0;
      display: inline-flex;
      right: -23px;
      top: -8px;
      width: 62px;
      height: 36px;
      justify-content: center;
      align-items: center;
      background: var(--zy-el-color-success);
      transform: rotate(45deg);
      color: #fff;

      span {
        font-size: 18px;
        margin-top: 11px;
        transform: rotate(-45deg);
        cursor: pointer;
      }
    }
  }

  .xyl-upload-body {
    width: 290px;
    height: 164px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: calc(var(--zy-distance-two) / 2) 0;
    margin-right: var(--zy-distance-two);
    border: 1px dashed var(--zy-el-border-color-darker);
    position: relative;

    &:hover {
      border: 0px;

      & > .zy-el-icon {
        display: none;
      }

      .xyl-upload-type {
        display: flex;
      }
    }

    & > .zy-el-icon {
      font-size: 28px;
      color: var(--zy-el-text-color-secondary);
    }

    .xyl-upload-type {
      width: 100%;
      height: 100%;
      position: absolute;
      display: none;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.4);

      .xyl-upload {
        width: 38%;
        height: 100%;
        display: inline-block;

        .zy-el-upload {
          width: 100%;
          height: 100%;

          .xyl-upload-type-item {
            width: 100%;
          }
        }
      }

      .xyl-upload-type-item {
        width: 38%;
        height: 100%;
        color: #fff;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;

        .zy-el-icon {
          font-size: 28px;
        }

        .xyl-upload-type-name {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
        }
      }
    }
  }

  .xyl-upload-body.zy-upload-body-disabled {
    cursor: not-allowed;
    background-color: var(--el-disabled-bg-color);
    border: 1px dashed var(--zy-el-border-color-darker);

    & > .zy-el-icon {
      display: inline-block;
    }

    .xyl-upload-type {
      display: none;
    }
  }
}
</style>
