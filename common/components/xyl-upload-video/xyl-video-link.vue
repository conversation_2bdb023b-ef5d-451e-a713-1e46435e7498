<template>
  <div class="xyl-video-link">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="视频链接" prop="videoLink" class="globalFormTitle">
        <el-input v-model="form.videoLink" placeholder="请输入视频链接" clearable />
      </el-form-item>
      <xyl-video-item :id="form.videoLink"></xyl-video-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">确定</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'XylVideoLink' }
</script>
<script setup>
import { reactive, ref, onMounted, defineAsyncComponent } from 'vue'
const XylVideoItem = defineAsyncComponent(() => import('./xyl-video-item.vue'))
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({ videoLink: '' })
const rules = reactive({ videoLink: [{ required: true, message: '请输入视频链接', trigger: ['blur', 'change'] }] })

onMounted(() => {})

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      emit('callback', form.videoLink)
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.xyl-video-link {
  width: 680px;

  .xyl-video-item {
    width: 290px;
    height: 164px;
    margin-bottom: var(--zy-distance-two);
  }
}
</style>
