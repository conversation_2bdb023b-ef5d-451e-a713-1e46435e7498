<template>
  <div class="xyl-tag" ref="tag">
    <div class="xyl-tag-wrap">
      <div class="xyl-tag-scroll" :style="scrollStyle" ref="scroll">
        <slot></slot>
      </div>
      <div class="xyl-tag-prev" v-if="prevShow" @click="scrollClick('prev')">
        <el-icon>
          <DArrowLeft />
        </el-icon>
      </div>
      <div class="xyl-tag-next" v-if="nextShow" @click="scrollClick('next')">
        <el-icon>
          <DArrowRight />
        </el-icon>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'XylTag' }
</script>
<script setup>
import { ref, onMounted, watch, computed, provide, nextTick } from 'vue'
const props = defineProps({ modelValue: [String, Number], distance: { type: Number, default: 168 } })
const emit = defineEmits(['update:modelValue', 'tagClick'])

onMounted(() => {
  if (tag.value.offsetWidth < scroll.value.offsetWidth) {
    nextShow.value = true
  }
})

watch(
  () => props.modelValue,
  () => {
    tagId.value = props.modelValue
  }
)
const scrollStyle = computed(() => ({ transform: `translateX(${scrollLeft.value}px)` }))

const tag = ref()
const scroll = ref()
const prevShow = ref(false)
const nextShow = ref(false)
const scrollLeft = ref(0)
const tagId = ref(props.modelValue)
const scrollClick = (type) => {
  const left = tag.value.offsetWidth - scroll.value.scrollWidth
  if (type === 'prev') {
    scrollLeft.value = scrollLeft.value + props.distance > 0 ? 0 : scrollLeft.value + props.distance
  } else if (type === 'next') {
    scrollLeft.value = scrollLeft.value - props.distance < left ? left : scrollLeft.value - props.distance
  }
  delay(() => {
    prevShow.value = scrollLeft.value !== 0
    nextShow.value = scrollLeft.value !== left
  }, 520)
}
const obtainActive = (key, id) => {
  emit(key, id)
}
const tagUpdateRef = (el) => {
  nextTick(() => {
    const left = tag.value.offsetWidth - scroll.value.offsetWidth
    const width = tag.value.offsetWidth
    const offset = el.offsetLeft
    if (tag.value.offsetWidth > scroll.value.offsetWidth) {
      scrollLeft.value = 0
      delay(() => {
        prevShow.value = false
        nextShow.value = false
      }, 520)
      return
    }
    if (
      width - (offset + el.offsetWidth) <= scrollLeft.value ||
      width - (offset + el.offsetWidth) - 222 <= scrollLeft.value
    ) {
      var is = width - (offset + el.offsetWidth) - 222
      if (width - (offset + el.offsetWidth) - 222 <= left) {
        is = width - (offset + el.offsetWidth)
      }
      scrollLeft.value = width - (offset + el.offsetWidth) - 222 <= left ? left : is
      delay(() => {
        prevShow.value = scrollLeft.value !== 0
        nextShow.value = scrollLeft.value !== left
      }, 520)
    }
    if (-offset > scrollLeft.value || -offset + 222 > scrollLeft.value) {
      var negative = -offset + 222
      if (-offset + 222 > 0) {
        negative = 0
      }
      scrollLeft.value = -offset > 0 ? 0 : negative
      delay(() => {
        prevShow.value = scrollLeft.value !== 0
        nextShow.value = scrollLeft.value !== left
      }, 520)
    }
  })
}
const delay = (() => {
  let timer = 0
  return (callback, ms) => {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
provide('tagId', tagId)
provide('obtainActive', obtainActive)
provide('tagUpdateRef', tagUpdateRef)
</script>
<style lang="scss">
.xyl-tag {
  width: 100%;
  background-color: #fff;
  position: relative;

  .xyl-tag-wrap {
    width: 100%;
    overflow: hidden;

    .xyl-tag-prev,
    .xyl-tag-next {
      position: absolute;
      top: 0;
      height: 100%;
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      z-index: 9;
    }

    .xyl-tag-prev {
      left: 0;
    }

    .xyl-tag-next {
      right: 0;
    }

    .xyl-tag-scroll {
      white-space: nowrap;
      position: relative;
      transition: transform 0.3s;
      float: left;
      display: block;
      z-index: 3;

      .xyl-tag-item {
        display: inline-block;
        padding: var(--zy-distance-five) 0;

        .xyl-tag-name {
          padding: 0 var(--zy-distance-three);
          line-height: var(--zy-line-height);
          font-size: var(--zy-text-font-size);
          background: var(--zy-el-color-info-light-9);
          cursor: pointer;
          -moz-user-select: none;
          /*火狐*/
          -webkit-user-select: none;
          /*webkit浏览器*/
          -ms-user-select: none;
          /*IE10*/
          -khtml-user-select: none;
          /*早期浏览器*/
          user-select: none;
        }
      }

      .xyl-tag-item + .xyl-tag-item {
        margin-left: var(--zy-distance-four);
      }

      .is-active {
        .xyl-tag-name {
          color: #fff;
          background: var(--zy-el-color-primary);
        }
      }
    }
  }
}
</style>
