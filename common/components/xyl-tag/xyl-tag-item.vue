<template>
  <div :class="['xyl-tag-item', { 'is-active': tagId === value }]" @click="tagClick" ref="XylTagItem">
    <div class="xyl-tag-name">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default { name: 'XylTagItem' }
</script>
<script setup>
import { ref, watch, inject, onMounted } from 'vue'

const props = defineProps({ value: [String, Number] })
watch(
  () => props.value,
  () => {
    value.value = props.value
  }
)
onMounted(() => {
  if (value.value === tagId.value) {
    tagUpdateRef(XylTagItem.value)
  }
})

const XylTagItem = ref()
const value = ref(props.value)
const tagId = inject('tagId')
const obtainActive = inject('obtainActive')
const tagUpdateRef = inject('tagUpdateRef')
const tagClick = () => {
  tagUpdateRef(XylTagItem.value)
  obtainActive('update:modelValue', value.value)
  obtainActive('tagClick', value.value)
}
</script>
