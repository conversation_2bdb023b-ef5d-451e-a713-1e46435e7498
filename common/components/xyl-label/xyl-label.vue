<template>
  <div class="xyl-label" ref="label">
    <div class="xyl-label-wrap">
      <div class="xyl-label-scroll" :style="scrollStyle" ref="scroll">
        <slot></slot>
      </div>
      <div class="xyl-label-prev" v-if="prevShow" @click="scrollClick('prev')">
        <el-icon>
          <DArrowLeft />
        </el-icon>
      </div>
      <div class="xyl-label-next" v-if="nextShow" @click="scrollClick('next')">
        <el-icon>
          <DArrowRight />
        </el-icon>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'XylLabel' }
</script>
<script setup>
import { ref, onMounted, watch, computed, provide, nextTick } from 'vue'
const props = defineProps({ modelValue: [String, Number], distance: { type: Number, default: 168 } })
const emit = defineEmits(['update:modelValue', 'labelClick'])

onMounted(() => {
  if (label.value.offsetWidth < scroll.value.offsetWidth) {
    nextShow.value = true
  }
})

watch(
  () => props.modelValue,
  () => {
    labelId.value = props.modelValue
  }
)
const scrollStyle = computed(() => ({ transform: `translateX(${scrollLeft.value}px)` }))

const label = ref()
const scroll = ref()
const prevShow = ref(false)
const nextShow = ref(false)
const scrollLeft = ref(0)
const labelId = ref(props.modelValue)
const scrollClick = (type) => {
  const left = label.value.offsetWidth - scroll.value.scrollWidth
  if (type === 'prev') {
    scrollLeft.value = scrollLeft.value + props.distance > 0 ? 0 : scrollLeft.value + props.distance
  } else if (type === 'next') {
    scrollLeft.value = scrollLeft.value - props.distance < left ? left : scrollLeft.value - props.distance
  }
  delay(() => {
    prevShow.value = scrollLeft.value !== 0
    nextShow.value = scrollLeft.value !== left
  }, 520)
}
const obtainActive = (key, id) => {
  emit(key, id)
}
const labelUpdateRef = (el) => {
  nextTick(() => {
    const left = label.value.offsetWidth - scroll.value.offsetWidth
    const width = label.value.offsetWidth
    const offset = el.offsetLeft
    if (label.value.offsetWidth > scroll.value.offsetWidth) {
      scrollLeft.value = 0
      delay(() => {
        prevShow.value = false
        nextShow.value = false
      }, 520)
      return
    }
    if (
      width - (offset + el.offsetWidth) <= scrollLeft.value ||
      width - (offset + el.offsetWidth) - 222 <= scrollLeft.value
    ) {
      var is = width - (offset + el.offsetWidth) - 222
      if (width - (offset + el.offsetWidth) - 222 <= left) {
        is = width - (offset + el.offsetWidth)
      }
      scrollLeft.value = width - (offset + el.offsetWidth) - 222 <= left ? left : is
      delay(() => {
        prevShow.value = scrollLeft.value !== 0
        nextShow.value = scrollLeft.value !== left
      }, 520)
    }
    if (-offset > scrollLeft.value || -offset + 222 > scrollLeft.value) {
      var negative = -offset + 222
      if (-offset + 222 > 0) {
        negative = 0
      }
      scrollLeft.value = -offset > 0 ? 0 : negative
      delay(() => {
        prevShow.value = scrollLeft.value !== 0
        nextShow.value = scrollLeft.value !== left
      }, 520)
    }
  })
}
const delay = (() => {
  let timer = 0
  return (callback, ms) => {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
provide('labelId', labelId)
provide('obtainActive', obtainActive)
provide('labelUpdateRef', labelUpdateRef)
</script>
<style lang="scss">
.xyl-label {
  width: 100%;
  background-color: #fff;
  position: relative;

  .xyl-label-wrap {
    width: 100%;
    overflow: hidden;

    .xyl-label-prev,
    .xyl-label-next {
      position: absolute;
      top: 0;
      height: 100%;
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      z-index: 9;
    }

    .xyl-label-prev {
      left: 0;
    }

    .xyl-label-next {
      right: 0;
    }

    .xyl-label-scroll {
      white-space: nowrap;
      position: relative;
      transition: transform 0.3s;
      float: left;
      display: block;
      z-index: 3;

      .xyl-label-item {
        display: inline-block;
        padding: var(--zy-distance-four) 0;

        .xyl-label-name {
          display: flex;
          align-items: center;
          padding: 0 var(--zy-distance-two);
          height: var(--zy-height);
          font-size: var(--zy-text-font-size);
          cursor: pointer;
          border: 1px solid var(--zy-el-color-primary);
          border-radius: var(--el-border-radius-base);
          color: var(--zy-el-color-primary);
          -moz-user-select: none;
          /*火狐*/
          -webkit-user-select: none;
          /*webkit浏览器*/
          -ms-user-select: none;
          /*IE10*/
          -khtml-user-select: none;
          /*早期浏览器*/
          user-select: none;
        }
      }

      .xyl-label-item + .xyl-label-item {
        margin-left: var(--zy-distance-two);
      }

      .is-active {
        .xyl-label-name {
          font-size: var(--zy-text-font-size);
          color: #fff;
          font-weight: bold;
          background: var(--zy-el-color-primary);
          position: relative;

          &::after {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border-top: 10px solid var(--zy-el-color-primary);
            border-right: 10px solid transparent;
            border-left: 10px solid transparent;
            border-bottom: 10px solid transparent;
          }
        }
      }
    }
  }
}
</style>
