<template>
  <div :class="['xyl-label-item', { 'is-active': labelId === value }]" @click="labelClick" ref="XylLabelItem">
    <div class="xyl-label-name">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default { name: 'XylLabelItem' }
</script>
<script setup>
import { ref, watch, inject, onMounted } from 'vue'

const props = defineProps({ value: [String, Number] })
watch(
  () => props.value,
  () => {
    value.value = props.value
  }
)
onMounted(() => {
  if (value.value === labelId.value) {
    labelUpdateRef(XylLabelItem.value)
  }
})

const XylLabelItem = ref()
const value = ref(props.value)
const labelId = inject('labelId')
const obtainActive = inject('obtainActive')
const labelUpdateRef = inject('labelUpdateRef')
const labelClick = () => {
  if (value.value === labelId.value) return
  labelUpdateRef(XylLabelItem.value)
  obtainActive('update:modelValue', value.value)
  obtainActive('labelClick', value.value)
}
</script>
