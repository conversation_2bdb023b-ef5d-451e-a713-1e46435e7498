<template>
  <div class="ZztCheckboxSelect-scrollbar">
    <el-checkbox-group v-model="chooseIdList" @change="chooseChange" class="ZztCheckboxItemCheck">
      <div
        v-for="(collapseItem, index) in modelValue"
        :key="index"
        :title="collapseItem[props.props.name]"
        :name="collapseItem[props.props.name]"
        class="ZztCheckboxItem">
        <div class="ZztCheckboxItemTitle">{{ collapseItem[props.props.name] }} ：</div>
        <div class="ZztCheckboxItemCheckbox">
          <el-checkbox
            class="ZztCheckboxItemBody"
            v-for="item in collapseItem[props.props.list]"
            :key="item[props.nodeId]"
            :label="item[props.nodeId]"
            :title="item.name">
            {{ item.name }}
          </el-checkbox>
        </div>
      </div>
    </el-checkbox-group>
  </div>
</template>

<script>
export default { name: 'ZztCheckboxSelect' }
</script>

<script setup>
import { ref, watch, computed } from 'vue'
import { collapseChoose } from '@/assets/js/utils'
const props = defineProps({
  modelValue: { type: Array, default: () => [] },
  nodeId: { type: String, default: 'id' },
  props: {
    type: Object,
    default: () => {
      return { name: 'name', list: 'list' }
    }
  }
})
const allList = ref([])
const chooseIdList = computed(() => collapseChoose.value.map((v) => v[props.nodeId]))

watch(
  () => props.modelValue.length,
  (val) => {
    props.modelValue.forEach((item) => {
      item.list.forEach((itemList) => {
        allList.value.push(itemList)
      })
    })
  }
)

const chooseChange = (data) => {
  collapseChoose.value = allList.value.filter((v) => data.includes(v.id))
}
</script>

<style lang="scss">
.ZztCheckboxSelect-scrollbar {
  width: 100%;
  height: 100%;
  border-top: 0;

  .ZztCheckboxItem {
    width: 100%;
    display: flex;
  }

  .ZztCheckboxItemTitle {
    width: 100px;
    height: var(--zy-height);
    display: flex;
    align-items: center;
    font-size: var(--zy-text-font-size);
  }

  .ZztCheckboxItemCheckbox {
    width: calc(100% - 100px);
  }

  .ZztCheckboxItemBody {
    padding: 10px 20px 10px 0;
    font-weight: 400;
    cursor: pointer;
    font-size: var(--zy-text-font-size);
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
  }
}
</style>
