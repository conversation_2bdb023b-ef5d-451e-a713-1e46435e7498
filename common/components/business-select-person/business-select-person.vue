<template>
  <div class="business-select-person">
    <div class="business-select-person-button">
      <div class="business-select-person-button-left">
        <el-button type="primary" @click="show = !show" :disabled="props.disabled">选择人员</el-button>
        <el-button type="primary" @click="isShow = !isShow" :disabled="props.disabled">Excel导入匹配系统人员</el-button>
      </div>
      <el-input v-model="keyWord" placeholder="请输入人员姓名" clearable />
    </div>
    <el-scrollbar class="business-select-person-body">
      <el-empty :image-size="120" description="暂未选中人员" v-if="!userData.length" />
      <div class="business-select-person-user" v-for="item in userData" :key="item.id">
        <div
          class="business-select-person-text row1"
          :class="row.class"
          v-for="row in userList"
          :key="row.key"
          :title="row.key === 'mobile' ? handleMobile(item[row.key]) : item[row.key]">
          {{ row.key === 'mobile' ? handleMobile(item[row.key]) : item[row.key] }}
        </div>
        <div class="business-select-person-del" @click="userDel(item)" v-if="!props.disabled">
          <el-icon>
            <Delete />
          </el-icon>
        </div>
      </div>
    </el-scrollbar>
    <div class="business-select-person-pagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="show" name="选择人员">
      <select-person
        :tabCode="props.tabCode"
        :userData="userDataArr"
        :filterUser="props.filterUser"
        :params="props.params"
        :urlParams="props.urlParams"
        :dataMethod="props.dataMethod"
        @callback="callback"></select-person>
    </xyl-popup-window>
    <xyl-popup-window v-model="isShow" name="Excel导入匹配系统人员">
      <import-excel-select-person @callback="handleImportCallback"></import-excel-select-person>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'BusinessSelectPerson' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, watch, defineAsyncComponent } from 'vue'
import { encryptPhone } from 'common/js/utils.js'
import { systemMobileEncrypt } from 'common/js/system_var.js'
const ImportExcelSelectPerson = defineAsyncComponent(() =>
  import('../import-excel-select-person/import-excel-select-person.vue')
)
const props = defineProps({
  soleKey: { type: String, default: '' },
  modelValue: { type: Array, default: () => [] },
  userData: { type: Array, default: () => [] },
  userList: {
    type: Array,
    default: () => [
      { key: 'userName', class: 'row1' },
      { key: 'mobile', class: 'row2' },
      { key: 'position', class: 'row4' }
    ]
  },
  filterUser: { type: Array, default: () => [] },
  disabled: { type: Boolean, default: false },
  tabCode: { type: Array, default: () => ['userOffice'] },
  params: { type: Object, default: () => ({}) },
  urlParams: { type: Object, default: () => ({}) },
  dataMethod: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['update:modelValue', 'callback'])
const keyWord = ref('')
const userList = computed(() => props.userList)
const pageNo = ref(1)
const pageSize = ref(10)
const show = ref(false)
const isShow = ref(false)
const userId = ref(props.modelValue)
const userData = ref([])
const userDataArr = ref([])
const userDataArrFilter = computed(() => {
  return userDataArr.value.filter((item) => item.userName.includes(keyWord.value))
})

const totals = computed(() => userDataArrFilter.value.length)
const handleMobile = (mobile) => (systemMobileEncrypt.value ? encryptPhone(mobile) : mobile)

const getSelectUser = async () => {
  if (!props.modelValue.length) {
    userDataArr.value = []
    handleQuery()
    emit('callback', [])
    return
  }
  const res = await api.getSelectUser({ existsUserIds: props.modelValue, key: props.soleKey })
  var { data } = res
  userDataArr.value = data
  for (let index = 0; index < props.userData.length; index++) {
    const item = props.userData[index]
    if (!userDataArr.value.map((v) => v.id).includes(item.id)) {
      userDataArr.value.push(item)
    }
  }
  handleQuery()
  emit('callback', userDataArr.value)
}
const handleQuery = () => {
  userData.value = userDataArrFilter.value.slice(pageSize.value * (pageNo.value - 1), pageSize.value * pageNo.value)
}
const callback = (data) => {
  show.value = false
  if (data) {
    userDataArr.value = data
    handleUserId()
    handleQuery()
  }
}
const userDel = (row) => {
  userDataArr.value = userDataArr.value.filter((item) => item.id !== row.id)
  handleUserId()
  handleQuery()
}
const handleUserId = () => {
  emit(
    'update:modelValue',
    userDataArr.value.map((v) => v.id)
  )
  emit('callback', userDataArr.value)
}
const handleImportCallback = (data, type) => {
  if (type) {
    if (!data.length) return
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      if (!userId.value.includes(item.id)) {
        userDataArr.value.push(item)
      }
    }
    handleUserId()
    handleQuery()
  } else {
    isShow.value = false
  }
}
watch(
  () => keyWord.value,
  () => {
    pageNo.value = 1
    handleQuery()
  },
  { immediate: true }
)
watch(
  () => props.modelValue,
  () => {
    userId.value = props.modelValue
    if (props.modelValue.length !== userDataArr.value.length) {
      getSelectUser()
    }
  },
  { immediate: true }
)
watch(
  () => props.userData,
  () => {
    for (let index = 0; index < props.userData.length; index++) {
      const item = props.userData[index]
      if (!userDataArr.value.map((v) => v.id).includes(item.id)) {
        userDataArr.value.push(item)
      }
    }
    handleQuery()
    emit('callback', userDataArr.value)
  },
  { immediate: true }
)
</script>
<style lang="scss">
.business-select-person {
  width: 100%;
  border: 1px solid var(--zy-el-border-color-lighter);
  border-radius: var(--el-border-radius-base);

  .business-select-person-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: normal;
    padding: var(--zy-distance-five) var(--zy-distance-two);
    border-bottom: 1px solid var(--zy-el-border-color-lighter);
    .business-select-person-button-left {
      display: flex;
      align-items: center;
    }
    .zy-el-button {
      height: var(--zy-height-secondary);
    }
    .zy-el-input {
      width: 220px;
      height: var(--zy-height-routine);
    }
  }

  .business-select-person-body {
    width: 100%;
    height: 288px;

    .business-select-person-user {
      display: flex;
      justify-content: space-between;
      padding: var(--zy-distance-four) 0;
      border-bottom: 1px solid var(--zy-el-border-color-lighter);

      .business-select-person-text {
        padding-left: var(--zy-distance-two);
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .row1 {
        flex: 1;
      }

      .row2 {
        flex: 2;
      }

      .row3 {
        flex: 3;
      }

      .row4 {
        flex: 4;
      }

      .business-select-person-del {
        width: 60px;
        display: flex;
        align-items: center;
        cursor: pointer;
        padding-left: var(--zy-distance-two);
      }
    }
  }

  .business-select-person-pagination {
    width: 100%;
    height: 42px;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid var(--zy-el-border-color-lighter);
    padding-right: var(--zy-distance-two);

    .zy-el-pagination {
      --zy-height: var(--zy-height-routine);
      --zy-el-component-size: var(--zy-height);

      .zy-el-select {
        width: 128px !important;
      }
    }
  }
}
</style>
