import axios from 'axios'
import qs from 'qs'
import config from '../config'
import router from '@/router'
import store from '@/store'
import md5 from 'crypto-js/md5'
import utils from 'common/js/utils.js'
import { MicroGlobal } from '../config/qiankun'
import { globalLocation } from 'common/config/location'
import { globalReadOpenConfig } from 'common/js/GlobalMethod'
import GlobalSystemVerify from 'common/components/global-system-verify'
import { ElMessage } from 'element-plus'
export const baseURL = config.API_URL
export const stringify = (data) => qs.stringify(data) // 序列化
export class HTTP {
  pending = {}
  CancelToken = axios.CancelToken
  getMd5String = (config) => {
    if (Object.prototype.toString.call(config.data) === '[object FormData]') {
      let objData = {}
      config.data.forEach((curValue, index) => {
        objData[index] = curValue
      })
      return md5(
        `${config.url}&${config.method}&${JSON.stringify(objData)}&${JSON.stringify(config.params)}`
      ).toString()
    }
    if (typeof config.data === 'string' && config.data.constructor === String) {
      return md5(`${config.url}&${config.method}&${config.data}&${JSON.stringify(config.params)}`).toString()
    } else {
      return md5(
        `${config.url}&${config.method}&${JSON.stringify(config.data)}&${JSON.stringify(config.params)}`
      ).toString()
    }
  }

  type = (obj) => {
    var toString = Object.prototype.toString
    var map = {
      '[object Boolean]': 'boolean',
      '[object Number]': 'number',
      '[object String]': 'string',
      '[object Function]': 'function',
      '[object Array]': 'array',
      '[object Date]': 'date',
      '[object RegExp]': 'regExp',
      '[object Undefined]': 'undefined',
      '[object Null]': 'null',
      '[object Object]': 'object'
    }
    return map[toString.call(obj)]
  }
  deepCopy = (data) => {
    var t = this.type(data)
    var o
    var i
    var ni
    if (t === 'array') {
      o = []
    } else if (t === 'object') {
      o = {}
    } else if (t === 'string') {
      return data.replace(/(^\s*)|(\s*$)/g, '')
    } else {
      return data
    }
    if (t === 'array') {
      for (i = 0, ni = data.length; i < ni; i++) {
        o.push(this.deepCopy(data[i]))
      }
      return o
    } else if (t === 'object') {
      for (i in data) {
        o[i] = this.deepCopy(data[i])
      }
      return o
    }
  }
  constructor() {
    this.instance = axios.create({
      baseURL: config.API_URL,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
      timeout: 180000
    })
    this.interceptors()
  }

  interceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        if (config.method === 'post') {
          if (Object.prototype.toString.call(config.data) !== '[object FormData]') {
            // eslint-disable-line
            if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {
              config.data = stringify(config.data)
            }
          }
        }
        // 通过请求url、method、params、data字段生成Sm3值
        const key = this.getMd5String(config)
        config.cancelToken = new this.CancelToken((cancel) => {
          if (this.pending[key]) {
            // 上次接口未返回时走此逻辑
            if (Date.now() - this.pending[key] > 5000) {
              // 超过5s，删除对应的请求记录，重新发起请求,即使未返回
              delete this.pending[key]
            } else {
              // 同一请求未返回,5s内再次发送,取消发送
              cancel('repeated:' + config.url)
            }
          }
        })
        // 记录当前的请求，已存在则更新时间戳
        this.pending[key] = Date.now()
        if (config.noVerify) {
          return config
        }
        const currentRoute = router.currentRoute.value
        const token = sessionStorage.getItem('token') || 'basic enlzb2Z0Onp5c29mdCo2MDc5' // 用户token
        const microAppToken = sessionStorage.getItem('microAppToken') || 'basic YW5vbnltb3VzOmFub255bW91cyo2MDc5' // 用户token
        const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区
        config.headers['u-login-areaId'] = config.areaId || AreaId
        if (currentRoute.meta?.moduleName === 'PUBLIC' && config.terminal !== 'PC') {
          config.headers.Authorization = microAppToken
          config.headers['u-terminal'] = 'PUBLIC'
          config.headers['u-menu'] = encodeURIComponent('公开页面')
        } else {
          const has = sessionStorage.getItem('has') || ''
          const hasMenu = has ? JSON.parse(has) : {}
          config.headers.Authorization = config.token || token
          config.headers['u-terminal'] = config.terminal || 'PC'
          if (currentRoute.name || window.__POWERED_BY_QIANKUN__) {
            if (currentRoute.meta?.moduleName === 'verify' || currentRoute.name === 'LoginView') {
              config.headers['u-menu'] = encodeURIComponent('登录')
            } else {
              config.headers['u-menu'] = encodeURIComponent(hasMenu?.name || '')
            }
          } else {
            config.headers['u-menu'] = encodeURIComponent('登录')
          }
        }
        config.headers['u-system-name'] = store.getters.getReadOpenConfig?.systemPlatform
        return config
      },
      (error) => {
        Promise.reject(error)
      }
    )
    // 响应拦截器
    this.instance.interceptors.response.use(
      (result) => {
        let res = result
        if (result.data?.isEncrypt && result.data?.data) {
          // const resultData = JSON.parse(utils.decrypt(result.data.data, new Date().getTime(), '1'))
          const resultData = JSON.parse(utils.gm_decrypt(result.data.data))
          res.data.data = resultData
        }
        const key = this.getMd5String(res.config)
        if (this.pending[key]) {
          // 请求结束，删除对应的请求记录
          delete this.pending[key]
        }
        if (res.config?.noVerify) {
          return Promise.resolve(res.data)
        }
        if (res.data instanceof Blob) {
          return Promise.resolve(res.data)
        }
        if (res.data instanceof ArrayBuffer) {
          return Promise.resolve(res.data)
        }
        if (res.data?.code === 200 || res.data?.status === 200) {
          return Promise.resolve(res.data)
        } else if (res.data.code === 999) {
          GlobalSystemVerify(() => {
            window.location.reload(true)
            // window.location.reload(window.location.href)
          })
          return Promise.resolve(res.data)
        } else if (res.data?.code === 403) {
          if (window.__POWERED_BY_QIANKUN__) {
            const openRoute = new MicroGlobal()
            openRoute.setGlobalState({ code: 403 })
            return Promise.reject(res.data)
          } else {
            router.push({ path: '/NoneAccessAuthority' })
            return Promise.reject(res.data)
          }
        } else if (res.data?.code === 302) {
          if (window.__POWERED_BY_QIANKUN__) {
            ElMessage.error(res.data?.message)
            sessionStorage.clear()
            window.open(globalLocation(), '_self')
            return Promise.reject(res.data)
          } else {
            ElMessage.error(res.data?.message)
            sessionStorage.clear()
            if (window.__PUBLIC__) {
              const currentRoute = router.currentRoute.value
              if (currentRoute.meta?.moduleName === 'PUBLIC') {
                const PUBLICPATH = sessionStorage.getItem('public_path') || ''
                if (PUBLICPATH) {
                  const PUBLICQUERY = sessionStorage.getItem('public_query') || {}
                  router.push({ path: PUBLICPATH, query: PUBLICQUERY })
                } else {
                  window.location.reload(true)
                  // window.location.reload(window.location.href)
                }
              } else {
                const goal_login_router_path = localStorage.getItem('goal_login_router_path')
                if (goal_login_router_path) {
                  const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''
                  const queryObject = goal_login_router_query ? JSON.parse(goal_login_router_query) : {}
                  let queryString = ''
                  if (Object.keys(queryObject).length) {
                    for (const key in queryObject) {
                      if (Object.prototype.hasOwnProperty.call(queryObject, key)) {
                        queryString += `${key}=${queryObject[key]}&`
                      }
                    }
                    queryString = `?${queryString}`
                  }
                  const urlString = `${globalLocation()}${goal_login_router_path}${queryString}`
                  window.open(urlString.replace(/(?<!:)\/\/+/g, '/'), '_self')
                } else {
                  window.open(globalLocation(), '_self')
                }
              }
            } else {
              const goal_login_router_path = localStorage.getItem('goal_login_router_path')
              if (goal_login_router_path) {
                const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''
                router.push({
                  path: goal_login_router_path,
                  query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}
                })
              } else {
                router.push({ path: '/LoginView' })
              }
            }
            store.commit('setState')
            globalReadOpenConfig()
            return Promise.reject(res.data)
          }
        } else {
          if (!res.config?.noErrorTip) {
            ElMessage.error(res.data?.message)
          }
          return Promise.reject(res.data)
        }
      },
      (error) => {
        if (error.code !== 'ERR_CANCELED') {
          if (error.code === 'ECONNABORTED') {
            ElMessage.error('哎呀，不好意思，系统压力有点大，请稍后再试吧。')
          } else {
            if (!error.config?.noErrorTip) {
              errTip(error)
            }
          }
        }
        return Promise.reject(error)
      }
    )
  }
  get(url, params = {}, config = {}) {
    return this.instance({ method: 'get', url, params: this.deepCopy(params), ...config })
  }
  post(url, data = {}, config = {}) {
    return this.instance({ method: 'post', url, data: data, ...config })
  }
  putJson(url, data = {}, config = {}) {
    return this.instance({
      method: 'put',
      url,
      data: this.deepCopy(data),
      headers: { 'Content-Type': 'application/json;charset=UTF-8' },
      ...config
    })
  }
  json(url, data = {}, config = {}) {
    return this.instance({
      method: 'post',
      url,
      data: this.deepCopy(data),
      headers: { 'Content-Type': 'application/json;charset=UTF-8' },
      ...config
    })
  }
  delete(url, data = {}, config = {}) {
    return this.instance({
      method: 'delete',
      url,
      data,
      headers: { 'Content-Type': 'application/json;charset=UTF-8' },
      ...config
    })
  }
  fileUpload(url, data = {}, callback, id, config = {}) {
    return this.instance({
      url,
      data: data,
      method: 'post',
      timeout: 600000000,
      headers: { 'Access-Control-Allow-Origin': '*' },
      ...config,
      onUploadProgress: (progressEvent) => {
        callback(progressEvent, id)
      }
    })
  }
  fileDownload(url, data = {}, responseType = 'blob', config = {}) {
    return this.instance({
      url,
      data,
      method: 'post',
      timeout: 600000000,
      responseType,
      headers: { 'Content-Type': 'application/json;charset=UTF-8' },
      ...config
    })
  }
  fileUploadDownload(url, data = {}, responseType = 'blob', config = {}) {
    return this.instance({
      url,
      data: data,
      method: 'post',
      timeout: 600000000,
      responseType,
      headers: { 'Access-Control-Allow-Origin': '*' },
      ...config
    })
  }
}

function errTip(error, msg = '用户您好，系统正在更新，请稍后再试。') {
  const tip = {
    400: '请求错误',
    401: '未授权，请登录',
    403: '拒绝访问',
    404: `请求地址出错${error?.response?.config?.url}`,
    405: `请求方式不允许`,
    408: '请求超时',
    500: '服务器内部错误',
    501: '服务未实现',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时',
    505: 'HTTP版本不受支持'
  }
  ElMessage.error(tip[error?.response?.status] || msg)
}

export default new HTTP()
