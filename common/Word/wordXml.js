import { ElMessage } from 'element-plus'
class WordXml {
  constructor() {
    this.imgData = []
    this.colorObj = {
      white: '#FFFFFF',
      ivory: '#FFFFF0',
      lightyellow: '#FFFFE0',
      yellow: '#FFFF00',
      snow: '#FFFAFA',
      floralwhite: '#FFFAF0',
      lemonchiffon: '#FFFACD',
      cornsilk: '#FFF8DC',
      seashell: '#FFF5EE',
      lavenderblush: '#FFF0F5',
      papayawhip: '#FFEFD5',
      blanchedalmond: '#FFEBCD',
      mistyrose: '#FFE4E1',
      bisque: '#FFE4C4',
      moccasin: '#FFE4B5',
      navajowhite: '#FFDEAD',
      peachpuff: '#FFDAB9',
      gold: '#FFD700',
      pink: '#FFC0CB',
      lightpink: '#FFB6C1',
      orange: '#FFA500',
      lightsalmon: '#FFA07A',
      darkorange: '#FF8C00',
      coral: '#FF7F50',
      hotpink: '#FF69B4',
      tomato: '#FF6347',
      orangered: '#FF4500',
      deeppink: '#FF1493',
      fuchsia: '#FF00FF',
      magenta: '#FF00FF',
      red: '#FF0000',
      oldlace: '#FDF5E6',
      lightgoldenrodyellow: '#FAFAD2',
      linen: '#FAF0E6',
      antiquewhite: '#FAEBD7',
      salmon: '#FA8072',
      ghostwhite: '#F8F8FF',
      mintcream: '#F5FFFA',
      whitesmoke: '#F5F5F5',
      beige: '#F5F5DC',
      wheat: '#F5DEB3',
      sandybrown: '#F4A460',
      azure: '#F0FFFF',
      honeydew: '#F0FFF0',
      aliceblue: '#F0F8FF',
      khaki: '#F0E68C',
      lightcoral: '#F08080',
      palegoldenrod: '#EEE8AA',
      violet: '#EE82EE',
      darksalmon: '#E9967A',
      lavender: '#E6E6FA',
      lightcyan: '#E0FFFF',
      burlywood: '#DEB887',
      plum: '#DDA0DD',
      gainsboro: '#DCDCDC',
      crimson: '#DC143C',
      palevioletred: '#DB7093',
      goldenrod: '#DAA520',
      orchid: '#DA70D6',
      thistle: '#D8BFD8',
      lightgray: '#D3D3D3',
      lightgrey: '#D3D3D3',
      tan: '#D2B48C',
      chocolate: '#D2691E',
      peru: '#CD853F',
      indianred: '#CD5C5C',
      mediumvioletred: '#C71585',
      silver: '#C0C0C0',
      darkkhaki: '#BDB76B',
      rosybrown: '#BC8F8F',
      mediumorchid: '#BA55D3',
      darkgoldenrod: '#B8860B',
      firebrick: '#B22222',
      powderblue: '#B0E0E6',
      lightsteelblue: '#B0C4DE',
      paleturquoise: '#AFEEEE',
      greenyellow: '#ADFF2F',
      lightblue: '#ADD8E6',
      darkgray: '#A9A9A9',
      darkgrey: '#A9A9A9',
      brown: '#A52A2A',
      sienna: '#A0522D',
      darkorchid: '#9932CC',
      palegreen: '#98FB98',
      darkviolet: '#9400D3',
      mediumpurple: '#9370DB',
      lightgreen: '#90EE90',
      darkseagreen: '#8FBC8F',
      saddlebrown: '#8B4513',
      darkmagenta: '#8B008B',
      darkred: '#8B0000',
      blueviolet: '#8A2BE2',
      lightskyblue: '#87CEFA',
      skyblue: '#87CEEB',
      gray: '#808080',
      grey: '#808080',
      olive: '#808000',
      purple: '#800080',
      maroon: '#800000',
      aquamarine: '#7FFFD4',
      chartreuse: '#7FFF00',
      lawngreen: '#7CFC00',
      mediumslateblue: '#7B68EE',
      lightslategray: '#778899',
      lightslategrey: '#778899',
      slategray: '#708090',
      slategrey: '#708090',
      olivedrab: '#6B8E23',
      slateblue: '#6A5ACD',
      dimgray: '#696969',
      dimgrey: '#696969',
      mediumaquamarine: '#66CDAA',
      cornflowerblue: '#6495ED',
      cadetblue: '#5F9EA0',
      darkolivegreen: '#556B2F',
      indigo: '#4B0082',
      mediumturquoise: '#48D1CC',
      darkslateblue: '#483D8B',
      steelblue: '#4682B4',
      royalblue: '#4169E1',
      turquoise: '#40E0D0',
      mediumseagreen: '#3CB371',
      limegreen: '#32CD32',
      darkslategray: '#2F4F4F',
      darkslategrey: '#2F4F4F',
      seagreen: '#2E8B57',
      forestgreen: '#228B22',
      lightseagreen: '#20B2AA',
      dodgerblue: '#1E90FF',
      midnightblue: '#191970',
      aqua: '#00FFFF',
      cyan: '#00FFFF',
      springgreen: '#00FF7F',
      lime: '#00FF00',
      mediumspringgreen: '#00FA9A',
      darkturquoise: '#00CED1',
      deepskyblue: '#00BFFF',
      darkcyan: '#008B8B',
      teal: '#008080',
      green: '#008000',
      darkgreen: '#006400',
      blue: '#0000FF',
      mediumblue: '#0000CD',
      darkblue: '#00008B',
      navy: '#000080',
      black: '#000000'
    }
  }

  func(str) {
    var reg = /<|>/g
    str = str.replace(reg, function ($1) {
      if ($1 === '<') {
        return '&lt;'
      } else {
        return '&gt;'
      }
    })
    return str
  }

  initData(html) {
    this.imgData = []
    return new Promise((resolve, reject) => {
      var obj = document.createElement('div')
      obj.innerHTML = html
      var img = obj.querySelectorAll('img')
      this.getBase64Image(img).then((res) => {
        var xml = this.xmlData(obj.children)
        var data = []
        for (let index = 0; index < this.imgData.length; index++) {
          const item = this.imgData[index]
          for (let i = 0; i < res.length; i++) {
            const row = res[i]
            if (item.url === row.url) {
              data.push({ id: item.id, name: item.name, file: row.file })
              var width = '9525width' + item.id
              var height = '9525height' + item.id
              xml = xml.replace(new RegExp(width, 'g'), 9525 * row.width + '')
              xml = xml.replace(new RegExp(height, 'g'), 9525 * row.height + '')
            }
          }
        }
        resolve({ imgData: data, xml: xml })
      })
    })
  }

  getBase64Image(img) {
    const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/
    const pathExp = new RegExp(Expression)
    return new Promise((resolve, reject) => {
      var imgData = []
      if (img.length) {
        for (let index = 0; index < img.length; index++) {
          const item = img[index]
          if (pathExp.test(item.outerHTML) && pathExp.test(item.src)) {
            var xhr = new XMLHttpRequest()
            xhr.onload = function () {
              const r = new FileReader()
              r.onload = (e) => {
                try {
                  const resData = JSON.parse(e.target.result)
                  console.log(resData.message)
                  ElMessage({
                    type: 'error',
                    message: '图片文件下载失败：' + resData.message,
                    duration: 6000,
                    showClose: true
                  })
                  imgData.push({ url: item.src, width: 0, height: 0, file: '' })
                  if (imgData.length === img.length) {
                    resolve(imgData)
                  }
                } catch (err) {
                  console.log('下载完成')
                  var url = URL.createObjectURL(this.response)
                  var image = new Image()
                  image.onload = () => {
                    // 此时你就可以使用canvas对img为所欲为了
                    var canvas = document.createElement('canvas')
                    canvas.width = image.width
                    canvas.height = image.height
                    var context = canvas.getContext('2d')
                    context.drawImage(image, 0, 0, image.width, image.height)
                    var dataURL = canvas.toDataURL('image/png') // 可选其他值 image/jpeg
                    imgData.push({
                      url: item.src,
                      width: image.width,
                      height: image.height,
                      file: dataURL.replace(/^data:image\/(png|jpg|jpeg);base64,/, '')
                    })
                    if (imgData.length === img.length) {
                      resolve(imgData)
                    }
                    // 图片用完后记得释放内存
                    URL.revokeObjectURL(url)
                  }
                  image.src = url
                }
              }
              r.readAsText(this.response)
            }
            xhr.onerror = function () {
              ElMessage({
                type: 'error',
                message: item.src + '图片文件不支持下载，您导出的Word文件将会缺失此图片',
                duration: 6000,
                showClose: true
              })
              imgData.push({ url: item.src, width: 0, height: 0, file: '' })
              if (imgData.length === img.length) {
                resolve(imgData)
              }
            }
            xhr.open('GET', item.src + '?v=' + item.src + new Date(), true)
            xhr.responseType = 'blob'
            xhr.send()
          } else {
            imgData.push({ url: item.src, width: 0, height: 0, file: '' })
            if (imgData.length === img.length) {
              resolve(imgData)
            }
          }
        }
      } else {
        resolve(imgData)
      }
    })
  }

  xmlData(data, listInfo = null) {
    var xml = ''
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      const tagName = item.tagName.toLowerCase()

      // 段落和div
      if (tagName === 'p' || tagName === 'div') {
        xml += '<w:p>'
        xml += this.xmlStyle(item.style, true, this.firstLineIndentation(item))
        if (item.children.length) {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          xml += this.spanXml(item.childNodes, this.xmlStyle(item.style, false), false, fontSize)
        } else {
          var spanStyle = this.xmlStyle(item.style, false)
          xml += `<w:r>${spanStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
        xml += '</w:p>'
      }
      // 标题标签 h1-h6
      else if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
        xml += '<w:p>'
        const headingLevel = parseInt(tagName.charAt(1))

        // 获取完整的样式（默认样式 + 用户样式）
        const completeStyle = this.getCompleteStyle(tagName, item.style)

        // 传递用户样式给标题样式方法
        const headingStyle = this.getHeadingStyle(headingLevel, completeStyle)
        xml += headingStyle

        // 确保标题的加粗样式被正确传递
        const headingStyleWithBold = {
          ...completeStyle,
          fontWeight: completeStyle.fontWeight || 'bold' // 确保标题默认加粗
        }

        if (item.children.length) {
          const combinedStyle = this.xmlStyle(headingStyleWithBold, false, 0, headingStyleWithBold.fontSize)
          xml += this.spanXml(
            item.childNodes,
            combinedStyle,
            false,
            headingStyleWithBold.fontSize,
            false,
            headingStyleWithBold // 传递包含加粗样式的完整样式
          )
        } else {
          const combinedStyle = this.xmlStyle(headingStyleWithBold, false, 0, headingStyleWithBold.fontSize)
          xml += `<w:r>${combinedStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
        xml += '</w:p>'
      }
      // 水平分割线
      else if (tagName === 'hr') {
        xml += '<w:p>'
        xml += '<w:pPr><w:pBdr><w:bottom w:val="single" w:sz="6" w:space="1" w:color="auto"/></w:pBdr></w:pPr>'
        xml += '<w:r><w:t></w:t></w:r>'
        xml += '</w:p>'
      }
      // 引用块
      else if (tagName === 'blockquote') {
        xml += '<w:p>'
        xml += this.xmlStyle(
          { ...item.style, marginLeft: '2em', fontStyle: 'italic' },
          true,
          this.firstLineIndentation(item)
        )
        if (item.children.length) {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          const combinedStyle = this.xmlStyle({ ...item.style, fontStyle: 'italic' }, false, 0, fontSize)
          xml += this.spanXml(item.childNodes, combinedStyle, false, fontSize)
        } else {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          var spanStyle = this.xmlStyle({ ...item.style, fontStyle: 'italic' }, false, 0, fontSize)
          xml += `<w:r>${spanStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
        xml += '</w:p>'
      }
      // 预格式化文本
      else if (tagName === 'pre') {
        xml += '<w:p>'
        xml += this.xmlStyle(
          { ...item.style, fontFamily: 'Courier New, monospace', whiteSpace: 'pre' },
          true,
          this.firstLineIndentation(item)
        )
        if (item.children.length) {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          const combinedStyle = this.xmlStyle(
            { ...item.style, fontFamily: 'Courier New, monospace' },
            false,
            0,
            fontSize
          )
          xml += this.spanXml(item.childNodes, combinedStyle, false, fontSize)
        } else {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          var spanStyle = this.xmlStyle({ ...item.style, fontFamily: 'Courier New, monospace' }, false, 0, fontSize)
          xml += `<w:r>${spanStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
        xml += '</w:p>'
      }
      // 表格
      else if (tagName === 'table') {
        xml += '<w:tbl>'
        xml += '<w:tblPr>'
        xml += '<w:tblBorders>'
        xml += '<w:top w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:left w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:bottom w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:right w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:insideH w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '<w:insideV w:val="single" w:sz="4" w:space="0" w:color="auto"/>'
        xml += '</w:tblBorders><w:tblW w:w="4975" w:type="pct"/>'
        xml += '<w:tblLayout w:type="autofit"/><w:tblCellMar>'
        xml += '<w:top w:w="220" w:type="dxa"/>'
        xml += '<w:start w:w="220" w:type="dxa"/>'
        xml += '<w:bottom w:w="220" w:type="dxa"/>'
        xml += '<w:end w:w="220" w:type="dxa"/>'
        xml += '</w:tblCellMar>'
        xml += '</w:tblPr>'
        if (item.children[0].children.length) {
          xml += this.tableXml(item.children[0].children)
        }
        xml += '</w:tbl>'
      } else if (item.tagName.toLowerCase() === 'article') {
        xml += this.xmlData(item.children, listInfo)
      } else if (tagName === 'ul') {
        // 无序列表
        xml += this.xmlData(item.children, { listType: 'ul', counter: 0 })
      } else if (tagName === 'ol') {
        // 有序列表
        xml += this.xmlData(item.children, { listType: 'ol', counter: 0 })
      } else if (tagName === 'li') {
        xml += '<w:p>'

        // 添加缩进
        xml += this.xmlStyle({ ...item.style, textIndent: '2em' }, true, this.firstLineIndentation(item))

        // 根据列表类型添加符号
        var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
        const sz = this.sizeCalculate(fontSize)
        const fontFamily = item?.style?.fontFamily || 'Microsoft YaHei'

        if (listInfo && listInfo.listType === 'ul') {
          // 无序列表使用圆点
          xml += `<w:r><w:rPr><w:rFonts w:ascii="${fontFamily}" w:eastAsia="${fontFamily}" w:hAnsi="${fontFamily}"/><w:sz w:val="${sz}"/></w:rPr><w:t>• </w:t></w:r>`
        } else if (listInfo && listInfo.listType === 'ol') {
          // 有序列表使用数字序号
          listInfo.counter = (listInfo.counter || 0) + 1
          xml += `<w:r><w:rPr><w:rFonts w:ascii="${fontFamily}" w:eastAsia="${fontFamily}" w:hAnsi="${fontFamily}"/><w:sz w:val="${sz}"/></w:rPr><w:t>${listInfo.counter}. </w:t></w:r>`
        }

        if (item.children.length) {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          const combinedStyle = this.xmlStyle(item.style, false, 0, fontSize)
          xml += this.spanXml(item.childNodes, combinedStyle, false, fontSize)
        } else {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          const combinedStyle = this.xmlStyle(item.style, false, 0, fontSize)
          xml += `<w:r>${combinedStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
        xml += '</w:p>'
      }
      // 定义列表
      else if (tagName === 'dl') {
        xml += this.xmlData(item.children, listInfo)
      } else if (tagName === 'dt') {
        xml += '<w:p>'
        xml += this.xmlStyle(
          { ...item.style, fontWeight: 'bold', marginBottom: '0.5em' },
          true,
          this.firstLineIndentation(item)
        )
        if (item.children.length) {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          const combinedStyle = this.xmlStyle({ ...item.style, fontWeight: 'bold' }, false, 0, fontSize)
          xml += this.spanXml(item.childNodes, combinedStyle, false, fontSize)
        } else {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          const combinedStyle = this.xmlStyle({ ...item.style, fontWeight: 'bold' }, false, 0, fontSize)
          xml += `<w:r>${combinedStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
        xml += '</w:p>'
      } else if (tagName === 'dd') {
        xml += '<w:p>'
        xml += this.xmlStyle(
          { ...item.style, marginLeft: '2em', marginBottom: '0.5em' },
          true,
          this.firstLineIndentation(item)
        )
        if (item.children.length) {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          const combinedStyle = this.xmlStyle(item.style, false, 0, fontSize)
          xml += this.spanXml(item.childNodes, combinedStyle, false, fontSize)
        } else {
          var fontSize = item?.style?.fontSize ? item?.style?.fontSize : '16px'
          const combinedStyle = this.xmlStyle(item.style, false, 0, fontSize)
          xml += `<w:r>${combinedStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
        xml += '</w:p>'
      }
      // 其他未处理的标签，递归处理子元素
      else {
        if (item.children && item.children.length > 0) {
          xml += this.xmlData(item.children, listInfo)
        } else if (item.innerText) {
          xml += '<w:p>'
          xml += this.xmlStyle(item.style, true, this.firstLineIndentation(item))
          xml += `<w:r><w:t>${this.func(item.innerText)}</w:t> </w:r>`
          xml += '</w:p>'
        }
      }
    }
    return xml
  }

  // 表格处理
  tableXml(data) {
    var xml = ''
    var rowspan = []
    var rowspanI = []
    var rowspanWidth = []
    var isIndex = 0
    for (let tableIndex = 0; tableIndex < data.length; tableIndex++) {
      const item = data[tableIndex]
      xml += '<w:tr>'
      for (let i = 0; i < rowspanI.length; i++) {
        const row = rowspanI[i]
        if (row === 0 && rowspan[i] !== 0) {
          isIndex = isIndex + 1
          xml += `<w:tc><w:tcPr>${
            rowspanWidth[i] ? `<w:tcW w:w="${rowspanWidth[i]}" w:type="dxa"/>` : ''
          }<w:gridSpan w:val="2"/><w:vMerge/></w:tcPr><w:p/></w:tc>`
          rowspan[i] = rowspan[i] - 1
        }
      }
      for (let i = 0; i < item.children.length; i++) {
        const row = item.children[i]
        xml += '<w:tc><w:tcPr>'
        if (row.rowSpan !== 1) {
          rowspan.push(row.rowSpan - 1)
          rowspanI.push(i)
          rowspanWidth.push(row.width || 0)
          xml += '<w:vMerge w:val="restart"/><w:vAlign w:val="center"/>'
        }
        if (row.colSpan !== 1) {
          var width = this.colSpanWidth(data, i, row.colSpan - 1)
          xml += `<w:tcW w:type="pct" w:w="${width}"/>`
        }
        if (row.width) {
          xml += `<w:tcW w:type="pct" w:w="${row.width}"/>`
        }
        xml += '</w:tcPr>'
        if (row.children.length) {
          var html = '<w:p>'
          for (let elIndex = 0; elIndex < row.childNodes.length; elIndex++) {
            const element = row.childNodes[elIndex]
            if (element.nodeName.toLowerCase() === 'p') {
              xml += '<w:p>'
              xml += this.xmlStyle(element.style, true, this.firstLineIndentation(row))
              if (element.children.length) {
                const fontSize = element?.style?.fontSize ? item?.style?.fontSize : '16px'
                xml += this.spanXml(element.childNodes, this.xmlStyle(element.style, false), false, fontSize)
              } else {
                xml += `<w:r>${this.xmlStyle(element.style, false)}<w:t>${this.func(element.innerText)}</w:t> </w:r>`
              }
              xml += '</w:p>'
            } else {
              const fontSize = element?.style?.fontSize ? item?.style?.fontSize : '16px'
              html += this.spanXml([element], this.xmlStyle(element.style, false), false, fontSize)
            }
          }
          html += '</w:p>'
          if (html.length > 11) {
            xml += html
          }
        } else {
          xml += '<w:p>'
          xml += `<w:r>${this.xmlStyle(row.style, false)}<w:t>${this.func(row.innerText)}</w:t> </w:r>`
          xml += '</w:p>'
        }
        xml += '</w:tc>'
        for (let index = 0; index < rowspanI.length; index++) {
          const elItem = rowspanI[index]
          if (elItem && elItem - isIndex === i + 1 && rowspan[index] !== 0) {
            isIndex = isIndex + 1
            xml += `<w:tc><w:tcPr>${
              rowspanWidth[index] ? `<w:tcW w:w="${rowspanWidth[index]}" w:type="dxa"/>` : ''
            }<w:gridSpan w:val="2"/><w:vMerge/></w:tcPr><w:p/></w:tc>`
            rowspan[index] = rowspan[index] - 1
          }
        }
      }
      isIndex = 0
      xml += '</w:tr>'
    }
    return xml
  }

  // 横向合并单元格
  colSpanWidth(data, index, length) {
    var arr = []
    var width = 0
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      if (item.children.length > arr.length) {
        arr = item.children
        var iswidth = 0
        for (let ins = 0; ins < item.children.length; ins++) {
          const row = item.children[ins]
          if (ins >= index && ins <= index + length) {
            if (row.width) {
              iswidth += Number(row.width)
            }
          }
        }
        width = iswidth
      }
    }
    return width
  }

  spanXml(data, style, type, fontSize, isNesting = false, parentStyle = {}) {
    var xml = ''
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      const nodeName = item.nodeName.toLowerCase()

      // 文本节点
      if (item.nodeName === '#text') {
        xml += `<w:r>${style}<w:t>${this.func(item.nodeValue)}</w:t> </w:r>`
      }
      // 换行标签
      else if (nodeName === 'br') {
        xml += '<w:r><w:br/></w:r>'
      }
      // 粗体
      else if (nodeName === 'strong' || nodeName === 'b') {
        const styleResult = this.processInlineStyle(nodeName, item, parentStyle, fontSize, {
          strong: '<w:b />',
          fontWeight: 'bold'
        })
        if (item.children.length) {
          xml += this.spanXml(
            item.childNodes,
            styleResult.combinedStyle,
            true,
            styleResult.fontSizeNum,
            true,
            styleResult.inheritedStyle
          )
        } else {
          xml += `<w:r>${styleResult.combinedStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 斜体
      else if (nodeName === 'em' || nodeName === 'i') {
        const styleResult = this.processInlineStyle(nodeName, item, parentStyle, fontSize, { em: '<w:i />' })
        if (item.children.length) {
          xml += this.spanXml(
            item.childNodes,
            styleResult.combinedStyle,
            false,
            styleResult.fontSizeNum,
            true,
            styleResult.inheritedStyle
          )
        } else {
          xml += `<w:r>${styleResult.combinedStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 下划线
      else if (nodeName === 'u' || nodeName === 'ins') {
        const styleResult = this.processInlineStyle(nodeName, item, parentStyle, fontSize, {
          underline: '<w:u w:val="single"/>'
        })
        if (item.children.length) {
          xml += this.spanXml(
            item.childNodes,
            styleResult.combinedStyle,
            false,
            styleResult.fontSizeNum,
            true,
            styleResult.inheritedStyle
          )
        } else {
          xml += `<w:r>${styleResult.combinedStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 删除线
      else if (nodeName === 'del' || nodeName === 's' || nodeName === 'strike') {
        if (item.children.length) {
          const fontSizeNum = item.style?.fontSize ? item.style?.fontSize : fontSize
          xml += this.spanXml(
            item.childNodes,
            this.xmlStyle({ strikethrough: '<w:strike/>' }, false, 0, fontSize),
            false,
            fontSizeNum,
            true
          )
        } else {
          xml += `<w:r>${style}<w:rPr><w:strike/></w:rPr><w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 上标
      else if (nodeName === 'sup') {
        if (item.children.length) {
          const fontSizeNum = item.style?.fontSize ? item.style?.fontSize : fontSize
          xml += this.spanXml(
            item.childNodes,
            this.xmlStyle({ superscript: '<w:vertAlign w:val="superscript"/>' }, false, 0, fontSize),
            false,
            fontSizeNum,
            true
          )
        } else {
          xml += `<w:r>${style}<w:rPr><w:vertAlign w:val="superscript"/></w:rPr><w:t>${this.func(
            item.innerText
          )}</w:t> </w:r>`
        }
      }
      // 下标
      else if (nodeName === 'sub') {
        if (item.children.length) {
          const fontSizeNum = item.style?.fontSize ? item.style?.fontSize : fontSize
          xml += this.spanXml(
            item.childNodes,
            this.xmlStyle({ subscript: '<w:vertAlign w:val="subscript"/>' }, false, 0, fontSize),
            false,
            fontSizeNum,
            true
          )
        } else {
          xml += `<w:r>${style}<w:rPr><w:vertAlign w:val="subscript"/></w:rPr><w:t>${this.func(
            item.innerText
          )}</w:t> </w:r>`
        }
      }
      // 代码
      else if (nodeName === 'code') {
        if (item.children.length) {
          const fontSizeNum = item.style?.fontSize ? item.style?.fontSize : fontSize
          xml += this.spanXml(
            item.childNodes,
            this.xmlStyle({ fontFamily: 'Courier New, monospace', backgroundColor: '#f5f5f5' }, false, 0, fontSize),
            false,
            fontSizeNum,
            true
          )
        } else {
          const codeStyle = this.xmlStyle({ fontFamily: 'Courier New, monospace', backgroundColor: '#f5f5f5' }, false)
          xml += `<w:r>${style}${codeStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 标记/高亮
      else if (nodeName === 'mark') {
        if (item.children.length) {
          const fontSizeNum = item.style?.fontSize ? item.style?.fontSize : fontSize
          xml += this.spanXml(
            item.childNodes,
            this.xmlStyle({ backgroundColor: '#ffff00' }, false, 0, fontSize),
            false,
            fontSizeNum,
            true
          )
        } else {
          const markStyle = this.xmlStyle({ backgroundColor: '#ffff00' }, false)
          xml += `<w:r>${style}${markStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 小字体
      else if (nodeName === 'small') {
        if (item.children.length) {
          const fontSizeNum = item.style?.fontSize ? item.style?.fontSize : '12px'
          xml += this.spanXml(
            item.childNodes,
            this.xmlStyle({ fontSize: '12px' }, false, 0, fontSize),
            false,
            fontSizeNum,
            true
          )
        } else {
          const smallStyle = this.xmlStyle({ fontSize: '12px' }, false)
          xml += `<w:r>${style}${smallStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 大字体
      else if (nodeName === 'big') {
        if (item.children.length) {
          const fontSizeNum = item.style?.fontSize ? item.style?.fontSize : '20px'
          xml += this.spanXml(
            item.childNodes,
            this.xmlStyle({ fontSize: '20px' }, false, 0, fontSize),
            false,
            fontSizeNum,
            true
          )
        } else {
          const bigStyle = this.xmlStyle({ fontSize: '20px' }, false)
          xml += `<w:r>${style}${bigStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 链接
      else if (nodeName === 'a') {
        if (item.children.length) {
          const fontSizeNum = item.style?.fontSize ? item.style?.fontSize : fontSize
          xml += this.spanXml(
            item.childNodes,
            this.xmlStyle({ color: '#0000ff', textDecoration: 'underline' }, false, 0, fontSize),
            false,
            fontSizeNum,
            true
          )
        } else {
          const linkStyle = this.xmlStyle({ color: '#0000ff', textDecoration: 'underline' }, false)
          xml += `<w:r>${style}${linkStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // span标签
      else if (nodeName === 'span') {
        const styleResult = this.processInlineStyle(nodeName, item, parentStyle, fontSize)
        if (item.children.length) {
          xml += this.spanXml(
            item.childNodes,
            styleResult.combinedStyle,
            false,
            styleResult.fontSizeNum,
            true,
            styleResult.inheritedStyle
          )
        } else {
          xml += `<w:r>${styleResult.combinedStyle}<w:t>${this.func(item.innerText)}</w:t> </w:r>`
        }
      }
      // 图片
      else if (nodeName === 'img') {
        xml += `<w:r>${style}<w:rPr></w:rPr><w:r><w:drawing><wp:inline distT="0" distB="0" distL="0" distR="0"><wp:extent cx="9525width1000${this.imgData.length}" cy="9525height1000${this.imgData.length}"/><wp:effectExtent l="0" t="0" r="7620" b="7620"/><wp:docPr id="1000${this.imgData.length}" name="图片 1000${this.imgData.length}"/><wp:cNvGraphicFramePr><a:graphicFrameLocks xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" noChangeAspect="1"/></wp:cNvGraphicFramePr><a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main"><a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture"><pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture"><pic:nvPicPr><pic:cNvPr id="1000${this.imgData.length}" name="图片 1000${this.imgData.length}"/><pic:cNvPicPr><a:picLocks noChangeAspect="1"/></pic:cNvPicPr></pic:nvPicPr><pic:blipFill><a:blip r:embed="rId1000${this.imgData.length}"/><a:stretch><a:fillRect/></a:stretch></pic:blipFill><pic:spPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="9525width1000${this.imgData.length}" cy="9525height1000${this.imgData.length}"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom><a:noFill/><a:ln><a:noFill/></a:ln></pic:spPr></pic:pic></a:graphicData></a:graphic></wp:inline></w:drawing></w:r></w:r>`
        this.imgData.push({
          id: '1000' + this.imgData.length,
          name: `images1000${this.imgData.length}.png`,
          url: item.src
        })
      } else {
        const fontSizeNum = item?.style?.fontSize ? item?.style?.fontSize : fontSize
        // 确保继承父级样式
        const inheritedStyle = this.mergeStyles(parentStyle, item.style)
        const combinedStyle = this.xmlStyle(inheritedStyle, false, 0, fontSizeNum)
        xml += this.spanXml(item.childNodes, combinedStyle, false, fontSizeNum, true, inheritedStyle)
      }
    }
    return xml
  }

  // 计算首行缩进
  firstLineIndentation(obj, type) {
    var num = 0
    if (obj.style.fontSize) {
      num = this.sizeCalculate(obj.style.fontSize)
    } else {
      num = 24
    }
    if (obj.children.length) {
      if (obj.childNodes[0].nodeName !== '#text') {
        var numText = this.firstLineIndentation(obj.children[0], true)
        num = numText === 24 ? num : numText
      }
    }
    const sizeNum = Math.floor((num / 2 / 72) * 96)
    return type ? num : sizeNum * 15
  }

  xmlStyle(style = {}, type, num, fontSize) {
    var text = type ? '<w:pPr>' : '<w:rPr>'
    if (type && style.textIndent) {
      const indentValue = this.parseSpacing(style.textIndent)
      text += `<w:ind w:firstLine="${indentValue}"/>`
    }
    if (type && (style.lineHeight || style.marginTop || style.marginBottom)) {
      var lineHeight = ''
      var marginTop = ''
      var marginBottom = ''
      if (style.lineHeight) {
        var lineHeightNum = this.parseLineHeight(style.lineHeight, style.fontSize)
        lineHeight = `w:line="${Number(lineHeightNum) * 240}"`
      }
      if (style.marginTop) {
        var marginTopNum = this.parseSpacing(style.marginTop)
        marginTop = `w:before="${marginTopNum}"`
      }
      if (style.marginBottom) {
        var marginBottomNum = this.parseSpacing(style.marginBottom)
        marginBottom = `w:after="${marginBottomNum}"`
      }
      text += `<w:spacing ${marginTop} ${marginBottom} ${lineHeight} w:lineRule="auto" w:beforeAutospacing="0" w:afterAutospacing="0"/>`
    }
    if (style.textAlign) {
      text += `<w:jc w:val="${style.textAlign}"/>`
    }
    if (style.fontSize) {
      const sz = this.sizeCalculate(style.fontSize)
      if (type) {
        text += `<w:rPr><w:sz w:val="${sz}"/></w:rPr>`
      } else {
        text += `<w:sz w:val="${sz}"/>`
      }
    } else {
      if (fontSize) {
        const sz = this.sizeCalculate(fontSize)
        if (type) {
          text += `<w:rPr><w:sz w:val="${sz}"/></w:rPr>`
        } else {
          text += `<w:sz w:val="${sz}"/>`
        }
      } else {
        if (type) {
          text += '<w:rPr><w:sz w:val="24"/></w:rPr>'
        } else {
          text += '<w:sz w:val="24"/>'
        }
      }
    }
    if (style.strong) {
      text += style.strong
    }
    if (style.fontWeight === 'bold' || style.fontWeight === 'bolder') {
      text += '<w:b />'
    }
    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {
      text += '<w:i />'
    }
    if (style.em) {
      text += style.em
    }
    if (style.underline) {
      text += style.underline
    }
    if (style.strikethrough) {
      text += style.strikethrough
    }
    if (style.superscript) {
      text += style.superscript
    }
    if (style.subscript) {
      text += style.subscript
    }
    if (style.color) {
      if (/^[A-Fa-f0-9]{1,4}$/.test(style.color)) {
        text += `<w:color w:val="${style.color}" />`
      } else {
        text += `<w:color w:val="${this.colorRGBtoHex(style.color)}" />`
      }
    }
    if (
      (style.textDecoration && style.textDecoration !== 'none') ||
      (style.textDecorationLine && style.textDecorationLine !== 'none')
    ) {
      text += '<w:u w:val="single"/>'
    }
    if (style.backgroundColor) {
      if (/^[A-Fa-f0-9]{1,4}$/.test(style.backgroundColor)) {
        text += `<w:shd w:val="clear" w:fill="${style.backgroundColor}" w:color="${style.backgroundColor}" />`
      } else {
        text += `<w:shd w:val="clear" w:fill="${this.colorRGBtoHex(
          style.backgroundColor
        )}" w:color="${this.colorRGBtoHex(style.backgroundColor)}" />`
      }
    }
    if (style.fontFamily) {
      text += `<w:rFonts w:ascii="${style.fontFamily}" w:eastAsia="${style.fontFamily}" w:hAnsi="${style.fontFamily}" />`
    }
    text += type ? '</w:pPr>' : '</w:rPr>'
    return text
  }

  // 颜色转换方法 - 支持多种格式
  colorRGBtoHex(color) {
    if (!color) return '#000000'

    // 处理十六进制颜色
    if (color.startsWith('#')) {
      return color.toUpperCase()
    }

    // 处理rgb/rgba格式
    if (color.startsWith('rgb')) {
      const rgbMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
      if (rgbMatch) {
        const r = parseInt(rgbMatch[1])
        const g = parseInt(rgbMatch[2])
        const b = parseInt(rgbMatch[3])
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()
      }
    }

    // 处理HSL格式（转换为RGB）
    if (color.startsWith('hsl')) {
      const hslMatch = color.match(/hsla?\((\d+),\s*(\d+)%,\s*(\d+)%(?:,\s*[\d.]+)?\)/)
      if (hslMatch) {
        const h = parseInt(hslMatch[1]) / 360
        const s = parseInt(hslMatch[2]) / 100
        const l = parseInt(hslMatch[3]) / 100
        const rgb = this.hslToRgb(h, s, l)
        return '#' + ((1 << 24) + (rgb[0] << 16) + (rgb[1] << 8) + rgb[2]).toString(16).slice(1).toUpperCase()
      }
    }

    // 处理命名颜色
    return this.colorObj[color.toLowerCase()] || '#000000'
  }

  // HSL转RGB
  hslToRgb(h, s, l) {
    let r, g, b
    if (s === 0) {
      r = g = b = l // 无色彩
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1
        if (t > 1) t -= 1
        if (t < 1 / 6) return p + (q - p) * 6 * t
        if (t < 1 / 2) return q
        if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6
        return p
      }
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      r = hue2rgb(p, q, h + 1 / 3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1 / 3)
    }
    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)]
  }

  // 把字体大小换算成Word单位 - 支持多种单位
  sizeCalculate(size) {
    if (!size) return 24 // 默认12pt = 24个Word单位

    const value = parseFloat(size)
    const unit = size.replace(/[\d.-]/g, '').toLowerCase()

    // 先转换为磅(pt)，再转换为Word单位
    let ptValue = 0

    switch (unit) {
      case 'pt':
        ptValue = value // 已经是磅
        break
      case 'px':
        ptValue = (value / 96) * 72 // px转磅：96px = 72pt
        break
      case 'em':
        ptValue = ((value * 16) / 96) * 72 // em转磅：基于16px
        break
      case 'rem':
        ptValue = ((value * 16) / 96) * 72 // rem转磅：基于16px
        break
      case '%':
        ptValue = (((value / 100) * 16) / 96) * 72 // 百分比转磅
        break
      case 'in':
        ptValue = value * 72 // 英寸转磅：1英寸 = 72磅
        break
      case 'cm':
        ptValue = value * 28.35 // 厘米转磅：1厘米 = 28.35磅
        break
      case 'mm':
        ptValue = value * 2.835 // 毫米转磅：1毫米 = 2.835磅
        break
      default:
        ptValue = value // 默认按磅处理
    }

    // Word使用半磅作为单位，所以1磅 = 2个Word单位
    return Math.round(ptValue * 2)
  }

  // 解析行高
  parseLineHeight(lineHeight, fontSize) {
    if (!lineHeight) return 1.2

    const value = parseFloat(lineHeight)
    const unit = lineHeight.replace(/[\d.-]/g, '').toLowerCase()

    switch (unit) {
      case 'em':
        return value // em单位直接返回
      case '%':
        return value / 100 // 百分比转换为小数
      case 'px':
        // px需要根据字体大小计算
        const fontSizeValue = fontSize ? this.sizeCalculate(fontSize) / 2 : 12
        return value / fontSizeValue
      case 'pt':
        // pt需要根据字体大小计算
        const fontSizePt = fontSize ? this.sizeCalculate(fontSize) / 2 : 12
        return value / fontSizePt
      default:
        return value // 无单位或数字，直接返回
    }
  }

  // 合并样式对象（子标签样式优先，但保留关键父级样式）
  mergeStyles(parentStyle = {}, childStyle = {}) {
    if (!parentStyle) return childStyle
    if (!childStyle) return parentStyle

    // 合并样式对象，子标签样式优先
    const merged = { ...parentStyle, ...childStyle }

    // 特殊处理：如果父级有加粗样式且子级没有明确设置，则保持加粗
    if (parentStyle.fontWeight === 'bold' && !childStyle.hasOwnProperty('fontWeight')) {
      merged.fontWeight = 'bold'
    }

    // 特殊处理：如果父级有斜体样式且子级没有明确设置，则保持斜体
    if (parentStyle.fontStyle === 'italic' && !childStyle.hasOwnProperty('fontStyle')) {
      merged.fontStyle = 'italic'
    }

    return merged
  }

  // 获取标签的默认样式
  getDefaultTagStyle(tagName) {
    const defaultStyles = {
      h1: { fontSize: '32px', fontWeight: 'bold' },
      h2: { fontSize: '28px', fontWeight: 'bold' },
      h3: { fontSize: '24px', fontWeight: 'bold' },
      h4: { fontSize: '20px', fontWeight: 'bold' },
      h5: { fontSize: '18px', fontWeight: 'bold' },
      h6: { fontSize: '16px', fontWeight: 'bold' },
      blockquote: { fontStyle: 'italic', marginLeft: '2em' },
      pre: { fontFamily: 'Courier New, monospace', whiteSpace: 'pre' },
      li: { textIndent: '2em' },
      dt: { fontWeight: 'bold', marginBottom: '0.5em' },
      dd: { marginLeft: '2em', marginBottom: '0.5em' }
    }
    return defaultStyles[tagName.toLowerCase()] || {}
  }

  // 获取完整的样式（默认样式 + 用户样式）
  getCompleteStyle(tagName, userStyle = {}) {
    const defaultStyle = this.getDefaultTagStyle(tagName)
    return this.mergeStyles(defaultStyle, userStyle)
  }

  // 处理内联标签的样式继承
  processInlineStyle(nodeName, item, parentStyle, fontSize, additionalStyle = {}) {
    // 合并父级样式和当前标签样式
    const inheritedStyle = this.mergeStyles(parentStyle, item.style)
    const fontSizeNum = inheritedStyle.fontSize || fontSize

    // 确保关键样式正确传递
    let finalStyle = this.mergeStyles(inheritedStyle, additionalStyle)

    // 如果父级有加粗样式，确保传递到最终样式
    if (parentStyle && parentStyle.fontWeight === 'bold' && !finalStyle.fontWeight) {
      finalStyle.fontWeight = 'bold'
    }

    // 如果父级有斜体样式，确保传递到最终样式
    if (parentStyle && parentStyle.fontStyle === 'italic' && !finalStyle.fontStyle) {
      finalStyle.fontStyle = 'italic'
    }

    return {
      inheritedStyle: finalStyle,
      fontSizeNum,
      combinedStyle: this.xmlStyle(finalStyle, false, 0, fontSizeNum)
    }
  }

  // 解析间距值（边距、内边距等）
  parseSpacing(spacing) {
    if (!spacing) return 0

    const value = parseFloat(spacing)
    const unit = spacing.replace(/[\d.-]/g, '').toLowerCase()

    switch (unit) {
      case 'px':
        return Math.round(value * 20) // px转Word单位（1px ≈ 20单位）
      case 'pt':
        return Math.round(value * 20) // pt转Word单位
      case 'em':
        return Math.round(value * 16 * 20) // em转Word单位
      case 'rem':
        return Math.round(value * 16 * 20) // rem转Word单位
      case '%':
        return Math.round((value / 100) * 16 * 20) // 百分比转Word单位
      case 'in':
        return Math.round(value * 1440) // 英寸转Word单位
      case 'cm':
        return Math.round(value * 567) // 厘米转Word单位
      case 'mm':
        return Math.round(value * 56.7) // 毫米转Word单位
      default:
        return Math.round(value * 20) // 默认按px处理
    }
  }

  // 获取标题样式
  getHeadingStyle(level, userStyle = {}) {
    const styles = {
      1: '<w:pStyle w:val="Heading1"/>',
      2: '<w:pStyle w:val="Heading2"/>',
      3: '<w:pStyle w:val="Heading3"/>',
      4: '<w:pStyle w:val="Heading4"/>',
      5: '<w:pStyle w:val="Heading5"/>',
      6: '<w:pStyle w:val="Heading6"/>'
    }

    let pPr = '<w:pPr>'
    pPr += styles[level] || styles[6]

    // 添加对齐样式
    if (userStyle.textAlign) {
      const alignMap = {
        left: 'left',
        center: 'center',
        right: 'right',
        justify: 'both'
      }
      const wordAlign = alignMap[userStyle.textAlign] || 'left'
      pPr += `<w:jc w:val="${wordAlign}"/>`
    }

    // 添加其他段落样式
    if (userStyle.fontSize) {
      const sz = this.sizeCalculate(userStyle.fontSize)
      pPr += `<w:rPr><w:sz w:val="${sz}"/></w:rPr>`
    }

    pPr += '</w:pPr>'
    return pPr
  }

  // 获取标题字体大小
  getHeadingFontSize(level) {
    const sizes = {
      1: '32px',
      2: '28px',
      3: '24px',
      4: '20px',
      5: '18px',
      6: '16px'
    }
    return sizes[level] || '16px'
  }
}

export { WordXml }
