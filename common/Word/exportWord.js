import <PERSON>z<PERSON><PERSON> from 'pizzip'
import J<PERSON><PERSON>ipUtils from 'jszip-utils'
import docxtemplater from 'docxtemplater'
import { saveAs } from 'file-saver'

export const exportWordHtml = (url, name, data, imgData = []) => {
  JSZipUtils.getBinaryContent(url, (error, content) => {
    if (error) { throw error }
    const zip = PizZip(content)
    if (imgData.length) {
      var img = zip.folder('word/media')
      var imgFile = zip.folder('word/_rels/')
      var imgUrlText = imgFile.file('document.xml.rels').asText().replace('</Relationships>', '')
      imgData.forEach(item => {
        img.file(item.name, item.file, { base64: true })
        imgUrlText += `<Relationship Id="rId${item.id}" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="media/${item.name}"/>`
      })
      imgUrlText += '</Relationships>'
      imgFile.file('document.xml.rels', imgUrlText)
    }
    const word = new docxtemplater().loadZip(zip)
    word.setData(data)
    try { word.render() } catch (error) {
      const e = { message: error.message, name: error.name, stack: error.stack, properties: error.properties }
      console.log(JSON.stringify({ error: e }))
      throw error
    }
    const docx = word.getZip().generate({ type: 'blob' })
    saveAs(docx, `${name}.docx`)
  })
}

export const exportWord = (url, name, data = {}) => {
  JSZipUtils.getBinaryContent(url, (error, content) => {
    if (error) { throw error }
    const zip = new PizZip(content)
    const word = new docxtemplater().loadZip(zip)
    word.setData(data)
    try { word.render() } catch (error) {
      const e = { message: error.message, name: error.name, stack: error.stack, properties: error.properties }
      console.log(JSON.stringify({ error: e }))
      throw error
    }
    const docx = word.getZip().generate({ type: 'blob' })
    saveAs(docx, `${name}.docx`)
  })
}

export const exportWordPromise = (url, data = {}) => new Promise((resolve, reject) => {
  JSZipUtils.getBinaryContent(url, (error, content) => {
    if (error) { throw reject(error) }
    const zip = new PizZip(content)
    const word = new docxtemplater().loadZip(zip)
    word.setData(data)
    try { word.render() } catch (error) {
      const e = { message: error.message, name: error.name, stack: error.stack, properties: error.properties }
      console.log(JSON.stringify({ error: e }))
      throw reject(error)
    }
    const docx = word.getZip().generate({ type: 'ArrayBuffer' })
    resolve(docx)
  })
})

export const exportWordList = async (url, data = []) => {
  const zip = new PizZip()
  for (let index = 0; index < data.length; index++) {
    const docx = await exportWordPromise(url, data[index])
    zip.file(`测试${index}.docx`, docx, { binary: true })
  }
  console.log(zip)
  const word = zip.generate({ type: 'blob' })
  saveAs(word, '测试.zip')
}
