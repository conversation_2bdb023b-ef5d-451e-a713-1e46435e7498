import store from '@/store'
import config from './index'
import { filePreview, downloadFile } from './MicroGlobal'
import { ElMessage, ElMessageBox } from 'element-plus'
import { file_preview_mode, file_preview_open } from 'common/js/system_var.js'
import XylFilePreview from 'common/components/xyl-file-preview'
export const globalLocation = (name) => name ? config.microAppPath(name) : config.mainPath
const handleFilePreview = ({ fileId, fileType, fileName, fileSize, isDownload = 'yes' }) => {
  const token = sessionStorage.getItem('token') || ''
  if (file_preview_open.value === '_self') {
    if (window.__POWERED_BY_QIANKUN__) {
      filePreview({ id: fileId, fileType, fileName, fileSize, isDownload })
    } else {
      if (!window.__PUBLIC__) {
        store.dispatch('handleFilePreview', { id: fileId, fileType, fileName, fileSize, isDownload })
      }
    }
  } else {
    window.open(`${config.mainPath}FilePreview?id=${fileId}&fileType=${fileType}&fileName=${fileName}&fileSize=${fileSize}&isDownload=${isDownload}&token=${token}`, '_blank')
  }
}
export const globalFileLocation = ({ name, fileId, fileType, fileName, fileSize, isDownload = 'yes' }) => {
  if (['zip', 'rar'].includes(fileType)) {
    ElMessageBox.confirm('当前文件不支持预览, 是否下载?', '提示', {
      confirmButtonText: '下载',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      if (window.__POWERED_BY_QIANKUN__) {
        downloadFile({ fileId, fileSize, fileName, fileType })
      } else {
        if (!window.__PUBLIC__) {
          store.commit('setDownloadFile', { fileId, fileSize, fileName, fileType })
        }
      }
    }).catch(() => { ElMessage({ type: 'info', message: '已取消下载' }) })
    return
  }
  if (typeof api !== 'undefined') {
    XylFilePreview({ fileObj: { fileId: fileId, fileType: fileType, fileName: fileName, fileSize: fileSize } })
  } else {
    if (file_preview_mode.value === 'system') {
      handleFilePreview({ fileId, fileType, fileName, fileSize, isDownload })
    } else {
      handleFilePreview({ fileId, fileType, fileName, fileSize, isDownload })
    }
  }
}
