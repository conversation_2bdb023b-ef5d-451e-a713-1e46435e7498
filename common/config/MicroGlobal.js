import { MicroGlobal } from './qiankun'

export const qiankunMicro = new MicroGlobal()

export const mainWindow = () => qiankunMicro.mainWindow
export const AiChatClass = () => new qiankunMicro.AiChatClass()

export const handleVersionUpdate = (val) => qiankunMicro.setGlobalState({ versionUpdate: val })

export const handleCustom = (id) => qiankunMicro.setGlobalState({ openRoute: { name: '列表自定义', path: '/system/TableCustom', query: { id } } })

export const filePreview = (file) => qiankunMicro.setGlobalState({ filePreview: file })
export const importList = (file) => qiankunMicro.setGlobalState({ importList: file })
export const downloadFile = (file) => qiankunMicro.setGlobalState({ downloadFile: file })
export const batchDownloadFile = (file) => qiankunMicro.setGlobalState({ batchDownloadFile: file })
export const extendDownloadFile = (file) => qiankunMicro.setGlobalState({ extendDownloadFile: file })
export const exportWordObj = (file) => qiankunMicro.setGlobalState({ exportWordObj: file })
export const exportWordHtmlObj = (file) => qiankunMicro.setGlobalState({ exportWordHtmlObj: file })
export const exportWordList = (file) => qiankunMicro.setGlobalState({ exportWordList: file })
export const exportWordHtmlList = (file) => qiankunMicro.setGlobalState({ exportWordHtmlList: file })
