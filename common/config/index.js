const origin = window.location.origin
const catalog = process.env?.VUE_APP_CATALOG || '/'
const isShow = process.env.NODE_ENV === 'development'
const microAppObject = {
  demo: 'http://localhost:200/',
  system: 'http://localhost:2001/',
  interaction: 'http://localhost:2002/',
  information: 'http://localhost:2003/',
  cloudDisk: 'http://localhost:2004/',
  networkPolitics: 'http://localhost:2005/',
  npcDeputy: 'http://localhost:2006/',
  cppccMember: 'http://localhost:2007/',
  activity: 'http://localhost:2008/',
  onDuty: 'http://localhost:2009/',
  performDuties: 'http://localhost:2010/',
  learningTraining: 'http://localhost:2011/',
  suggest: 'http://localhost:2012/',
  proposal: 'http://localhost:2013/',
  dataVisual: 'http://localhost:2022/',
  partyBuilding: 'http://localhost:2033/',
  negotiation: 'http://localhost:2220/',
  interactionContact: 'http://localhost:2023/',
  legislationGeneral: 'http://localhost:2024/',
  legislation: 'http://localhost:2025/',
  recordingReview: 'http://localhost:2026/',
  dataSupportPlatform: 'http://localhost:2028/',
  apporem: 'http://localhost:2029/',
  largeScreen: 'http://localhost:2068/',
  minSuggest: 'http://localhost:2030/',
  publicOpinion: 'http://localhost:2031/',
  networkSupervis: 'http://localhost:2032/',
  chorography: 'http://localhost:2098/',
  chroniclesMuseum: 'http://localhost:2099/',
  contactStation: 'http://localhost:2036/',
  meetting: 'http://localhost:2052/',
  readingText: 'http://localhost:2053/',
  zhtzWeb: 'http://localhost:3333/',
  kanban: 'http://localhost:2034/',
  infoManage: 'http://localhost:2036/',
  exhibition: 'http://localhost:2035/',
  oldSupervision: 'http://localhost:2222/',
  oldSuggest: 'http://localhost:2227/',
  oldAcademy: 'http://localhost:2228/',
  supportSystem: 'http://localhost:2229/',
  oldScreenData: 'http://localhost:2111/',
  zhtDeputyWorkstation: 'http://localhost:2066/',
  zhtIntelligenceMeetting: 'http://localhost:2067/',
  partyBuildingResumptionCedit: 'http://localhost:2069/',
  keyProjectSupervision: 'http://localhost:2110/',
  bigScreenManage: 'http://localhost:3001/',
  administrationManage: 'http://localhost:2037/',
  unitedFront: 'http://localhost:2091/',
  allCFIC: 'http://localhost:2055/',
  conferenceSystem: 'http://localhost:2073/',
  supervisionManagement: 'http://localhost:2072/',
  businessInspectionPoint: 'http://localhost:2071/',
  leaveModule: 'http://localhost:2074/',
  PartyBuildingPerformanceCreditDepartment: 'http://localhost:2070/',
  eedsGeneralModule: 'http://localhost:2112/',
  opMgtSys: 'http://localhost:2088/',
  electronicFiling: 'http://localhost:2089/',
  supervisionWork: 'http://localhost:2095/'
}
const microAppData = () => {
  /**
   * 微应用配置
   * name: 微应用名称 - 具有唯一性
   * entry: 微应用入口 - 通过该地址加载微应用
   * container: 微应用挂载节点 - 微应用加载完成后将挂载在该节点上
   * activeRule: 微应用触发的路由规则 - 触发路由规则后将加载该微应用
   */
  var microApp = {}
  var prefetchApp = []
  const timeStr = new Date().getHours()
  if (window?.globalConfig?.VUE_APP_MICRO_APP.length) {
    const VUE_APP_MICRO_APP = window?.globalConfig?.VUE_APP_MICRO_APP
    for (const key in microAppObject) {
      if (Object.hasOwnProperty.call(microAppObject, key) && VUE_APP_MICRO_APP.includes(key)) {
        microApp[key] = isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`
        prefetchApp.push({
          name: key,
          entry: isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`
        })
      }
    }
  } else {
    for (const key in microAppObject) {
      if (Object.hasOwnProperty.call(microAppObject, key)) {
        microApp[key] = isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`
        prefetchApp.push({
          name: key,
          entry: isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`
        })
      }
    }
  }
  return { microApp, prefetchApp }
}
const mainPath = isShow ? 'http://localhost:2000/' : origin + catalog
const microAppPath = (key) =>
  Object.hasOwnProperty.call(microAppObject, key)
    ? isShow
      ? microAppObject[key]
      : `${origin}${catalog}microApp/${key}/`
    : ''
const systemUrl = (url) => {
  if (!url) return ''
  const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/
  const pathExp = new RegExp(Expression)
  return pathExp.test(url) ? url : `${window.location.protocol}//${window.location.hostname}:${url}`
}
const config = {
  // 后台接口配置
  API_URL:
    systemUrl(window?.globalConfig?.API_URL || process.env.VUE_APP_URL) || 'https://productpc.cszysoft.com:8080/lzt',
  origin: origin,
  catalog: catalog,
  mainPath: mainPath,
  microAppPath: microAppPath,
  // 生成子应用配置
  ...microAppData()
}
// console.log(process.env)
console.log(config)
export default config
