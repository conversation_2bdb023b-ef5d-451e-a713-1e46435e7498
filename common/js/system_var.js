import { computed } from 'vue'
import store from '@/store'
import config from '../config'

export const user = computed(() => store.getters.getUserFn)
export const menu = computed(() => store.getters.getMenuFn)
export const theme = computed(() => store.getters.getThemeFn)
export const readConfig = computed(() => store.getters.getReadConfig)
export const openConfig = computed(() => store.getters.getReadOpenConfig)
export const selectUser = computed(() => store.getters.getSelectUser)
export const systemLogo = computed(() => `${config.API_URL}/pageImg/open/logo?areaId=${user.value.areaId}`)
export const IntelligentAssistant = computed(
  () => `${config.API_URL}/pageImg/open/IntelligentAssistant?areaId=${user.value.areaId}`
)
export const systemSign = computed(() => openConfig.value.systemSign)
export const systemName = computed(() => openConfig.value.systemName)
export const platformAreaName = computed(() => openConfig.value.platformAreaName)
export const whetherAiChat = computed(() => readConfig.value.whetherAiChat === 'true')
export const loginNameLineFeedPosition = computed(() => openConfig.value.loginNameLineFeedPosition)
export const systemNameAreaPrefix = computed(() => openConfig.value.systemNameAreaPrefix)
export const file_preview_mode = computed(() => readConfig.value.file_preview_mode)
export const file_preview_open = computed(() => readConfig.value.file_preview_open)
export const whetherUseIntelligentize = computed(() => readConfig.value.whetherUseIntelligentize !== 'false')
export const screenWindowDefault = computed(() => readConfig.value.screenWindowDefault === 'true')
export const queryTypeShow = computed(() => readConfig.value.queryTypeShow !== 'false')
export const systemMobileEncrypt = computed(() => readConfig.value.systemMobileEncrypt === 'true')
export const appOnlyHeader = computed(() => openConfig.value.appOnlyHeader)
export const appDownloadUrl = computed(() => openConfig.value.appDownloadUrl)
export const systemLoginContact = computed(() => openConfig.value.systemLoginContact)
export const defaultPageSize = computed(() => (readConfig.value.pageSize ? Number(readConfig.value.pageSize) : 10))
export const pageSizes = computed(() =>
  readConfig.value.pageSizes ? readConfig.value.pageSizes.split('/').map((v) => Number(v)) : [10, 20, 50, 100, 200]
)

export const systemChatIcon = computed(() => `${config.API_URL}/pageImg/open/chatIcon?areaId=${user.value.areaId}`)
export const layoutBg = computed(() => `${config.API_URL}/pageImg/open/layoutBg?areaId=${user.value.areaId}`)
export const layoutNameBg = computed(() => `${config.API_URL}/pageImg/open/layoutNameBg?areaId=${user.value.areaId}`)
export const layoutChildBg = computed(() => `${config.API_URL}/pageImg/open/layoutChildBg?areaId=${user.value.areaId}`)
export const layoutChildNameBg = computed(
  () => `${config.API_URL}/pageImg/open/layoutChildNameBg?areaId=${user.value.areaId}`
)

export const WorkBenchBgImg = computed(
  () => `${config.API_URL}/pageImg/open/WorkBenchBgImg?areaId=${user.value.areaId}`
)
