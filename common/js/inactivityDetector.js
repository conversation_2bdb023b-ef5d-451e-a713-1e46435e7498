class InactivityDetector {
  constructor(
    inactivityTimeout = 5 * 60 * 1000, // 默认5分钟
    checkInterval = 1000, // 每秒检查一次
    onInactiveCallback = () => {}
  ) {
    this.lastActivityTime = Date.now()
    this.inactivityTimeout = inactivityTimeout
    this.checkInterval = checkInterval
    this.onInactiveCallback = onInactiveCallback
    this.timer = null
    this.isRunning = false
  }

  /**
   * 开始监听用户活动
   */
  start() {
    if (this.isRunning) return

    // 添加事件监听器
    this.addEventListeners()

    // 开始定时检查
    this.startTimer()

    this.isRunning = true
  }

  /**
   * 停止监听
   */
  stop() {
    if (!this.isRunning) return

    // 移除事件监听器
    this.removeEventListeners()

    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    this.isRunning = false
  }

  /**
   * 重置最后活动时间
   */
  reset() {
    this.lastActivityTime = Date.now()
  }

  /**
   * 获取用户不活跃时间（毫秒）
   */
  getInactivityTime() {
    return Date.now() - this.lastActivityTime
  }

  /**
   * 设置不活跃回调函数
   */
  setInactiveCallback(callback) {
    this.onInactiveCallback = callback
  }

  /**
   * 设置不活跃超时时间
   */
  setTimeout(timeout) {
    this.inactivityTimeout = timeout
  }

  /**
   * 重新设置不活跃时间并重置计时器
   * @param {number} timeout - 新的超时时间（毫秒）
   */
  resetTimeout(timeout) {
    // 设置新的超时时间
    this.setTimeout(timeout)
    // 重置最后活动时间
    this.reset()
    // 如果正在运行，重新启动定时器
    if (this.isRunning) {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.startTimer()
    }
  }

  /**
   * 获取当前设置的不活跃超时时间
   */
  getTimeout() {
    return this.inactivityTimeout
  }

  addEventListeners() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click', 'keydown']

    events.forEach((event) => {
      window.addEventListener(event, this.handleUserActivity.bind(this))
    })
  }

  removeEventListeners() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click', 'keydown']

    events.forEach((event) => {
      window.removeEventListener(event, this.handleUserActivity.bind(this))
    })
  }

  handleUserActivity() {
    this.lastActivityTime = Date.now()
  }

  startTimer() {
    this.timer = setInterval(() => {
      const currentTime = Date.now()
      const inactiveTime = currentTime - this.lastActivityTime

      if (inactiveTime >= this.inactivityTimeout) {
        this.onInactiveCallback()
      }
    }, this.checkInterval)
  }
}

// 创建全局单例实例
const inactivityDetector = new InactivityDetector()

// 导出单例实例
export default inactivityDetector

// 为了向后兼容，也导出类
export { InactivityDetector }
