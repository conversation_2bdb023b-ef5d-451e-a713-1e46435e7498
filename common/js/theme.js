import store from '@/store'
export const changeTheme = (data, name = '--zy-el') => {
  const primary = sessionStorage.getItem('primary') || data?.primaryColor
  const obj = {
    primary: primary || '#bc1d1d',
    success: data?.success || '',
    warning: data?.warning || '',
    danger: data?.danger || '',
    info: data?.info || '',
    height: data?.height || '36px',
    height_routine: data?.heightRoutine || '32px',
    height_secondary: data?.heightSecondary || '26px',
    line_height: data?.lineHeight || '1.5',
    system_font_size: data?.systemFontSize || '28px',
    title_font_size: data?.titleFontSize || '26px',
    navigation_font_size: data?.navigationFontSize || '18px',
    name_font_size: data?.nameFontSize || '16px',
    text_font_size: data?.textFontSize || '14px',
    text_color_primary: data?.textColorPrimary || '#303133',
    text_color_regular: data?.textColorRegular || '#606266',
    text_color_secondary: data?.textColorSecondary || '#909399',
    border_color: data?.borderColor || '#dcdfe6',
    border_color_light: data?.borderColorLight || '#e4e7ed',
    border_color_lighter: data?.borderColorLighter || '#ebeef5',
    border_color_extra_light: data?.borderColorExtraLight || '#f2f6fc',
    distance_one: data?.distanceOne || '40px',
    distance_two: data?.distanceTwo || '20px',
    distance_three: data?.distanceThree || '16px',
    distance_four: data?.distanceFour || '12px',
    distance_five: data?.distanceFive || '10px',
    font_name_distance_five: data?.fontNameDistanceFive || '6px',
    font_text_distance_five: data?.fontTextDistanceFive || '3px',
    border_radius_base: data?.borderRadiusBase || '4px',
    border_radius_small: data?.borderRadiusSmall || '2px',
    box_shadow: data?.boxShadow || '0px 12px 32px 4px rgba(0,0,0,.04),0px 8px 20px rgba(0,0,0,.08)',
    box_shadow_light: data?.boxShadowLight || '0px 0px 12px rgba(0,0,0,.12)',
    box_shadow_lighter: data?.boxShadowLighter || '0px 0px 6px rgba(0,0,0,.12)',
    form_width_one: data?.formWidthOne || 'calc(var(--zy-distance-one) + (var(--zy-distance-two) * 4) + (var(--zy-form-width-item) * 3))',
    form_width_two: data?.formWidthTwo || 'calc(var(--zy-distance-one) + (var(--zy-distance-two) * 3) + (var(--zy-form-width-item) * 2))',
    form_width_item: data?.formWidthItem || '290px',
    form_distance_bottom: data?.formDistanceBottom || '22px'
  }
  store.commit('setTheme', obj)
  const el = document.documentElement
  // 主题颜色
  if (obj.primary) { setThemeColor('primary', obj.primary, name) }
  if (obj.success) { setThemeColor('success', obj.success, name) }
  if (obj.warning) { setThemeColor('warning', obj.warning, name) }
  if (obj.danger) { setThemeColor('danger', obj.danger, name) }
  if (obj.danger) { setThemeColor('error', obj.danger, name) }
  if (obj.info) { setThemeColor('info', obj.info, name) }
  // 高度
  if (obj.height) { el.style.setProperty('--zy-height', obj.height) }
  if (obj.height_routine) { el.style.setProperty('--zy-height-routine', obj.height_routine) }
  if (obj.height_secondary) { el.style.setProperty('--zy-height-secondary', obj.height_secondary) }
  // 行高
  if (obj.line_height) { el.style.setProperty('--zy-line-height', obj.line_height) }
  // 字体大小
  if (obj.system_font_size) { el.style.setProperty('--zy-system-font-size', obj.system_font_size) }
  if (obj.title_font_size) { el.style.setProperty('--zy-title-font-size', obj.title_font_size) }
  if (obj.navigation_font_size) { el.style.setProperty('--zy-navigation-font-size', obj.navigation_font_size) }
  if (obj.name_font_size) { el.style.setProperty('--zy-name-font-size', obj.name_font_size) }
  if (obj.text_font_size) { el.style.setProperty('--zy-text-font-size', obj.text_font_size) }
  // 文字颜色
  if (obj.text_color_primary) { el.style.setProperty(`${name}-text-color-primary`, obj.text_color_primary) }
  if (obj.text_color_regular) { el.style.setProperty(`${name}-text-color-regular`, obj.text_color_regular) }
  if (obj.text_color_secondary) { el.style.setProperty(`${name}-text-color-secondary`, obj.text_color_secondary) }
  // 边框颜色
  if (obj.border_color) { el.style.setProperty(`${name}-border-color`, obj.border_color) }
  if (obj.border_color_light) { el.style.setProperty(`${name}-border-color-light`, obj.border_color_light) }
  if (obj.border_color_lighter) { el.style.setProperty(`${name}-border-color-lighter`, obj.border_color_lighter) }
  if (obj.border_color_extra_light) { el.style.setProperty(`${name}-border-color-extra-light`, obj.border_color_extra_light) }
  // 边距
  if (obj.distance_one) { el.style.setProperty('--zy-distance-one', obj.distance_one) }
  if (obj.distance_two) { el.style.setProperty('--zy-distance-two', obj.distance_two) }
  if (obj.distance_three) { el.style.setProperty('--zy-distance-three', obj.distance_three) }
  if (obj.distance_four) { el.style.setProperty('--zy-distance-four', obj.distance_four) }
  if (obj.distance_five) { el.style.setProperty('--zy-distance-five', obj.distance_five) }
  if (obj.font_name_distance_five) { el.style.setProperty('--zy-font-name-distance-five', obj.font_name_distance_five) }
  if (obj.font_text_distance_five) { el.style.setProperty('--zy-font-text-distance-five', obj.font_text_distance_five) }
  // 圆角
  if (obj.border_radius_base) { el.style.setProperty('--el-border-radius-base', obj.border_radius_base) }
  if (obj.border_radius_small) { el.style.setProperty('--el-border-radius-small', obj.border_radius_small) }
  // 盒子阴影
  if (obj.box_shadow) { el.style.setProperty(`${name}-box-shadow`, obj.box_shadow) }
  if (obj.box_shadow_light) { el.style.setProperty(`${name}-box-shadow-light`, obj.box_shadow_light) }
  if (obj.box_shadow_lighter) { el.style.setProperty(`${name}-box-shadow-lighter`, obj.box_shadow_lighter) }
  // 表单宽度，边距
  if (obj.form_width_one) { el.style.setProperty('--zy-form-width-one', obj.form_width_one) }
  if (obj.form_width_two) { el.style.setProperty('--zy-form-width-two', obj.form_width_two) }
  if (obj.form_width_item) { el.style.setProperty('--zy-form-width-item', obj.form_width_item) }
  if (obj.form_distance_bottom) { el.style.setProperty('--zy-form-distance-bottom', obj.form_distance_bottom) }
}
export const setThemeColor = (type, color, name) => {
  const el = document.documentElement
  el.style.setProperty(`${name}-color-${type}`, color)
  el.style.setProperty(`${name}-color-${type}-light-3`, colourBlend(color, '#ffffff', 3 / 10))
  el.style.setProperty(`${name}-color-${type}-light-5`, colourBlend(color, '#ffffff', 5 / 10))
  el.style.setProperty(`${name}-color-${type}-light-7`, colourBlend(color, '#ffffff', 7 / 10))
  el.style.setProperty(`${name}-color-${type}-light-8`, colourBlend(color, '#ffffff', 8 / 10))
  el.style.setProperty(`${name}-color-${type}-light-9`, colourBlend(color, '#ffffff', 9 / 10))
  el.style.setProperty(`${name}-color-${type}-dark-2`, colourBlend(color, '#000000', 2 / 10))
}

export const colourBlend = (c1, c2, ratio) => {
  ratio = Math.max(Math.min(Number(ratio), 1), 0)
  let r1 = parseInt(c1.substring(1, 3), 16)
  let g1 = parseInt(c1.substring(3, 5), 16)
  let b1 = parseInt(c1.substring(5, 7), 16)
  let r2 = parseInt(c2.substring(1, 3), 16)
  let g2 = parseInt(c2.substring(3, 5), 16)
  let b2 = parseInt(c2.substring(5, 7), 16)
  let r = Math.round(r1 * (1 - ratio) + r2 * ratio)
  let g = Math.round(g1 * (1 - ratio) + g2 * ratio)
  let b = Math.round(b1 * (1 - ratio) + b2 * ratio)
  r = ('0' + (r || 0).toString(16)).slice(-2)
  g = ('0' + (g || 0).toString(16)).slice(-2)
  b = ('0' + (b || 0).toString(16)).slice(-2)
  return '#' + r + g + b
}

export const colourBlendRgb = (c1, c2, ratio) => {
  ratio = Math.max(Math.min(Number(ratio), 1), 0)
  let r1 = parseInt(c1.substring(1, 3), 16)
  let g1 = parseInt(c1.substring(3, 5), 16)
  let b1 = parseInt(c1.substring(5, 7), 16)
  let r2 = parseInt(c2.substring(1, 3), 16)
  let g2 = parseInt(c2.substring(3, 5), 16)
  let b2 = parseInt(c2.substring(5, 7), 16)
  let r = Math.round(r1 * (1 - ratio) + r2 * ratio)
  let g = Math.round(g1 * (1 - ratio) + g2 * ratio)
  let b = Math.round(b1 * (1 - ratio) + b2 * ratio)
  return `rgb(${r},${g},${b})`
}

export const colourBlendRgba = (c1, c2, ratio, a) => {
  ratio = Math.max(Math.min(Number(ratio), 1), 0)
  let r1 = parseInt(c1.substring(1, 3), 16)
  let g1 = parseInt(c1.substring(3, 5), 16)
  let b1 = parseInt(c1.substring(5, 7), 16)
  let r2 = parseInt(c2.substring(1, 3), 16)
  let g2 = parseInt(c2.substring(3, 5), 16)
  let b2 = parseInt(c2.substring(5, 7), 16)
  let r = Math.round(r1 * (1 - ratio) + r2 * ratio)
  let g = Math.round(g1 * (1 - ratio) + g2 * ratio)
  let b = Math.round(b1 * (1 - ratio) + b2 * ratio)
  return `rgba(${r},${g},${b},${a})`
}
