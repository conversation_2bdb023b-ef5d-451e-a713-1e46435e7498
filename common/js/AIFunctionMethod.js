import api from '@/api'
import { ref, onMounted } from 'vue'
import { whetherAiChat } from 'common/js/system_var.js'

export const AIFunctionMethod = (code) => {
  const isAIFunction = ref(false)
  const AISceneDetail = async (code) => {
    const { data } = await api.AISceneDetail({ detailId: code })
    isAIFunction.value = data.isUsing
  }
  onMounted(() => {
    if (whetherAiChat.value) AISceneDetail(code)
  })
  return { isAIFunction }
}
