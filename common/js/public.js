import api from '@/api'
import store from '@/store'
import { saveAs } from 'file-saver'
import { ElMessage, ElMessageBox } from 'element-plus'
const isShow = process.env.NODE_ENV === 'development'
export const globalLocation = (name) => isShow ? 'http://localhost:2000/' : window.location.href.substring(0, window.location.href.indexOf(name))
export const globalFileLocation = ({ name, fileId, fileType, fileName, fileSize, isDownload = 'yes' }) => {
  if (['zip', 'rar'].includes(fileType)) {
    ElMessageBox.confirm('当前文件不支持预览, 是否下载?', '提示', {
      confirmButtonText: '下载',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      store.commit('setDownloadFile', { fileId, fileSize, fileName, fileType })
    }).catch(() => { ElMessage({ type: 'info', message: '已取消下载' }) })
    return
  }
  var routeUrl = name
  if (isShow) {
    routeUrl = `${origin}/LegislationFilePreview`
  } else {
    routeUrl = `${origin}/microApp/legislationGeneral/LegislationFilePreview`
  }
  window.open(`${routeUrl}?id=${fileId}&fileType=${fileType}&fileName=${fileName}&fileSize=${fileSize}&isDownload=${isDownload}`, '_blank')
}

export const inSystemFileDownload = async (fileId, fileName) => {
  const res = await api.inSystemFileDownload(fileId)
  saveAs(new Blob([res]), fileName)
}
