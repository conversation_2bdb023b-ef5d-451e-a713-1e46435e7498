import api from '@/api'
import store from '@/store'
import router from '@/router'
import { changeTheme } from './theme'
import onWholeWaterMark from './water-market.js'
import { setTimeListener } from './GlobalTimeListener.js'
import { ElMessage } from 'element-plus'
export const configCode = [
  'systemType',
  'systemName',
  'platformAreaId',
  'systemWatermark',
  'appOnlyHeader',
  'pageSize',
  'pageSizes',
  'whetherAiChat',
  'file_preview_mode',
  'file_preview_open',
  'screenWindowDefault',
  'queryTypeShow',
  'systemMobileEncrypt',
  'systemInactivityTimeout',
  'whetherUseIntelligentize'
]
export const openConfigCode = [
  'systemSign',
  'systemPlatform',
  'systemName',
  'platformAreaName',
  'loginSystemName',
  'loginNameLineFeedPosition',
  'systemNameAreaPrefix',
  'systemGrayscale',
  'appOnlyHeader',
  'appDownloadUrl',
  'forbidWeakPassword',
  // 'whetherRegionSelect',
  'systemLoginContact',
  'noVerifyAdmin',
  'DetectionVersion',
  'VersionUpdatePrompt'
]
export const currentTheme = async () => {
  try {
    const { data } = await api.currentTheme({})
    changeTheme(data)
  } catch (err) {
    if (err?.code === 302) {
      currentTheme()
    } else {
      changeTheme()
    }
  }
}

export const loginMenu = async () => {
  const { data: menu } = await api.loginMenu()
  sessionStorage.setItem('menu', JSON.stringify(menu))
  store.commit('setMenu', menu)
}
export const loginArea = async () => {
  const { data: area } = await api.loginAreas()
  sessionStorage.setItem('area', JSON.stringify(area))
  store.commit('setArea', area)
}
export const loginRole = async () => {
  const { data: role } = await api.loginRole()
  sessionStorage.setItem('role', JSON.stringify(role))
  store.commit('setRole', role)
}

export const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: configCode })
  if (data?.systemWatermark && data?.systemWatermark !== 'false') {
    if (data?.systemWatermark === 'user') {
      onWholeWaterMark(store.state?.user?.userName)
    } else {
      onWholeWaterMark(data?.systemWatermark === 'true' ? data?.systemName : data?.systemWatermark)
    }
  }
  store.commit('setReadConfig', data)
}
export const globalReadOpenConfig = async (callback) => {
  try {
    const { data } = await api.globalReadOpenConfig({ codes: openConfigCode })
    sessionStorage.setItem('noVerifyAdmin', data.noVerifyAdmin)
    sessionStorage.setItem('DetectionVersion', data.DetectionVersion)
    sessionStorage.setItem('VersionUpdatePrompt', data.VersionUpdatePrompt)
    document.title = data?.systemName
    if (data?.systemGrayscale === 'true') {
      window.document.documentElement.setAttribute('grey', '1')
    }
    store.commit('setReadOpenConfig', data)
    if (callback) callback(data)
  } catch (err) {
    if (callback) callback()
  }
}

export const loginRefreshToken = async () => {
  const refreshToken = sessionStorage.getItem('refreshToken')
  if (!refreshToken) return
  try {
    const { data } = await api.login({ grant_type: 'refresh_token', refresh_token: refreshToken, }, { token: 'basic enlzb2Z0Onp5c29mdCo2MDc5' })
    const refreshTime = data.expireDate + (data.expire * 1000)
    sessionStorage.setItem('token', data.token)
    sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)
    sessionStorage.setItem('expires', data.expires_in)
    sessionStorage.setItem('refreshTime', refreshTime)
    sessionStorage.setItem('refreshToken', data.refreshToken.value)
    sessionStorage.setItem('expiration', data.refreshToken.expiration)
    const refreshTokenExpiration = Number(refreshTime) - 300000
    setTimeListener(refreshTokenExpiration, () => {
      console.log(refreshTokenExpiration, '刷新令牌时间到了！')
      loginRefreshToken()
    })
  } catch (error) {
    console.log(error)
    loginOut('为保障您的账户安全，请重新登录。')
  }
}
export const loginOutClear = () => {
  sessionStorage.clear()
  const goal_login_router_path = localStorage.getItem('goal_login_router_path')
  if (goal_login_router_path) {
    const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''
    router.push({
      path: goal_login_router_path,
      query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}
    })
  } else {
    router.push({ path: '/LoginView' })
  }
  store.commit('setState')
  globalReadOpenConfig()
  // store.state.socket.disconnect()
  // store.state.socket = null
}
export const loginOut = async (text, type) => {
  const { code } = await api.loginOut()
  if (code === 200) {
    loginOutClear()
    ElMessage({ message: text, showClose: true, type: type || 'success' })
  }
}

export const rongCloudToken = async (type, appkey, url, key) => {
  const res = await api.rongCloud(
    url,
    {
      type: 'getToken',
      userId: key + store.state.user.accountId,
      userName: store.state.user.userName,
      userPortrait: store.state.user.image,
      environment: appkey === 'y745wfm84be5v' ? '1' : '2'
    },
    type
  )
  if (type) {
    store.commit('setRongCloudToken', res.data)
  } else {
    store.commit('setRongCloudToken', res.token)
  }
}
