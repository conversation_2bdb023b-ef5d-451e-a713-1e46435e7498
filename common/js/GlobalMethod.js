import api from '@/api'
import store from '@/store'
import { changeTheme } from './theme'
import onWholeWaterMark from './water-market.js'
export const configCode = [
  'systemType',
  'systemName',
  'platformAreaId',
  'systemWatermark',
  'appOnlyHeader',
  'pageSize',
  'pageSizes',
  'whetherAiChat',
  'file_preview_mode',
  'file_preview_open',
  'screenWindowDefault',
  'queryTypeShow',
  'systemMobileEncrypt',
  'systemInactivityTimeout',
  'whetherUseIntelligentize'
]
export const openConfigCode = [
  'systemSign',
  'systemPlatform',
  'systemName',
  'platformAreaName',
  'loginNameLineFeedPosition',
  'systemNameAreaPrefix',
  'systemGrayscale',
  'appOnlyHeader',
  'appDownloadUrl',
  'forbidWeakPassword',
  'whetherRegionSelect',
  'systemLoginContact',
  'noVerifyAdmin',
  'DetectionVersion',
  'VersionUpdatePrompt'
]
export const currentTheme = async () => {
  try {
    const { data } = await api.currentTheme({})
    changeTheme(data)
  } catch (err) {
    if (err?.code === 302) {
      currentTheme()
    } else {
      changeTheme()
    }
  }
}
export const loginMenu = async () => {
  const { data: menu } = await api.loginMenu()
  sessionStorage.setItem('menu', JSON.stringify(menu))
  store.commit('setMenu', menu)
}
export const loginArea = async () => {
  const { data: area } = await api.loginAreas()
  sessionStorage.setItem('area', JSON.stringify(area))
  store.commit('setArea', area)
}
export const loginRole = async () => {
  const { data: role } = await api.loginRole()
  sessionStorage.setItem('role', JSON.stringify(role))
  store.commit('setRole', role)
}

export const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: configCode })
  if (data?.systemWatermark && data?.systemWatermark !== 'false') {
    if (data?.systemWatermark === 'user') {
      onWholeWaterMark(store.state?.user?.userName)
    } else {
      onWholeWaterMark(data?.systemWatermark === 'true' ? data?.systemName : data?.systemWatermark)
    }
  }
  store.commit('setReadConfig', data)
}
export const globalReadOpenConfig = async (callback) => {
  try {
    const { data } = await api.globalReadOpenConfig({ codes: openConfigCode })
    sessionStorage.setItem('noVerifyAdmin', data.noVerifyAdmin)
    sessionStorage.setItem('DetectionVersion', data.DetectionVersion)
    sessionStorage.setItem('VersionUpdatePrompt', data.VersionUpdatePrompt)
    document.title = data?.systemName
    if (data?.systemGrayscale === 'true') {
      window.document.documentElement.setAttribute('grey', '1')
    }
    store.commit('setReadOpenConfig', data)
    if (callback) callback(data)
  } catch (err) {
    if (callback) callback()
  }
}

export const rongCloudToken = async (type, appkey, url, key) => {
  const res = await api.rongCloud(
    url,
    {
      type: 'getToken',
      userId: key + store.state.user.accountId,
      userName: store.state.user.userName,
      userPortrait: store.state.user.image,
      environment: appkey === 'y745wfm84be5v' ? '1' : '2'
    },
    type
  )
  if (type) {
    store.commit('setRongCloudToken', res.data)
  } else {
    store.commit('setRongCloudToken', res.token)
  }
}
