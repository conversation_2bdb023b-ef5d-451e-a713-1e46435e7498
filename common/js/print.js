import api from '@/api'
import { MicroGlobal } from 'common/config/qiankun'
import { ElLoading } from 'element-plus'

export const Print = {
  /**
   * 初始化
   * @param {HTMLElement} dom - 要打印的DOM元素
   * @param {Object} options - 配置选项
   * @param {boolean} options.waitForImages - 是否等待图片加载完成，默认true
   * @param {number} options.imageTimeout - 图片加载超时时间(ms)，默认10000
   * @param {boolean} options.showLoading - 是否显示加载动画，默认true
   */
  init(dom, options = {}) {
    const config = {
      waitForImages: options.waitForImages !== false, // 默认等待图片加载
      imageTimeout: options.imageTimeout || 10000,
      showLoading: options.showLoading !== false // 默认显示加载动画
    }
    this.writeIframe(this.getStyle() + dom.outerHTML, config)
  },
  /**
   * 复制原网页的样式
   */
  getStyle() {
    var str = ''
    var styles = []
    const qiankun = new MicroGlobal()
    if (window.__POWERED_BY_QIANKUN__) {
      styles = document.querySelector(`#${qiankun.name}`).querySelectorAll('style,link')
    } else {
      styles = document.querySelector('head').querySelectorAll('style,link')
    }
    for (var i = 0; i < styles.length; i++) {
      str += styles[i].outerHTML
    }
    str +=
      '<style>*{margin: 0;padding: 0;-webkit-print-color-adjust: exact;}html,body{height: auto!important;box-sizing: border-box;}.no-print{display:none;}</style>'
    return str
  },
  /**
   * 创建iframe
   */
  writeIframe(content, config) {
    var iframe = document.createElement('iframe')
    var iframeEl = document.body.appendChild(iframe)
    iframe.id = 'myIframe'
    iframe.setAttribute('style', 'position:absolute;width:0;height:0;top:-10px;left:-10px;')

    // 创建加载动画
    let loadingInstance = null
    if (config.showLoading) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '正在准备打印内容...',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'print-loading'
      })
    }

    const processPrint = (res) => {
      const w = iframeEl.contentWindow || iframeEl.contentDocument
      const doc = iframeEl.contentDocument || iframeEl.contentWindow.document
      doc.open()
      doc.write(content)
      this.changeTheme(iframe, res?.data)

      if (config.waitForImages) {
        // 更新加载文本
        if (loadingInstance) {
          loadingInstance.setText('正在加载图片...')
        }

        // 等待所有图片加载完成
        this.waitForImagesLoaded(doc, config.imageTimeout, loadingInstance)
          .then(() => {
            if (loadingInstance) {
              loadingInstance.setText('正在生成打印预览...')
            }
            setTimeout(() => {
              doc.close()
              this.toPrint(w)
              if (loadingInstance) {
                loadingInstance.close()
              }
              setTimeout(() => {
                document.body.removeChild(iframe)
              }, 1000)
            }, 500)
          })
          .catch((error) => {
            // 如果图片加载超时，仍然进行打印
            console.warn('图片加载超时，继续打印:', error.message)
            if (loadingInstance) {
              loadingInstance.setText('正在生成打印预览...')
            }
            setTimeout(() => {
              doc.close()
              this.toPrint(w)
              if (loadingInstance) {
                loadingInstance.close()
              }
              setTimeout(() => {
                document.body.removeChild(iframe)
              }, 1000)
            }, 500)
          })
      } else {
        // 不等待图片加载，直接打印
        if (loadingInstance) {
          loadingInstance.setText('正在生成打印预览...')
        }
        setTimeout(() => {
          doc.close()
          this.toPrint(w)
          if (loadingInstance) {
            loadingInstance.close()
          }
          setTimeout(() => {
            document.body.removeChild(iframe)
          }, 1000)
        }, 500) // 给一个较短的延时让DOM渲染
      }
    }

    api
      .currentTheme({})
      .then((res) => {
        processPrint(res)
      })
      .catch(() => {
        processPrint()
      })
  },
  /**
   * 等待所有图片加载完成
   */
  waitForImagesLoaded(doc, timeout = 10000, loadingInstance) {
    return new Promise((resolve, reject) => {
      const images = doc.querySelectorAll('img')
      if (images.length === 0) {
        resolve()
        return
      }

      let loadedCount = 0
      let errorCount = 0
      const totalImages = images.length
      const timeoutId = setTimeout(() => {
        reject(new Error(`图片加载超时，已加载 ${loadedCount}/${totalImages} 张图片`))
      }, timeout)

      const checkComplete = () => {
        loadedCount++
        // 更新加载进度
        if (loadingInstance) {
          const progress = Math.round((loadedCount / totalImages) * 100)
          loadingInstance.setText(`正在加载图片... (${loadedCount}/${totalImages}) ${progress}%`)
        }

        if (loadedCount === totalImages) {
          clearTimeout(timeoutId)
          if (errorCount > 0) {
            console.warn(`图片加载完成，但有 ${errorCount} 张图片加载失败`)
            if (loadingInstance) {
              loadingInstance.setText(`图片加载完成，${errorCount} 张图片加载失败`)
            }
          }
          resolve()
        }
      }

      images.forEach((img, index) => {
        if (img.complete && img.naturalHeight !== 0) {
          // 图片已经加载完成
          checkComplete()
        } else {
          // 监听图片加载事件
          img.onload = () => {
            checkComplete()
          }
          img.onerror = () => {
            errorCount++
            console.warn(`图片加载失败: ${img.src || `第${index + 1}张图片`}`)
            checkComplete() // 即使加载失败也继续
          }

          // 如果图片src为空或者无效，直接算作完成
          if (!img.src || img.src === '' || img.src === 'data:') {
            checkComplete()
          }
        }
      })
    })
  },
  /**
  打印
  */
  toPrint(f) {
    try {
      setTimeout(() => {
        f.focus()
        try {
          if (!f.document.execCommand('print', false, null)) {
            f.print()
          }
        } catch (e) {
          f.print()
        }
        f.close()
      }, 10)
    } catch (err) {
      console.log('err', err)
    }
  },
  changeTheme(iframe, data) {
    const obj = {
      primary: data?.primaryColor || '#bc1d1d',
      success: data?.success || '',
      warning: data?.warning || '',
      danger: data?.danger || '',
      info: data?.info || '',
      height: data?.height || '36px',
      height_routine: data?.heightRoutine || '32px',
      height_secondary: data?.heightSecondary || '26px',
      line_height: data?.lineHeight || '1.5',
      system_font_size: data?.systemFontSize || '28px',
      title_font_size: data?.titleFontSize || '26px',
      navigation_font_size: data?.navigationFontSize || '18px',
      name_font_size: data?.nameFontSize || '16px',
      text_font_size: data?.textFontSize || '14px',
      text_color_primary: data?.textColorPrimary || '#303133',
      text_color_regular: data?.textColorRegular || '#606266',
      text_color_secondary: data?.textColorSecondary || '#909399',
      border_color: data?.borderColor || '#dcdfe6',
      border_color_light: data?.borderColorLight || '#e4e7ed',
      border_color_lighter: data?.borderColorLighter || '#ebeef5',
      border_color_extra_light: data?.borderColorExtraLight || '#f2f6fc',
      distance_one: data?.distanceOne || '40px',
      distance_two: data?.distanceTwo || '20px',
      distance_three: data?.distanceThree || '16px',
      distance_four: data?.distanceFour || '12px',
      distance_five: data?.distanceFive || '10px',
      font_name_distance_five: data?.fontNameDistanceFive || '6px',
      font_text_distance_five: data?.fontTextDistanceFive || '3px',
      border_radius_base: data?.borderRadiusBase || '4px',
      border_radius_small: data?.borderRadiusSmall || '2px',
      box_shadow: data?.boxShadow || '0px 12px 32px 4px rgba(0,0,0,.04),0px 8px 20px rgba(0,0,0,.08)',
      box_shadow_light: data?.boxShadowLight || '0px 0px 12px rgba(0,0,0,.12)',
      box_shadow_lighter: data?.boxShadowLighter || '0px 0px 6px rgba(0,0,0,.12)',
      form_width_one:
        data?.formWidthOne ||
        'calc(var(--zy-distance-one) + (var(--zy-distance-two) * 4) + (var(--zy-form-width-item) * 3))',
      form_width_two:
        data?.formWidthTwo ||
        'calc(var(--zy-distance-one) + (var(--zy-distance-two) * 3) + (var(--zy-form-width-item) * 2))',
      form_width_item: data?.formWidthItem || '290px',
      form_distance_bottom: data?.formDistanceBottom || '22px'
    }
    const el = iframe.contentWindow.document.documentElement
    // 主题颜色
    if (obj.primary) {
      this.setThemeColor(el, 'primary', obj.primary)
    }
    if (obj.success && success !== '#67c23a') {
      this.setThemeColor(el, 'success', obj.success)
    }
    if (obj.warning && warning !== '#e6a23c') {
      this.setThemeColor(el, 'warning', obj.warning)
    }
    if (obj.danger && danger !== '#f56c6c') {
      this.setThemeColor(el, 'danger', obj.danger)
    }
    if (obj.danger && danger !== '#f56c6c') {
      this.setThemeColor(el, 'error', obj.danger)
    }
    if (obj.info && info !== '#909399') {
      this.setThemeColor(el, 'info', obj.info)
    }
    // 高度
    if (obj.height) {
      el.style.setProperty('--zy-height', obj.height)
    }
    if (obj.height_routine) {
      el.style.setProperty('--zy-height-routine', obj.height_routine)
    }
    if (obj.height_secondary) {
      el.style.setProperty('--zy-height-secondary', obj.height_secondary)
    }
    // 行高
    if (obj.line_height) {
      el.style.setProperty('--zy-line-height', obj.line_height)
    }
    // 字体大小
    if (obj.system_font_size) {
      el.style.setProperty('--zy-system-font-size', obj.system_font_size)
    }
    if (obj.title_font_size) {
      el.style.setProperty('--zy-title-font-size', obj.title_font_size)
    }
    if (obj.navigation_font_size) {
      el.style.setProperty('--zy-navigation-font-size', obj.navigation_font_size)
    }
    if (obj.name_font_size) {
      el.style.setProperty('--zy-name-font-size', obj.name_font_size)
    }
    if (obj.text_font_size) {
      el.style.setProperty('--zy-text-font-size', obj.text_font_size)
    }
    // 文字颜色
    if (obj.text_color_primary) {
      el.style.setProperty('--zy-el-text-color-primary', obj.text_color_primary)
    }
    if (obj.text_color_regular) {
      el.style.setProperty('--zy-el-text-color-regular', obj.text_color_regular)
    }
    if (obj.text_color_secondary) {
      el.style.setProperty('--zy-el-text-color-secondary', obj.text_color_secondary)
    }
    // 边框颜色
    if (obj.border_color) {
      el.style.setProperty('--zy-el-border-color', obj.border_color)
    }
    if (obj.border_color_light) {
      el.style.setProperty('--zy-el-border-color-light', obj.border_color_light)
    }
    if (obj.border_color_lighter) {
      el.style.setProperty('--zy-el-border-color-lighter', obj.border_color_lighter)
    }
    if (obj.border_color_extra_light) {
      el.style.setProperty('--zy-el-border-color-extra-light', obj.border_color_extra_light)
    }
    // 边距
    if (obj.distance_one) {
      el.style.setProperty('--zy-distance-one', obj.distance_one)
    }
    if (obj.distance_two) {
      el.style.setProperty('--zy-distance-two', obj.distance_two)
    }
    if (obj.distance_three) {
      el.style.setProperty('--zy-distance-three', obj.distance_three)
    }
    if (obj.distance_four) {
      el.style.setProperty('--zy-distance-four', obj.distance_four)
    }
    if (obj.distance_five) {
      el.style.setProperty('--zy-distance-five', obj.distance_five)
    }
    if (obj.font_name_distance_five) {
      el.style.setProperty('--zy-font-name-distance-five', obj.font_name_distance_five)
    }
    if (obj.font_text_distance_five) {
      el.style.setProperty('--zy-font-text-distance-five', obj.font_text_distance_five)
    }
    // 圆角
    if (obj.border_radius_base) {
      el.style.setProperty('--el-border-radius-base', obj.border_radius_base)
    }
    if (obj.border_radius_small) {
      el.style.setProperty('--el-border-radius-small', obj.border_radius_small)
    }
    // 盒子阴影
    if (obj.box_shadow) {
      el.style.setProperty('--zy-el-box-shadow', obj.box_shadow)
    }
    if (obj.box_shadow_light) {
      el.style.setProperty('--zy-el-box-shadow-light', obj.box_shadow_light)
    }
    if (obj.box_shadow_lighter) {
      el.style.setProperty('--zy-el-box-shadow-lighter', obj.box_shadow_lighter)
    }
    // 表单宽度，边距
    if (obj.form_width_one) {
      el.style.setProperty('--zy-form-width-one', obj.form_width_one)
    }
    if (obj.form_width_two) {
      el.style.setProperty('--zy-form-width-two', obj.form_width_two)
    }
    if (obj.form_width_item) {
      el.style.setProperty('--zy-form-width-item', obj.form_width_item)
    }
    if (obj.form_distance_bottom) {
      el.style.setProperty('--zy-form-distance-bottom', obj.form_distance_bottom)
    }
  },
  setThemeColor(el, type, color) {
    el.style.setProperty(`--zy-el-color-${type}`, color)
    el.style.setProperty(`--zy-el-color-${type}-light-3`, this.colourBlend(color, '#ffffff', 3 / 10))
    el.style.setProperty(`--zy-el-color-${type}-light-5`, this.colourBlend(color, '#ffffff', 5 / 10))
    el.style.setProperty(`--zy-el-color-${type}-light-7`, this.colourBlend(color, '#ffffff', 7 / 10))
    el.style.setProperty(`--zy-el-color-${type}-light-8`, this.colourBlend(color, '#ffffff', 8 / 10))
    el.style.setProperty(`--zy-el-color-${type}-light-9`, this.colourBlend(color, '#ffffff', 9 / 10))
    el.style.setProperty(`--zy-el-color-${type}-dark-2`, this.colourBlend(color, '#000000', 2 / 10))
  },
  colourBlend(c1, c2, ratio) {
    ratio = Math.max(Math.min(Number(ratio), 1), 0)
    let r1 = parseInt(c1.substring(1, 3), 16)
    let g1 = parseInt(c1.substring(3, 5), 16)
    let b1 = parseInt(c1.substring(5, 7), 16)
    let r2 = parseInt(c2.substring(1, 3), 16)
    let g2 = parseInt(c2.substring(3, 5), 16)
    let b2 = parseInt(c2.substring(5, 7), 16)
    let r = Math.round(r1 * (1 - ratio) + r2 * ratio)
    let g = Math.round(g1 * (1 - ratio) + g2 * ratio)
    let b = Math.round(b1 * (1 - ratio) + b2 * ratio)
    r = ('0' + (r || 0).toString(16)).slice(-2)
    g = ('0' + (g || 0).toString(16)).slice(-2)
    b = ('0' + (b || 0).toString(16)).slice(-2)
    return '#' + r + g + b
  }
}

/*
使用示例：

// 基本使用（等待图片加载，显示加载动画）
Print.init(document.getElementById('printArea'))

// 不等待图片加载，直接打印
Print.init(document.getElementById('printArea'), {
  waitForImages: false
})

// 自定义图片加载超时时间
Print.init(document.getElementById('printArea'), {
  imageTimeout: 15000 // 15秒超时
})

// 不显示加载动画
Print.init(document.getElementById('printArea'), {
  showLoading: false
})

// 完整配置
Print.init(document.getElementById('printArea'), {
  waitForImages: true,
  imageTimeout: 20000,
  showLoading: true
})
*/

// 添加自定义样式
const style = document.createElement('style')
style.textContent = `
.print-loading .el-loading-spinner {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.print-loading .el-loading-text {
  color: #409eff;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}

.print-loading .el-loading-spinner .circular {
  width: 42px;
  height: 42px;
  animation: loading-rotate 2s linear infinite;
}

.print-loading .el-loading-spinner .path {
  stroke: #409eff;
  stroke-width: 2;
  stroke-dasharray: 90,150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: loading-dash 1.5s ease-in-out infinite;
}

@keyframes loading-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1,200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90,150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90,150;
    stroke-dashoffset: -120px;
  }
}
`
document.head.appendChild(style)
