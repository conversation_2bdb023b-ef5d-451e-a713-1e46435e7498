
import store from '@/store'
import config from '../config'
import { handleVersionUpdate } from '../config/MicroGlobal'
import { ElMessageBox } from 'element-plus'

let version = null // 版本标识
let timer = undefined

const handleLogVersion = (val, version_obj) => {
  const logVersion = localStorage.getItem('version_log') || ''
  if (logVersion === 'true') console.info(val, version_obj)
}
const updateNotice = () => {
  if (window.__POWERED_BY_QIANKUN__) {
    handleVersionUpdate(true)
  } else {
    store.commit('setVersionUpdate', true)
    const VersionUpdatePrompt = sessionStorage.getItem('VersionUpdatePrompt') || ''
    if (VersionUpdatePrompt === 'true') {
      ElMessageBox.confirm('检测到新版本，建议立即更新以确保平台正常使用。', '更新提示', {
        confirmButtonText: '确认更新',
        cancelButtonText: '稍后更新',
        type: 'success'
      }).then(() => {
        window.location.href = `${config.mainPath}?v=${new Date().getTime()}`
        // window.location.reload(true)
      })
    } else {
      window.location.href = `${config.mainPath}?v=${new Date().getTime()}`
      // window.location.reload(true)
    }
  }
}
/**
 * 获取首页的 ETag 或 Last-Modified 值，作为当前版本标识
 * @returns {Promise<string|null>} 返回 ETag 或 Last-Modified 值
 */
const getVersion = async () => {
  const url = process.env?.VUE_APP_CATALOG || '/'
  const res = await fetch(url, { cache: 'no-cache' })
  return res.headers.get('etag') || res.headers.get('last-modified')
}
/**
 * 比较当前的 ETag 或 Last-Modified 值与最新获取的值
 */
const compareVersion = async () => {
  const newVersion = await getVersion()
  const localVersion = localStorage.getItem('version_number') || ''
  const version_obj = { localVersion: localVersion, oldVersion: version, newVersion: newVersion }
  handleLogVersion('版本号', version_obj)
  if (version === null) {
    if (localVersion && localVersion !== newVersion) {
      localStorage.setItem('version_number', newVersion)
      window.location.href = `${config.mainPath}?v=${new Date().getTime()}`
      // window.location.reload(true)
    } else {
      version = newVersion
      localStorage.setItem('version_number', newVersion)
    }
  } else if (version !== newVersion) {
    localStorage.setItem('version_number', newVersion)
    handleLogVersion('更新了', version_obj)
    if (timer) clearInterval(timer)
    updateNotice() // 提示用户更新
  } else {
    handleLogVersion('没更新', version_obj)
  }
}
export const detectionVersion = () => {
  if (process.env.NODE_ENV === 'development') return
  const detection_version = sessionStorage.getItem('DetectionVersion') || ''
  if (detection_version === 'true') {
    compareVersion()
    timer = setInterval(compareVersion, 60000)
  } else {
    handleCompareVersion()
  }
}
/**
 * 比较当前的 ETag 或 Last-Modified 值与最新获取的值
 */
export const handleCompareVersion = async () => {
  if (process.env.NODE_ENV === 'development') return
  const newVersion = await getVersion()
  const localVersion = localStorage.getItem('version_number') || ''
  const version_obj = { localVersion: localVersion, oldVersion: version, newVersion: newVersion }
  handleLogVersion('版本号', version_obj)
  if (localVersion) {
    if (localVersion !== newVersion) {
      localStorage.setItem('version_number', newVersion)
      handleLogVersion('更新了', version_obj)
      window.location.href = `${config.mainPath}?v=${new Date().getTime()}`
      // window.location.reload(true)
    } else {
      handleLogVersion('没更新', version_obj)
    }
  } else {
    localStorage.setItem('version_number', newVersion)
  }
}