import api from '@/api'
import { ElMessage } from 'element-plus'

export function getFileInfo (_name) {
  var name = (_name || '').toLocaleLowerCase()
  var convertType = ''
  try {
    if (name.indexOf('.') != -1) name = name.split('.')[name.split('.').length - 1]
    switch (name) {
      case 'xlsx':
      case 'xlsm':
      case 'xlsb':
      case 'xltx':
      case 'xltm':
      case 'xls':
      case 'xlt':
      case 'et':
      case 'csv':
      case 'uos':
        convertType = '0'
        break
      case 'doc':
      case 'docx':
      case 'docm':
      case 'dotx':
      case 'dotm':
      case 'dot':
      case 'xps':
      case 'rtf':
      case 'wps':
      case 'wpt':
      case 'uot':
        convertType = '0'
        break
      case 'pdf':
        convertType = '20'
        break
      case 'ppt':
      case 'pptx':
      case 'pps':
      case 'pot':
      case 'pptm':
      case 'potx':
      case 'potm':
      case 'ppsx':
      case 'ppsm':
      case 'ppa':
      case 'ppam':
      case 'dps':
      case 'dpt':
      case 'uop':
        convertType = '0'
        break
      case 'bmp':
      case 'gif':
      case 'jpg':
      case 'pic':
      case 'png':
      case 'tif':
      case 'jpeg':
      case 'jpe':
      case 'icon':
      case 'jfif':
      case 'dib':
      case 'webp':
        convertType = '440'
        break
      case 'txt':
        convertType = '0'
        break
      case 'rar':
      case 'zip':
      case '7z':
      case 'tar':
      case 'gz':
      case 'jar':
      case 'ios':
        convertType = '19'
        break
      case 'mp4':
      case 'avi':
      case 'flv':
      case 'f4v':
      case 'webm':
      case 'm4v':
      case 'mov':
      case '3gp':
      case 'rm':
      case 'rmvb':
      case 'mkv':
      case 'mpeg':
      case 'wmv':
        convertType = '450'
        break
      case 'mp3':
      case 'm4a':
      case 'amr':
      case 'pcm':
      case 'wav':
      case 'aiff':
      case 'aac':
      case 'ogg':
      case 'wma':
      case 'flac':
      case 'alac':
      case 'wma':
      case 'cda':
        convertType = '660'
        break
      case 'folder':
        type = 'folder'
        break
    }
  } catch (e) {
    console.log(e.message)
  }
  return convertType
}
export const file_preview = async (url) => {
  const res = await api.file_preview({ fileUrl: url })
  const data = res?.data?.data
  if (data) {
    file_preview_url(url, data)
  } else {
    ElMessage({ type: 'error', message: '打开失败，请重试' })
  }
}
const file_preview_url = async (url, path) => {
  const res = await api.file_preview_url({
    srcRelativePath: path,
    convertType: getFileInfo(path.substring(path.lastIndexOf('.'))) || '0',
    isDccAsync: 1, isCopy: 0, noCache: 0, fileUrl: url, showFooter: 0,
    isHeaderBar: 0, htmlTitle: '详情', acceptTracks: 0
  })
  const viewUrl = res?.data?.viewUrl
  if (viewUrl) {
    window.open(viewUrl, '_blank')
  } else {
    ElMessage({ type: 'error', message: '打开失败，请重试' })
  }
}

