import { ref, onMounted } from 'vue'
import httpApi from '@/api'
import config from '../config'

export const getVideoMeetinConfig = () => {
  const VideoMeetinVender = ref('')
  const yl_client_id = ref('')
  const xy_wss_server = ref('')
  const xy_http_server = ref('')
  const xy_log_server = ref('')
  const xy_ext_id = ref('')
  const xy_client_id = ref('')
  const xy_client_secret = ref('')

  const globalReadConfig = async () => {
    const { data } = await httpApi.globalReadConfig({ codes: ['VideoMeetinVender', 'yl_client_id', 'xy_wss_server', 'xy_http_server', 'xy_log_server', 'xy_ext_id', 'xy_client_id', 'xy_client_secret'] })
    VideoMeetinVender.value = data.VideoMeetinVender || 'xy'
    yl_client_id.value = data.yl_client_id || '1ff67f7b996848608ed80ef7097a20cc'
    xy_wss_server.value = data.xy_wss_server || 'wss://cloudapi.xylink.com'
    xy_http_server.value = data.xy_http_server || 'https://cloudapi.xylink.com'
    xy_log_server.value = data.xy_log_server || 'https://log.xylink.com'
    xy_ext_id.value = data.xy_ext_id || '606e6604e882a63891f1471e3e1d9d32af26e918'
    xy_client_id.value = data.xy_client_id || 'lleI2gfbcKaszk9dyUzFxIZ8'
    xy_client_secret.value = data.xy_client_secret || 'TrXAokrTBUHRXZ2HdJsWJioZHx6ZOdKT'
  }
  onMounted(() => { globalReadConfig() })

  return { VideoMeetinVender, yl_client_id, xy_wss_server, xy_http_server, xy_log_server, xy_ext_id, xy_client_id, xy_client_secret }
}

export const openMeetingYL = (data, page) => {
  //app中打开
  if (typeof api !== 'undefined') {
    openMeetingYLAPP(data)
  } else {
    var iframe = document.createElement('iframe')
    iframe.id = 'YLVideoMeetingIframe'
    iframe.setAttribute('style', 'position: fixed;top: 0;left: 0;width: 100%;height: 100%;overflow: auto;z-index: 9999;')
    iframe.setAttribute('src', `${config.mainPath}${page || 'yl'}/`)
    iframe.setAttribute('frameborder', '0')
    document.body.appendChild(iframe)
    if (iframe.attachEvent) {
      iframe.attachEvent('onload', () => {
        iframe.contentWindow.postMessage(data, '*')
        window.addEventListener('message', (event) => {
          if (event.data === 'close') document.body.removeChild(iframe)
        }, true)
      })
    } else {
      iframe.onload = () => {
        iframe.contentWindow.postMessage(data, '*')
        window.addEventListener('message', (event) => {
          if (event.data === 'close') document.body.removeChild(iframe)
        }, true)
      }
    }
    window.yl_video_meeting = () => {
      document.body.removeChild(iframe)
    }
  }
}
export const openMeetingXY = (data) => {
  //app中打开
  if (typeof api !== 'undefined') {
    openMeetingXYAPP(data)
  } else {
    var iframe = document.createElement('iframe')
    iframe.id = 'XYVideoMeetingIframe'
    iframe.setAttribute('style', 'position: fixed;top: 0;left: 0;width: 100%;height: 100%;overflow: auto;z-index: 9999;')
    iframe.setAttribute('src', `${config.mainPath}xy/`)
    iframe.setAttribute('frameborder', '0')
    document.body.appendChild(iframe)
    if (iframe.attachEvent) {
      iframe.attachEvent('onload', () => {
        iframe.contentWindow.postMessage(data, '*')
        window.addEventListener('message', (event) => {
          if (event.data === 'close') document.body.removeChild(iframe)
        }, true)
      })
    } else {
      iframe.onload = () => {
        iframe.contentWindow.postMessage(data, '*')
        window.addEventListener('message', (event) => {
          if (event.data === 'close') document.body.removeChild(iframe)
        }, true)
      }
    }
    window.xy_video_meeting = () => {
      document.body.removeChild(iframe)
    }
  }
}

const openMeetingYLAPP = (data) => {
  var zyYealinkCloud = api.require('zyYealinkCloud') // eslint-disable-line
  if (!zyYealinkCloud) {
    alert('未绑定亿联模块，请联系管理员或更新至最新版本')
    return
  }
  if (!api.hasPermission({ list: ['camera'] })[0].granted) { // eslint-disable-line
    api.requestPermission({ list: ['camera'] }, function (ret, err) { // eslint-disable-line
      if (ret.list[0].granted) {
        openMeetingYLAPP()
      }
    })
    return
  }
  if (!api.hasPermission({ list: ['microphone'] })[0].granted) { // eslint-disable-line
    api.requestPermission({ list: ['microphone'] }, function (ret, err) { // eslint-disable-line
      if (ret.list[0].granted) {
        openMeetingYLAPP()
      }
    })
    return
  }
  api.showProgress() // eslint-disable-line
  zyYealinkCloud.init({ appId: 'com.zhengyu.hnrdpc', appSecret: '983c64e61b9c4e8b8cec9ddb4030ca7d' },
    (ret) => {
      if (!ret.status) {
        api.hideProgress() // eslint-disable-line
        api.alert({ msg: JSON.stringify(ret) }) // eslint-disable-line
        return
      }
      // var num = Math.ceil(Math.random() * 1000)
      api.hideProgress() // eslint-disable-line
      var param = {
        mId: data.meetingNumber,
        passCode: data.meetingPassword,
        // meetingNumber: data.meetingNumber, //呼叫号 终端号
        // meetingPassword: data.meetingPassword, //密码
        nickName: data.userName,
        cameraOn: true, //是否开启摄像头
        micOn: true, //是否开启麦克风
        server: 'onylyun.com'
      }
      zyYealinkCloud.joinMeetingWithoutLogin(param)
      setTimeout(() => {
        zyYealinkCloud.joinMeetingWithoutLogin(param)
      }, 300)
    }
  )
}
const openMeetingXYAPP = (data) => {
  var zyXylink = api.require('zyXylink') // eslint-disable-line
  if (!zyXylink) {
    alert('未绑定小鱼模块，请联系管理员或更新至最新版本')
    return
  }
  if (!api.hasPermission({ list: ['camera'] })[0].granted) { // eslint-disable-line
    api.requestPermission({ list: ['camera'] }, (ret, err) => { // eslint-disable-line
      if (ret.list[0].granted) {
        openMeetingXYAPP(data)
      }
    })
    return
  }
  if (!api.hasPermission({ list: ['microphone'] })[0].granted) { // eslint-disable-line
    api.requestPermission({ list: ['microphone'] }, (ret, err) => { // eslint-disable-line
      if (ret.list[0].granted) {
        openMeetingXYAPP(data)
      }
    })
    return
  }
  api.showProgress() // eslint-disable-line
  zyXylink.init({ exId: data.extId },
    (ret) => {
      if (!ret.status) {
        api.hideProgress() // eslint-disable-line
        api.alert({ msg: JSON.stringify(ret) }) // eslint-disable-line
        return
      }
      var num = Math.ceil(Math.random() * 100000)
      var param1 = {
        userId: '12345' + num, // 用户名 必须为数字
        name: data.meetingName //用户唯一标识 id或手机号
        //avatar: T.getPrefs("sys_AppPhoto") || ""//头像 非必填
      }
      zyXylink.loginAccount(param1, (ret) => {
        if (!ret.status) {
          api.hideProgress() // eslint-disable-line
          api.alert({ msg: JSON.stringify(ret) }) // eslint-disable-line
          return
        }
        var param2 = {
          meetingNumber: data.meeting, //呼叫号 终端号
          meetingPassword: data.meetingPassword, //密码
          muteAudio: false, //静音
          muteVideo: false, //关闭视频
          defaultCameraFront: true //是否默认前置，false为后置
        }
        api.hideProgress() // eslint-disable-line
        zyXylink.joinNow(param2, (ret) => {
          if (!ret.status) {
            api.toast({ msg: ret.result }) // eslint-disable-line
          }
        })
      })
    }
  )
}