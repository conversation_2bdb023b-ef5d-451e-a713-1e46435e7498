// 检查元素是否可点击的工具函数
const isClickable = (element) => {
  // 检查元素的所有属性
  const attributes = Array.from(element.attributes || [])
  // 特别检查 Vue 的事件绑定
  const hasVueClick = attributes.some(attr => {
    const name = attr.name.toLowerCase()
    return (
      name === '@click' ||
      name.startsWith('v-on') ||
      name.includes('click') ||
      // 检查 Vue 编译后的事件处理程序
      (name.startsWith('data-v-') && element._vei) ||
      name.includes('onClick')
    )
  })
  // 如果找到 Vue 点击事件，直接返回 true
  if (hasVueClick) return true
  // 检查标准可点击元素
  const clickableTags = ['A', 'BUTTON', 'INPUT', 'SELECT']
  if (clickableTags.includes(element.tagName)) return true
  // 检查样式
  const computedStyle = window.getComputedStyle(element)
  if (computedStyle.cursor === 'pointer') return true
  // 检查 role
  return element.getAttribute('role') === 'button'
}

class TextSelector {
  constructor(options = {}) {
    this.config = {
      onSelect: (text, event, hasClick) => console.log('选中的文本:', text, '是否可点击:', hasClick),
      onDeselect: () => console.log('取消选中'),
      enabled: true,
      ignoreAttribute: 'data-no-text-select',
      ...options
    }
    this.handleTextSelection = this.handleTextSelection.bind(this)
    this.handleSelectionChange = this.handleSelectionChange.bind(this)
    this.lastSelection = null
  }

  checkElementAndParents (element) {
    let current = element
    while (current && current !== document.body) {
      if (isClickable(current)) return true
      current = current.parentElement
    }
    return false
  }

  shouldIgnoreSelection (element) {
    let current = element
    while (current && current !== document.body) {
      if (current.hasAttribute(this.config.ignoreAttribute)) {
        return true
      }
      current = current.parentElement
    }
    return false
  }

  handleSelectionChange () {
    if (!this.config.enabled) return

    const selection = window.getSelection()
    const selectedText = selection.toString().trim()

    if (this.lastSelection && !selectedText) {
      this.config.onDeselect()
      this.lastSelection = null
    }
  }

  handleTextSelection (event) {
    if (!this.config.enabled) return

    const selection = window.getSelection()
    const selectedText = selection.toString().trim()

    if (!selectedText) return

    try {
      const range = selection.getRangeAt(0)
      // 获取选中范围内的所有相关元素
      const startElement = range.startContainer.nodeType === 1 ? range.startContainer : range.startContainer.parentElement
      const endElement = range.endContainer.nodeType === 1 ? range.endContainer : range.endContainer.parentElement
      const commonAncestor = range.commonAncestorContainer.nodeType === 1 ? range.commonAncestorContainer : range.commonAncestorContainer.parentElement
      // 获取选中文本的坐标信息
      const rect = range.getBoundingClientRect()
      const coordinates = {
        x: rect.left + window.scrollX,
        y: rect.top + window.scrollY,
        width: rect.width,
        height: rect.height
      }
      // 检查是否应该忽略这个选区
      if (this.shouldIgnoreSelection(startElement)) {
        // selection.removeAllRanges()
        return
      }
      // 检查所有相关元素
      const hasClick = this.checkElementAndParents(startElement) || this.checkElementAndParents(endElement) || this.checkElementAndParents(commonAncestor)
      // 输出调试信息
      console.debug('检查元素:', { startElement, endElement, commonAncestor, hasClick, coordinates })
      // 更新最后一次选中的状态
      this.lastSelection = {
        text: selectedText,
        coordinates
      }
      this.config.onSelect(selectedText, event, hasClick, coordinates)
    } catch (error) {
      console.error('Error checking selection:', error)
    }
  }

  enable () {
    this.config.enabled = true
  }

  disable () {
    this.config.enabled = false
  }

  listen (callback, deselectCallback) {
    if (typeof callback === 'function') {
      this.config.onSelect = callback
    }
    if (typeof deselectCallback === 'function') {
      this.config.onDeselect = deselectCallback
    }
  }

  cleanup () {
    document.removeEventListener('mouseup', this.handleTextSelection)
    document.removeEventListener('selectionchange', this.handleSelectionChange)
  }

  init () {
    document.addEventListener('mouseup', this.handleTextSelection)
    document.addEventListener('selectionchange', this.handleSelectionChange)
  }
}

// 创建 Vue 插件
const TextSelectPlugin = {
  install (app, options = {}) {
    const textSelector = new TextSelector(options)
    textSelector.init()

    // 注入全局属性
    app.config.globalProperties.$textSelection = textSelector

    // 提供依赖注入
    app.provide('textSelection', textSelector)

    // 添加到 window 对象（可选）
    window.$textSelection = textSelector

    // 添加全局的 mixin 来处理自动清理
    app.mixin({
      unmounted () {
        // 检查是否是根组件
        if (this === this.$root) {
          textSelector.cleanup()
        }
      }
    })

    // 添加应用卸载时的清理
    app.config.globalProperties.$unmount = app.unmount
    app.unmount = function () {
      textSelector.cleanup()
      this.$unmount()
    }
  }
}

export default TextSelectPlugin