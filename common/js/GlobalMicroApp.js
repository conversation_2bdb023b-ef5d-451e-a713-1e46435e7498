import { createRouter, createWebHistory } from 'vue-router'
import router, { routes } from '@/router'
import store from '@/store'
import api from '@/api'
import config from 'common/config'
import { MicroGlobal } from 'common/config/qiankun'
import { detectionVersion } from 'common/js/CheckVersion'
import { initFont, get_font_family, change_font_family } from 'common/js/utils'
import { currentTheme, globalReadOpenConfig } from 'common/js/GlobalMethod'
export const customSelectUser = async (selectUserData) => {
  try {
    const { data } = await api.customSelectUser({})
    store.commit('setSelectUser', { ...selectUserData, ...data })
  } catch (err) {
    store.commit('setSelectUser', selectUserData)
  }
}
export const selectUserJson = async () => {
  const { data } = await api.selectUserJson()
  customSelectUser(data)
}
export const microAppMethod = () => {
  window.__PUBLIC__ = true
  const $favicon = document.querySelector('link[rel="icon"]')
  $favicon.href = `${config.API_URL}/pageImg/open/logo`
  initFont()
  currentTheme()
  detectionVersion()
  globalReadOpenConfig()
  change_font_family(get_font_family())
  var whether = sessionStorage.getItem('whether') || 'yes'
  if (whether === 'no' && sessionStorage.getItem('token')) {
    store.dispatch('loginUser')
  }
  router.beforeEach((to, from, next) => {
    // if (process.env.NODE_ENV !== 'development') { throttleIsNewVersion() }
    if (to.query.token) {
      sessionStorage.setItem('token', to.query.token)
      if (whether === 'no') { store.dispatch('loginUser') }
    }
    if (whether === 'yes') {
      whether = 'no'
      sessionStorage.setItem('whether', whether)
      if (sessionStorage.getItem('token')) {
        if (to.meta.moduleName === 'PUBLIC') {
          sessionStorage.removeItem('token')
        } else {
          store.dispatch('loginUser')
        }
      }
    }
    if (to.query.token) {
      let query = to.query
      delete query.token
      next({ path: to.path, query: query })
    } else {
      next()
    }
  })
}
const microAppData = (state, prev) => {
  if (JSON.stringify(state.theme) !== JSON.stringify(prev.theme) || JSON.stringify(store.state.theme) !== JSON.stringify(state.theme)) {
    store.commit('setTheme', state.theme)
  }
  if (JSON.stringify(state.user) !== JSON.stringify(prev.user) || JSON.stringify(store.state.user) !== JSON.stringify(state.user)) {
    store.commit('setUser', state.user)
  }
  if (JSON.stringify(state.menu) !== JSON.stringify(prev.menu) || JSON.stringify(store.state.menu) !== JSON.stringify(state.menu)) {
    store.commit('setMenu', state.menu)
  }
  if (JSON.stringify(state.area) !== JSON.stringify(prev.area) || JSON.stringify(store.state.area) !== JSON.stringify(state.area)) {
    store.commit('setArea', state.area)
  }
  if (JSON.stringify(state.role) !== JSON.stringify(prev.role) || JSON.stringify(store.state.role) !== JSON.stringify(state.role)) {
    store.commit('setRole', state.role)
  }
  if (JSON.stringify(state.readConfig) !== JSON.stringify(prev.readConfig) || JSON.stringify(store.state.readConfig) !== JSON.stringify(state.readConfig)) {
    store.commit('setReadConfig', state.readConfig)
  }
  if (JSON.stringify(state.readOpenConfig) !== JSON.stringify(prev.readOpenConfig) || JSON.stringify(store.state.readOpenConfig) !== JSON.stringify(state.readOpenConfig)) {
    store.commit('setReadOpenConfig', state.readOpenConfig)
  }
  if (JSON.stringify(state.keepAliveRoute) !== JSON.stringify(prev.keepAliveRoute)) {
    const keepAliveRoute = state.keepAliveRoute.filter(v => v.substring(v.indexOf('/') + 1, v.lastIndexOf('/')) === process.env.VUE_APP_NAME)
    store.commit('setKeepAliveRoute', Array.from(new Set(keepAliveRoute.map(v => {
      const index = v.indexOf('?')
      const to = index !== -1 ? v.substring(0, index) : v
      return to.substring(to.lastIndexOf('/') + 1)
    }))))
  }
  if (state.refreshRoute.substring(state.refreshRoute.indexOf('/') + 1, state.refreshRoute.lastIndexOf('/')) === process.env.VUE_APP_NAME) {
    const index = state.refreshRoute.indexOf('?')
    const to = index !== -1 ? state.refreshRoute.substring(0, index) : state.refreshRoute
    store.commit('setRefreshRoute', to.substring(to.lastIndexOf('/') + 1))
  }
  if (!state.refreshRoute && prev.refreshRoute.substring(prev.refreshRoute.indexOf('/') + 1, prev.refreshRoute.lastIndexOf('/')) === process.env.VUE_APP_NAME) {
    store.commit('setRefreshRoute', '')
  }
}
export const microAppRender = (props) => {
  selectUserJson()
  detectionVersion()
  const { setGlobalState, onGlobalStateChange, globalState } = props
  onGlobalStateChange(microAppData)
  MicroGlobal.prototype.name = props.name
  MicroGlobal.prototype.setGlobalState = setGlobalState
  MicroGlobal.prototype.mainWindow = globalState.mainWindow
  MicroGlobal.prototype.AiChatClass = globalState.AiChatClass
  const history = createWebHistory(props.routerBase)
  const router = createRouter({ history, routes })
  return { history, router }
}
