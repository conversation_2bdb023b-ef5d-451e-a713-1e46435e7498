const watermark = { timer: null, set: () => { } }
const setWatermark = (str = '默认水印内容') => {
  const id = 'GLOBAL_WATER_MARK'
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id))
  }
  const can = document.createElement('canvas')
  can.width = (14 * str.length) + 88
  can.height = 14 * (str.length / 2) + 88

  const cans = can.getContext('2d')
  cans.rotate(-20 * Math.PI / 180)
  cans.font = '14px Microsoft YaHei'
  cans.fillStyle = 'rgba(000, 000, 000, 0.1)'
  cans.textAlign = 'center'
  cans.textBaseline = 'middle'
  cans.fillText(str, can.width / 3, can.height)

  const WATER_MARK = document.createElement('div')
  const ALL_WIDTH = document.documentElement.clientWidth + 520 + 'px'
  const ALL_HEIGHT = document.documentElement.clientHeight + 520 + 'px'

  WATER_MARK.id = id
  WATER_MARK.style.cssText = `
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999999;
  pointer-events: none;
  width: ${ALL_WIDTH};
  height: ${ALL_HEIGHT};
  background: url(${can.toDataURL('image/png')}) center center repeat;`
  document.body.appendChild(WATER_MARK)
  return id
}

// 该方法只允许调用一次
watermark.set = (str) => {
  let id = setWatermark(str)
  watermark.timer && clearTimeout(watermark.timer)
  watermark.timer = setTimeout(() => {
    if (document.getElementById(id) === null) {
      id = setWatermark(str)
    }
  }, 300)
  window.onresize = () => { setWatermark(str) }
}

const onWholeWaterMark = (str) => watermark.set(str)
export default onWholeWaterMark
