import api from '@/api'
import { ref, onMounted, onActivated } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { useStore } from 'vuex'
import { handleCustom } from '../config/MicroGlobal'
import { defaultPageSize, pageSizes } from 'common/js/system_var.js'
import { ElMessage, ElMessageBox } from 'element-plus'

export const GlobalTable = ({
  tableId,
  valId = 'id',
  tableApi = 'globalList',
  delApi = 'globalDel',
  tableHeadList = [],
  tableHeadParams = {},
  tableSortList = [],
  tableDataObj = {},
  tableParamsMethod,
  tableDataMap,
  moreSort = false
}) => {
  const store = useStore()
  const headIndex = ref(0)
  const headSuccess = ref(false)
  const tableIdRef = ref('')
  const keyword = ref('')
  const queryRef = ref()
  const tableRef = ref()
  const tableTop = ref(0)
  const tableLeft = ref(0)
  const totals = ref(0)
  const pageNo = ref(1)
  const pageSize = ref(defaultPageSize.value)
  const tableSort = ref([])
  const tableHead = ref([])
  const tableData = ref([])
  const tableDataArray = ref([])
  const tableQuery = ref({})
  const tableParams = ref([])
  const tableDefaultWheres = ref([])
  const exportId = ref([])
  const exportParams = ref({})
  const exportShow = ref(false)

  onMounted(() => {
    if (tableHeadList.length) {
      tableHead.value = tableHeadList
    }
  })

  onActivated(() => {
    setTimeout(() => {
      if (tableRef.value?.setScrollTop) {
        tableRef.value?.setScrollTop(tableTop.value || 0)
      }
      if (tableRef.value?.setScrollLeft) {
        tableRef.value?.setScrollLeft(tableLeft.value || 0)
      }
    }, 50)
  })
  onBeforeRouteLeave(() => {
    if (tableRef.value?.$refs?.bodyWrapper) {
      tableTop.value = tableRef.value?.$refs?.bodyWrapper?.getElementsByClassName('zy-el-scrollbar__wrap')[0]?.scrollTop
      tableLeft.value =
        tableRef.value?.$refs?.bodyWrapper?.getElementsByClassName('zy-el-scrollbar__wrap')[0]?.scrollLeft
    }
  })

  const handleQuery = () => {
    if (headSuccess.value) {
      tableDefaultWheres.value = []
      tableBodyData()
    } else {
      tableHeadData()
    }
  }

  const tableHeadData = async (importTableId = '') => {
    if (headIndex.value && !importTableId) return
    headIndex.value = 1
    if (!tableId && !importTableId) {
      headSuccess.value = true
      tableBodyData()
      return
    }
    tableIdRef.value = importTableId
    const res = await api.tableHead(tableIdRef.value || tableId, tableHeadParams)
    var { data } = res
    tableHead.value = data.filter((v) => v.isShow || v.isQuery || v.isSort)
    tableSort.value = data
      .filter((v) => v.isShow && v.isSort && v.isDefaultsort)
      .map((v) => ({ prop: v.id, order: v.sortFlag ? 'descending' : 'ascending' }))
    tableDefaultWheres.value = data
      .filter((v) => v.isQuery && v.isQuerydefault && v.defaultValue)
      .map((v) => ({ columnId: v.id, queryType: v.queryType, value: v.defaultValue }))
    if (tableSortList.length) {
      tableSort.value = tableSortList
    }
    headSuccess.value = true
    tableBodyData()
  }

  const tableBodyData = async () => {
    if (!headSuccess.value) return
    const params = {
      tableId: tableIdRef.value || tableId,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      keyword: keyword.value,
      isAnd: queryRef.value?.getIsAnd(),
      wheres: [...tableDefaultWheres.value, ...tableParams.value, ...(queryRef.value?.getWheres() || [])],
      orderBys: tableSort.value.map((v) => ({ columnId: v.prop, isDesc: v.order === 'ascending' ? '0' : '1' })),
      ...tableDataObj,
      ...tableQuery.value
    }
    const res = await api[tableApi](tableParamsMethod ? tableParamsMethod(params) || params : params)
    var { data, total } = res
    tableData.value = tableDataMap ? tableDataMap(data, res) || data : data
    totals.value = total
  }

  const handleHeaderClass = ({ row, column }) => {
    if (tableSort.value.map((v) => v.prop).includes(column.property)) {
      tableSort.value.forEach((element) => {
        if (column.property === element.prop) {
          column.order = element.order
        }
      })
    } else {
      column.order = ''
    }
  }

  const handleSortChange = ({ column, prop, order }) => {
    if (column && prop && order) {
      if (tableSort.value.map((v) => v.prop).includes(prop)) {
        tableSort.value.forEach((element) => {
          if (element.prop === prop) {
            element.order = order
          }
        })
      } else {
        if (moreSort) {
          tableSort.value.push({ prop: prop, order: order })
        } else {
          tableSort.value = [{ prop: prop, order: order }]
        }
      }
    } else {
      tableSort.value = tableSort.value.filter((v) => v.prop !== prop)
    }
    tableBodyData()
  }
  const handlesSetSort = (sortValue = []) => {
    tableSort.value = sortValue
    tableBodyData()
  }
  const handleTableSelect = (selection) => {
    tableDataArray.value = selection
  }

  const handleGlobalUnify = (text, data, param = {}, type = false) => {
    if (tableDataArray.value.length) {
      if (type) {
        unifyStatus(data, param)
      } else {
        ElMessageBox.confirm(text, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            unifyStatus(data, param)
          })
          .catch(() => {
            ElMessage({ type: 'info', message: '已取消操作' })
          })
      }
    } else {
      ElMessage({ type: 'warning', message: '请至少选择一条数据' })
    }
  }

  const unifyStatus = async ({ status, entityName, prop }, param = {}) => {
    const { code } = await api.unifyStatus({
      status,
      entityName,
      prop,
      ...param,
      ids: tableDataArray.value.map((v) => v[valId])
    })
    if (code === 200) {
      ElMessage({ type: 'success', message: '操作成功' })
      tableRefReset()
      handleQuery()
    }
  }

  const handleDel = (name, nameType = '删除', text) => {
    if (tableDataArray.value.length) {
      ElMessageBox.confirm(text ? text : `此操作将${nameType}当前选中的${name}, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          globalDel(nameType)
        })
        .catch(() => {
          ElMessage({ type: 'info', message: `已取消${nameType}` })
        })
    } else {
      ElMessage({ type: 'warning', message: '请至少选择一条数据' })
    }
  }

  const globalDel = async (nameType) => {
    const { code } = await api[delApi]({ ids: tableDataArray.value.map((v) => v[valId]) })
    if (code === 200) {
      ElMessage({ type: 'success', message: `${nameType}成功` })
      tableRefReset()
      handleQuery()
    }
  }

  const tableRefReset = () => {
    tableRef.value.clearSelection()
    tableDataArray.value = []
  }

  const handleEditorCustom = () => {
    if (window.__POWERED_BY_QIANKUN__) {
      handleCustom(tableIdRef.value || tableId)
    } else {
      store.commit('setOpenRoute', {
        name: '列表自定义',
        path: '/system/TableCustom',
        query: { id: tableIdRef.value || tableId }
      })
    }
  }

  const handleExportExcel = () => {
    const params = {
      tableId: tableIdRef.value || tableId,
      keyword: keyword.value,
      isAnd: queryRef.value?.getIsAnd(),
      wheres: [...tableDefaultWheres.value, ...tableParams.value, ...(queryRef.value?.getWheres() || [])],
      orderBys: tableSort.value.map((v) => ({ columnId: v.prop, isDesc: v.order === 'ascending' ? '0' : '1' })),
      ...tableDataObj,
      ...tableQuery.value
    }
    exportId.value = tableDataArray.value.map((v) => v[valId])
    exportParams.value = tableParamsMethod ? tableParamsMethod(params) || params : params
    exportShow.value = true
  }

  const handleGetParams = () => {
    const params = {
      tableId: tableIdRef.value || tableId,
      keyword: keyword.value,
      isAnd: queryRef.value?.getIsAnd(),
      wheres: [...tableDefaultWheres.value, ...tableParams.value, ...(queryRef.value?.getWheres() || [])],
      orderBys: tableSort.value.map((v) => ({ columnId: v.prop, isDesc: v.order === 'ascending' ? '0' : '1' })),
      ...tableDataObj,
      ...tableQuery.value
    }
    return {
      selectId: tableDataArray.value.map((v) => v[valId]),
      params: tableParamsMethod ? tableParamsMethod(params) || params : params
    }
  }

  return {
    keyword,
    queryRef,
    tableRef,
    totals,
    pageNo,
    pageSize,
    pageSizes,
    tableHead,
    tableData,
    tableQuery,
    tableParams,
    tableDefaultWheres,
    tableDataArray,
    exportId,
    exportParams,
    exportShow,
    handleQuery,
    tableHeadData,
    handleSortChange,
    handleHeaderClass,
    handleTableSelect,
    handlesSetSort,
    handleDel,
    tableRefReset,
    handleGetParams,
    handleEditorCustom,
    handleExportExcel,
    handleGlobalUnify
  }
}
