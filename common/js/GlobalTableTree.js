import api from '@/api'
import { ref, onMounted, onActivated } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { useStore } from 'vuex'
import { handleCustom } from '../config/MicroGlobal'
import { ElMessage, ElMessageBox } from 'element-plus'

export const GlobalTable = ({
  tableId,
  valId = 'id',
  tableApi = 'globalList',
  delApi = 'globalDel',
  tableHeadList = [],
  tableHeadParams = {},
  tableSortList = [],
  tableDataObj = {},
  tableParamsMethod,
  filterName = '',
  moreSort = false,
  isLazy = false
}) => {
  const store = useStore()
  const headIndex = ref(0)
  const headSuccess = ref(false)
  const tableIdRef = ref('')
  const keyword = ref('')
  const queryRef = ref()
  const tableRef = ref()
  const tableTop = ref(0)
  const tableLeft = ref(0)
  const tableSort = ref([])
  const tableHead = ref([])
  const tableData = ref([])
  const tableDataList = ref([])
  const tableDataArray = ref([])
  const tableQuery = ref({})
  const tableParams = ref([])
  const tableDefaultWheres = ref([])
  const exportId = ref([])
  const exportParams = ref({})
  const exportShow = ref(false)

  onMounted(() => {
    if (tableHeadList.length) {
      tableHead.value = tableHeadList
    }
  })

  onActivated(() => {
    setTimeout(() => {
      if (tableRef.value?.setScrollTop) {
        tableRef.value?.setScrollTop(tableTop.value || 0)
      }
      if (tableRef.value?.setScrollLeft) {
        tableRef.value?.setScrollLeft(tableLeft.value || 0)
      }
    }, 50)
  })
  onBeforeRouteLeave(() => {
    if (tableRef.value?.$refs?.bodyWrapper) {
      tableTop.value = tableRef.value?.$refs?.bodyWrapper?.getElementsByClassName('zy-el-scrollbar__wrap')[0]?.scrollTop
      tableLeft.value =
        tableRef.value?.$refs?.bodyWrapper?.getElementsByClassName('zy-el-scrollbar__wrap')[0]?.scrollLeft
    }
  })

  const handleQuery = () => {
    if (headSuccess.value) {
      tableDefaultWheres.value = []
      tableBodyData()
    } else {
      tableHeadData()
    }
  }

  const tableHeadData = async (importTableId = '') => {
    if (headIndex.value && !importTableId) return
    headIndex.value = 1
    if (!tableId && !importTableId) {
      headSuccess.value = true
      tableBodyData()
      return
    }
    tableIdRef.value = importTableId
    const res = await api.tableHead(tableIdRef.value || tableId, tableHeadParams)
    var { data } = res
    tableHead.value = data.filter((v) => v.isShow || v.isQuery || v.isSort)
    tableSort.value = data
      .filter((v) => v.isShow && v.isSort && v.isDefaultsort)
      .map((v) => ({ prop: v.id, order: v.sortFlag ? 'descending' : 'ascending' }))
    tableDefaultWheres.value = data
      .filter((v) => v.isQuery && v.isQuerydefault && v.defaultValue)
      .map((v) => ({ columnId: v.id, queryType: v.queryType, value: v.defaultValue }))
    if (tableSortList.length) {
      tableSort.value = tableSortList
    }
    headSuccess.value = true
    tableBodyData()
  }

  const tableBodyData = async () => {
    if (!headSuccess.value) return
    const params = {
      tableId: tableIdRef.value || tableId,
      keyword: filterName ? '' : keyword.value,
      isAnd: queryRef.value?.getIsAnd(),
      wheres: [...tableDefaultWheres.value, ...tableParams.value, ...(queryRef.value?.getWheres() || [])],
      orderBys: tableSort.value.map((v) => ({ columnId: v.prop, isDesc: v.order === 'ascending' ? '0' : '1' })),
      ...tableDataObj,
      ...tableQuery.value
    }
    const { data } = await api[tableApi](tableParamsMethod ? tableParamsMethod(params) || params : params)
    if (isLazy) {
      const tableFilterData = filterName && keyword.value ? filterTree(data) : data
      var arr = []
      tableFilterData.forEach((item) => {
        arr.push({ ...item, hasChildren: item?.children?.length ? true : false, children: [] })
      })
      tableData.value = arr
      tableDataList.value = tableFilterData
      var obj = {}
      const key = Object.keys(tableRef.value.store.states.lazyTreeNodeMap.value)
      key.forEach((item) => {
        obj[item] = dataList(tableDataList.value, item)
      })
      if (keyword.value) {
        obj = dataQuery(tableFilterData)
      }
      tableRef.value.store.states.lazyTreeNodeMap.value = obj
    } else {
      tableData.value = filterName && keyword.value ? filterTree(data) : data
    }
  }

  const filterTree = (nodes) => {
    if (!nodes || !nodes.length) return []
    const children = []
    for (let node of nodes) {
      node = Object.assign({}, node)
      const sub = filterTree(node.children)
      if ((sub && sub.length) || node[filterName].includes(keyword.value)) {
        sub && (node.children = sub)
        children.push(node)
      }
    }
    return children.length ? children : []
  }
  const dataQuery = (data) => {
    var obj = {}
    data.forEach((item) => {
      obj[item[valId]] = dataList(tableDataList.value, item[valId])
      if (item.children.length) {
        obj = { ...obj, ...dataQuery(item.children) }
      }
    })
    return obj
  }
  const tableLoad = (tree, treeNode, resolve) => {
    resolve(dataList(tableDataList.value, tree[valId]))
  }
  const dataList = (data, id) => {
    var arr = []
    data.forEach((item) => {
      if (item[valId] === id) {
        item.children.forEach((row) => {
          var obj = row
          obj.hasChildren = obj.children.length ? true : false // eslint-disable-line
          arr.push({ ...obj, children: [] })
        })
      }
      if (item.children.length) {
        arr = [...arr, ...dataList(item.children, id)]
      }
    })
    return arr
  }

  const handleHeaderClass = ({ row, column }) => {
    if (tableSort.value.map((v) => v.prop).includes(column.property)) {
      tableSort.value.forEach((element) => {
        if (column.property === element.prop) {
          column.order = element.order
        }
      })
    } else {
      column.order = ''
    }
  }

  const handleSortChange = ({ column, prop, order }) => {
    if (column && prop && order) {
      if (tableSort.value.map((v) => v.prop).includes(prop)) {
        tableSort.value.forEach((element) => {
          if (element.prop === prop) {
            element.order = order
          }
        })
      } else {
        if (moreSort) {
          tableSort.value.push({ prop: prop, order: order })
        } else {
          tableSort.value = [{ prop: prop, order: order }]
        }
      }
    } else {
      tableSort.value = tableSort.value.filter((v) => v.prop !== prop)
    }
    tableBodyData()
  }
  const handlesSetSort = (sortValue = []) => {
    tableSort.value = sortValue
    tableBodyData()
  }
  const handleTableSelect = (selection) => {
    tableDataArray.value = selection
  }

  const handleGlobalUnify = (text, data, param = {}, type = false) => {
    if (tableDataArray.value.length) {
      if (type) {
        unifyStatus(data, param)
      } else {
        ElMessageBox.confirm(text, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            unifyStatus(data, param)
          })
          .catch(() => {
            ElMessage({ type: 'info', message: '已取消操作' })
          })
      }
    } else {
      ElMessage({ type: 'warning', message: '请至少选择一条数据' })
    }
  }

  const unifyStatus = async ({ status, entityName, prop }, param = {}) => {
    const { code } = await api.unifyStatus({
      status,
      entityName,
      prop,
      ...param,
      ids: tableDataArray.value.map((v) => v[valId])
    })
    if (code === 200) {
      ElMessage({ type: 'success', message: '操作成功' })
      tableRefReset()
      handleQuery()
    }
  }

  const handleDel = (name, nameType = '删除', text) => {
    if (tableDataArray.value.length) {
      ElMessageBox.confirm(text ? text : `此操作将${nameType}当前选中的${name}, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          globalDel(nameType)
        })
        .catch(() => {
          ElMessage({ type: 'info', message: `已取消${nameType}` })
        })
    } else {
      ElMessage({ type: 'warning', message: '请至少选择一条数据' })
    }
  }

  const globalDel = async (nameType) => {
    const { code } = await api[delApi]({ ids: tableDataArray.value.map((v) => v[valId]) })
    if (code === 200) {
      ElMessage({ type: 'success', message: `${nameType}成功` })
      tableRef.value.clearSelection()
      handleQuery()
    }
  }

  const tableRefReset = () => {
    tableRef.value.clearSelection()
    tableDataArray.value = []
  }

  const handleEditorCustom = () => {
    if (window.__POWERED_BY_QIANKUN__) {
      handleCustom(tableIdRef.value || tableId)
    } else {
      store.commit('setOpenRoute', {
        name: '列表自定义',
        path: '/system/TableCustom',
        query: { id: tableIdRef.value || tableId }
      })
    }
  }

  const handleExportExcel = () => {
    const params = {
      tableId: tableIdRef.value || tableId,
      keyword: filterName ? '' : keyword.value,
      isAnd: queryRef.value?.getIsAnd(),
      wheres: [...tableDefaultWheres.value, ...tableParams.value, ...(queryRef.value?.getWheres() || [])],
      orderBys: tableSort.value.map((v) => ({ columnId: v.prop, isDesc: v.order === 'ascending' ? '0' : '1' })),
      ...tableDataObj,
      ...tableQuery.value
    }
    exportId.value = tableDataArray.value.map((v) => v[valId])
    exportParams.value = tableParamsMethod ? tableParamsMethod(params) || params : params
    exportShow.value = true
  }

  const handleGetParams = () => {
    const params = {
      tableId: tableIdRef.value || tableId,
      keyword: filterName ? '' : keyword.value,
      isAnd: queryRef.value?.getIsAnd(),
      wheres: [...tableDefaultWheres.value, ...tableParams.value, ...(queryRef.value?.getWheres() || [])],
      orderBys: tableSort.value.map((v) => ({ columnId: v.prop, isDesc: v.order === 'ascending' ? '0' : '1' })),
      ...tableDataObj,
      ...tableQuery.value
    }
    return {
      selectId: tableDataArray.value.map((v) => v[valId]),
      params: tableParamsMethod ? tableParamsMethod(params) || params : params
    }
  }

  return {
    keyword,
    queryRef,
    tableRef,
    tableHead,
    tableData,
    tableQuery,
    tableParams,
    tableDefaultWheres,
    tableDataArray,
    exportId,
    exportParams,
    exportShow,
    tableLoad,
    handleQuery,
    tableHeadData,
    handleSortChange,
    handleHeaderClass,
    handleTableSelect,
    handlesSetSort,
    handleDel,
    tableRefReset,
    handleGetParams,
    handleEditorCustom,
    handleExportExcel,
    handleGlobalUnify
  }
}
