import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
export function useIndex (appRef, type) {
  var innerWidth = 0
  var innerHeight = 0
  // * appRef指向最外层容器
  // * 定时函数
  let timer = null
  // * 默认缩放值
  const scale = { width: '1', height: '1' }
  // * 设计稿尺寸（px）
  const baseWidth = 1920
  const baseHeight = 1080
  // * 需保持的比例
  const baseProportion = parseFloat((baseWidth / baseHeight).toFixed(5))
  // const baseProportion = 2
  const calcRate = () => {
    // 当前宽高比
    const currentRate = type ? parseFloat((innerWidth / innerHeight).toFixed(5)) : parseFloat((window.innerWidth / window.innerHeight).toFixed(5))
    if (appRef) {
      if (currentRate > baseProportion) {
        if (type) {
          // 表示更宽
          scale.width = ((innerHeight * baseProportion) / baseWidth).toFixed(5)
          scale.height = (innerHeight / baseHeight).toFixed(5)
        } else {
          // 表示更宽
          scale.width = ((window.innerHeight * baseProportion) / baseWidth).toFixed(5)
          scale.height = (window.innerHeight / baseHeight).toFixed(5)
        }
        appRef.style.transform = `scale(${scale.width}, ${scale.height}) translate(-50%, -50%) rotate(0.0001deg)`
      } else {
        if (type) {
          // 表示更高
          scale.height = ((innerWidth / baseProportion) / baseHeight).toFixed(5)
          scale.width = (innerWidth / baseWidth).toFixed(5)
        } else {
          // 表示更高
          scale.height = ((window.innerWidth / baseProportion) / baseHeight).toFixed(5)
          scale.width = (window.innerWidth / baseWidth).toFixed(5)
        }
        appRef.style.transform = `scale(${scale.width}, ${scale.height}) translate(-50%, -50%) rotate(0.0001deg)`
      }
    }
  }
  const resize = () => {
    clearTimeout(timer)
    timer = setTimeout(() => { calcRate() }, 200)
  }
  // 改变窗口大小重新绘制
  const windowDraw = () => {
    if (type) {
      erd.listenTo(appRef.parentNode, (element) => {
        innerWidth = element.offsetWidth
        innerHeight = element.offsetHeight
        resize()
      })
    } else {
      window.addEventListener('resize', resize)
    }
  }
  return { appRef, calcRate, windowDraw }
}
