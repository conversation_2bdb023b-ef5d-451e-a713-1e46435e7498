import axios from 'axios'
import { SM4 } from 'gm-crypto'
import AES from 'crypto-js/aes'
import pad from 'crypto-js/pad-pkcs7'
import mode from 'crypto-js/mode-ecb'
import encHex from 'crypto-js/enc-hex'
import encUtf8 from 'crypto-js/enc-utf8'
import encBase64 from 'crypto-js/enc-base64'

import md5 from 'crypto-js/md5'
const SECRET_IV = encUtf8.parse('qazwsxedcrfv0000')
const SECRET_KEY = encUtf8.parse('qazwsxedcrfv0000')
const VUE_APP_SECRET_KEY = '42a6503ed517bb49b78c4b199f6001e1cf6ced30674e2296c65bad2419755e57'
const VUE_APP_SECRET_KEY_ONE = '9d74ccc271c7a9fca1605fa2d9c0090dcf6ced30674e2296c65bad2419755e57'
const VUE_APP_SECRET_KEY_TWO = '079d9d88b05861f03f13ad091d88e744cf6ced30674e2296c65bad2419755e57'
const stringToHex = (str) => Array.prototype.map.call(str, (c) => c.charCodeAt(0).toString(16)).join('')
/**
 * 加密方法
 * @param data - 数据
 */
const encryptKey = (data) => {
  const dataHex = encUtf8.parse(data)
  const encrypted = AES.encrypt(dataHex, SECRET_KEY, { iv: SECRET_IV, mode: mode, padding: pad })
  return encrypted.ciphertext.toString()
}
/**
 * 解密方法
 * @param data - 数据
 */
const decryptKey = (data) => {
  const encryptedHexStr = encHex.parse(data)
  const str = encBase64.stringify(encryptedHexStr)
  const decrypt = AES.decrypt(str, SECRET_KEY, { iv: SECRET_IV, mode: mode, padding: pad })
  const decryptedStr = decrypt.toString(encUtf8)
  return decryptedStr.toString()
}
/**
 * 加密方法
 * @param data - 数据
 */
const gm_encrypt = (data, key, iv) => {
  const ivUtil = stringToHex(iv || 'zysoft2025518888')
  const keyUtil = stringToHex(key || 'zysoft2025518888')
  const sm4Util = SM4.encrypt(data, keyUtil, {
    iv: ivUtil,
    mode: SM4.constants.CBC,
    inputEncoding: 'utf8',
    outputEncoding: 'base64'
  })
  return sm4Util
}
/**
 * 解密方法
 * @param data - 数据
 */
const gm_decrypt = (data, key, iv) => {
  const ivUtil = stringToHex(iv || 'zysoft2025518888')
  const keyUtil = stringToHex(key || 'zysoft2025518888')
  const sm4Util = SM4.decrypt(data, keyUtil, {
    iv: ivUtil,
    mode: SM4.constants.CBC,
    inputEncoding: 'base64',
    outputEncoding: 'utf8'
  })
  return sm4Util
}
export default {
  gm_encrypt,
  gm_decrypt,
  encryptKey,
  decryptKey,
  // 十六位十六进制数作为密钥偏移量
  encrypt(word, keyStr, type) {
    let key = encUtf8.parse(keyStr)
    if (type && type === '1') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY))
    }
    if (type && type === '2') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY_ONE))
    }
    if (type && type === '3') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY_TWO))
    }
    const srcs = encUtf8.parse(word)
    const encrypted = AES.encrypt(srcs, key, { mode: mode, padding: pad })
    return encrypted.toString()
  },
  // 解密
  decrypt(word, keyStr, type) {
    let key = encUtf8.parse(keyStr)
    if (type && type === '1') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY))
    }
    if (type && type === '2') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY_ONE))
    }
    if (type && type === '3') {
      key = encUtf8.parse(decryptKey(VUE_APP_SECRET_KEY_TWO))
    }
    const decrypt = AES.decrypt(word, key, { mode: mode, padding: pad })
    return encUtf8.stringify(decrypt).toString()
  },
  // 加密
  Md5Js(text) {
    return md5(text).toString()
  },
  tmp(type) {
    var tmp = Date.parse(new Date()).toString()
    if (type) {
      tmp = tmp.substr(0, 10)
    }
    return Number(tmp)
  },
  tmpNte() {
    let date = new Date()
    const Y = date.getFullYear()
    const M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
    const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
    const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
    const minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
    const seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
    date = Y + '' + M + '' + D + '' + hours + '' + minutes + '' + seconds
    return date
  }
}
// 限制只能输入数字
export const validNum = (value) => value.replace(/[^0-9]/g, '')
// 去除输入的空格
export const deleteBlank = (value) => value.replace(/\s+/g, '')
// 限制只能输入英文和数字
export const englishNum = (value) => value.replace(/[^\w\.\/]/gi, '')
// 限制不能输入特殊字符
export const validForbid = (value) =>
  value
    .replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g, '')
    .replace(/\s/g, '')
// 手机号码的中间四位替换为星号*
export const encryptPhone = (value) => (value ? value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '')
/**
 * @description: 计算文件大小
 * @param {String} size 文件大小
 * @return  {String} B KB MB GB
 */
export const size2Str = (size) => {
  size = Number(size)
  if (size < 1024) {
    return size + 'B'
  } else if (size >= 1024 && size < Math.pow(1024, 2)) {
    return parseFloat(size / 1024).toFixed(2) + 'KB'
  } else if (size >= Math.pow(1024, 2) && size < Math.pow(1024, 3)) {
    return parseFloat(size / Math.pow(1024, 2)).toFixed(2) + 'MB'
  } else if (size > Math.pow(1024, 3)) {
    return parseFloat(size / Math.pow(1024, 3)).toFixed(2) + 'GB'
  } else {
    return 0 + 'B'
  }
}

export const timePoor = (data) => {
  const o = new Date().getTime()
  const n = new Date(data).getTime()
  const f = Math.abs(n - o)
  if (f < 6e4) {
    return '1分钟'
  }
  if (f < 36e5) {
    return parseInt(f / 6e4) + '分钟'
  }
  if (f < 864e5) {
    return parseInt(f / 36e5) + '小时'
  }
  if (f < 2592e6) {
    return parseInt(f / 864e5) + '天'
  }
  if (f < 31536e6) {
    return parseInt(f / 2592e6) + '个月'
  }
  return parseInt(f / 31536e6) + '年'
}

export const numberToString = (number) => {
  number = number + ''
  if (number.match(/\D/) || number.length >= 14) return
  const zhArray = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'] // 数字对应中文
  const baseArray = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万'] //进位填充字符，第一位是 个位，可省略
  let string = String(number)
    .split('')
    .reverse()
    .map((item, index) => {
      // 把数字切割成数组并倒序排列，然后进行遍历转成中文
      // 如果当前位为0，直接输出数字， 否则输出 数字 + 进位填充字符
      item = Number(item) == 0 ? zhArray[Number(item)] : zhArray[Number(item)] + baseArray[index]
      return item
    })
    .reverse()
    .join('') // 倒叙回来数组，拼接成字符串
  string = string.replace(/^一十/, '十') // 如果以 一十 开头，可省略一
  string = string.replace(/零+/, '零') // 如果有多位相邻的零，只写一个即可
  string = string[string.length - 1] === '零' ? string.slice(0, string.length - 1) : string
  return string
}
export const urlToBase64 = (url, size = false) => {
  return new Promise((resolve, reject) => {
    let canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    let img = new Image()
    img.crossOrigin = 'Anonymous' // 解决Canvas.toDataURL 图片跨域问题
    img.src = url
    img.onload = function () {
      canvas.width = img.width
      canvas.height = img.height
      ctx.fillStyle = '#fff' // canvas背景填充颜色默认为黑色
      ctx.fillRect(0, 0, img.width, img.height)
      ctx.drawImage(img, 0, 0) // 参数可自定义
      const dataURL = canvas.toDataURL('image/png') // 获取Base64编码
      resolve(size ? { width: img.width, height: img.height, dataURL } : dataURL)
      canvas = null // 清除canvas元素
      img = null // 清除img元素
    }
    img.onerror = () => {
      resolve('')
      // reject(new Error('Could not load image at ' + url))
    }
  })
}
// 递归处理对象中的null值，将其替换为空字符串
export const replaceNullWithEmptyString = (obj) => {
  // 如果传入的是null或undefined，直接返回空字符串
  if (obj === null || obj === undefined) return ''

  // 如果是基本类型（string, number, boolean, symbol），直接返回
  if (typeof obj !== 'object') return obj

  // 如果是Date对象，直接返回
  if (obj instanceof Date) return obj

  // 如果是RegExp对象，直接返回
  if (obj instanceof RegExp) return obj

  // 如果是Function，直接返回
  if (typeof obj === 'function') return obj

  // 如果是数组，递归处理每个元素
  if (Array.isArray(obj)) return obj.map((item) => replaceNullWithEmptyString(item))

  // 如果是对象，递归处理每个属性
  if (typeof obj === 'object') {
    const result = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        result[key] = replaceNullWithEmptyString(obj[key])
      }
    }
    return result
  }

  // 其他情况直接返回
  return obj
}

// 更严格的版本：只处理null，保留undefined
export const replaceOnlyNullWithEmptyString = (obj) => {
  // 只处理null，保留undefined
  if (obj === null) return ''

  // 如果是基本类型（string, number, boolean, symbol），直接返回
  if (typeof obj !== 'object') return obj

  // 如果是Date对象，直接返回
  if (obj instanceof Date) return obj

  // 如果是RegExp对象，直接返回
  if (obj instanceof RegExp) return obj

  // 如果是Function，直接返回
  if (typeof obj === 'function') return obj

  // 如果是数组，递归处理每个元素
  if (Array.isArray(obj)) return obj.map((item) => replaceOnlyNullWithEmptyString(item))

  // 如果是对象，递归处理每个属性
  if (typeof obj === 'object') {
    const result = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        result[key] = replaceOnlyNullWithEmptyString(obj[key])
      }
    }
    return result
  }

  // 其他情况直接返回
  return obj
}

export const initFont = () => {
  const baseURL =
    process.env.NODE_ENV === 'development' ? 'http://localhost:2000/' : process.env?.VUE_APP_CATALOG || '/'
  const url = `${baseURL}global/font/`
  let fontList = [
    {
      fontUrl: `${url}FZPHFW.TTF`,
      fontFamily: 'FZPHFW',
      label: 'FZPHFW'
    },
    {
      fontUrl: `${url}LCD2Bold.ttf`,
      fontFamily: 'LCD2',
      label: 'LCD2 Bold'
    },
    {
      fontUrl: `${url}YouSheBiaoTiHei.ttf`,
      fontFamily: 'YouSheBiaoTiHei',
      label: 'YouSheBiaoTiHei'
    },
    {
      fontUrl: `${url}zangwenxinbai.TTF`,
      fontFamily: 'zangwenxinbai',
      label: 'zangwenxinbai'
    }
  ]
  async function loadFonts(fontFamily, fontUrl) {
    const font = new FontFace(fontFamily, `url(${fontUrl})`)
    await font.load()
    document.fonts.add(font)
  }
  for (let i in fontList) {
    loadFonts(fontList[i].fontFamily, fontList[i].fontUrl)
  }
}
/**
 * @description: 设置系统字体
 * @param {String} family 字体
 */
export const change_font_family = (family) => {
  sessionStorage.setItem('data-family', family)
  window.document.documentElement.setAttribute('data-family', family)
}
export const get_font_family = () => sessionStorage.getItem('data-family') || 'cn'
/**
 * @description: 获取地图文件
 * @param {String} url 文件地址
 */
export const getMap = (areaLevel, areaId) =>
  new Promise((resolve, reject) => {
    const baseURL =
      process.env.NODE_ENV === 'development' ? 'http://localhost:2000/' : process.env?.VUE_APP_CATALOG || '/'
    const url = `${baseURL}map/${areaLevel}/${areaId}.json`
    axios
      .get(url)
      .then((res) => {
        resolve(res.data)
      })
      .catch((err) => {
        reject(err)
      })
  })
export const isNewVersion = async () => {
  const baseURL =
    process.env.NODE_ENV === 'development' ? 'http://localhost:2000/' : process.env?.VUE_APP_CATALOG || '/'
  const url = `${baseURL}global/json/version.json?id=${new Date().getTime()}`
  const { data } = await axios.get(url)
  const version = localStorage.getItem('version') || ''
  if (version && version !== data.version) {
    console.log('版本更新')
    localStorage.setItem('version', data.version)
    window.location.reload(true)
    // window.location.reload(window.location.href)
  } else {
    localStorage.setItem('version', data.version)
  }
}
export const throttle = (func, delay) => {
  let timeout = null
  return function () {
    const context = this
    const args = arguments
    if (!timeout) {
      timeout = setTimeout(function () {
        timeout = null
        func.apply(context, args)
      }, delay)
    }
  }
}
export const throttleIsNewVersion = throttle(isNewVersion, 1000)

/**
 * 字数统计方法 - 模拟TinyMCE wordcount插件算法
 *
 * @description 该方法用于统计富文本内容的字数，包括字词数、字数(不含空格)和字数(含空格)
 * 算法逻辑与TinyMCE wordcount插件保持一致：
 * - 字词数：中文字符 + 英文单词 + 数字（排除标点符号）
 * - 字数(不含空格)：所有非空白字符的总数
 * - 字数：所有字符的总数（包含空格）
 *
 * @param {string} richTextContent - 富文本内容，支持HTML标签和HTML实体
 * @returns {object} 字数统计结果对象
 * @returns {number} returns.totalWords - 字词数（排除标点符号）
 * @returns {number} returns.charCountNoSpace - 字数(不含空格)
 * @returns {number} returns.charCount - 字数(含空格)
 *
 * @example
 * const result = calculateWordCount('<p>你好，世界！Hello World 123</p>')
 * // 返回: { totalWords: 8, charCountNoSpace: 12, charCount: 12 }
 *
 * <AUTHOR> Assistant
 * @since 2025-01-14
 */
export const calculateWordCount = (richTextContent) => {
  // 参数验证：如果内容为空，返回零值
  if (!richTextContent) {
    return { totalWords: 0, charCountNoSpace: 0, charCount: 0 }
  }

  // 调试日志：输出原始富文本内容
  // console.log('原始富文本内容:', richTextContent)

  // 第一步：移除所有HTML标签，保留纯文本内容
  // 使用正则表达式 <[^>]+> 匹配所有HTML标签
  const plainText = richTextContent.replace(/<[^>]+>/g, '')
  // console.log('移除HTML标签后:', plainText)

  // 第二步：解码HTML实体字符，转换为可读的普通字符
  // 常见的HTML实体包括：&nbsp;(空格)、&amp;(&)、&lt;(<)、&gt;(>)等
  const decodedText = plainText
    .replace(/&nbsp;/g, ' ') // 不间断空格
    .replace(/&amp;/g, '&') // 和号
    .replace(/&lt;/g, '<') // 小于号
    .replace(/&gt;/g, '>') // 大于号
    .replace(/&quot;/g, '"') // 双引号
    .replace(/&#39;/g, "'") // 单引号
    .replace(/&ldquo;/g, '"') // 左双引号
    .replace(/&rdquo;/g, '"') // 右双引号
    .replace(/&lsquo;/g, "'") // 左单引号
    .replace(/&rsquo;/g, "'") // 右单引号
    .replace(/&hellip;/g, '...') // 省略号
    .replace(/&mdash;/g, '—') // 长破折号
    .replace(/&ndash;/g, '–') // 短破折号
  // console.log('解码HTML实体后:', decodedText)

  // 第三步：统计字数(含空格) - 所有字符的总数
  const charCount = decodedText.length

  // 第四步：统计字数(不含空格) - 移除所有空白字符后的字符总数
  // 使用正则表达式 \s 匹配所有空白字符（空格、制表符、换行符等）
  const charCountNoSpace = decodedText.replace(/\s/g, '').length

  // 第五步：统计字词数 - 使用TinyMCE wordcount插件的算法
  // 移除所有空白字符，准备进行字符分类统计
  const cleanText = decodedText.replace(/\s/g, '')

  // 使用正则表达式分离不同类型的字符：
  // [\u4e00-\u9fa5] - 匹配所有中文字符（Unicode范围：4E00-9FA5）
  // [a-zA-Z]+ - 匹配连续的英文字母（一个或多个）
  // \d+ - 匹配连续的数字（一个或多个）
  // [^\u4e00-\u9fa5a-zA-Z0-9\s] - 匹配既不是中文、英文、数字，也不是空白字符的字符（标点符号等）
  const chineseChars = cleanText.match(/[\u4e00-\u9fa5]/g) || []
  const englishWords = cleanText.match(/[a-zA-Z]+/g) || []
  const numbers = cleanText.match(/\d+/g) || []
  // const punctuation = cleanText.match(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g) || []

  // 计算原始的字词数：包含所有字符类型
  // const totalWords = chineseChars.length + englishWords.length + numbers.length + punctuation.length

  // 计算TinyMCE算法的字词数：排除标点符号
  // 只计算：中文字符 + 英文单词 + 数字
  // 这样计算出的结果与TinyMCE wordcount插件的"字词数"保持一致
  const wordCount = chineseChars.length + englishWords.length + numbers.length
  const accurateTotalWords = wordCount

  // 输出详细的调试信息，帮助开发者理解统计过程
  // console.log('字数统计详情:', {
  //   chineseChars: chineseChars.length, // 中文字符数量
  //   englishWords: englishWords.length, // 英文单词数量
  //   numbers: numbers.length, // 数字数量
  //   punctuation: punctuation.length, // 标点符号数量
  //   totalWords, // 原始算法结果（包含所有字符）
  //   accurateTotalWords, // 优化算法结果（排除标点符号）
  //   charCountNoSpace, // 字数(不含空格)
  //   charCount // 字数(含空格)
  // })

  // 返回统计结果对象
  return {
    totalWords: accurateTotalWords, // 字词数：中文字符 + 英文单词 + 数字（排除标点符号）
    charCountNoSpace, // 字数(不含空格)：所有非空白字符
    charCount // 字数：所有字符(含空格)
  }
}

/**
 * 智能HTML标签去除方法 - 保留段落结构
 * @param {string} htmlString - 包含HTML标签的字符串
 * @param {Object} options - 配置选项
 * @param {boolean} options.decodeEntities - 是否解码HTML实体，默认true
 * @param {boolean} options.trimWhitespace - 是否去除首尾空白，默认true
 * @param {boolean} options.collapseWhitespace - 是否合并多个空白字符，默认true
 * @param {boolean} options.preserveParagraphs - 是否保留段落结构，默认true
 * @param {boolean} options.preserveLineBreaks - 是否保留换行，默认true
 * @returns {string} 处理后的纯文本（保留段落结构）
 */
export const stripHtmlTagsWithStructure = (htmlString, options = {}) => {
  if (!htmlString || typeof htmlString !== 'string') {
    return ''
  }

  const {
    decodeEntities = true,
    trimWhitespace = true,
    collapseWhitespace = true,
    preserveParagraphs = true,
    preserveLineBreaks = true
  } = options

  let result = htmlString

  // 第一步：处理段落和换行标签，在去除前先转换为换行符
  if (preserveParagraphs || preserveLineBreaks) {
    // 块级元素转换为双换行（段落分隔）
    const blockElements = ['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'blockquote', 'section', 'article']
    const blockRegex = new RegExp(`<\\/?(${blockElements.join('|')})[^>]*>`, 'gi')
    result = result.replace(blockRegex, '\n\n')

    // 换行元素转换为单换行
    const lineBreakElements = ['br', 'hr']
    const lineBreakRegex = new RegExp(`<\\/?(${lineBreakElements.join('|')})[^>]*>`, 'gi')
    result = result.replace(lineBreakRegex, '\n')
  }

  // 第二步：移除HTML注释
  result = result.replace(/<!--[\s\S]*?-->/g, '')

  // 第三步：移除script和style标签及其内容
  result = result.replace(/<(script|style)[^>]*>[\s\S]*?<\/\1>/gi, '')

  // 第四步：移除所有剩余的HTML标签
  result = result.replace(/<[^>]+>/g, '')

  // 第五步：解码HTML实体
  if (decodeEntities) {
    result = decodeHtmlEntities(result)
  }

  // 第六步：处理空白字符
  if (collapseWhitespace) {
    // 合并多个连续的空格为单个空格
    result = result.replace(/[ \t]+/g, ' ')
    // 合并多个连续的换行符
    result = result.replace(/\n\s*\n\s*\n+/g, '\n\n')
  }

  if (trimWhitespace) {
    result = result.trim()
  }

  return result
}

/**
 * HTML实体解码函数
 * @param {string} text - 包含HTML实体的文本
 * @returns {string} 解码后的文本
 */
const decodeHtmlEntities = (text) => {
  const entityMap = {
    '&nbsp;': ' ',
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
    '&apos;': "'",
    '&ldquo;': '"',
    '&rdquo;': '"',
    '&lsquo;': "'",
    '&rsquo;': "'",
    '&hellip;': '...',
    '&mdash;': '—',
    '&ndash;': '–',
    '&copy;': '©',
    '&reg;': '®',
    '&trade;': '™',
    '&euro;': '€',
    '&pound;': '£',
    '&yen;': '¥',
    '&cent;': '¢'
  }

  // 处理命名实体
  let result = text
  Object.entries(entityMap).forEach(([entity, char]) => {
    result = result.replace(new RegExp(entity, 'gi'), char)
  })

  // 处理数字实体 &#123; 或 &#x1A;
  result = result.replace(/&#(\d+);/g, (match, dec) => {
    return String.fromCharCode(dec)
  })

  result = result.replace(/&#x([0-9A-Fa-f]+);/g, (match, hex) => {
    return String.fromCharCode(parseInt(hex, 16))
  })

  return result
}
