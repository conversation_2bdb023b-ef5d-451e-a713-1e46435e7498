import store from '@/store'
import api from '@/api'
import config from 'common/config'
import { changeTheme } from 'common/js/theme'
import { get_font_family, change_font_family } from 'common/js/utils'
export const microAppMain = (state, prev) => {
  if (JSON.stringify(state.theme) !== JSON.stringify(prev.theme) || JSON.stringify(store.state.theme) !== JSON.stringify(state.theme)) {
    store.commit('setTheme', state.theme)
  }
  if (JSON.stringify(state.user) !== JSON.stringify(prev.user) || JSON.stringify(store.state.user) !== JSON.stringify(state.user)) {
    store.commit('setUser', state.user)
  }
  if (JSON.stringify(state.menu) !== JSON.stringify(prev.menu) || JSON.stringify(store.state.menu) !== JSON.stringify(state.menu)) {
    store.commit('setMenu', state.menu)
  }
  if (JSON.stringify(state.area) !== JSON.stringify(prev.area) || JSON.stringify(store.state.area) !== JSON.stringify(state.area)) {
    store.commit('setArea', state.area)
  }
  if (JSON.stringify(state.role) !== JSON.stringify(prev.role) || JSON.stringify(store.state.role) !== JSON.stringify(state.role)) {
    store.commit('setRole', state.role)
  }
  if (JSON.stringify(state.readConfig) !== JSON.stringify(prev.readConfig) || JSON.stringify(store.state.readConfig) !== JSON.stringify(state.readConfig)) {
    store.commit('setReadConfig', state.readConfig)
  }
  if (JSON.stringify(state.readOpenConfig) !== JSON.stringify(prev.readOpenConfig) || JSON.stringify(store.state.readOpenConfig) !== JSON.stringify(state.readOpenConfig)) {
    store.commit('setReadOpenConfig', state.readOpenConfig)
  }
}

export const currentTheme = async () => {
  try {
    const { data } = await api.currentTheme({})
    changeTheme(data)
  } catch (err) {
    changeTheme()
  }
}
const globalReadOpenConfig = async () => {
  const { data } = await api.globalReadOpenConfig({ codes: ['systemSign', 'systemPlatform', 'systemName', 'platformAreaName', 'loginNameLineFeedPosition', 'systemGrayscale', 'appOnlyHeader', 'appDownloadUrl', 'forbidWeakPassword', 'noVerifyAdmin'] })
  sessionStorage.setItem('noVerifyAdmin', data.noVerifyAdmin)
  document.title = data?.systemName
  if (data?.systemGrayscale === 'true') { window.document.documentElement.setAttribute('grey', '1') }
  store.commit('setReadOpenConfig', data)
}
export const selectUserJson = async () => {
  const { data } = await api.selectUserJson()
  customSelectUser(data)
}
export const customSelectUser = async (selectUserData) => {
  try {
    const { data } = await api.customSelectUser({})
    store.commit('setSelectUser', { ...selectUserData, ...data })
  } catch (err) {
    store.commit('setSelectUser', selectUserData)
  }
}
export const microAppMethod = () => {
  // 本地调试
  if (!window.__POWERED_BY_QIANKUN__) {
    window.__PUBLIC__ = true
    const $favicon = document.querySelector('link[rel="icon"]')
    $favicon.href = `${config.API_URL}/pageImg/open/logo`
    currentTheme()
    globalReadOpenConfig()
    change_font_family(get_font_family())
    if (sessionStorage.getItem('token')) {
      store.dispatch('loginUser')
    }
  }
  if (window.__POWERED_BY_QIANKUN__) {
    selectUserJson()
  }
}
