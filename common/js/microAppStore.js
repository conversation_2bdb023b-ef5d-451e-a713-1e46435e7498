import api from '@/api'
import { loginMenu, loginArea, loginRole, globalReadConfig, globalReadOpenConfig } from 'common/js/GlobalMethod'
export default {
  state: {
    theme: {},
    user: {},
    menu: [],
    area: [],
    role: [],
    selectUser: {},
    readConfig: {},
    readOpenConfig: {}
  },
  getters: {
    getThemeFn (state) {
      return state.theme
    },
    getUserFn (state) {
      return state.user || JSON.parse(sessionStorage.getItem('user'))
    },
    getMenuFn (state) {
      return state.menu || JSON.parse(sessionStorage.getItem('menu'))
    },
    getAreaFn (state) {
      return state.area || JSON.parse(sessionStorage.getItem('area'))
    },
    getRoleFn (state) {
      return state.role || JSON.parse(sessionStorage.getItem('role'))
    },
    getSelectUser (state) {
      return state.selectUser
    },
    getReadConfig (state) {
      return state.readConfig
    },
    getReadOpenConfig (state) {
      return state.readOpenConfig
    }
  },
  mutations: {
    setTheme (state, theme = {}) {
      state.theme = theme
    },
    setUser (state, user = {}) {
      state.user = user
    },
    setMenu (state, menu = []) {
      state.menu = menu
    },
    setArea (state, area = []) {
      state.area = area
    },
    setRole (state, role = []) {
      state.role = role
    },
    setSelectUser (state, selectUser = {}) {
      state.selectUser = selectUser
    },
    setReadConfig (state, readConfig = {}) {
      state.readConfig = readConfig
    },
    setReadOpenConfig (state, readOpenConfig = {}) {
      state.readOpenConfig = readOpenConfig
    }
  },
  actions: {
    async loginUser ({ commit, dispatch }) {
      const { data: user } = await api.loginUser()
      user.image = user.photo || user.headImg ? api.fileURL(user.photo || user.headImg) : api.defaultImgURL('default_user_head.jpg')
      sessionStorage.setItem('user', JSON.stringify(user))
      sessionStorage.setItem('AreaId', user.areaId)
      commit('setUser', user)
      loginMenu()
      loginArea()
      loginRole()
      globalReadConfig()
      globalReadOpenConfig()
      // dispatch('globalSocketMethod')
    },
    globalSocketMethod ({ state }) {
      const token = sessionStorage.getItem('token') || ''
      const socket = io('http://101.35.130.122:54390/push', {
        'path': '/push',
        'force new connection': true,
        'reconnection': true,
        'reconnectionDelay': 2000,
        'reconnectionDelayMax': 5000,
        'reconnectionAttempts': 10,
        'timeout': 10000,
        'transports': ['websocket', 'polling'],
        'query': { 'Authorization': token, 'uri': window.location.pathname }
      })
      state.socket = socket
    }
  }
}
