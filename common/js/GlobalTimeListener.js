// 全局时间监听器 - 单例模式
class GlobalTimeListener {
  constructor() {
    this.storageKey = 'globalTargetTime'
    this.checkInterval = 1000
    this.timer = null
    this.isRunning = false
    this.callback = null // 只有一个回调函数
  }

  // 设置目标时间和回调
  setTargetTime (targetTime, callback) {
    let timeValue
    if (typeof targetTime === 'string') {
      timeValue = targetTime.includes('T') || targetTime.includes('-')
        ? new Date(targetTime).getTime()
        : parseInt(targetTime)
    } else if (typeof targetTime === 'number') {
      timeValue = targetTime
    } else if (targetTime instanceof Date) {
      timeValue = targetTime.getTime()
    }

    if (isNaN(timeValue)) throw new Error('无效的时间格式')

    sessionStorage.setItem(this.storageKey, timeValue.toString())
    this.callback = callback
    this.start()
  }

  // 获取目标时间
  getTargetTime () {
    const timeStr = sessionStorage.getItem(this.storageKey)
    return timeStr ? parseInt(timeStr) : null
  }

  // 开始监听
  start () {
    // 如果已经在运行，先停止
    if (this.isRunning) {
      this.stop()
    }

    // 检查是否有目标时间
    const targetTime = this.getTargetTime()
    if (!targetTime) {
      return
    }

    // 检查目标时间是否小于当前时间
    if (targetTime <= Date.now()) {
      console.log('GlobalTimeListener: 目标时间已过期，停止监听')
      this.stop()
      return
    }

    this.startTimer()
    this.isRunning = true
  }

  // 停止监听
  stop () {
    if (!this.isRunning) return
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    this.isRunning = false
  }

  // 启动定时器
  startTimer () {
    this.timer = setInterval(() => {
      const targetTime = this.getTargetTime()
      if (!targetTime) {
        this.stop()
        return
      }

      if (Date.now() >= targetTime) {
        // 触发回调函数
        if (this.callback) {
          try {
            this.callback()
          } catch (error) {
            console.error('GlobalTimeListener: 回调函数执行错误', error)
          }
        }
        this.stop()
      }
    }, this.checkInterval)
  }
}

// 创建全局单例实例
const globalTimeListener = new GlobalTimeListener()

// ==================== 全局时间监听器导出方法 ====================

/**
 * 设置时间监听
 * @param {string|number|Date} targetTime - 目标时间
 * @param {Function} callback - 时间到达时的回调函数
 * 重复调用会更新触发时间
 */
export const setTimeListener = (targetTime, callback) => {
  globalTimeListener.setTargetTime(targetTime, callback)
}

/**
 * 停止时间监听
 */
export const stopTimeListener = () => {
  globalTimeListener.stop()
}