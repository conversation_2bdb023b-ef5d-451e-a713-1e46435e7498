const menuPermission = (data, id) => {
  var dataList = []
  for (let i = 0, len = data.length; i < len; i++) {
    if (data[i].menuId === id) { dataList = data[i].permissions }
    if (data[i].children?.length) { dataList = [...dataList, ...menuPermission(data[i].children, id)] }
  }
  return dataList
}
const permissionList = (id) => {
  if (!id) return []
  const menu = JSON.parse(sessionStorage.getItem('menu'))
  return menuPermission(menu, id)
}

const isPermissionList = (logo, hasData) => {
  if (!logo.length || !hasData.length) return false
  var ishasPermission = true
  for (let i = 0, len = logo.length; i < len; i++) {
    if (!hasData.includes(logo[i])) { ishasPermission = false }
  }
  return ishasPermission
}

export const hasPermission = (logo, arg) => {
  const user = JSON.parse(sessionStorage.getItem('user')) || {}
  const noVerifyAdmin = sessionStorage.getItem('noVerifyAdmin') || ''
  if (user.accountId === '1' && noVerifyAdmin === 'true') return true
  var ishasPermission = false
  const hasList = JSON.parse(sessionStorage.getItem('has')) || {}
  const hasData = hasList?.query?.oldRouteId ? permissionList(hasList?.query?.oldRouteId) : hasList?.has || []
  if (logo instanceof Array) {
    if (arg === 'all') {
      ishasPermission = isPermissionList(logo, hasData)
    } else {
      for (let i = 0, len = hasData.length; i < len; i++) {
        if (logo.includes(hasData[i])) { ishasPermission = true }
      }
    }
  } else {
    if (hasData.includes(logo)) { ishasPermission = true }
  }
  return ishasPermission
}
